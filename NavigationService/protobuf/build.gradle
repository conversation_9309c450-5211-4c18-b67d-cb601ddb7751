apply plugin: 'com.android.library'
apply plugin: 'com.google.protobuf'

android {

    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    sourceSets {
        main {
            // 定义proto文件目录
            proto {
                srcDir 'src/main/proto'
                //add new dir here
                include 'src/main/proto/bean'
                include 'src/main/proto/event'
                include 'src/main/proto/request'
                include 'src/main/proto/response'
                include 'src/main/proto/waiter'
                include '**/*.proto'
            }
        }
    }

}

protobuf {
    protoc {
        // You still need protoc like in the non-Android case
        if (osdetector.os == "osx") {
            artifact = 'com.google.protobuf:protoc:3.6.1:osx-x86_64'
        } else {
            artifact = 'com.google.protobuf:protoc:3.6.1'
        }
    }
    generateProtoTasks {
        all().each { task ->
            task.builtins {
                java {}
            }
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation rootProject.protobuf_java
    implementation rootProject.protobuf_protoc
}
