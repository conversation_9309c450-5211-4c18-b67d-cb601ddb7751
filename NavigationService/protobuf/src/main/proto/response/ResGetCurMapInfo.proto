syntax = "proto3";

import public "response/ResHead.proto";
import public "bean/LandMarkBean.proto";
import public "bean/MapIdBean.proto";
import public "bean/MapBean.proto";

package com.ainirobot.navigationservice.protocol.response;

option java_outer_classname = "ResGetCurMapInfoCreator";//

message ResGetCurMapInfoProto {
    ResHeadProto header = 1;
    ParamCurMapInfo param = 2;
}

message ParamCurMapInfo {
    com.ainirobot.navigationservice.protocol.bean.MapIdBeanProto mapIdInfo = 1;
    repeated com.ainirobot.navigationservice.protocol.bean.LandMarkBeanProto landMarkList = 2;
    com.ainirobot.navigationservice.protocol.bean.MapBeanProto mapContent = 3;
}