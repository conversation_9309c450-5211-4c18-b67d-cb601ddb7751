syntax = "proto3";

import public "response/ResHead.proto";

package com.ainirobot.navigationservice.protocol.response;

option java_outer_classname = "ResGetSystemInfoCreator";//

message ResGetSystemInfoProto {
    ResHeadProto header = 1;
    ParamSystemInfo param = 2;
}

message ParamSystemInfo {
    string romVersion = 1;
    string apkVersion = 2;
    string boardId = 3;
    string serialId = 4;
    int64 hardDiskTotalMb = 5;
    int64 hardDiskLeftMb = 6;
    bool hasCalibrationFile = 7;
    string systemTime = 8;
}