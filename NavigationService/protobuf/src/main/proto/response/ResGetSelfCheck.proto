syntax = "proto3";

import public "response/ResHead.proto";

package com.ainirobot.navigationservice.protocol.response;

option java_outer_classname = "ResGetSelfCheckCreator";//

message ResGetSelfCheckProto {
    ResHeadProto header = 1;
    ParamSelfCheck param = 2;
}

message ParamSelfCheck {
    bool isFishEyeReady = 1;
    bool isRgbdReady = 2;
    bool isSonarReady = 3;
    bool isLaserReady = 4;
    bool isIrReady = 5;
    bool isOdomReady = 6;
    bool isCalibrationReady = 7;
    bool isHardDiskSpaceOk = 8;
}
