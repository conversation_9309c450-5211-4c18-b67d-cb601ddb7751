syntax = "proto3";

import public "response/ResHead.proto";
import public "bean/LandMarkBean.proto";

package com.ainirobot.navigationservice.protocol.response;

option java_outer_classname = "ResModifyLandmarkInfoCreator";//

message ResModifyLandmarkInfoProto {
    ResHeadProto header = 1;
    ParamModifyLandMark param = 2;
}

message ParamModifyLandMark {
    repeated com.ainirobot.navigationservice.protocol.bean.LandMarkBeanProto landMarkList = 1;
}