syntax = "proto3";

import public "response/ResHead.proto";
import public "bean/LandMarkBean.proto";
import public "bean/MapIdBean.proto";
import public "bean/MapBean.proto";

package com.ainirobot.navigationservice.protocol.response;

option java_outer_classname = "ResStopCreateMapCreator";//

message ResStopCreateMapProto {
    ResHeadProto header = 1;
    ParamStopCreateMap param = 2;
}

message ParamStopCreateMap {
    com.ainirobot.navigationservice.protocol.bean.MapIdBeanProto mapIdInfo = 1;
    repeated com.ainirobot.navigationservice.protocol.bean.LandMarkBeanProto landMarkList = 2;
    com.ainirobot.navigationservice.protocol.bean.MapBeanProto mapContent = 3;
}