syntax = "proto3";

import public "response/ResHead.proto";
import public "bean/LandMarkBean.proto";

package com.ainirobot.navigationservice.protocol.response;

option java_outer_classname = "ResAddLandMarkCreator";//

message ResAddLandMarkProto {
    ResHeadProto header = 1;
    ParamAddLandMark param = 2;
}

message ParamAddLandMark {
    repeated com.ainirobot.navigationservice.protocol.bean.LandMarkBeanProto landMarkList = 1;
}