syntax = "proto3";

import public "response/ResHead.proto";

package com.ainirobot.navigationservice.protocol.response;

option java_outer_classname = "ResGetEstimateStateCreator";//

message ResGetEstimateStateProto {
    ResHeadProto header = 1;
    ParamEstimateState param = 2;
}

message ParamEstimateState {
    int32 state = 1; //  POSE_STATE_NONE = 0; POSE_STATE_LOST = 1; POSE_STATE_NORMAL = 2;
}