syntax = "proto3";

import public "response/ResHead.proto";
import public "bean/LandMarkBean.proto";

package com.ainirobot.navigationservice.protocol.response;

option java_outer_classname = "ResGetLandMarkListCreator";//

message ResGetLandMarkListProto {
    ResHeadProto header = 1;
    ParamGetLandMark param = 2;
}

message ParamGetLandMark {
    repeated com.ainirobot.navigationservice.protocol.bean.LandMarkBeanProto landMarkList = 1;
}