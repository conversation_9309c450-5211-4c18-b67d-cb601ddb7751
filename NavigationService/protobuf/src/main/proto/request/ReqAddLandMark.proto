syntax = "proto3";

import public "request/ReqHead.proto";
import public "bean/LandMarkBean.proto";

package com.ainirobot.navigationservice.protocol.request;

option java_outer_classname = "ReqAddLandMarkCreator";//1

message ReqAddLandMarkProto {//2
    ReqHeadProto header = 1;
    ParamAddLandMark param = 2;
}

message ParamAddLandMark {
    string mapId = 1;
    com.ainirobot.navigationservice.protocol.bean.LandMarkBeanProto newLandMark = 2;
}