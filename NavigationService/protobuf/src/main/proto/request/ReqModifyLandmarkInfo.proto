syntax = "proto3";

import public "request/ReqHead.proto";
import public "bean/LandMarkBean.proto";

package com.ainirobot.navigationservice.protocol.request;

option java_outer_classname = "ReqModifyLandmarkInfoCreator";//1

message ReqModifyLandmarkInfoProto {
    ReqHeadProto header = 1;
    ParamModifyLandmarkInfo param = 2;
}

message ParamModifyLandmarkInfo {
    string mapId = 1;
    string landMarkId = 2;
    com.ainirobot.navigationservice.protocol.bean.LandMarkBeanProto newLandMark = 3;
}