syntax = "proto3";

import public "request/ReqHead.proto";

import public "bean/PoseBean.proto";
import public "bean/VelocityBean.proto";

package com.ainirobot.navigationservice.protocol.request;

option java_outer_classname = "ReqSetGoalCreator";//1

message ReqSetGoalProto {//2
    ReqHeadProto header = 1;
    ParamSetGoal param = 2;
}

message ParamSetGoal {
    com.ainirobot.navigationservice.protocol.bean.PoseBeanProto pose = 1;
    com.ainirobot.navigationservice.protocol.bean.VelocityBeanProto velocity = 2;
    bool withoutAdjustAngle = 3;
}