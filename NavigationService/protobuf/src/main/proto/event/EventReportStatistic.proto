syntax = "proto3";

import public "event/EventHead.proto";

package com.ainirobot.navigationservice.protocol.event;

option java_outer_classname = "EventReportStatisticCreator";

message EventReportStatisticProto {
    EventHeadProto header = 1;
    ParamReportStatistic param = 2;
}

message ParamReportStatistic {
    int32 type = 1;
    int32 int1 = 2;
    int32 int2 = 3;
    int32 int3 = 4;
    int32 int4 = 5;
    double double1 = 6;
    double double2 = 7;
    double double3 = 8;
    double double4 = 9;
    string stringValue = 10;
}