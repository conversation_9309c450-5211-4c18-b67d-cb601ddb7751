syntax = "proto3";

import public "event/EventHead.proto";
import public "bean/PoseBean.proto";

package com.ainirobot.navigationservice.protocol.event;

option java_outer_classname = "EventUpdatePoseCreator";

message EventUpdatePoseProto {
    EventHeadProto header = 1;
    ParamPose param = 2;
}

message ParamPose {
    com.ainirobot.navigationservice.protocol.bean.PoseBeanProto pose = 1;
    int32 status = 2;
}