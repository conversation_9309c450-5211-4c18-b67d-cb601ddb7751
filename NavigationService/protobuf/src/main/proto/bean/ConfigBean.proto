syntax = "proto3";


package com.ainirobot.navigationservice.protocol.bean;

option java_outer_classname = "ConfigBeanCreator";//1

message ConfigBeanProto {
    string rosServerIp = 1;
    bool enableCamera = 2;
    int32 deviceType = 3;
    int32 groundType = 4;
    int32 scenesType = 5;
    bool enableFishEye = 6;
    bool enableRgbd = 7;
    bool enableSonar = 8;
    bool enableIr = 9;
    bool mapWithRecord = 10;
    bool recordMono = 11;
    bool recordRgbd = 12;
    bool enableObstaclesAvoid = 13;
    int32 lethalRadius = 14;
    int32 maxLostDistance = 15;
}