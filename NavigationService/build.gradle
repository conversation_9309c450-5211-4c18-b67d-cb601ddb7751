// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.objectboxVersion = "3.5.1"
    repositories {
        jcenter()
        google()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.0'
        //版本必须和服务端保持一致，解析数据很重要
        classpath 'com.google.protobuf:protobuf-gradle-plugin:0.8.8'
        classpath("io.objectbox:objectbox-gradle-plugin:$objectboxVersion")
    }
}

allprojects {
    repositories {
        jcenter()
        google()
        maven { url 'https://jitpack.io' }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

ext {
    versionCode = 21000
    versionName = "2.10.0"

    compileSdkVersion = 28
    buildToolsVersion = '28.0.3'
    minSdkVersion = 26
    targetSdkVersion = 28

    def support_lib_version = '28.0.0'
    test_espresso_core = "com.android.support.test.espresso:espresso-core:2.2.2"
    support_appcompat_v7 = "com.android.support:appcompat-v7:$support_lib_version"
    support_v4 = "com.android.support:support-v4:$support_lib_version"
    support_annotations = "com.android.support:support-annotations:$support_lib_version"
    constraint_layout = "com.android.support.constraint:constraint-layout:2.0.4"
    protobuf_java = "com.google.protobuf:protobuf-java:3.6.1"
    protobuf_protoc = "com.google.protobuf:protoc:3.1.0"
    java_websocket = "org.java-websocket:Java-WebSocket:1.4.0"
    aws_android_sdk = "com.amazonaws:aws-android-sdk-s3:2.37.0"
    android_async = "com.koushikdutta.async:androidasync:2.1.6"
    android_serial_port = 'com.github.licheedev.Android-SerialPort-API:serialport:1.0.1'
    android_usb_serial = "com.github.mik3y:usb-serial-for-android:3.4.1"
    xstream = "com.thoughtworks.xstream:xstream:1.4.16"
}
