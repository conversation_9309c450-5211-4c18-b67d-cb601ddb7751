LOCAL_PATH := $(call my-dir)
include $(CLEAR_VARS)

include $(LOCAL_PATH)/depends.inc

LOCAL_MODULE := $(module_name)
LOCAL_SRC_FILES := $(src)
LOCAL_STATIC_LIBRARIES := $(NINJIA_STATIC_LIBRARIES)
LOCAL_SHARED_LIBRARIES := $(NINJIA_SHARED_LIBRARIESE)
LOCAL_C_INCLUDES := $(NINJIA_C_INCLUDES)
LOCAL_LDLIBS := -llog -lz

include $(BUILD_SHARED_LIBRARY)

$(call module-add-static-depends,$(module_name),NvidiaCudaLibs)
$(call module-add-static-depends,$(module_name),AndroidSystemLibs)
