apply plugin: 'com.android.application'

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        applicationId "com.ainirobot.navigationservice"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode rootProject.ext.versionCode
        versionName rootProject.ext.versionName
//        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
    }
    signingConfigs {
        signConfig {
            keyAlias 'platform'
            keyPassword 'android'
            storeFile file('debug.keystore')
            storePassword 'android'
        }

        signConfig_845 {
            keyAlias 'AiniBox'
            keyPassword 'AiniRobot@9102'
            storeFile file('platform.keystore')
            storePassword 'AiniRobot@9102'
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.signConfig_845
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.signConfig_845
            debuggable true
        }
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
        disable 'MissingTranslation'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }

    packagingOptions {
        doNotStrip '**/*.so'
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation fileTree(include: ['*.aar'], dir: 'libs')
    androidTestImplementation(rootProject.test_espresso_core, {
        exclude group: 'com.android.support', module: 'support-annotations'
    })
    implementation rootProject.support_appcompat_v7
    implementation rootProject.support_v4
    implementation rootProject.support_annotations
    implementation rootProject.constraint_layout
    implementation rootProject.protobuf_java
    implementation rootProject.java_websocket
    implementation rootProject.aws_android_sdk
    implementation rootProject.android_async
    implementation project(':protobuf')
    implementation project(':uwb')

    debugImplementation("io.objectbox:objectbox-android-objectbrowser:$objectboxVersion")
    releaseImplementation("io.objectbox:objectbox-android:$objectboxVersion")
}

task copyHooks(type: Copy) {
    from("../../hooks") {
        include "**"
    }
    into "../../.git/hooks"
}

task setCommitTemplate() {
//    def setCommitTemplate = "git config commit.template ./.git/hooks/commitTemplate".execute()
//    setCommitTemplate.waitFor()
}

preBuild.dependsOn setCommitTemplate
preBuild.dependsOn copyHooks

apply plugin: 'io.objectbox'//必须最后一行