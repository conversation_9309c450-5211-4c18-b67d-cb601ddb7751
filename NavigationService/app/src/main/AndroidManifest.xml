<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.ainirobot.navigationservice"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="com.ainirobot.coreservice.LanguageProvider" />
    <!-- Wifi NAN直连模式功能 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <application
        android:name=".ApplicationWrapper"
        android:allowBackup="true"
        android:icon="@mipmap/orion_default_icon"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/orion_default_icon"
        android:supportsRtl="true"
        android:theme="@android:style/Theme">
<!--        <activity android:name=".TestYueFan"></activity>-->
        <activity android:name=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

<!--        <activity android:name=".TestWaiterActivity">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN"/>-->
<!--                <category android:name="android.intent.category.LAUNCHER"/>-->
<!--            </intent-filter>-->
<!--        </activity>-->
<!--        <activity-->
            <!--android:name=".dance.DanceToolActivity"-->
            <!--android:configChanges="keyboardHidden|orientation|screenSize" />-->

        <service
            android:name=".NavigationService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.ainirobot.robotos.SERVICE" />
            </intent-filter>
        </service>

        <service
            android:name=".JoyStickControlService"
            android:enabled="true"
            android:exported="true" />

        <provider
            android:name=".commonModule.logs.db.LogDataProvider"
            android:authorities="com.ainirobot.navigation.dataprovider"
            android:exported="true" />
        <provider
            android:name=".db.provider.MapDBProvider"
            android:authorities="com.ainirobot.navigation.mapprovider" />
    </application>

</manifest>