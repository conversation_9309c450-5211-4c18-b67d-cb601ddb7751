<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="request_cmd"><!-- 指令不翻译-->
        <item>set_manual_mode</item>
        <item>set_control_mode</item>
        <item>set_fix_point_mode</item>
        <item>charge_pile_recognize</item>
        <item>charge_pile_search</item>
        <item>charge_pile_docking</item>
        <item>force_forward_move</item>
        <item>force_rotation_move</item>
        <item>set_navi_goal_point</item>
        <item>save_mapping_pose</item>
        <item>get_sensor_status</item>
        <item>delete_map</item>
        <item>start_create_map</item>
        <item>get_map_info</item>
        <item>get_landmark_list</item>
        <item>modify_landmark_info</item>
        <item>delete_landmark</item>
        <item>add_landmark</item>
        <item>get_cur_map_info</item>
        <item>switch_map</item>
        <item>get_realtime_map</item>
        <item>get_map_list</item>
        <item>stop_create_map</item>
        <item>set_relocalization</item>
        <item>get_localization_state</item>
        <item>go_on_patrol</item>
        <item>go_charge</item>
        <item>set_navi_speed_param</item>
    </string-array>
    <string-array name="test_cmd"><!-- 指令不翻译-->
        <item>set_manual_mode</item>
        <item>start_create_map</item>
        <item>save_mapping_pose</item>
        <item>stop_create_map</item>
        <item>get_cur_map_info</item>
        <item>switch_map</item>
        <item>set_relocalization</item>
        <item>get_localization_state</item>
        <item>set_navi_goal_point</item>
        <item>go_on_patrol</item>
        <item>go_charge</item>
        <item>set_navi_speed_param</item>
    </string-array>
</resources>