<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">NavigationService</string>
    <string name="stop_navigation">Pare de cruzeiro</string>
    <string name="start_navigation">Comece a cruzar</string>
    <string name="stop_plan_route">Salvar rota de cruzeiro</string>
    <string name="start_plan_route">Comece a planejar a rota de cruzeiro</string>
    <string name="open_radar">Trun On Lidar</string>
    <string name="close_radar">Vire Fff Lidar</string>
    <string name="get_radar_status">Estado de Lidar</string>
    <string name="stop_ros">Pare ROS</string>
    <string name="start_ros">Iniciar ROS</string>
    <string name="interval_time">Intervalo de relatório de tempo ：</string>
    <string name="is_repeat">Quer fazer um cruzeiro repetidamente?</string>
    <string name="speak_content">A hora atual é %1$s %2$s</string>
    <string name="not_support_chinese">O idioma chinês não está disponível</string>
    <string name="reset_location">Reposição</string>
    <string name="nav_ip_empty">O IP do chassi não pode estar vazio</string>
    <string name="reset_pose_estimate">Reposição</string>
    <string name="save_pose">Salvar ponto de posicionamento</string>
    <string name="cancel_navigation">Pare de navegar</string>
    <string name="switch_free">Mudar para o modo livre</string>
    <string name="switch_navigation">Mudar para o modo de navegação</string>
    <string name="get_motion_mode">getMotionMode</string>
    <string name="get_work_mode">Inspecionando Modo Atual</string>
    <string name="stop_create_map">Parar de mapear</string>
    <string name="start_create_map">Comece a mapear</string>
    <string name="new_map_name">Novo Nome do Mapa</string>
    <string name="switch_map">Mudar de mapa</string>
    <string name="map_name">Nome do mapa:</string>
    <string name="stop">Pare</string>
    <string name="stop_direct">Pare imediatamente</string>
    <string name="backward">Ir para trás</string>
    <string name="forward">Vá em frente</string>
    <string name="forward_avoid">Vá em frente evitando obstáculos.</string>
    <string name="turn_right">Vire à direita</string>
    <string name="turn_left">Vire à esquerda</string>
    <string name="change_config">Alterar configuração</string>
    <string name="ros_ip">IP ROS:</string>
    <string name="navigation_ip">IP do chassi:</string>
    <string name="inspect_tk1_error">Falha na conexão do chassi.</string>
    <string name="inspect_tk1_service_start_fail">Tempo de espera para inicialização do serviço de navegação esgotado</string>
    <string name="inspect_tk1_get_sensor_status_fail">Falha na autoinspeção do chassi.</string>
    <string name="inspect_reset_camera">Reposicionar câmera</string>
    <string name="inspect_rgbd">RGBD</string>
    <string name="inspect_laser">Lidar</string>
    <string name="inspect_speedometer">Odômetro</string>
    <string name="inspect_infrared">Infravermelho</string>
    <string name="inspect_laser_available">Dados Lidar</string>
    <string name="inspect_can_control">Conexão do chassi</string>
    <string name="inspect_gyro_ready">Giroscópio</string>
    <string name="inspect_ir_camera_ready">Câmera infravermelha</string>
    <string name="inspect_calibration_ready">Arquivo de Calibração</string>
    <string name="inspect_hard_disk">Espaço no Disco Rígido</string>
    <string name="inspect_ota_socket">Falha na conexão OTA</string>
    <string name="inspect_error">Erro</string>
    <string name="navigation_content">%1$s ponto de carregamento</string>
    <string name="charge_content">Carregando %1$s</string>
    <string name="goaction_content">Navegando em %1$s</string>
    <string name="uwb_set_first">Please set UWB parameters first</string>
</resources>
