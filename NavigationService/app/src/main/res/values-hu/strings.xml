<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">NavigationService</string>
    <string name="stop_navigation">Stop Cruising</string>
    <string name="start_navigation">Start Cruising</string>
    <string name="stop_plan_route">Save Cruising Route</string>
    <string name="start_plan_route">Start Planning Cruising Route</string>
    <string name="open_radar">Trun On Lidar</string>
    <string name="close_radar">Turn Off Lidar</string>
    <string name="get_radar_status">Lidar State</string>
    <string name="stop_ros">Stop ROS</string>
    <string name="start_ros">Start ROS</string>
    <string name="interval_time">Time Reporting Interval ：</string>
    <string name="is_repeat">Whether Cruising Repeatedly?</string>
    <string name="speak_content">The Current Time Is %1$s %2$s</string>
    <string name="not_support_chinese">Chinese language is not available</string>
    <string name="reset_location">Reposition</string>
    <string name="nav_ip_empty">Chassis IP cannot be empty</string>
    <string name="reset_pose_estimate">Reposition</string>
    <string name="save_pose">Save Positioning Spot</string>
    <string name="cancel_navigation">Stop Navigating</string>
    <string name="switch_free">Switch to Free Mode</string>
    <string name="switch_navigation">Switch to Navigating Mode</string>
    <string name="get_motion_mode">getMotionMode</string>
    <string name="get_work_mode">Inspecting Current Mode</string>
    <string name="stop_create_map">Stop mapping</string>
    <string name="start_create_map">Start mapping</string>
    <string name="new_map_name">New Map Name</string>
    <string name="switch_map">Switch Map</string>
    <string name="map_name">Map Name:</string>
    <string name="stop">Stop</string>
    <string name="stop_direct">Stop-Immedieately</string>
    <string name="backward">Go Backward</string>
    <string name="forward">Go Forward</string>
    <string name="forward_avoid">Go Forward with obstacle avoidance.</string>
    <string name="turn_right">Turn Right</string>
    <string name="turn_left">Turn Left</string>
    <string name="change_config">Change Configuration</string>
    <string name="ros_ip">ROS IP :</string>
    <string name="navigation_ip">Chassis IP :</string>
    <string name="inspect_tk1_error">Chassis Connecting Failed.</string>
    <string name="inspect_tk1_service_start_fail">Waiting For Navigation Service Booting Timed Out</string>
    <string name="inspect_tk1_get_sensor_status_fail">Chassis Self Inspecting Failed.</string>
    <string name="inspect_reset_camera">Reposition Camera</string>
    <string name="inspect_rgbd">RGBD</string>
    <string name="inspect_laser">Lidar</string>
    <string name="inspect_speedometer">Odometer</string>
    <string name="inspect_infrared">Infrared</string>
    <string name="inspect_laser_available">Lidar Data</string>
    <string name="inspect_can_control">Chassis Connection</string>
    <string name="inspect_gyro_ready">Gyroscope</string>
    <string name="inspect_ir_camera_ready">Infrared Camera</string>
    <string name="inspect_calibration_ready">Calibration File</string>
    <string name="inspect_hard_disk">Hard Disk Space</string>
    <string name="inspect_ota_socket">OTA Connection Failed</string>
    <string name="inspect_error">Error</string>
    <string name="navigation_content">%1$s Charging Spot</string>
    <string name="charge_content">Charging %1$s</string>
    <string name="goaction_content">Navigating %1$s</string>
    <string name="uwb_set_first">Please set UWB parameters first</string>
</resources>
