<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Servicio Navegación</string>
    <string name="stop_navigation">Detener crucero</string>
    <string name="start_navigation">Empezar el crucero</string>
    <string name="stop_plan_route">Guardar ruta de crucero</string>
    <string name="start_plan_route">Iniciar ruta de crucero</string>
    <string name="open_radar">Truncar en Lidar</string>
    <string name="close_radar">Turn Fff Lidar</string>
    <string name="get_radar_status">Lidar State</string>
    <string name="stop_ros">Detener ROS</string>
    <string name="start_ros">Iniciar ROS</string>
    <string name="interval_time">Intervalo de Reportación de Tiempo ：</string>
    <string name="is_repeat">¿Si el crucero repetidamente?</string>
    <string name="speak_content">La hora actual es %1$s %2$s</string>
    <string name="not_support_chinese">El idioma chino no está disponible</string>
    <string name="reset_location">Reposición</string>
    <string name="nav_ip_empty">La IP del chasis no puede estar vacía</string>
    <string name="reset_pose_estimate">Reposición</string>
    <string name="save_pose">Guardar punto de posición</string>
    <string name="cancel_navigation">Dejar de navegar</string>
    <string name="switch_free">Cambiar a Modo Gratis</string>
    <string name="switch_navigation">Cambiar a Modo Navegación</string>
    <string name="get_motion_mode">getMotionMode</string>
    <string name="get_work_mode">Inspección del modo actual</string>
    <string name="stop_create_map">Detener mapeo</string>
    <string name="start_create_map">Comenzar mapeo</string>
    <string name="new_map_name">Nuevo nombre del mapa</string>
    <string name="switch_map">Cambiar mapa</string>
    <string name="map_name">Nombre del mapa:</string>
    <string name="stop">Parar</string>
    <string name="stop_direct">Detener inmediatamente</string>
    <string name="backward">Retroceder</string>
    <string name="forward">Ir adelante</string>
    <string name="forward_avoid">Adelante, evitando obstáculos.</string>
    <string name="turn_right">Girar a la derecha</string>
    <string name="turn_left">Girar a la izquierda</string>
    <string name="change_config">Cambiar configuración</string>
    <string name="ros_ip">IP ROS :</string>
    <string name="navigation_ip">Chassis IP :</string>
    <string name="inspect_tk1_error">Falló la conexión con Chassis.</string>
    <string name="inspect_tk1_service_start_fail">Tiempo de espera para arrancar el servicio de navegación agotado</string>
    <string name="inspect_tk1_get_sensor_status_fail">Chassis Auto-inspección falló.</string>
    <string name="inspect_reset_camera">Reemplazar cámara</string>
    <string name="inspect_rgbd">RGBD</string>
    <string name="inspect_laser">Lidar</string>
    <string name="inspect_speedometer">Odometer</string>
    <string name="inspect_infrared">Infrarrojos</string>
    <string name="inspect_laser_available">Lidar Data</string>
    <string name="inspect_can_control">Conexión de Chassis</string>
    <string name="inspect_gyro_ready">Giroscopio</string>
    <string name="inspect_ir_camera_ready">Cámara infrarroja</string>
    <string name="inspect_calibration_ready">Archivo de calibración</string>
    <string name="inspect_hard_disk">Espacio de disco duro</string>
    <string name="inspect_ota_socket">Error de conexión OTA</string>
    <string name="inspect_error">Error</string>
    <string name="navigation_content">%1$s punto de carga</string>
    <string name="charge_content">Cargando %1$s</string>
    <string name="goaction_content">Navegando %1$s</string>
    <string name="uwb_set_first">Primero configure los parámetros UWB</string>
</resources>
