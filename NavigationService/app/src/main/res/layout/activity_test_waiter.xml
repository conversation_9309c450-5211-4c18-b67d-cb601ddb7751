<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:padding="10dp"
    tools:context="com.ainirobot.navigationservice.TestWaiterActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="10dp">

        <TextView
            android:id="@+id/text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="版本：该版本仅针对845框架进行界面调试"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="queryEstimate"
                android:text="查询定位"
                android:textSize="14sp"
                android:textAllCaps="false" />

            <TextView
                android:id="@+id/showEstimate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:textColor="@android:color/background_dark"
                android:textSize="14sp" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/navigation_ip"
                android:textAllCaps="false"
                android:textColor="@android:color/black"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/navIp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:minWidth="150dp"
                android:textAllCaps="false" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/changeConfig"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/change_config"
                android:textAllCaps="false" />


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="距离" />

            <EditText
                android:id="@+id/linear_distance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:inputType="numberDecimal"
                android:minWidth="100dp"
                android:textAllCaps="false" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:text="角度" />

            <EditText
                android:id="@+id/angle_distance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:inputType="numberDecimal"
                android:minWidth="100dp"
                android:textAllCaps="false" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:text="线速度" />

            <EditText
                android:id="@+id/linear_speed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:inputType="numberDecimal"
                android:minWidth="100dp"
                android:textAllCaps="false" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:text="角速度" />

            <EditText
                android:id="@+id/angle_speed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:inputType="numberDecimal"
                android:minWidth="100dp"
                android:textAllCaps="false" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/turnLeft"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="80dp"
                android:text="@string/turn_left"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/turnRight"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="80dp"
                android:text="@string/turn_right"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/forward"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="80dp"
                android:text="@string/forward"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/backward"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="80dp"
                android:text="@string/backward"
                android:textAllCaps="false" />


        </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/stop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/stop"
            android:minWidth="80dp"
            android:textAllCaps="false" />

        <Button
            android:id="@+id/forward_avoid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="100dp"
            android:text="@string/forward_avoid"
            android:textAllCaps="false" />

        <Button
            android:id="@+id/stop_direct"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="100dp"
            android:text="@string/stop_direct"
            android:textAllCaps="false" />

    </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/map_name"
                android:textAllCaps="false"
                android:textColor="@android:color/black"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/mapName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:minWidth="100dp"
                android:text="lg102001-1020111641" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/new_map_name"
                android:textAllCaps="false"
                android:textColor="@android:color/black"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/newMapName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:minWidth="100dp" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/switchMap"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="100dp"
                android:text="@string/switch_map"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/createMap"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="100dp"
                android:text="@string/start_create_map"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/stopCreateMap"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="100dp"
                android:text="@string/stop_create_map"
                android:textAllCaps="false" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/cancelNavigation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="100dp"
                android:text="@string/cancel_navigation"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/resetPoseEstimate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="100dp"
                android:text="@string/reset_pose_estimate"
                android:textAllCaps="false" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/checkboxEnableCamera"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="开启Camera"
                android:textColor="@android:color/black"
                android:visibility="visible" />

            <CheckBox
                android:id="@+id/enableRgbd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="开启RGBD"
                android:textColor="@android:color/black" />


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/enableIR"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="开启IR"
                android:textColor="@android:color/black" />

            <CheckBox
                android:id="@+id/enableSonar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="开启Sonar"
                android:textColor="@android:color/black" />

        </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        
        <CheckBox
            android:id="@+id/enableFishEye"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:text="开启FishEye"
            android:textColor="@android:color/black" />

    </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/setRoverConfig"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="设置底盘参数"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/getRoverConfig"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="获取底盘参数"
                android:textAllCaps="false" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/obs_distance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:inputType="numberDecimal"
                android:hint="最大避障距离"
                android:minWidth="100dp"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/navigation_recharge_point"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="导航到回充点"
                android:textAllCaps="false"
                android:textSize="14sp" />

            <Button
                android:id="@+id/automatic_recharge"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="自动回充"
                android:textAllCaps="false"
                android:textSize="14sp" />

        </LinearLayout>

        <Button
            android:id="@+id/get_system_info_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="获取底盘信息"
            android:textAllCaps="false"
            android:textSize="14sp"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/transfer_targets_data_btn"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="转存Targets信息"
                android:textAllCaps="false"
                android:textSize="14sp" />

            <Button
                android:id="@+id/transfer_road_data_btn"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="转存Road信息"
                android:textAllCaps="false"
                android:textSize="14sp" />

            <Button
                android:id="@+id/transfer_map_area_data_btn"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="转存MapArea信息"
                android:textAllCaps="false"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>
</ScrollView>
