<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:padding="10dp"
    tools:context="com.ainirobot.navigationservice.MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="10dp">

        <TextView
            android:id="@+id/text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="版本：地图分包，只适用TK1 4242以上版本"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">
            <Button
                android:id="@+id/openIrLed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="openIrLed"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/closeIrLed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="closeIrLed"
                android:textAllCaps="false" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/startPlan"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/start_plan_route"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/stopPlan"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="@string/stop_plan_route"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/openRadar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="@string/open_radar"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/closeRadar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="@string/close_radar"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/getRadarStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="@string/get_radar_status"
                android:textAllCaps="false" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/interval_time"
                android:textAllCaps="false"
                android:textColor="@android:color/black"
                android:textSize="18sp" />

            <EditText
                android:id="@+id/intervalTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:inputType="number"
                android:minWidth="80dp"
                android:text="1"
                android:textAllCaps="false" />

            <CheckBox
                android:id="@+id/isRepeat"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:checked="true"
                android:text="@string/is_repeat"
                android:textSize="18sp" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/startNavigation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/start_navigation"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/stopNavigation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/stop_navigation"
                android:textAllCaps="false" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="queryEstimate"
                android:text="查询定位"
                android:textAllCaps="false" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="ZipMapPKG"
                android:text="ZipMapPKG"
                android:textAllCaps="false" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="CopyFile"
                android:text="CopyFile"
                android:textAllCaps="false" />

            <TextView
                android:id="@+id/showEstimate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:textColor="@android:color/background_dark"
                android:textSize="16sp" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/navigation_ip"
                android:textAllCaps="false"
                android:textColor="@android:color/black"
                android:textSize="18sp" />

            <EditText
                android:id="@+id/navIp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:minWidth="150dp"
                android:textAllCaps="false" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ros_ip"
                android:textAllCaps="false"
                android:textColor="@android:color/black"
                android:textSize="18sp" />

            <EditText
                android:id="@+id/rosIp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:minWidth="150dp"
                android:textAllCaps="false" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/changeConfig"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/change_config"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/setEstimateLost"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="模拟定位丢失"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/setEstimateRecovery"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="模拟定位恢复"
                android:textAllCaps="false" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="距离" />

            <EditText
                android:id="@+id/linear_distance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:inputType="numberDecimal"
                android:minWidth="30dp"
                android:textAllCaps="false" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="2dp"
                android:text="线速度" />

            <EditText
                android:id="@+id/linear_speed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:inputType="numberDecimal"
                android:minWidth="30dp"
                android:textAllCaps="false" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="2dp"
                android:text="角度" />

            <EditText
                android:id="@+id/angle_distance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:inputType="numberDecimal"
                android:minWidth="30dp"
                android:textAllCaps="false" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="2dp"
                android:text="角速度" />

            <EditText
                android:id="@+id/angle_speed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:inputType="numberDecimal"
                android:minWidth="30dp"
                android:textAllCaps="false" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/turnLeft"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/turn_left"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/turnRight"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/turn_right"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/forward"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/forward"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/backward"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/backward"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/stop"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/stop"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/forward_avoid"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/forward_avoid"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/stop_direct"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/stop_direct"
                android:textAllCaps="false" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/map_name"
                android:textAllCaps="false"
                android:textColor="@android:color/black"
                android:textSize="18sp" />

            <EditText
                android:id="@+id/mapName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:minWidth="80dp"
                android:text="4th" />

            <Button
                android:id="@+id/switchMap"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/switch_map"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/locateVision"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="视觉定位"
                android:textAllCaps="false" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/new_map_name"
                android:textAllCaps="false"
                android:textColor="@android:color/black"
                android:textSize="18sp" />

            <EditText
                android:id="@+id/newMapName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:minWidth="80dp" />

            <Button
                android:id="@+id/createMap"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/start_create_map"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/stopCreateMap"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/stop_create_map"
                android:textAllCaps="false" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/getWorkingMode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/get_work_mode"
                android:textAllCaps="false" />


            <Button
                android:id="@+id/getMotionMode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/get_motion_mode"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/switchNavigation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/switch_navigation"
                android:textAllCaps="false" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/switchManual"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/switch_free"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/cancelNavigation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/cancel_navigation"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/savePoseEstimate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/save_pose"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/resetPoseEstimate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/reset_pose_estimate"
                android:textAllCaps="false" />

        </LinearLayout>


        <RadioGroup
            android:id="@+id/deviceGroup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/radioDeviceTrex"
                style="@style/RadioButton"
                android:text="T-Rex" />

            <RadioButton
                android:id="@+id/radioDeviceMessia"
                style="@style/RadioButton"
                android:checked="true"
                android:text="Messia" />
        </RadioGroup>

        <RadioGroup
            android:id="@+id/groundGroup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/radioGroundSoft"
                style="@style/RadioButton"
                android:checked="true"
                android:text="软地面" />

            <RadioButton
                android:id="@+id/radioGroundHard"
                style="@style/RadioButton"
                android:text="硬地面" />
        </RadioGroup>

        <RadioGroup
            android:id="@+id/scenesGroup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/radioScenesNarrow"
                style="@style/RadioButton"
                android:checked="true"
                android:text="狭窄空间" />

            <RadioButton
                android:id="@+id/radioScenesSpacious"
                style="@style/RadioButton"
                android:text="宽阔空间" />
        </RadioGroup>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/checkboxEnableCamera"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="开启Camera"
                android:textColor="@android:color/black"
                android:visibility="gone" />

            <CheckBox
                android:id="@+id/enableRgbd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="开启RGBD"
                android:textColor="@android:color/black" />

            <CheckBox
                android:id="@+id/enableFishEye"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="开启FishEye"
                android:textColor="@android:color/black" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/enableIR"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="开启IR"
                android:textColor="@android:color/black" />

            <CheckBox
                android:id="@+id/enableSonar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="开启Sonar"
                android:textColor="@android:color/black" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/setRoverConfig"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="设置底盘参数"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/getRoverConfig"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="获取底盘参数"
                android:textAllCaps="false" />
        </LinearLayout>

        <Button
            android:id="@+id/danceTool"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Dance Tool"
            android:textAllCaps="false" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/obs_distance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:inputType="numberDecimal"
                android:hint="最大避障距离"
                android:minWidth="100dp"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/navigation_recharge_point"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="导航到回充点"
                android:textAllCaps="false"
                android:textSize="18sp" />

            <Button
                android:id="@+id/automatic_recharge"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="自动回充"
                android:textAllCaps="false"
                android:textSize="18sp" />

        </LinearLayout>

        <Button
            android:id="@+id/get_system_info_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="获取底盘信息"
            android:textAllCaps="false"
            android:textSize="18sp"
            />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/transfer_targets_data_btn"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="转存Targets信息"
                android:textAllCaps="false"
                android:textSize="14sp" />

            <Button
                android:id="@+id/transfer_road_data_btn"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="转存Road信息"
                android:textAllCaps="false"
                android:textSize="14sp" />

            <Button
                android:id="@+id/transfer_map_area_data_btn"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="转存MapArea信息"
                android:textAllCaps="false"
                android:textSize="14sp" />


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/setMapSyncState"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="setMapSyncState"
                android:textAllCaps="false"
                android:textSize="14sp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/current_db_mode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <Button
                    android:id="@+id/change_db_mode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="切换" />

                <Button
                    android:id="@+id/query_db_data"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="查询数据库" />
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>
</ScrollView>
