<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="com.ainirobot.headservice.dance.DanceToolActivity"
    >

    <EditText
        android:id="@+id/dance_tool_edit_angle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="angle"
        android:inputType="numberSigned"
        />

    <EditText
        android:id="@+id/dance_tool_edit_speed"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="speed"
        android:inputType="number"
        />

    <EditText
        android:id="@+id/dance_tool_edit_distance"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="distance"
        android:inputType="number"
        />

    <EditText
        android:id="@+id/dance_tool_edit_duration"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="duration"
        android:inputType="number"
        />

    <EditText
        android:id="@+id/dance_tool_edit_repeat"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="repeat"
        android:inputType="number"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        >
        <Button
            android:id="@+id/dance_tool_edit_save"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="save"
            android:textSize="16sp"
            android:textStyle="bold"
            />
        <Button
            android:id="@+id/dance_tool_edit_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="cancel"
            android:textSize="16sp"
            android:textStyle="bold"
            />
    </LinearLayout>


</LinearLayout>
