<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    tools:context="com.ainirobot.headservice.dance.DanceToolActivity"
    >

    <LinearLayout
        android:id="@+id/dance_tool_button_parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        >

        <Button
            android:id="@+id/dance_tool_edit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:onClick="showDanceEditFragment"
            android:text="edit dance"
            />

        <Button
            android:id="@+id/dance_tool_start"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:onClick="startDance"
            android:text="start dance"
            />
    </LinearLayout>

    <EditText
        android:id="@+id/dance_tool_repeat"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:hint="repeat"
        android:inputType="number"
        />

    <FrameLayout
        android:id="@+id/dance_tool_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        />

</LinearLayout>
