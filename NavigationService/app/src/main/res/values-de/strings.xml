<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">NavigationService</string>
    <string name="stop_navigation">Cruising beenden</string>
    <string name="start_navigation">Cruising starten</string>
    <string name="stop_plan_route">Cruise-Route speichern</string>
    <string name="start_plan_route">Mit der Planung der Cruise-Route beginnen</string>
    <string name="open_radar">Lidar einschalten</string>
    <string name="close_radar">Lidar ausschalten</string>
    <string name="get_radar_status">Lidar-Status</string>
    <string name="stop_ros">ROS stoppen</string>
    <string name="start_ros">ROS starten</string>
    <string name="interval_time">Zeitmeldeintervall：</string>
    <string name="is_repeat">Wiederholt cruisen?</string>
    <string name="speak_content">Die aktuelle Uhrzeit ist %1$s %2$s</string>
    <string name="not_support_chinese">Chinesische Sprache ist nicht verfügbar</string>
    <string name="reset_location">Neu positionieren</string>
    <string name="nav_ip_empty">Chassis IP darf nicht leer sein</string>
    <string name="reset_pose_estimate">Neu positionieren</string>
    <string name="save_pose">Positionierungspunkt speichern</string>
    <string name="cancel_navigation">Navigation beenden</string>
    <string name="switch_free">Zum freien Modus wechseln</string>
    <string name="switch_navigation">Zum Navigationsmodus wechseln</string>
    <string name="get_motion_mode">getMotionMode</string>
    <string name="get_work_mode">Überprüfung des aktuellen Modus</string>
    <string name="stop_create_map">Kartierung stoppen</string>
    <string name="start_create_map">Kartierung starten</string>
    <string name="new_map_name">Neuer Kartenname</string>
    <string name="switch_map">Karte wechseln</string>
    <string name="map_name">Kartenname:</string>
    <string name="stop">Stoppen</string>
    <string name="stop_direct">Sofort stoppen</string>
    <string name="backward">Rückwärts gehen</string>
    <string name="forward">Vorwärts gehen</string>
    <string name="forward_avoid">Mit Hindernisvermeidung vorwärts gehen.</string>
    <string name="turn_right">Nach rechts drehen</string>
    <string name="turn_left">Nach links drehen</string>
    <string name="change_config">Konfiguration ändern</string>
    <string name="ros_ip">ROS IP :</string>
    <string name="navigation_ip">Chassis IP :</string>
    <string name="inspect_tk1_error">Chassis Verbindung fehlgeschlagen.</string>
    <string name="inspect_tk1_service_start_fail">Zeitüberschreitung beim Warten auf das Booten des Navigationsdienstes</string>
    <string name="inspect_tk1_get_sensor_status_fail">Chassis Selbstinspektion fehlgeschlagen.</string>
    <string name="inspect_reset_camera">Kamera neu positionieren</string>
    <string name="inspect_rgbd">RGBD</string>
    <string name="inspect_laser">Lidar</string>
    <string name="inspect_speedometer">Wegmesser</string>
    <string name="inspect_infrared">Infrarot</string>
    <string name="inspect_laser_available">Lidar Daten</string>
    <string name="inspect_can_control">Chassis Verbindung</string>
    <string name="inspect_gyro_ready">Gyroskop</string>
    <string name="inspect_ir_camera_ready">Infrarot-Kamera</string>
    <string name="inspect_calibration_ready">Kalibrierungsdatei</string>
    <string name="inspect_hard_disk">Festplattenspeicher</string>
    <string name="inspect_ota_socket">OTA-Verbindung fehlgeschlagen</string>
    <string name="inspect_error">Fehler</string>
    <string name="navigation_content">%1$s Ladepunkt</string>
    <string name="charge_content">%1$s geladen</string>
    <string name="goaction_content">Navigieren %1$s</string>
    <string name="uwb_set_first">Bitte stellen Sie zuerst die UWB-Parameter ein</string>
</resources>
