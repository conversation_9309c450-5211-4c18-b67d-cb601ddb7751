<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Service de navigation</string>
    <string name="stop_navigation">Arrêter la croisière</string>
    <string name="start_navigation">Commencer à naviguer</string>
    <string name="stop_plan_route">Garder la route de croisière</string>
    <string name="start_plan_route">Commencer à planifier la route de croisière</string>
    <string name="open_radar">Lidar ouvert</string>
    <string name="close_radar">Lidar éteint</string>
    <string name="get_radar_status">État de Lidar</string>
    <string name="stop_ros">Arrêter ROS</string>
    <string name="start_ros">Démarrer ROS</string>
    <string name="interval_time">Intervalle de rapport de temps ：</string>
    <string name="is_repeat">Si la croisière est répétée ?</string>
    <string name="speak_content">L\'heure actuelle est %1$s %2$s</string>
    <string name="not_support_chinese">La langue chinoise n\'est pas disponible</string>
    <string name="reset_location">Repositionner</string>
    <string name="nav_ip_empty">L\'adresse IP du châssis ne peut pas être vide</string>
    <string name="reset_pose_estimate">Repositionner</string>
    <string name="save_pose">Enregistrer le point de positionnement</string>
    <string name="cancel_navigation">Arrêter la navigation</string>
    <string name="switch_free">Passer en mode libre</string>
    <string name="switch_navigation">Passer en mode de navigation</string>
    <string name="get_motion_mode">getMotionMode</string>
    <string name="get_work_mode">Inspecter le mode actuel</string>
    <string name="stop_create_map">Arrêter le mappage</string>
    <string name="start_create_map">Commencer le mappage</string>
    <string name="new_map_name">Nouveau nom de la carte</string>
    <string name="switch_map">Changer de carte</string>
    <string name="map_name">Nom de la carte :</string>
    <string name="stop">Arrêter</string>
    <string name="stop_direct">Arrêter immédiatement</string>
    <string name="backward">Revenir en arrière</string>
    <string name="forward">Avancer</string>
    <string name="forward_avoid">Avancer en évitant des obstacles.</string>
    <string name="turn_right">Tourner à droite</string>
    <string name="turn_left">Tourner à gauche</string>
    <string name="change_config">Modifier la configuration</string>
    <string name="ros_ip">IP ROS :</string>
    <string name="navigation_ip">Chassis IP :</string>
    <string name="inspect_tk1_error">La connexion au châssis a échoué.</string>
    <string name="inspect_tk1_service_start_fail">En attente de l\'expiration du délai de démarrage du service de navigation</string>
    <string name="inspect_tk1_get_sensor_status_fail">L\'auto-inspection du châssis a échoué.</string>
    <string name="inspect_reset_camera">Repositionner la caméra</string>
    <string name="inspect_rgbd">RVB</string>
    <string name="inspect_laser">Lidar</string>
    <string name="inspect_speedometer">Odomètre</string>
    <string name="inspect_infrared">infrarouge</string>
    <string name="inspect_laser_available">Lidar Donnée</string>
    <string name="inspect_can_control">Connexion au châssis</string>
    <string name="inspect_gyro_ready">Gyroscope</string>
    <string name="inspect_ir_camera_ready">Caméra infrarouge</string>
    <string name="inspect_calibration_ready">Fichier de calibrage</string>
    <string name="inspect_hard_disk">Espace de disque dur</string>
    <string name="inspect_ota_socket">Échec de la connexion OTA</string>
    <string name="inspect_error">Erreur</string>
    <string name="navigation_content">%1$s Point de charge</string>
    <string name="charge_content">Recharge de %1$s</string>
    <string name="goaction_content">Naviguer dans %1$s</string>
    <string name="uwb_set_first">Veuillez d\'abord définir les paramètres UWB</string>
</resources>
