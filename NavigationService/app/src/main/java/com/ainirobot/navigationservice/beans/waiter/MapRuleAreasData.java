package com.ainirobot.navigationservice.beans.waiter;

import java.util.ArrayList;

public class MapRuleAreasData {
    private int up_level;
    private int down_level;
    private boolean force_lock_wheel = false;
    private ArrayList<Vector2d> points;

    public MapRuleAreasData() {
    }

    public MapRuleAreasData(int up_level, int down_level, ArrayList<Vector2d> points) {
        this.up_level = up_level;
        this.down_level = down_level;
        this.points = points;
    }

    public int getUp_level() {
        return up_level;
    }

    public void setUp_level(int up_level) {
        this.up_level = up_level;
    }

    public int getDown_level() {
        return down_level;
    }

    public void setDown_level(int down_level) {
        this.down_level = down_level;
    }

    public ArrayList<Vector2d> getPoints() {
        return points;
    }

    public void setPoints(ArrayList<Vector2d> points) {
        this.points = points;
    }

    public boolean isForce_lock_wheel() {
        return force_lock_wheel;
    }

    public void setForce_lock_wheel(boolean force_lock_wheel) {
        this.force_lock_wheel = force_lock_wheel;
    }

    @Override
    public String toString() {
        return "MapRuleAreasData{" +
                "up_level=" + up_level +
                ", down_level=" + down_level +
                ", force_lock_wheel=" + force_lock_wheel +
                ", points=" + points +
                '}';
    }
}
