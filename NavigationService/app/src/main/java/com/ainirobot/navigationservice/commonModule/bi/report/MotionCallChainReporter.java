package com.ainirobot.navigationservice.commonModule.bi.report;

import com.ainirobot.coreservice.client.upload.bi.CallChainReport;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by Orion on 2020/4/30.
 */
public class MotionCallChainReporter extends CallChainReport {


    public MotionCallChainReporter() {
        super("");
    }

    public static class Builder {

        public static MotionCallChainReporter build() {
            return new MotionCallChainReporter();
        }

        public static MotionCallChainReporter buildRobotOsReporter() {
            return (MotionCallChainReporter) Builder.build()
                    .addSourceRobotOs()
                    .addPackageNameNavigationService();
        }

        public static MotionCallChainReporter buildRoverServiceReporter() {
            return (MotionCallChainReporter) Builder.build()
                    .addSourceRoverService()
                    .addPackageNameNavigationService();
        }

    }

    public static void motionArcWithObstaclesReport(String name, String params) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .addNodeParams(params)
                .report();
    }

    public static void motionWithObstaclesReport(String name, double angularSpeed, double linearSpeed, double minDistance) {
        JSONObject params = new JSONObject();
        try {
            params.put("angularSpeed", angularSpeed);
            params.put("linearSpeed", linearSpeed);
            params.put("minDistance", minDistance);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .addNodeParams(params.toString())
                .report();
    }

    public static void stopMoveReport(String name) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .report();
    }

    public static void startDecTimerEntranceReport(String name, int decNumber, double tAngularSpeed, double tLinearSpeed) {
        JSONObject params = new JSONObject();
        try {
            params.put("decNumber", decNumber);
            params.put("tAngularSpeed", tAngularSpeed);
            params.put("tLinearSpeed", tLinearSpeed);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .addNodeEntrance(params.toString())
                .report();
    }

    public static void controlWheelCallReport(String name, double mAngularSpeed, double mLinearSpeed) {
        JSONObject params = new JSONObject();
        try {
            params.put("mAngularSpeed", mAngularSpeed);
            params.put("mLinearSpeed", mLinearSpeed);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .addNodeParams(params.toString())
                .report();
    }

    public static void controlWheelResultReport(String name, String result) {
        Builder.buildRoverServiceReporter()
                .addNodeMethodTypeApiCallback()
                .addNodeMethodName(name)
                .addNodeResult(result)
                .report();
    }

}
