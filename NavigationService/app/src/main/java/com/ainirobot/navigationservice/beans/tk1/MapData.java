/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.tk1;

/**
 * Parse the map data from the navigation.
 */
public class MapData {

    public static final int GLOBAL = 0;
    public static final int REAL_TIME = 1;

    private final int type;
    private final int width;
    private final int height;
    private final double resolution;
    private final double originalX;
    private final double originalY;
    private final byte[] data;

    public MapData(int type, int width, int height, double resolution,
                   double originalX, double originalY, byte[] data) {
        this.type = type;
        this.width = width;
        this.height = height;
        this.resolution = resolution;
        this.originalX = originalX;
        this.originalY = originalY;
        this.data = data;
    }

    public int getWidth() {
        return width;
    }

    public int getHeight() {
        return height;
    }

    public double getResolution() {
        return resolution;
    }

    public double getOriginalX() {
        return originalX;
    }

    public double getOriginalY() {
        return originalY;
    }

    public byte[] getData() {
        return data;
    }

    public int getType() {
        return type;
    }

}
