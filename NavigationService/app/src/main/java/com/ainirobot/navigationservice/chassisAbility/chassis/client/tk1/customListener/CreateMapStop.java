package com.ainirobot.navigationservice.chassisAbility.chassis.client.tk1.customListener;

import com.ainirobot.navigationservice.beans.MappingPose;

import java.util.List;

public class CreateMapStop {
    public static final String RESULT_NOT_CREATING_MAP_MODE = "not in creating map mode";
    public static final String RESULT_ALREADY_STOPPING_CREATE_MAP = "already stopping create map";

    public static final int STATUS_STOP_CREATE_MAP_EXPECTED_TIME = 1;

    private boolean isAvailable = true;
    private ResponseListener responseListener;
    private int mStatus;

    public void onResult(boolean result, Object value) {
        if (!isAvailable) {
            return;
        }

        isAvailable = false;

        if (responseListener != null) {
            responseListener.onResult(result, value);
        }
    }

    public void onStatusUpdate(int status, String value) {
        if (!isAvailable) {
            return;
        }

        try {
            updateStatus(status, value);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (status != mStatus) {
            mStatus = status;
        }
    }

    public void onSaveMap(boolean saveReuslt, List<MappingPose> list){
        if (!isAvailable){
            return;
        }
        if(responseListener != null){
            responseListener.onSaveMap(saveReuslt, list);
        }
    }

    private void updateStatus(int status, String value) {
        if (responseListener != null) {
            responseListener.onStatusUpdate(status, value);
        }
    }

    public void setResponseListener(ResponseListener responseListener) {
        this.responseListener = responseListener;
    }

    public interface ResponseListener {
        void onResult(boolean result, Object value);

        void onStatusUpdate(int status, String value);

        void onSaveMap(boolean saveResult, List<MappingPose> list);
    }
}
