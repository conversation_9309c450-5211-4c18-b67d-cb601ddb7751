package com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1;

import com.ainirobot.navigationservice.beans.tk1.MotionMode;
import com.ainirobot.navigationservice.beans.tk1.RelocateMode;
import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.tk1.WorkMode;
import com.ainirobot.navigationservice.chassisAbility.chassis.connector.tk1.IChassisConnect;

import java.util.Queue;

public interface IChassisCommand {

    void init();

    boolean isChannelConnected();

    boolean setWorkingMode(WorkMode workMode, ResponseListener listener);

    boolean getWorkingMode(ResponseListener listener);

    void updatePoseState(ResponseListener listener);

    boolean getSystemInformation(ResponseListener listener);

    boolean getMotionMode(ResponseListener listener);

    boolean setMotionMode(MotionMode motionMode, ResponseListener listener);

    boolean setRoverConfig(RoverConfig roverConfig, ResponseListener listener);

    boolean getRoverConfig(ResponseListener listener);

    boolean setRelocate(RelocateMode mode, double x, double y, double t);

    boolean sendPrimitiveMovingCommand(double angular, double liner);

    void setWorkModeState(int state);

    boolean isWorkModeReady();

    boolean performOprations(Queue<Operations.Oper> opers, ResponseListener listener);

    void injectConnector(IChassisConnect connectApi);

    void registerEventListener(String type, EventListener listener);

    void unRegisterEventListener(String type);

    void setCnnListener(OnCmdTk1CnnListener listener);

    boolean addMappingPose(Double x, Double y, Double theta, ResponseListener listener);

    boolean deleteMappingPose(int id, ResponseListener listener);

    boolean saveMap(ResponseListener listener);

    interface EventListener {
        void onEvent(Object param);
    }

    interface ResponseListener {
        void onResponse(boolean status, int resultCode, Object result);
    }

    interface OnCmdTk1CnnListener {
        void onConnected();//连接成功，必须保证都连接成功，这应该内聚

        void onDisconnected(String channelName);//断连上报哪条路断了
    }
}
