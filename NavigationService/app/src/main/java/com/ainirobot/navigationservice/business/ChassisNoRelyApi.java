package com.ainirobot.navigationservice.business;

import android.content.Context;

/**
 * 不需要调用底盘接口，内部实现接口逻辑
 */
public interface ChassisNoRelyApi {

    void init(Context mContext);

    boolean getLocation(String cmdType, String cmdParam);
    boolean setLocation(String cmdType, String cmdParam);
    boolean getNavigationStatus(String cmdType);
    boolean startRoutePlan(String cmdType);
    boolean stopRoutePlan(String cmdType);
    boolean isInLocation(String cmdType, String cmdParam);
    boolean setPoseLocation(String cmdType, String cmdParam);
    boolean getPlace(String cmdType, String cmdParam);
    boolean getPlaceList(String cmdType, String language);
    boolean updatePlaceList(String cmdType, String cmdParam, String language);
    boolean getInternationalPlaceList(String cmdType, String cmdParam);
    boolean getPlaceListWithNameList(String cmdType, String cmdParam);
    boolean getPlaceListByMapName(String cmdType, String cmdParam, String language);
    boolean hasPlaceInMap(String cmdType, String cmdParam);
    boolean getPlacesByType(String cmdType, String cmdParam);
    boolean isEstimate(String cmdType);
    boolean savePoseEstimate(String cmdType);
    boolean getCurrentMap(String cmdType);
    boolean getCurrentPose(String cmdType);
    boolean getCurrentPoseWithoutEstimate(String cmdType);
    boolean setMapInfo(String cmdType, String cmdParam);
    boolean setPatrolList(String cmdType, String cmdParam);
    boolean getPatrolList(String cmdType, String cmdParam);
    boolean refreshMd5(String cmdType, String cmdParam);
    boolean parseDownloadMapPlacePropToNaviProp(String cmdType, String cmdParam);
    boolean savePlaceListToPlaceFile(String cmdType, String cmdParam);
    boolean setCruiseRoute(String cmdType, String cmdParam);
    boolean getCruiseRoute(String cmdType, String cmdParam);
    boolean editPlaceProcess(String cmdType, String cmdParam);
    boolean reportCmdTimeOut(String cmdType, String cmdParam);
    boolean timeOutMsgDelete(String cmdType, String cmdParam);
    boolean getLogTaskById(String cmdType, String cmdParam);
    boolean updateLogStatusById(String cmdType, String cmdParam);
    boolean renameMap(String cmdType, String cmdParam);
    boolean clearCruiseRoute(String cmdType, String cmdParam);
    boolean checkPosePositionTheta(String cmdType, String cmdParam);
    boolean checkObstacle(String cmdType, String cmdParam);
    boolean hasObstacleInArea(String cmdType, String cmdParam);
    boolean startStatusSocket(String type, int socketPort);
    boolean closeStatusSocket(String type, int socketPort);
    boolean checkSocketConnected(String cmdType);
    boolean getMapInfo(String cmdType, String cmdParam);
    boolean updateMapSyncState(String cmdType, String cmdParam);
    boolean updateMapUuid(String cmdType, String cmdParam);
    boolean updateMapFinishState(String cmdType, String cmdParam);
    boolean updateMapForbidLine(String cmdType, String cmdParam);
    boolean updateMapUpdateTime(String cmdType, String cmdParam);
    boolean addMapInfo(String cmdType, String cmdParam);
    boolean insertMultiFloorConfig(String cmdType, String cmdParam);
    boolean updateMultiFloorConfig(String cmdType, String cmdParam);
    boolean getMultiFloorConfig(String cmdType, String cmdParam);
    boolean removeMultiFloorConfig(String cmdType, String cmdParam);
    boolean getChargeAreaConfig(String cmdType, String cmdParam);
    boolean insertChargeAreaConfig(String cmdType, String cmdParam);
    boolean updateChargeAreaConfig(String cmdType, String cmdParam);
    boolean removeChargeAreaConfig(String cmdType, String cmdParam);
    /**
     * 解析地图相关数据
     * 2021.3.9添加是由于招财豹并线后MapTool未正确解析地图数据
     */
    boolean parseMapData(String cmdType, String cmdParam);
    boolean getAdditionalDevice(String cmdType);
    boolean getMultiRobotSettingConfig(String cmdType, String cmdParam);
    /**
     * 设置特殊点位的映射点
     */
    boolean setMappingPlace(String cmdType, String cmdParam);

    /**
     * 获取特殊点位的映射信息
     */
    boolean getMappingInfo(String cmdType, String cmdParam);

    /**
     * 保存巡线数据
     */
    boolean saveRoadData(String cmdType, String cmdParam);
    /**
     * 保存闸机数据
     */
    boolean saveGateData(String cmdType, String cmdParam);

    /**
     * 开启录制数据集
     */
    boolean startDataSetRecord(String cmdType, String cmdParam);

    /**
     * 停止录制数据集
     */
    boolean stopDataSetRecord(String cmdType, String cmdParam);

    /**
     * 上传录制的导航数据集
     */
    boolean uploadNaviDataSet(String cmdType, String cmdParam);

    /**
     * 获得多层配置及每层地图里的点位信息
     */
    boolean getMultiFloorConfigAndPose(String cmdType, String cmdParam, String language);

    /**
     * 检查地图类型是否包含视觉
     */
    boolean isMapHasVision(String cmdType, String cmdParam);

    /**
     * 更新导航额外文件信息
     */
    boolean updateNavigationExtraData(String cmdType, String cmdParams);

    /**
     * 获取导航额外文件信息
     */
    boolean getNavigationExtraData(String cmdType, String cmdParams);

    /**
     * 检查是否存在导航额外文件
     */
    boolean hasNavigationExtraData(String cmdType, String cmdParams);

    /**
     * 压缩导航额外文件
     */
    boolean zipNavigationExtraData(String cmdType, String cmdParams);

    /**
     * 解压导航额外文件
     */
    boolean unzipNavigationExtraData(String cmdType, String cmdParams);

    /**
     * 压缩地图文件
     */
    boolean zipMapFile(String cmdType, String cmdParam);

    /**
     * 解压地图文件
     */
    boolean unzipMapFile(String cmdType, String cmdParam);

    /**
     * 获取本地地图信息列表，从数据库获取
     */
    boolean getLocalMapInfoList(String cmdType);

    boolean getMapInfoBySdMapNames(String cmdType);

    boolean getCurrentMapName(String cmdType);

    /**
     * 获取导航运动距离
     */
    boolean getMotionDistance(String cmdType);

    /**
     * 拷贝导入地图源文件到地图文件夹
     */
    boolean copyImportMapFile(String cmdType, String cmdParam);
    /**
     * OTA降级
     */
    boolean otaDowngrade(String cmdType, String cmdParam);

    /**
     * 根据闸机主键批量查询闸机关系信息
     */
    boolean findByGateIds(String cmdType, String cmdParam);

    /**
     * 根据闸机线主键批量查询闸机关系信息
     */
    boolean findByLineIds(String cmdType, String cmdParam);


    /**
     * 批量更新闸机关系信息
     */
    boolean batchInsertOrUpdateGate(String cmdType, String cmdParam);

    /**
     * 根据闸机线主键批量删除闸机关系信息
     */
    boolean deleteByLineIds(String cmdType, String cmdParam);

    /**
     * 根据闸机主键批量删除闸机关系信息
     */
    boolean deleteByGateIds(String cmdType, String cmdParam);

    boolean getAllGateRelationData(String cmdType, String cmdParam);

    boolean deleteExceptLineIds(String cmdType, String cmdParam);

}
