package com.ainirobot.navigationservice.db.helper.objectbox;

import android.util.Log;

import com.ainirobot.navigationservice.db.entity.MultiFloorInfo;
import com.ainirobot.navigationservice.db.entity.MultiFloorInfo_;
import com.ainirobot.navigationservice.db.helper.iml.MultiFloorInfoHelperIml;

import java.util.List;

import io.objectbox.Box;
import io.objectbox.BoxStore;
import io.objectbox.query.Query;

public class MultiFloorInfoObjectHelper extends BaseObjectHelper<MultiFloorInfo> implements MultiFloorInfoHelperIml {
    public MultiFloorInfoObjectHelper(BoxStore boxStore) {
        super(boxStore);
    }

    @Override
    public MultiFloorInfo getMultiFloorConfigByFloorId(long floorId) {
        Query<MultiFloorInfo> floorInfoQuery = getBox()
                .query(MultiFloorInfo_.floorId.equal(floorId)).build();
        MultiFloorInfo multiFloorInfo = floorInfoQuery.findFirst();
        floorInfoQuery.close();
        return multiFloorInfo;
    }

    @Override
    public void initMultiFloorInfoData(List<MultiFloorInfo> mapInfoList) {
        if (null == mapInfoList || mapInfoList.isEmpty()) {
            Log.d(TAG, "initMultiFloorInfoData: list is null");
            return;
        }
        Log.d(TAG, "initMultiFloorInfoData: start");
        Box<MultiFloorInfo> mapInfoBox = getBox();
        mapInfoBox.removeAll();
        mapInfoBox.put(mapInfoList);
        Log.d(TAG, "initMultiFloorInfoData: " + mapInfoBox.count());
    }

    @Override
    public List<MultiFloorInfo> getMultiFloorConfig() {
        Query<MultiFloorInfo> floorInfoQuery = getBox().query().build();
        List<MultiFloorInfo> multiFloorInfos = floorInfoQuery.find();
        floorInfoQuery.close();
        return multiFloorInfos;
    }

    @Override
    public boolean updateMultiFloorInfo(List<MultiFloorInfo> infoList) {
        Box<MultiFloorInfo> multiFloorInfoBox = getBox();
        multiFloorInfoBox.put(infoList);
        return true;
    }

    @Override
    public boolean updateMultiFloorInfo(MultiFloorInfo floorInfo) {
        Box<MultiFloorInfo> floorInfoBox = getBox();
        return floorInfoBox.put(floorInfo) > 0;
    }

    @Override
    public boolean deleteMultiFloorInfo(MultiFloorInfo info) {
        Box<MultiFloorInfo> multiFloorInfoBox = getBox();
        return multiFloorInfoBox.remove(info);
    }
}
