package com.ainirobot.navigationservice.commonModule.uwb;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.hardware.RobotCore;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingListener;
import com.ainirobot.navigationservice.ApplicationWrapper;
import com.ainirobot.navigationservice.R;
import com.ainirobot.navigationservice.utils.ToastUtil;
import com.ainirobot.uwb.bean.UwbInfo;
import com.ainirobot.uwb.serial.IOnNewUwbDataListener;
import com.ainirobot.uwb.serial.USBDataManager;

/**
 * 根据配置保存的危险中心点的半径Uwb测量的实时距离，计算是否在
 */
public class UwbManager {

    private static final String TAG = UwbManager.class.getSimpleName();
    private static final int ERROR = -1;
    private String uwb_station_1 = "";
    private String uwb_station_2 = "";
    private float mStationS = 1.0f;
    private float center_L1 = 1.0f;
    private float center_L2 = 1.0f;
    private float mRadius = 1.0f;
    private volatile float mCurrentDis1 = 1.0f; // 机器人距离基站的实时距离
    private volatile float mCurrentDis2 = 1.0f;

    private Context mAppContext = ApplicationWrapper.getContext();
//    private volatile boolean uwbSwitchOn = false;
    private MyOnNewDataListener mOnNewDataListener = new MyOnNewDataListener();
    private volatile int mOldStatus = -1;


    private static final class SingletonHolder {
        private static final UwbManager mInstance = new UwbManager();
    }

    public static UwbManager getInstance() {
        return SingletonHolder.mInstance;
    }

    private UwbManager() {
        registerUwbSettingsListener();
    }

    public void init() {

        USBDataManager.getInstance().initUSB(mAppContext);
        if (getRobotSettingInt(Definition.ROBOT_UWB_SWITCH) == Definition.ROBOT_UWB_SWITCH_ON){
            if (getRobotSettingInt(Definition.ROBOT_UWB_CHECKING) == Definition.ROBOT_UWB_CHECKING_START) {
                startUwb();
            }
        }
    }

    private void startUwb() {
        Log.d(TAG, "Navigation startUwb ");
        USBDataManager.getInstance().startConnectUSBData();
        USBDataManager.getInstance().setOnUwbInfoListener(mOnNewDataListener);
    }

    private void stopUwb(){
        Log.d(TAG, "Navigation stopUwb ");
        USBDataManager.getInstance().stopConnectUSB();
    }

    private class MyOnNewDataListener implements IOnNewUwbDataListener {

        @Override
        public void onNewUwbUpdate(UwbInfo uwbInfo) {
            onNewData(uwbInfo);
        }
    }

    /**
     * 根据实时的uwbInfo距离，和设计算法, 判定是否在危险区域
     *
     * @param uwbInfo
     */
    private void onNewData(UwbInfo uwbInfo) {
//        if (!uwbSwitchOn) {
//            Log.d(TAG, "onNewData uwbSwitchOn false ");
//            return;
//        }
        String mac = uwbInfo.getTagMac();
        float distance = uwbInfo.getDistance();

        if (uwb_station_1.equals(mac) || uwb_station_2.equals(mac)){
            if (uwb_station_1.equals(mac)) {
                mCurrentDis1 = distance;
            }
            if (uwb_station_2.equals(mac)) {
                mCurrentDis2 = distance;
            }
        }else {
            return;
        }

        boolean danger = isDanger();
        int data = danger ? Definition.HARDWARE_BLE_NEAR : Definition.HARDWARE_BLE_FAR;
        Log.d(TAG, "OldStatus : " + mOldStatus + ", newStatus :" + data);
        if (mOldStatus == data) {
            Log.d(TAG, "same to Old status , not sendStatusReport");
            return;
        }
        mOldStatus = data;
        RobotCore.sendStatusReport(mAppContext.getPackageName(), Definition.STATUS_BLE_SIGNAL, String.valueOf(data));
    }


    private void initParams() {
        uwb_station_1 = getRobotSettingSting(Definition.ROBOT_UWB_STATION_1);
        uwb_station_2 = getRobotSettingSting(Definition.ROBOT_UWB_STATION_2);
        mStationS = getRobotSettingFloat(Definition.ROBOT_UWB_STATIONS_DISTANCE);
        center_L1 = getRobotSettingFloat(Definition.ROBOT_CENTER_POINT_L1);
        center_L2 = getRobotSettingFloat(Definition.ROBOT_CENTER_POINT_L2);
        mRadius = getRobotSettingFloat(Definition.ROBOT_CENTER_POINT_RADIUS);
        Log.d(TAG, "initParams station_1 :" + uwb_station_1 + ", center_L1 :" + center_L1
                + " ,station_2 :" + uwb_station_2 + ", center_L2:" + center_L2 +
                " mStationS :" + mStationS + ", mRadius :" + mRadius);
    }

    /**
     * 判断是否危险的算法
     *
     * @return true 表示危险,  false 表示安全
     */
    private boolean isDanger() {
        float d1_times = mCurrentDis1 * mCurrentDis1;
        float d2_times = mCurrentDis2 * mCurrentDis2;
        float L1_times = center_L1 * center_L1;
        float L2_times = center_L2 * center_L2;

        float a = (d1_times + L2_times - L1_times - d2_times) / (4 * mStationS);

        float b = (d1_times - d2_times + 4 * mStationS * mStationS) / (4 * mStationS);

        float c = (L1_times - L2_times + 4 * mStationS * mStationS) / (4 * mStationS);

        double d = Math.sqrt(d1_times - b * b) - Math.sqrt(L1_times - c * c);

        return (a * a + d * d) <= (mRadius * mRadius);
    }

    private void registerUwbSettingsListener() {
        try {
            RobotSettings.registerRobotSettingListener(mAppContext, new MyListener(),
                    Definition.ROBOT_UWB_CHECKING,
                    Definition.ROBOT_UWB_SWITCH // Settings中　uwb 开关
            );
        } catch (Exception e) {
            Log.d(TAG, "registerUwbSettingsListener e :"+e.getLocalizedMessage());
            e.printStackTrace();
        }
    }

    private class MyListener extends RobotSettingListener {
        @Override
        public void onRobotSettingChanged(String key) {
            super.onRobotSettingChanged(key);
            onKeySettingsChanged(key);
        }
    }

    private void onKeySettingsChanged(String key) {
        if (TextUtils.isEmpty(key))
            return;
        Log.e(TAG, "onKeySettingsChanged key : "+key);
        switch (key) {
            case Definition.ROBOT_UWB_CHECKING:
                if (getRobotSettingInt(Definition.ROBOT_UWB_SWITCH) != Definition.ROBOT_UWB_SWITCH_ON){
                    Log.d(TAG, "Settings SWITCH is off , turn on it in RobotSettings");
                    return;
                }
                    
                int value = getRobotSettingInt(key);
                if (value == Definition.ROBOT_UWB_CHECKING_START) {
                    initParams();
                    startUwb();
                } else {
                    stopUwb();
                }
                break;
            case Definition.ROBOT_UWB_SWITCH:
                int uwbSwitch = getRobotSettingInt(key);
                if (uwbSwitch == Definition.ROBOT_UWB_SWITCH_ON){
                    // Settings 开关开启,并且　UwbSetting apk 没有在设置占用uwb时　
                    if (getRobotSettingInt(Definition.ROBOT_UWB_CHECKING) == Definition.ROBOT_UWB_CHECKING_START) {
                        initParams();
                        startUwb();
                    }else {
                        ToastUtil.showToast(mAppContext,mAppContext.getString(R.string.uwb_set_first));
                        Log.e(TAG, "UwbSetting apk has not set Uwb param " );
                    }
                } else {
                    stopUwb();
                }
                break;
            default:
                break;
        }
    }

    private int getRobotSettingInt(String key) {
        try {
            return RobotSettings.getRobotInt(mAppContext, key);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return ERROR;
    }

    private float getRobotSettingFloat(String key) {
        try {
            return RobotSettings.getRobotFloat(mAppContext, key);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return ERROR;
    }

    private String getRobotSettingSting(String key) {
        try {
            return RobotSettings.getRobotString(mAppContext, key);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "";
    }


}
