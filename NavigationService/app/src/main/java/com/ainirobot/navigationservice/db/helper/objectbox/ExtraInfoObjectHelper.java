package com.ainirobot.navigationservice.db.helper.objectbox;

import android.util.Log;

import com.ainirobot.navigationservice.db.entity.ExtraInfo;
import com.ainirobot.navigationservice.db.entity.ExtraInfo_;
import com.ainirobot.navigationservice.db.helper.iml.ExtraInfoHelperIml;

import java.util.List;

import io.objectbox.Box;
import io.objectbox.BoxStore;
import io.objectbox.query.Query;

public class ExtraInfoObjectHelper extends BaseObjectHelper<ExtraInfo> implements ExtraInfoHelperIml {
    public ExtraInfoObjectHelper(BoxStore boxStore) {
        super(boxStore);
    }

    @Override
    public boolean deleteExtraData(String mapName) {
        Query<ExtraInfo> infoQuery = getExtraInfoQuery(mapName);
        boolean remove = infoQuery.remove() > 0;
        infoQuery.close();
        Log.d(TAG, "deleteExtraData : " + remove);
        return remove;
    }

    @Override
    public ExtraInfo getExtraInfo(String mapName) {
        Query<ExtraInfo> infoQuery = getExtraInfoQuery(mapName);
        ExtraInfo extraInfo = infoQuery.findFirst();
        infoQuery.close();
        return extraInfo;
    }

    @Override
    public void initExtraInfoData(List<ExtraInfo> extraInfoList) {
        if (null == extraInfoList || extraInfoList.isEmpty()) {
            Log.d(TAG, "initExtraInfoData: list is null");
            return;
        }
        Log.d(TAG, "initExtraInfoData: start");
        Box<ExtraInfo> extraInfoBox = getBox();
        extraInfoBox.removeAll();
        extraInfoBox.put(extraInfoList);
        Log.d(TAG, "initExtraInfoData: " + extraInfoBox.count());
    }

    @Override
    public boolean updateExtraInfo(ExtraInfo extraInfo) {
        ExtraInfo dbExtraInfo = getExtraInfo(extraInfo.getMapName());
        if (null != dbExtraInfo) {
            extraInfo.id = dbExtraInfo.id;
        }
        return getBox().put(extraInfo) > 0;
    }

    private Query<ExtraInfo> getExtraInfoQuery(String mapName) {
        return getBox().query(ExtraInfo_.mapName.equal(mapName)).build();
    }
}
