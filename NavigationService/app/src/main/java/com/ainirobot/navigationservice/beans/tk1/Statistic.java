package com.ainirobot.navigationservice.beans.tk1;

public class Statistic {

    private int type;
    private int int1;
    private int int2;
    private int int3;
    private int int4;
    private double double1;
    private double double2;
    private double double3;
    private double double4;
    private String strValue;

    public Statistic(int type, int int1, int int2, int int3, int int4, double double1,
                     double double2, double double3, double double4, String strValue) {
        this.type = type;
        this.int1 = int1;
        this.int2 = int2;
        this.int3 = int3;
        this.int4 = int4;
        this.double1 = double1;
        this.double2 = double2;
        this.double3 = double3;
        this.double4 = double4;
        this.strValue = strValue;
    }

    public int getType() {
        return type;
    }

    public int getInt1() {
        return int1;
    }

    public int getInt2() {
        return int2;
    }

    public int getInt3() {
        return int3;
    }

    public int getInt4() {
        return int4;
    }

    public double getDouble1() {
        return double1;
    }

    public double getDouble2() {
        return double2;
    }

    public double getDouble3() {
        return double3;
    }

    public double getDouble4() {
        return double4;
    }

    public String getStrValue() {
        return strValue;
    }

    @Override
    public String toString() {
        return "Statistic{" +
                "type=" + type +
                ", int1=" + int1 +
                ", int2=" + int2 +
                ", int3=" + int3 +
                ", int4=" + int4 +
                ", double1=" + double1 +
                ", double2=" + double2 +
                ", double3=" + double3 +
                ", double4=" + double4 +
                ", strValue='" + strValue + '\'' +
                '}';
    }
}
