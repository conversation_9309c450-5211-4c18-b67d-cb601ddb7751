package com.ainirobot.navigationservice.roversdkhelper;

import static com.ainirobot.navigationservice.Defs.Def.TK1Def.ERROR_LOG;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.SHOT_LOG;

import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.navigationservice.beans.waiter.SubDeviceBean;
import com.ainirobot.navigationservice.commonModule.configuration.beans.DeviceInfo;
import com.ainirobot.navigationservice.utils.IOUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * 地图文件操作，新包结构的命名和操作
 * <p>
 * 2022.12.28日之后版本，修改包结构：
 * 1. pgm.zip 和 map.zip 去掉，底盘生成文件都直接放在 mapName/navi_data/ 目录下
 * 2. 只在机器端修改为新包结构，机器和服务端的交互仍然使用旧地图包结构，机器端做兼容
 * 3. 机器端修改不考虑兼容 Tk1和 “navigation.properties” 的旧版本
 */
public class MapFileHelper {
    private static final String TAG = MapFileHelper.class.getSimpleName();

    private static final String DEVICE_PROPERTIES = "device.properties";
    private static final String DEVICE_CONFIG_DIR = "/persist/orionoem/";
    private static final Properties sDeviceProperties = getProperties(DEVICE_CONFIG_DIR, DEVICE_PROPERTIES);
    public static final String SDCARD_PATH = Environment.getExternalStorageDirectory() + "/";
    private static final String ROBOT_MAP_PATH = SDCARD_PATH + "robot/map/";
    private static final String NAVI_DATA_PATH = "navi_data/";
    private static final String PGM_FILE_NAME = "map.pgm";

    /**
     * 大文件扩展包
     */
    private static final String EXTRA_FILES_ZIP = "extraFiles.zip";

    /**
     * 招财豹地图的特殊处理，需要转存targets.data为target.json，然后上传
     * 从云端下载的地图，需要转存target.json位targets.data，然后下发给地盘
     * <p>标识码
     */
    public static final String TARGETS_DATA = "targets.data";
    public static final String TARGETS_JSON = "target.json";

    /**
     * 招财豹地图的特殊处理，mapping_track.mapping_track.json，然后上传
     * <p>建图时推动的线路
     */
    private static final String TRACK_DATA = "mapping_track.data";
    private static final String TRACK_JSON = "mapping_track.json";


    /**
     * 从云端下载的豹跑堂地图，需要转存road.json为road_graph.data，然后下发给底盘
     * <p>巡线数据
     */
    public static final String ROAD_DATA = "road_graph.data";
    private static final String ROAD_JSON = "road.json";
    private static final String MAP_AREA_JSON = "map_area.json";
    public static final String MAP_AREA_DATA = "map_area.data";
    public static final String GATE_JSON = "turnstile.json";


    /**
     * 地图信息 JSON 文件
     */
    public static final String MAP_INFO_JSON = "mapinfo.json";

    /**
     * 点位信息序列化文件，
     * 旧版本：place.properties
     * 新版本：place.json
     */
    public static final String PLACE_PROP = "place.properties";
    public static final String PLACE_JSON = "place.json";

    /**
     * 地图导入目录
     */
    private static final String IMPORT_MAP_PATH = SDCARD_PATH + "Download/map_import/";

    public static File getRobotMapDir() {
        File file = new File(ROBOT_MAP_PATH);
        if (!file.exists()) {
            file.mkdirs();
        }
        return file;
    }

    public static String getRobotMapPath() {
        return ROBOT_MAP_PATH;
    }

    public static final String MAP_CONFIG_JSON = "mapConfig.json";


    public static File getMapZipFile(String mapName) {
        return new File(getMapZipPath(mapName));
    }

    public static String getMapZipPath(String mapName) {
        return ROBOT_MAP_PATH + mapName + ".zip";
    }

    public static File getMapDir(String mapName) {
        return new File(getMapFilePath(mapName));
    }

    public static String getMapFilePath(String mapName) {
        return ROBOT_MAP_PATH + mapName + File.separator;
    }

    public static File getNaviDataFile(String mapName) {
        String path = getNaviDataPath(mapName);
        return new File(path);
    }

    public static String getNaviDataPath(String mapName) {
        return ROBOT_MAP_PATH + mapName + File.separator + NAVI_DATA_PATH;
    }

    public static File getMapPgm(String mapName) {
        String path = getNaviDataPath(mapName) + PGM_FILE_NAME;
        return new File(path);
    }

    public static File getMapData(String mapName) {
        return new File(getNaviDataPath(mapName));
    }

    public static File getRoadData(String mapName) {
        String path = getNaviDataPath(mapName) + ROAD_DATA;
        return new File(path);
    }

    public static File getMapAreaData(String mapName) {
        String path = getNaviDataPath(mapName) + MAP_AREA_DATA;
        return new File(path);
    }

    public static File getTargetsData(String mapName) {
        String path = getNaviDataPath(mapName) + TARGETS_DATA;
        return new File(path);
    }

    public static File getTrackData(String mapName) {
        String path = getNaviDataPath(mapName) + TRACK_DATA;
        return new File(path);
    }

    public static File getTargetsDataTempFile(String mapName) {
        String path = getMapFilePath(mapName) + TARGETS_DATA;
        return new File(path);
    }

    public static File getRoadDataTempFile(String mapName) {
        String path = getMapFilePath(mapName) + ROAD_DATA;
        return new File(path);
    }

    public static File getMapAreaDataTempFile(String mapName) {
        String path = getMapFilePath(mapName) + MAP_AREA_DATA;
        return new File(path);
    }

    public static File getTargetsJsonFile(String mapName) {
        String path = getMapFilePath(mapName) + TARGETS_JSON;
        return new File(path);
    }

    public static File getTrackJsonFile(String mapName) {
        String path = getMapFilePath(mapName) + TRACK_JSON;
        Log.d(TAG, "getTrackJsonFile: path=" + path);
        return new File(path);
    }

    public static File getRoadJsonFile(String mapName) {
        String path = getMapFilePath(mapName) + ROAD_JSON;
        Log.d(TAG, "getRoadJsonFilePath: path=" + path);
        return new File(path);
    }
    public static File getGateJsonFile(String mapName) {
        String path = getMapFilePath(mapName) + GATE_JSON;
        Log.d(TAG, "getGateJsonFilePath: path=" + path);
        return new File(path);
    }

    public static File getMapAreaJsonFile(String mapName) {
        String path = getMapFilePath(mapName) + MAP_AREA_JSON;
        Log.d(TAG, " getMapAreaJsonFile:: path=" + path);
        return new File(path);
    }

    public static File getMapInfoJsonFile(String mapName) {
        String path = getMapFilePath(mapName) + MAP_INFO_JSON;
        Log.d(TAG, "getMapInfoJsonFile: path=" + path);
        return new File(path);
    }

    public static File getPlaceJsonFile(String mapName) {
        String path = getMapFilePath(mapName) + PLACE_JSON;
        Log.d(TAG, "getPlaceJsonFile: path=" + path);
        return new File(path);
    }

    public static String getRoadJsonPath(String mapName) {
        String path = getMapFilePath(mapName) + ROAD_JSON;
        Log.d(TAG, "getRoadJsonPath: path=" + path);
        return path;
    }
    public static String getGateJsonPath(String mapName) {
        String path = getMapFilePath(mapName) + GATE_JSON;
        Log.d(TAG, "getGateJsonPath: path=" + path);
        return path;
    }

    public static String getTargetsJsonPath(String mapName) {
        String path = getMapFilePath(mapName) + TARGETS_JSON;
        Log.d(TAG, "getTargetsJsonPath: path=" + path);
        return path;
    }

    public static File getMapConfig(String mapName) {
        String path = getMapFilePath(mapName) + MAP_CONFIG_JSON;
        Log.d(TAG, "getPlaceJsonFile: path=" + path);
        return new File(path);
    }

    public static String getTempExtraFilePath(String mapName) {
        return getNaviDataPath(mapName) + EXTRA_FILES_ZIP;
    }

    public static boolean isMapInfoJsonExists(String mapName) {
        File file = getMapInfoJsonFile(mapName);//isMapInfoJsonExists
        return file.exists() && file.length() > 0;
    }

    public static boolean isPlaceJsonExists(String mapName) {
        File file = getPlaceJsonFile(mapName);
        return file.exists() && file.length() > 0;
    }

    public static boolean isTargetsJsonExists(String mapName) {
        File file = getTargetsJsonFile(mapName);
        return file.exists() && file.length() > 0;
    }

    public static boolean isMapConfigJsonExists(String mapName) {
        File file = getMapConfig(mapName);//isMapConfigJsonExists
        return file.exists() && file.length() > 0;
    }

    public static boolean isTrackJsonExists(String mapName) {
        File file = getTrackJsonFile(mapName);//isTrackJsonExists
        return file.exists() && file.length() > 0;
    }

    public static Properties getDeviceProperties() {
        return sDeviceProperties;
    }

    /**
     * 删除 place 文件
     */
    public static void deletePlacePropFile(String mapName) {
        Log.d(TAG, "deletePlacePropFile: " + mapName);
        File dir = getMapDir(mapName);
        if (!dir.exists()) {
            return;
        }
        File oldPlaceFile = new File(dir, PLACE_PROP);
        if (oldPlaceFile.exists()) {
            oldPlaceFile.delete();
        }
        File newPlaceFile = new File(dir, PLACE_JSON);
        if (newPlaceFile.exists()) {
            newPlaceFile.delete();
        }
    }

    /**
     * 删除地图目录
     */
    public static boolean removeMapDir(String mapName) {
        if (TextUtils.isEmpty(mapName)) {
            return false;
        }
        File sdcard = Environment.getExternalStorageDirectory();
        File dir = new File(sdcard, "robot/map/" + mapName);
        if (!dir.exists()) {
            return false;
        }

        //delete map then delete mapPkg at same time
        File mapPkg = new File(sdcard, "/robot/map/" + mapName + ".zip");
        if (mapPkg.exists()) {
            mapPkg.delete();
        }

        return deleteDirectory(dir);
    }

    public static boolean deleteDirectory(File folder) {
        if (folder.exists()) {
            File[] files = folder.listFiles();
            if (files == null) {
                return false;
            }
            for (int i = 0; i < files.length; i++) {
                if (files[i].isDirectory()) {
                    deleteDirectory(files[i]);
                } else {
                    files[i].delete();
                }
            }
        }
        return folder.delete();
    }

    public static SubDeviceBean getSubDeviceInfo() {
        Properties properties = getDeviceProperties();
        if (properties == null) {
            Log.d(TAG, "getSubDeviceInfo properties is null.");
            return null;
        }

        SubDeviceBean deviceBean = new SubDeviceBean();
        deviceBean.setLora(Integer.parseInt(properties.getProperty("lora") == null ? "-1" : properties.getProperty("lora", "0")));
        deviceBean.setLightFilter(Integer.parseInt(properties.getProperty("light_filter", "0")));
        deviceBean.setDampener(Integer.parseInt(properties.getProperty("dampener", "0")));
        deviceBean.setLidarEai(Integer.parseInt(properties.getProperty("lidar_EAI", "0")));
        deviceBean.setCplFilter(Integer.parseInt(properties.getProperty("CPL_filter", "0")));
        deviceBean.setTopIr(Integer.parseInt(properties.getProperty("TOPIR_mini2", "0")));
        deviceBean.setRgbdD430(Integer.parseInt(properties.getProperty("RGBD_D430", "1")));
        deviceBean.setEsp32(Integer.parseInt(properties.getProperty("esp32") == null ? "-1" : properties.getProperty("esp32", "0")));
        deviceBean.setHeightBody(Integer.parseInt(properties.getProperty("ZCB_G10CM", "0")));
        deviceBean.setLidarFov(Integer.parseInt(properties.getProperty("LIDAR_FOV", "0")));
        deviceBean.setChargeIr(Integer.parseInt(properties.getProperty("CHGIR", "0")));
        deviceBean.setRgbdFm1(Integer.parseInt(properties.getProperty("RGBD_FM1", "0")));
        deviceBean.setRgbdWf(Integer.parseInt(properties.getProperty("RGBD_WF", "0")));
        deviceBean.setDepthType(properties.getProperty("DEPTH", ""));
        deviceBean.setAutoDoor(Integer.parseInt(properties.getProperty("autodoor_4", "0")));
        deviceBean.setSsMono(Integer.parseInt(properties.getProperty("SS_MONO", "0")));
        deviceBean.setTopMono(Integer.parseInt(properties.getProperty("TOP_MONO", "0")));
        Log.d(TAG, "getSubDeviceInfo end:" + deviceBean.toString());
        return deviceBean;
    }

    public static DeviceInfo getDeviceInfo() {
        Properties properties = getDeviceProperties();
        if (properties == null) {
            Log.d(TAG, "getSubDeviceInfo properties is null.");
            return null;
        }
        // 从 Properties 转换为 CameraInfo 对象
        DeviceInfo info = DeviceInfo.fromProperties(properties);
        Log.d(TAG, "getDeviceInfo:" + info);
        if (info.isEmptyDeviceInfo()) {
            Log.d(TAG, "return null deviceInfo");
            return null;
        }
        return info;
    }

    private static Properties getProperties(String path, String name) {
        File file = new File(path, name);
        if (file == null || !file.exists()) {
            Log.d(TAG, "getDeviceProperties file is not exists.");
            return null;
        }
        Properties properties = new Properties();
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            properties.load(fis);
        } catch (IOException e) {
            e.printStackTrace();
            return properties;
        } finally {
            IOUtils.close(fis);
        }
        Log.d(TAG, "getDeviceProperties: start --->>>");
        printProperty(properties);
        Log.d(TAG, "getDeviceProperties: end <<<---");
        return properties;
    }

    private static void printProperty(Properties properties) {
        if (properties == null) {
            Log.d(TAG, "printProperty properties is null");
            return;
        }
        for (Map.Entry<Object, Object> objectObjectEntry : properties.entrySet()) {
            Object key = objectObjectEntry.getKey();
            Object value = objectObjectEntry.getValue();
            Log.d(TAG, "key=" + key + " value=" + value);
        }
    }

    public static File createSnapshotLogFile(String logID) {
        File sdcard = Environment.getExternalStorageDirectory();
        File logFile = new File(sdcard, "/ninjia/log/shotLog/" + logID + ".zip");
        return logFile;
    }

    public static File getLogFile(String logName) {
        File sdcard = Environment.getExternalStorageDirectory();
        File dir = new File(sdcard, "robot/log");
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return new File(dir, logName);
    }

    public static File buildLogFile(String logPath, String fileType) {
        String[] split = logPath.split("/");
        String fileName = split[split.length - 1];
        File sdcard = Environment.getExternalStorageDirectory();
        File dir;
        if (TextUtils.equals(fileType, ERROR_LOG)) {
            dir = new File(sdcard, "/ninjia/log/errorLog");
        } else if (TextUtils.equals(fileType, SHOT_LOG)) {
            dir = new File(sdcard, "/ninjia/log/shotLog");
        } else {
            dir = new File(sdcard, "/ninjia/log/packLog");
        }

        if (!dir.exists()) {
            dir.mkdirs();
        }
        return new File(dir, fileName);
    }

    public static List<String> getMapList() {
        List<String> list = new ArrayList<>();
        File mapDir = getRobotMapDir();
        if (!mapDir.isDirectory() || mapDir.list() == null
                || (mapDir.list() != null && mapDir.list().length == 0)) {
            return list;
        }

        File[] files = mapDir.listFiles();
        if (files == null) {
            return list;
        } else {
            for (File file : files) {
                if (file.isDirectory() && file.list() != null && file.list().length > 0) {
                    list.add(file.getName());
                }
            }
        }
        Log.d(TAG, "getMapList: list=" + list);
        return list;
    }

    /**
     * 导入地图源文件是否存在且不为空
     */
    public static boolean isImportMapExist(String mapName) {
        File importMapDir = new File(getImportMapFilePath(mapName));
        return importMapDir.exists() && importMapDir.list() != null;
    }

    public static String getImportMapFilePath(String mapName) {
        return IMPORT_MAP_PATH + mapName + File.separator;
    }

}
