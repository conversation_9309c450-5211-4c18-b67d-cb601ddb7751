package com.ainirobot.navigationservice.Defs;

public abstract class AbsCommonConfig implements IDeviceConfig {

    public AbsCommonConfig(){

    }

    @Override
    public double getMinLinearAcceleration() {
        return 0.3;
    }

    @Override
    public double getMaxLinearAcceleration() {
        return 0.8;
    }

    @Override
    public double getMinAngularAcceleration() {
        return 0.5;
    }

    @Override
    public double getMaxAngularAcceleration() {
        return 3.0;
    }
}
