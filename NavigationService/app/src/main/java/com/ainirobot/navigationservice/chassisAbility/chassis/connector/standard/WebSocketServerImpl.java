package com.ainirobot.navigationservice.chassisAbility.chassis.connector.standard;

import android.util.Log;

import org.java_websocket.WebSocket;
import org.java_websocket.handshake.ClientHandshake;
import org.java_websocket.server.WebSocketServer;

import java.net.InetSocketAddress;
import java.nio.ByteBuffer;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;


public class WebSocketServerImpl extends WebSocketServer {

    private final static String TAG = TAGPRE + WebSocketServerImpl.class.getSimpleName();

    private WebSocket mConn = null;
    private Callback callback;
    private ConnectCallback connectCallback;
    private String name;

    public WebSocketServerImpl(InetSocketAddress address, String name) {
        super(address);
        this.name = name;
    }


    @Override
    public void onOpen(WebSocket conn, ClientHandshake handshake) {
        mConn = conn;
        Log.d(TAG, name + " onOpen in");
        conn.send("Welcome to the server!"); //This method sends a message to the new client
        broadcast( "new connection: " + handshake.getResourceDescriptor() ); //This method sends a message to all clients connected
//        Log.d(TAG,"new connection to " + conn.getRemoteSocketAddress());
        if (connectCallback != null) {
            connectCallback.onConnectIn(name, mConn);
        }
    }

    @Override
    public void onClose(WebSocket conn, int code, String reason, boolean remote) {
        mConn = null;
        Log.d(TAG,name + " is closed with exit code " + code + " additional info: " + reason);
        if (connectCallback != null) {
            connectCallback.onCloseIn(name, mConn, code);
        }
    }

    @Override
    public void onMessage(WebSocket conn, String message) {
        Log.d(TAG,name + " received message from " + ": " + message);
    }

    @Override
    public void onMessage( WebSocket conn, ByteBuffer message ) {
//        Log.d(TAG,name + " received ByteBuffer from "	+ conn.getRemoteSocketAddress());
        if (callback != null) {
            byte [] b = new byte[message.remaining()];
            message.get(b);
            callback.onMsgIn(b);
        }
    }

    @Override
    public void onError(WebSocket conn, Exception ex) {
//        Log.e(TAG,name + " an error occured on connection " + ":" + ex);
        if (connectCallback != null) {
            connectCallback.onError(name, conn);
        }
    }

    @Override
    public void onStart() {
        Log.d(TAG,name + " server started successfully");
    }

    public void sendMsgString(String text) {
        if (mConn != null) {
            mConn.send(text);
        }
    }

    public boolean sendMsgBytes(byte [] data) {
        if (mConn != null) {
            Log.d(TAG, name + " sendMsgBytes");
            mConn.send(data);
            return true;
        }
        return false;
    }

    public void listenMsg(Callback callback) {
        this.callback = callback;
    }

    public interface Callback {
        void onMsgIn(byte[] data);
    }

    public void listenConnect(ConnectCallback connectCallback) {
        this.connectCallback = connectCallback;
    }

    public interface ConnectCallback {
        void onConnectIn(String name, WebSocket mConn);
        void onCloseIn(String name, WebSocket mConn, int code);
        void onError(String name, WebSocket mConn);
    }
}