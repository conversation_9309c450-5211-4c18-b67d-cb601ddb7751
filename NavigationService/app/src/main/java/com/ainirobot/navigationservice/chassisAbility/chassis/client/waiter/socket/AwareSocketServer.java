/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.navigationservice.chassisAbility.chassis.client.waiter.socket;


import android.util.Log;


import com.ainirobot.navigationservice.utils.IOUtils;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 与LXC系统直接通信的 Socket测试通道。端口号8071
 *
 * 截至目前节点，暂未应用.所以暂未初始化
 */
public class AwareSocketServer {

    private static final String TAG = AwareSocketServer.class.getSimpleName();

    private ServerSocket mServerSocket = null;
    private Thread mServerThread = null;
    private int mServerPort = 8071;
    private ConcurrentMap<String, Client> mClients;
    private OnMessageListener mMessageListener;
    private OnConnectListener mConnectListener;

    public static final String sChassisID = "ChassisClient";

    public AwareSocketServer(int port) {
        this.mClients = new ConcurrentHashMap<>();
        this.mServerPort = port;
    }

    public boolean writeMessageToChassis(String clientId, byte[] message) {
        Client client = mClients.get(clientId);
        return client != null && client.write(message);
    }

    public boolean isAlive(String clientId) {
        Client client = mClients.get(clientId);
        return client != null && client.isAlive();
    }

    public void setMessageListener(OnMessageListener listener) {
        this.mMessageListener = listener;
        for (Client client : mClients.values()) {
            client.setMessageListener(listener);
        }
    }

    public void setConnectListener(OnConnectListener listener) {
        this.mConnectListener = listener;
    }

    public void start() {
        startServer();
    }

    private void startServer() {
        if (mServerThread != null) {
            return;
        }

        mServerThread = new Thread() {
            @Override
            public void run() {
                try {
                    mServerSocket = new ServerSocket(mServerPort);
                    while (!isInterrupted()) {
                        Log.i(TAG, "server in will call accept ServerPort: " + mServerPort);
                        Socket clientSocket = mServerSocket.accept();
//                        BufferedReader reader = new BufferedReader(
//                                new InputStreamReader(clientSocket.getInputStream()));
//                        String msgInfo = reader.readLine();
//                        Log.i(TAG, "server in read from chassis=" + msgInfo);
//                        if (AwareMessage.verifyMessagePlatform(msgInfo)) {
                            Client preClient = mClients.get(sChassisID);
                            if (preClient != null && preClient.isAlive){
                                preClient.destroy();
                            }
                            Client client = new Client(sChassisID, clientSocket, null, mMessageListener);
                            mClients.put(sChassisID, client);
                            if (mConnectListener != null) {
                                Log.i(TAG, "mConnectListener != null, report onConnected");
                                mConnectListener.onConnected();
                            }
//                        } else {
//                            reader.close();
//                            clientSocket.close();
//                        }
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    dealWithSocketException(e);
                }
            }
        };
        mServerThread.start();
    }

    private void dealWithSocketException(Exception e) {
        Log.e(TAG, "dealWithSocketException will call startserver");
        if (null != mServerSocket) {
            try {
                mServerSocket.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
            mServerSocket = null;
        }
        mServerThread = null;
        startServer();
    }


    public void stop() {
        if (mServerThread != null){
            mServerThread.interrupt();
            mServerThread = null;
        }
        closeSocket();
    }

    /**
     * Closes this socket
     */
    private void closeSocket() {
        if (mServerSocket != null) {
            try {
                mServerSocket.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            mServerSocket = null;
        }
    }

    private class Client {
        private final String mId;
        private final Socket mSocket;
        private final BufferedReader mReader;
        private volatile boolean isAlive = true;
        private BlockingQueue<byte[]> mMessageQueue;
        private volatile OnMessageListener mListener;

        private Thread mReceiveThread = new Thread() {
            @Override
            public void run() {
                try {
                    while (!isInterrupted() && isAlive()) {
                        byte[] message = new byte[256];
                        int length = mSocket.getInputStream().read(message);
                        Log.d(TAG, "Aware received length:" + length + " data:" + message);
                        if (message == null || length <= 0) {
                            if (mConnectListener != null && isAlive) {
                                Log.e(TAG, "message == null, report disconnect");
                                mConnectListener.onDisconnected();
                            }
                            Log.e(TAG, "message == null");
                            isAlive = false;
                            return;
                        }else {
                            byte[] msgData = new byte[length];
                            System.arraycopy(message, 0, msgData, 0, length);
                            handleNewMessage(msgData);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    Log.e(TAG, "Exception mConnectListener != null " + (mConnectListener != null)
                            + "isAlive " + isAlive);
                    if (mConnectListener != null && isAlive) {
                        mConnectListener.onDisconnected();
                    }
                    isAlive = false;
                }
            }
        };

        private Thread mSendThread = new Thread() {
            @Override
            public void run() {
                try {
                    DataOutputStream dos = new DataOutputStream(mSocket.getOutputStream());
                    while (!isInterrupted() && isAlive()) {
                        if (mMessageQueue != null && !mMessageQueue.isEmpty()){
                            byte[] message = mMessageQueue.take();
                            dos.write(message);
                            Log.d(TAG, "Write to Chassis : " + message);
                        }
                    }
                } catch (IOException | InterruptedException e) {
                    Log.e(TAG, "IOException | InterruptedException mConnectListener != null "
                            + (mConnectListener != null) + "isAlive " + isAlive);
                    if (mConnectListener != null && isAlive) {
                        mConnectListener.onDisconnected();
                    }
                    isAlive = false;
                }
            }
        };

        public Client(String clientId, Socket socket, BufferedReader reader, OnMessageListener listener) {
            this.mId = clientId;
            this.mSocket = socket;
            this.mReader = reader;
            this.mListener = listener;
            mMessageQueue = new LinkedBlockingQueue<>(100);
            mReceiveThread.start();
            mSendThread.start();
        }

        public void setMessageListener(OnMessageListener listener) {
            mListener = listener;
        }

        public boolean isAlive() {
            return isAlive;
        }

        public boolean write(byte[] message) {
            return message != null && message.length > 0 && isAlive() && mMessageQueue.offer(message);
        }

        public void destroy() {
            Log.d(TAG, "destroy");
            isAlive = false;
            IOUtils.close(mSocket);
            mReceiveThread.interrupt();
            mSendThread.interrupt();
        }

        private void handleNewMessage(byte[] content) {
//            String message = AwareMessage.parse(content);
//            if (message == null) {
//                return;
//            }

            if (mListener != null) {
                mListener.onNewMessage(content);
            }
        }
    }

    public interface OnMessageListener {
        void onNewMessage(byte[] message);
    }

    public interface OnConnectListener {
        void onConnected();

        void onDisconnected();

    }
}
