package com.ainirobot.navigationservice.business.rpc.algorithm;

import android.util.Log;

import com.ainirobot.navigationservice.business.rpc.SpeedBean;

/**
 * mix motion Algorithm
 *
 * @version V1.0.0
 * @date 2019/11/15 15:00
 */
public class MixMotion extends MotionAlgorithm {
    public MixMotion(SpeedBean paramSpeedBean) {
        super(paramSpeedBean);
    }

    @Override
    public void motion() {

        SpeedBean targetSpeed = target;
        Log.i(TAG, "motion: targetSpeed= " + targetSpeed);
        LinearMotion linearMotion = new LinearMotion(targetSpeed);
        linearMotion.motion();
    }
}
