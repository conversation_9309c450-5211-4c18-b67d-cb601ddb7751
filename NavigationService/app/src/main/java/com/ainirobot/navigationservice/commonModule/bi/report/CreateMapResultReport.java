package com.ainirobot.navigationservice.commonModule.bi.report;


import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class CreateMapResultReport extends BaseBiReport {

    public CreateMapResultReport() {
        super("base_robot_map_create_result");
    }

    public CreateMapResultReport mapName(String mapName) {
        addData("map_name", mapName);
        return this;
    }

    public CreateMapResultReport mapType(int mapType) {
        addData("map_type", mapType);
        return this;
    }

    public CreateMapResultReport errorCode(int errorCode) {
        addData("error_code", errorCode);
        return this;
    }

    public CreateMapResultReport targetTotalNum(int targetTotalNum) {
        addData("target_total_num", targetTotalNum);
        return this;
    }

    public CreateMapResultReport targetSuccNum(int targetSuccNum) {
        addData("target_succ_num", targetSuccNum);
        return this;
    }

    public CreateMapResultReport targetErrorNum(int targetErrorNum) {
        addData("target_error_num", targetErrorNum);
        return this;
    }

    public CreateMapResultReport targetErrorInfo(String targetErrorInfo) {
        addData("target_error_info", targetErrorInfo);
        return this;
    }

    public CreateMapResultReport targetsData(String targetsData) {
        addData("targets_data", targetsData);
        return this;
    }

    public CreateMapResultReport createTime() {
        addData("ctime", System.currentTimeMillis());
        return this;
    }
}
