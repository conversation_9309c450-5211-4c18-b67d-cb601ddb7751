package com.ainirobot.navigationservice.chassisAbility.chassis.client.waiter.monitor;

import android.util.Log;

import com.ainirobot.navigationservice.beans.waiter.BasePoseBean;
import com.ainirobot.navigationservice.utils.IOUtils;
import com.ainirobot.navigationservice.utils.MD5;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.util.Arrays;

/**
 * WifiNan数据Bean
 * 2021-12-17版本修改记录：为减少nan单次传输数据量，Gson格式传输改为字节流传输
 * 格式：数据单元如果是定长则只有数据，非定长则首字节为数据长度，校验首字节0x05
 */
public class WifiNanTransBean {
    private static final String TAG = "WifiNanTransBean";

    private String head = "OrionStar";
    private String tail = "Tail";
    private String mapName = "";

    //实际传输的字节流包含的数据
    public static final byte STREAM_HEAD = 0x05; //首个byte
    private int loraId = 0;
    private long timestamp = 0L;
    private BasePoseBean poseBean;
    private String md5Prefix = "";//地图名称md5值前四个字符
    private long multiBytesTimeDiff = 0;//多机字节流时间差，单位：毫秒
    private int multiBytesCounts = 0;//多机字节流长度
    private byte[] multiBytes = new byte[]{};//多机字节流

    public WifiNanTransBean() {
    }

    public WifiNanTransBean(int loraId, long timestamp, BasePoseBean poseBean, String mapName) {
        this.loraId = loraId;
        this.timestamp = timestamp;
        this.poseBean = poseBean;
        this.mapName = mapName;
    }

    public WifiNanTransBean(String head, int loraId, long timestamp, BasePoseBean poseBean, String mapName, String tail) {
        this.head = head;
        this.loraId = loraId;
        this.timestamp = timestamp;
        this.poseBean = poseBean;
        this.mapName = mapName;
        this.tail = tail;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getLoraId() {
        return loraId;
    }

    public void setLoraId(int loraId) {
        this.loraId = loraId;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public BasePoseBean getPoseBean() {
        return poseBean;
    }

    public void setPoseBean(BasePoseBean poseBean) {
        this.poseBean = poseBean;
    }

    public String getMapName() {
        return mapName;
    }

    public void setMapName(String mapName) {
        this.mapName = mapName;
    }

    public String getTail() {
        return tail;
    }

    public void setTail(String tail) {
        this.tail = tail;
    }

    public String getMd5Prefix() {
        return md5Prefix;
    }

    public void setMd5Prefix(String md5Prefix) {
        this.md5Prefix = md5Prefix;
    }

    public int getMultiBytesCounts() {
        return multiBytesCounts;
    }

    public void setMultiBytesCounts(int multiBytesCounts) {
        this.multiBytesCounts = multiBytesCounts;
    }

    public byte[] getMultiBytes() {
        return multiBytes;
    }

    public void setMultiBytes(byte[] multiBytes) {
        this.multiBytes = multiBytes;
    }

    public long getMultiBytesTimeDiff() {
        return multiBytesTimeDiff;
    }

    public void setMultiBytesTimeDiff(long multiBytesTimeDiff) {
        this.multiBytesTimeDiff = multiBytesTimeDiff;
    }

    /**
     * 把bean文件数据写入字节流，本机数据外发
     * @return
     */
    public byte[] beanToStream() {
        Log.d(TAG, "beanToStream:本机数据外发： bean=" + toString());
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        DataOutputStream dos = new DataOutputStream(bos);
        try {
            dos.writeByte(STREAM_HEAD);
            dos.writeInt(loraId);
            dos.writeLong(timestamp);
            dos.writeDouble(poseBean != null ? poseBean.getX() : 0);
            dos.writeDouble(poseBean != null ? poseBean.getY() : 0);
            dos.writeDouble(poseBean != null ? poseBean.getTheta() : 0);
            String md5 = MD5.createMd5(mapName);
            dos.writeBytes(md5.substring(0, 4)); //以上一共41个字节
            dos.writeLong(multiBytesTimeDiff);
            dos.writeInt(multiBytesCounts);
            dos.write(multiBytes);
            Log.d(TAG, "beanToStream: stream length=" + bos.size()
                    + " mapName=" + mapName + " md5=" + md5);
            return bos.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "beanToStream:e: " + e.getMessage());
        } finally {
            IOUtils.close(dos);
            IOUtils.close(bos);
        }

        return bos.toByteArray();
    }

    /**
     * 从字节流中解析数据，解析顺序必须和写入顺序保持一直，其他机器发过来的数据
     * @param stream
     * @return
     */
    public WifiNanTransBean streamToBean(byte[] stream) {
        if (stream == null || stream.length <= 0 || stream[0] != STREAM_HEAD) {
            return null;
        }
        Log.d(TAG, "streamToBean: stream length=" + stream.length);
        ByteArrayInputStream bis = new ByteArrayInputStream(stream);
        DataInputStream dis = new DataInputStream(bis);
        try {
            byte first = dis.readByte();
            Log.d(TAG, "streamToBean: first byte=" + first);
            setLoraId(dis.readInt());
            setTimestamp(dis.readLong());
            double x = dis.readDouble();
            double y = dis.readDouble();
            double theta = dis.readDouble();
            setPoseBean(new BasePoseBean(x, y, theta));
            byte[] md5 = new byte[4];
            dis.read(md5);
            setMd5Prefix(new String(md5)); //md5 以上一共41个byte
            Log.d(TAG, "streamToBean: md5=" + new String(md5) + " bis size:" + bis.available());
            setMultiBytesTimeDiff(dis.readLong());
            Log.d(TAG, "streamToBean: time diff=" + getMultiBytesTimeDiff());
            int multiByteCounts = dis.readInt();
            Log.d(TAG, "streamToBean: multiByteCounts=" + multiByteCounts);
            if (multiByteCounts > 0 && multiByteCounts <= 255) {
                setMultiBytesCounts(multiByteCounts);
                byte[] multiBytes = new byte[multiByteCounts];
                dis.read(multiBytes);
                setMultiBytes(multiBytes);
            }
            Log.d(TAG, "streamToBean: result=" + toString());
            return this;
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "streamToBean:e: " + e.getMessage());
        } finally {
            IOUtils.close(dis);
            IOUtils.close(bis);
        }

        return this;
    }

    @Override
    public String toString() {
        return "WifiNanTransBean{" +
                "head='" + head + '\'' +
                ", tail='" + tail + '\'' +
                ", mapName='" + mapName + '\'' +
                ", loraId=" + loraId +
                ", timestamp=" + timestamp +
                ", poseBean=" + poseBean +
                ", md5Prefix='" + md5Prefix + '\'' +
                ", multiBytesTimeDiff=" + multiBytesTimeDiff +
                ", multiBytesCounts=" + multiBytesCounts +
                ", multiBytes=" + Arrays.toString(multiBytes) +
                '}';
    }
}
