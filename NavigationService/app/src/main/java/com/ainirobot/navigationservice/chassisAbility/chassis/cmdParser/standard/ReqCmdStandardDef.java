package com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard;

public class ReqCmdStandardDef {

    public static final String REQ_PRIMITIVE_MOVE = "req_primitive_move";

    //yuefan command def, begin
    public static final String REQ_SET_MANUAL_MODE = "set_manual_mode";
    public static final String REQ_SET_CONTROL_MODE = "set_control_mode";
    public static final String REQ_SET_FIX_POINT_MODE = "set_fix_point_mode";
    public static final String REQ_CHARGE_PILE_RECOGNIZE = "charge_pile_recognize";
    public static final String REQ_CHARGE_PILE_SEARCH = "charge_pile_search";
    public static final String REQ_CHARGE_PILE_DOCKING = "charge_pile_docking";
    public static final String REQ_FORCE_FORWARD_MOVE = "force_forward_move";
    public static final String REQ_FORCE_ROTATION_MOVE = "force_rotation_move";
    public static final String REQ_SET_NAVI_GOAL_POINT = "set_navi_goal_point";
    public static final String REQ_SAVE_MAPPING_PATH = "save_mapping_path";
    public static final String REQ_SAVE_MAPPING_POSE = "save_mapping_pose";
    public static final String REQ_GET_SENSOR_STATUS = "get_sensor_status";

    //map about
    public static final String REQ_DELETE_MAP = "delete_map";
    public static final String REQ_START_CREATE_MAP = "start_create_map";
    public static final String REQ_GET_MAP_INFO = "get_map_info";
    public static final String REQ_GET_LANDMARK_LIST = "get_landmark_list";
    public static final String REQ_MODIFY_LANDMARK_INFO = "modify_landmark_info";
    public static final String REQ_DELETE_LANDMARK = "delete_landmark";
    public static final String REQ_ADD_LANDMARK = "add_landmark";
    public static final String REQ_GET_CUR_MAP_INFO = "get_cur_map_info";
    public static final String REQ_SWITCH_MAP = "switch_map";
    public static final String REQ_GET_REALTIME_MAP = "get_realtime_map";
    public static final String REQ_GET_MAP_LIST = "get_map_list";
    public static final String REQ_STOP_CREATE_MAP = "stop_create_map";
    public static final String REQ_SET_RELOCALIZATION = "set_relocalization";
    public static final String REQ_GET_LOCALIZATION_STATE = "get_localization_state";
    public static final String REQ_SET_NAVI_SPEED_PARAM = "set_navi_speed_param";

    //yuefan command def, end
}
