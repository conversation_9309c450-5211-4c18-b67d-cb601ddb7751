package com.ainirobot.navigationservice.commonModule.bi.bean;

import com.ainirobot.navigationservice.beans.waiter.SubDeviceBean;

/**
 * 底盘设备类型
 * 1.deviceType，底盘设备号，RobotConfig配置，int类型
 * 2.deviceSubType，子设备类型，properties中信息，SubDeviceBean类型
 */
public class BiDeviceType {
    private int deviceType;
    private SubDeviceBean deviceSubType;

    public BiDeviceType(int deviceType, SubDeviceBean deviceSubType) {
        this.deviceType = deviceType;
        this.deviceSubType = deviceSubType;
    }

    public int getDeviceType() {
        return deviceType;
    }

    public SubDeviceBean getDeviceSubType() {
        return deviceSubType;
    }

    @Override
    public String toString() {
        return "BiDeviceType{" +
                "deviceType='" + deviceType + '\'' +
                ", deviceSubType='" + deviceSubType.toString() + '\'' +
                '}';
    }
}
