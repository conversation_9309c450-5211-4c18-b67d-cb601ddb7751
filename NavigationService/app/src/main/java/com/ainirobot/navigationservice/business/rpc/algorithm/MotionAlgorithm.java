package com.ainirobot.navigationservice.business.rpc.algorithm;

import com.ainirobot.navigationservice.business.rpc.SpeedBean;
import com.ainirobot.navigationservice.chassisAbility.ChassisManager;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;

import java.math.BigDecimal;

/**
 * base motion Algorithm
 *
 * @version V1.0.0
 * @date 2019/11/15 15:00
 */
public abstract class MotionAlgorithm {
    public static volatile SpeedBean current = new SpeedBean();

    public static volatile SpeedBean target;

    protected final String TAG = getClass().getSimpleName();

    public MotionAlgorithm(SpeedBean paramSpeedBean) {
        target = paramSpeedBean;
    }

    /**
     * control motion
     */
    public abstract void motion();

    public void motion(float linearSpeed, float angularSpeed) {
        IChassisClient client =  ChassisManager.getInstance().getChassisClient();
//        ChassisClientTk1Impl client2 = (ChassisClientTk1Impl) ChassisManager.getInstance().getChassisClient();

        client.sendPrimitiveMovingSpeed(angularSpeed, linearSpeed);
//        client2.getCommandParser().sendPrimitiveMovingCommand(angularSpeed, linearSpeed);

        current.setAngularSpeed(new BigDecimal(Float.toString(angularSpeed)));
        current.setLinearSpeed(new BigDecimal(Float.toString(linearSpeed)));
    }
}
