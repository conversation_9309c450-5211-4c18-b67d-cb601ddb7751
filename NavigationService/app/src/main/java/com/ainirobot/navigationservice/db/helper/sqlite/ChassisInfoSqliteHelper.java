package com.ainirobot.navigationservice.db.helper.sqlite;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.provider.Settings;
import android.util.Log;

import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.waiter.MultiRobotConfigBean;
import com.ainirobot.navigationservice.db.entity.ChassisInfo;
import com.ainirobot.navigationservice.db.helper.iml.ChassisInfoHelperIml;
import com.ainirobot.navigationservice.db.sqlite.TableInfoDef;
import com.ainirobot.navigationservice.db.sqlite.SqliteDbMigrate;
import com.ainirobot.navigationservice.utils.GsonUtil;

import java.util.List;
import java.util.Map;

public class ChassisInfoSqliteHelper extends BaseSqliteHelper<ChassisInfo> implements ChassisInfoHelperIml {

    public ChassisInfoSqliteHelper(SqliteDbMigrate sqliteDbMigrate) {
        super(sqliteDbMigrate, TableInfoDef.TABLE_NAME_CHASSIS_INFO);
    }

    @Override
    protected Map<String, Integer> updateCursorIndexMap(Cursor cursor) {
        return sqliteDbMigrate.getChassisInfoIndex(cursor);
    }

    @Override
    public String getRoverConfig() {
        return getChassisInfoString(TableInfoDef.COLUMN_ROVER_CONFIG);
    }

    @Override
    public String getIpNavigation() {
        return getChassisInfoString(TableInfoDef.COLUMN_IP_NAVIGATION);
    }

    @Override
    public String getMultiRobotConfig() {
        return getChassisInfoString(TableInfoDef.COLUMN_LORA_CONFIG);
    }

    @Override
    public String getIpSdkRos() {
        return getChassisInfoString(TableInfoDef.COLUMN_IP_SDK_ROS);
    }

    @Override
    public void initChassisInfoData(Context context, List<ChassisInfo> chassisInfos) {
        if (null == chassisInfos || chassisInfos.isEmpty()) {
            Log.d(TAG, "initChassisInfoData: list is null");
            return;
        }
        Log.d(TAG, "initChassisInfoData: start");
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        writeDb.beginTransaction();
        try {
            writeDb.delete(mTableName, null, null);
            for (ChassisInfo chassisInfo : chassisInfos) {
                ContentValues values = sqliteDbMigrate.chassisInfoToContentValues(chassisInfo);
                writeDb.insert(mTableName, null, values);
                Log.d(TAG, "initChassisInfoData: insert chassisInfo = " + chassisInfo);
            }
            writeDb.setTransactionSuccessful();

            updateLoraEnableSetting(context, chassisInfos);
        } catch (Exception e) {
            Log.e(TAG, "initChassisInfoData: error", e);
        } finally {
            writeDb.endTransaction();
        }
    }

    @Override
    public boolean updateIpNavigation(String ipNavigation) {
        return updateSingleColumn(TableInfoDef.COLUMN_IP_NAVIGATION, ipNavigation);
    }

    @Override
    public boolean updateIpSdkRos(String ip) {
        return updateSingleColumn(TableInfoDef.COLUMN_IP_SDK_ROS, ip);
    }

    @Override
    public boolean updateMultiRobotConfig(RoverConfig roverConfig) {
        return updateSingleColumn(TableInfoDef.COLUMN_LORA_CONFIG, GsonUtil.toJson(roverConfig));
    }

    @Override
    public boolean updateMultiRobotConfig(MultiRobotConfigBean multiRobotConfigBean) {
        return updateSingleColumn(TableInfoDef.COLUMN_LORA_CONFIG, GsonUtil.toJson(multiRobotConfigBean));
    }

    private boolean updateSingleColumn(String columnName, String value) {
        if (columnName == null || value == null) {
            Log.e(TAG, "updateSingleColumn: columnName or value is null");
            return false;
        }
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        ContentValues values = new ContentValues();
        values.put(columnName, value);
        // 尝试更新数据
        int rowsUpdated = writeDb.update(mTableName, values, null, null);
        // 如果更新失败（没有记录被更新），尝试插入新记录
        if (rowsUpdated <= 0) {
            long rowId = writeDb.insert(mTableName, null, values);
            if (rowId == -1) {
                Log.d(TAG, "updateSingleColumn: insert failed for column = " + columnName + " with value = " + value);
                return false;
            } else {
                Log.d(TAG, "updateSingleColumn: insert succeeded for column = " + columnName + " with value = " + value);
                return true;
            }
        } else {
            Log.d(TAG, "updateSingleColumn: update succeeded for column = " + columnName + " with value = " + value);
            return true;
        }
    }

    private String getChassisInfoString(String columnName) {
        if (columnName == null) {
            Log.e(TAG, "getChassisInfoString: columnName is null");
            return null;
        }
        SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
        Cursor cursor = null;
        String result = null;
        try {
            cursor = readDb.query(mTableName, new String[]{columnName}, null, null, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                result = cursor.getString(cursor.getColumnIndex(columnName));
            }
        } catch (Exception e) {
            Log.e(TAG, "Error querying " + columnName, e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return result;
    }

    private void updateLoraEnableSetting(Context context, List<ChassisInfo> chassisInfos) {
        ChassisInfo info;
        MultiRobotConfigBean configBean;
        if ((info = chassisInfos.get(0)) != null && (configBean =
                GsonUtil.fromJson(info.getMultiRobotConfig(), MultiRobotConfigBean.class)) != null) {
            Settings.Global.putInt(context.getContentResolver(), "multi_robot_lora_enable", configBean.isEnable() ? 1 : 0);
        }
    }
}