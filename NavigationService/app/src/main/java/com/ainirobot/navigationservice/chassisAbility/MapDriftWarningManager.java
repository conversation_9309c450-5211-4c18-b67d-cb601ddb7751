package com.ainirobot.navigationservice.chassisAbility;

import android.content.IntentFilter;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.hardware.RobotCore;

import org.json.JSONObject;

import ninjia.android.proto.CommonProtoWrapper;

/**
 * 地图漂移管理
 */
public class MapDriftWarningManager {
    private final String TAG = "MapDriftWarningManager";

    private static MapDriftWarningManager mInstance;

    private WarningPolicy currentPolicy;

    private volatile int mRadarStatus = Definition.RADAR_STATUS_OPENED;

    private MapDriftWarningManager() {
        if (ProductInfo.isMiniProduct() || ProductInfo.isMeissa2()
        ) {
            currentPolicy = new RadarOffPolicy();
        }

    }

    public static synchronized MapDriftWarningManager getInstance() {
        if (mInstance == null) {
            mInstance = new MapDriftWarningManager();
        }
        return mInstance;
    }

    public void onUpdateOdom(CommonProtoWrapper.OdomDataProto odomDataProto, int mRadarStatus) {
        if (currentPolicy == null) {
            return;
        }
        this.mRadarStatus = mRadarStatus;
        if (currentPolicy.canCalculate()) {
            if (!currentPolicy.isStarted()) {
                currentPolicy.start(odomDataProto);
            }
            currentPolicy.calculate(odomDataProto);
        } else if (currentPolicy.isStarted()){
            currentPolicy.stop();
        }
    }

    private void sendWarningStatus(int status, String msg) {
        JSONObject param = new JSONObject();
        try {
            param.put("status", status);
            param.put("msg", msg);
            sendStatusReport(Definition.STATUS_MAP_DRIFT_WARNING, param.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void sendStatusReport(String type, String data) {
        RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, type, data);
    }

    /**
     * 漂移计算
     * 根据雷达开闭的状态来计算偏移数据，超过规定阈值后上报漂移状态
     *
     * Definition.MAP_DRIFT_WARNING -- 偏移警告 通知上层开启雷达
     * Definition.MAP_DRIFT_DANGEROUS -- 偏移危险 地图已漂移通知上层系统接管引导用户重定位
     */
    private class RadarOffPolicy implements WarningPolicy{
        private boolean isStarted = false;

        private double firstOffsetWhirl;
        private double firstLeft;
        private double firstRight;
        private double curLeft;
        private double curRight;

        private boolean isWarning = false;
        private boolean isDangerous = false;

        @Override
        public void calculate(CommonProtoWrapper.OdomDataProto odomDataProto) {
            curLeft = odomDataProto.getLeftAcc();
            curRight = odomDataProto.getRightAcc();
            Log.v(TAG, "RadarOffPolicy calculate curLeft: " + curLeft + ", curRight: " + curRight + ", firstLeft: " + firstLeft + ", firstRight: " + firstRight);

            double offsetLin = Math.max(Math.abs(curLeft - firstLeft), Math.abs(curRight - firstRight));
            double offsetWhirl = Math.abs((curLeft - curRight) - firstOffsetWhirl);

            Log.v(TAG, "RadarOffPolicy calculate offsetLin: " + offsetLin + ", offsetWhirl: " + offsetWhirl + ", isWarning: " + isWarning + "isDangerous: " + isDangerous);
            if (offsetLin < 1.0 && offsetLin >= 0.3 && !isWarning) {
                isWarning = true;
                sendWarningStatus(Definition.MAP_DRIFT_WARNING, "");
            } else if(offsetWhirl < 0.15 && offsetWhirl >= 0.05 && !isWarning) {
                isWarning = true;
                sendWarningStatus(Definition.MAP_DRIFT_WARNING, "");
            } else if (offsetLin >= 1.0 && !isDangerous) {
                isWarning = true;
                isDangerous = true;
                sendWarningStatus(Definition.MAP_DRIFT_DANGEROUS, "");
            } else if (offsetWhirl > 0.15 && !isDangerous) {
                isWarning = true;
                isDangerous = true;
                sendWarningStatus(Definition.MAP_DRIFT_DANGEROUS, "");
            }

        }


        @Override
        public void start(CommonProtoWrapper.OdomDataProto odomDataProto) {
            isStarted = true;
            firstLeft = odomDataProto.getLeftAcc();
            firstRight = odomDataProto.getRightAcc();
            firstOffsetWhirl = firstLeft - firstRight;
            isWarning = false;
            isDangerous = false;
            Log.d(TAG, "RadarOffPolicy start firstLeft: " + firstLeft + ", firstRight: " + firstRight);
        }

        @Override
        public void stop() {
            isStarted = false;
        }

        @Override
        public boolean canCalculate() {
            if (mRadarStatus == Definition.RADAR_STATUS_OPENED) {
                return false;
            }
            return true;
        }

        @Override
        public boolean isStarted() {
            return isStarted;
        }
    }


    interface WarningPolicy {
        void calculate(CommonProtoWrapper.OdomDataProto odomDataProto);

        void start(CommonProtoWrapper.OdomDataProto odomDataProto);

        void stop();

        boolean canCalculate();

        boolean isStarted();
    }
}
