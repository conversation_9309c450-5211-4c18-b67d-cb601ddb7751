package com.ainirobot.navigationservice.utils;

import com.ainirobot.coreservice.client.Definition;

import static com.ainirobot.navigationservice.Defs.Def.ResultCode.FAIL_NO_REASON;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.SUCCESS;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.ERROR_INVALID_PROTO;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.ERROR_ORIGIN_NOT_CONNECT;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.ERROR_REMOTE_ERROR;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.ERROR_START_CREATE_MAP;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.ERROR_START_CREATE_MAP_GET_FEATURE_SPACE_FAILED;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.ERROR_START_CREATE_MAP_INVALID_MAP_NAME;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.ERROR_START_CREATE_MAP_NOT_IN_WORKING_MODE_CREATE_MAP;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.ERROR_START_CREATE_MAP_NO_FEATURE_SPACE;

public class ResponseUtils {
    
    public static String getResponseStrByCode(int resultCode) {
        switch (resultCode) {
            case SUCCESS:
                return Definition.SUCCEED;

            case FAIL_NO_REASON:
                return Definition.FAILED;

            case ERROR_ORIGIN_NOT_CONNECT:
                return Definition.ERROR_ORIGIN_NOT_CONNECT;

            case ERROR_REMOTE_ERROR:
                return Definition.ERROR_REMOTE_ERROR;

            case ERROR_INVALID_PROTO:
                return Definition.ERROR_INVALID_PROTO;

            case ERROR_START_CREATE_MAP:
                return Definition.ERROR_START_CREATE_MAP;

            case ERROR_START_CREATE_MAP_INVALID_MAP_NAME:
                return Definition.ERROR_START_CREATE_MAP_INVALID_MAP_NAME;

            case ERROR_START_CREATE_MAP_NOT_IN_WORKING_MODE_CREATE_MAP:
                return Definition.ERROR_START_CREATE_MAP_NOT_IN_WORKING_MODE_CREATE_MAP;

            case ERROR_START_CREATE_MAP_NO_FEATURE_SPACE:
                return Definition.ERROR_START_CREATE_MAP_NO_FEATURE_SPACE;

            case ERROR_START_CREATE_MAP_GET_FEATURE_SPACE_FAILED:
                return Definition.ERROR_START_CREATE_MAP_GET_FEATURE_SPACE_FAILED;

            default:
                return "";
        }
    }
}
