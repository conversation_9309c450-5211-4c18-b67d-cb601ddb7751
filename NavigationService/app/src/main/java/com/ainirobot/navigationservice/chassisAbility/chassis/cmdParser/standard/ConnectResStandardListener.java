package com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard;

import android.util.Log;

import com.ainirobot.navigationservice.beans.standard.LandMarkBean;
import com.ainirobot.navigationservice.beans.standard.MapContentBean;
import com.ainirobot.navigationservice.beans.standard.MapIdBean;
import com.ainirobot.navigationservice.beans.standard.MapInfo;
import com.ainirobot.navigationservice.beans.standard.PoseInfo;
import com.ainirobot.navigationservice.beans.standard.SensorStatus;
import com.ainirobot.navigationservice.chassisAbility.chassis.connector.standard.ConnectApi;
import com.ainirobot.navigationservice.protocol.bean.LandMarkBeanCreator;
import com.ainirobot.navigationservice.protocol.bean.MapBeanCreator;
import com.ainirobot.navigationservice.protocol.bean.MapIdBeanCreator;
import com.ainirobot.navigationservice.protocol.bean.PoseBeanCreator;
import com.ainirobot.navigationservice.protocol.bean.SensorBeanCreator;
import com.ainirobot.navigationservice.protocol.response.ResAddLandMarkCreator;
import com.ainirobot.navigationservice.protocol.response.ResChargePileRecognizeCreator;
import com.ainirobot.navigationservice.protocol.response.ResGetCurMapInfoCreator;
import com.ainirobot.navigationservice.protocol.response.ResGetLandMarkListCreator;
import com.ainirobot.navigationservice.protocol.response.ResGetMapInfoCreator;
import com.ainirobot.navigationservice.protocol.response.ResGetMapListCreator;
import com.ainirobot.navigationservice.protocol.response.ResHeadCreator;
import com.ainirobot.navigationservice.protocol.response.ResModifyLandmarkInfoCreator;
import com.ainirobot.navigationservice.protocol.response.ResPacketCreator;
import com.ainirobot.navigationservice.protocol.response.ResSaveMappingPoseCreator;
import com.ainirobot.navigationservice.protocol.response.ResStopCreateMapCreator;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;

import java.util.ArrayList;
import java.util.List;

import static com.ainirobot.navigationservice.Defs.Def.ResultCode.FAIL_NO_REASON;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.SUCCESS;
import static com.ainirobot.navigationservice.Defs.Def.StandardDef.CANCEL;
import static com.ainirobot.navigationservice.Defs.Def.StandardDef.COMPLETE;
import static com.ainirobot.navigationservice.Defs.Def.StandardDef.EXECUTION;
import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_ADD_LANDMARK;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_CHARGE_PILE_DOCKING;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_CHARGE_PILE_RECOGNIZE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_CHARGE_PILE_SEARCH;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_DELETE_LANDMARK;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_DELETE_MAP;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_FORCE_FORWARD_MOVE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_FORCE_ROTATION_MOVE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_GET_CUR_MAP_INFO;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_GET_LANDMARK_LIST;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_GET_LOCALIZATION_STATE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_GET_MAP_INFO;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_GET_MAP_LIST;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_GET_REALTIME_MAP;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_GET_SENSOR_STATUS;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_MODIFY_LANDMARK_INFO;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_SAVE_MAPPING_PATH;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_SAVE_MAPPING_POSE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_SET_CONTROL_MODE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_SET_FIX_POINT_MODE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_SET_MANUAL_MODE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_SET_NAVI_GOAL_POINT;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_SET_RELOCALIZATION;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_START_CREATE_MAP;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_STOP_CREATE_MAP;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_SWITCH_MAP;


public class ConnectResStandardListener implements ConnectApi.ResListener {
    private final static String TAG = TAGPRE + ConnectResStandardListener.class.getSimpleName();

    private ChassisCommandStandardImpl cmdApi;

    public ConnectResStandardListener(ChassisCommandStandardImpl cmdApi) {
        this.cmdApi = cmdApi;
    }

    @Override
    public void onResponse(Message message) {
        if (message instanceof ResPacketCreator.ResPacketProto) {
            Log.d(TAG, "onResponse in");
            ResPacketCreator.ResPacketProto resPacketProto = (ResPacketCreator.ResPacketProto) message;
            ResHeadCreator.ResHeadProto resHead = resPacketProto.getHeader();
            Log.d(TAG, "print resHead = " + generateResHeadStr(resHead));
            ByteString param = resPacketProto.getParam();
            CMDRequest cr = cmdApi.findRequestFromList(resHead.getReqId());
            if (cr == null) {
                Log.e(TAG, "no CMDRequest ReqId = " + resHead.getReqId());
                return;
            }
            Log.d(TAG, "cr Response cmd = " + cr.toString());
            switch (cr.cmdName) {
                case REQ_SET_MANUAL_MODE:
                case REQ_SET_FIX_POINT_MODE:
                case REQ_SET_CONTROL_MODE:
                case REQ_CHARGE_PILE_DOCKING:
                case REQ_FORCE_FORWARD_MOVE:
                case REQ_FORCE_ROTATION_MOVE:
                case REQ_SET_NAVI_GOAL_POINT:
                case REQ_SAVE_MAPPING_PATH:
                    processResponseWithStageNoParam(cr, resHead);
                    break;
                case REQ_CHARGE_PILE_RECOGNIZE:
                case REQ_CHARGE_PILE_SEARCH:
                case REQ_SAVE_MAPPING_POSE:
                case REQ_GET_SENSOR_STATUS:
                case REQ_SET_RELOCALIZATION:
                case REQ_GET_LOCALIZATION_STATE:
                    processResponseWithStageHasParam(cr, resHead, param);
                    break;

                case REQ_DELETE_MAP:
                case REQ_START_CREATE_MAP:
                case REQ_DELETE_LANDMARK:
                case REQ_SWITCH_MAP:
                    processResponseNoParam(cr, resHead);
                    break;
                case REQ_GET_MAP_INFO:
                case REQ_GET_LANDMARK_LIST:
                case REQ_MODIFY_LANDMARK_INFO:
                case REQ_ADD_LANDMARK:
                case REQ_GET_CUR_MAP_INFO:
                case REQ_GET_REALTIME_MAP:
                case REQ_GET_MAP_LIST:
                case REQ_STOP_CREATE_MAP:
                    processResponseHasParam(cr, resHead, param);
                    break;

                default:
                    break;
            }
        }
    }

    private synchronized void processResponseHasParam(CMDRequest cr, ResHeadCreator.ResHeadProto resHead
            , ByteString param) {
        if (SUCCESS == resHead.getResultCode()) {
            cr.getResponseListener().onResult(resHead.getResultCode(), resHead.getMsg(), generateParam(cr.cmdName, param));
        } else {
            cr.getResponseListener().onError(resHead.getResultCode(), resHead.getMsg());
        }
        cmdApi.releaseAndRemoveCr(cr);
        cmdApi.removeCmdReqIdMap(cr);
    }

    private Object generateParam(String cmdName, ByteString param) {

        try {
            switch (cmdName) {
                case REQ_GET_MAP_INFO:
                    ResGetMapInfoCreator.ParamMapInfo mapInfo = ResGetMapInfoCreator.ParamMapInfo.parseFrom(param);
                    MapIdBeanCreator.MapIdBeanProto idBean = mapInfo.getMapIdInfo();
                    List<LandMarkBeanCreator.LandMarkBeanProto> landMarks = mapInfo.getLandMarkListList();
                    MapBeanCreator.MapBeanProto mapContent = mapInfo.getMapContent();
                    ArrayList<LandMarkBean> landMarkBeans = new ArrayList<>();
                    for (LandMarkBeanCreator.LandMarkBeanProto item : landMarks) {
                        landMarkBeans.add(new LandMarkBean(item.getName(), item.getId()
                                , new PoseInfo(item.getPose().getX(), item.getPose().getY()
                                , item.getPose().getTheta())));
                    }

                    return new MapInfo(new MapIdBean(idBean.getMapName(), idBean.getMapId()), landMarkBeans
                            , new MapContentBean(mapContent.getResolution(), mapContent.getOriginX()
                            , mapContent.getOriginY(), mapContent.getWidth(), mapContent.getHeight()
                            , mapContent.getData().toByteArray()));

                case REQ_GET_LANDMARK_LIST:
                    ResGetLandMarkListCreator.ParamGetLandMark paramGetLandMark = ResGetLandMarkListCreator
                            .ParamGetLandMark.parseFrom(param);
                    List<LandMarkBeanCreator.LandMarkBeanProto> landMarkList = paramGetLandMark.getLandMarkListList();
                    ArrayList<LandMarkBean> getLandMarks = new ArrayList<>();
                    for (LandMarkBeanCreator.LandMarkBeanProto item : landMarkList) {
                        getLandMarks.add(new LandMarkBean(item.getName(), item.getId()
                                , new PoseInfo(item.getPose().getX(), item.getPose().getY()
                                , item.getPose().getTheta())));
                    }
                    return getLandMarks;

                case REQ_MODIFY_LANDMARK_INFO:
                    ResModifyLandmarkInfoCreator.ParamModifyLandMark paramModifyLandMark =
                            ResModifyLandmarkInfoCreator.ParamModifyLandMark.parseFrom(param);
                    List<LandMarkBeanCreator.LandMarkBeanProto> modifyLandMarkList = paramModifyLandMark.getLandMarkListList();
                    ArrayList<LandMarkBean> modifyLandMarks = new ArrayList<>();
                    for (LandMarkBeanCreator.LandMarkBeanProto item : modifyLandMarkList) {
                        modifyLandMarks.add(new LandMarkBean(item.getName(), item.getId()
                                , new PoseInfo(item.getPose().getX(), item.getPose().getY()
                                , item.getPose().getTheta())));
                    }
                    return modifyLandMarks;


                case REQ_ADD_LANDMARK:
                    ResAddLandMarkCreator.ParamAddLandMark paramAddLandMark =
                            ResAddLandMarkCreator.ParamAddLandMark.parseFrom(param);
                    List<LandMarkBeanCreator.LandMarkBeanProto> addLandMarkList = paramAddLandMark.getLandMarkListList();
                    ArrayList<LandMarkBean> addLandMarks = new ArrayList<>();
                    for (LandMarkBeanCreator.LandMarkBeanProto item : addLandMarkList) {
                        addLandMarks.add(new LandMarkBean(item.getName(), item.getId()
                                , new PoseInfo(item.getPose().getX(), item.getPose().getY()
                                , item.getPose().getTheta())));
                    }
                    return addLandMarks;

                case REQ_GET_CUR_MAP_INFO:
                    ResGetCurMapInfoCreator.ParamCurMapInfo curMapInfo =
                            ResGetCurMapInfoCreator.ParamCurMapInfo.parseFrom(param);
                    MapIdBeanCreator.MapIdBeanProto curIdBean = curMapInfo.getMapIdInfo();
                    List<LandMarkBeanCreator.LandMarkBeanProto> curLandMarks = curMapInfo.getLandMarkListList();
                    MapBeanCreator.MapBeanProto curMapContent = curMapInfo.getMapContent();
                    ArrayList<LandMarkBean> curLandMarkList = new ArrayList<>();
                    for (LandMarkBeanCreator.LandMarkBeanProto item : curLandMarks) {
                        curLandMarkList.add(new LandMarkBean(item.getName(), item.getId()
                                , new PoseInfo(item.getPose().getX(), item.getPose().getY()
                                , item.getPose().getTheta())));
                    }

                    return new MapInfo(new MapIdBean(curIdBean.getMapName(), curIdBean.getMapId()), curLandMarkList
                            , new MapContentBean(curMapContent.getResolution(), curMapContent.getOriginX()
                            , curMapContent.getOriginY(), curMapContent.getWidth(), curMapContent.getHeight()
                            , curMapContent.getData().toByteArray()));

                case REQ_GET_REALTIME_MAP:
                    return param.toByteArray();
                case REQ_GET_MAP_LIST:
                    ResGetMapListCreator.ParamMapList mapList = ResGetMapListCreator.ParamMapList.parseFrom(param);
                    List<MapIdBeanCreator.MapIdBeanProto> mapIds = mapList.getMapIdInfoList();
                    ArrayList<MapIdBean> mapIdBeans = new ArrayList<>();
                    for (MapIdBeanCreator.MapIdBeanProto item : mapIds) {
                        mapIdBeans.add(new MapIdBean(item.getMapName(), item.getMapId()));
                    }
                    return mapIdBeans;
                case REQ_STOP_CREATE_MAP:
                    ResStopCreateMapCreator.ParamStopCreateMap stopMapInfo =
                            ResStopCreateMapCreator.ParamStopCreateMap.parseFrom(param);
                    MapIdBeanCreator.MapIdBeanProto stopIdBean = stopMapInfo.getMapIdInfo();
                    List<LandMarkBeanCreator.LandMarkBeanProto> stopLandMarks = stopMapInfo.getLandMarkListList();
                    MapBeanCreator.MapBeanProto stopMapContent = stopMapInfo.getMapContent();
                    ArrayList<LandMarkBean> stopLandMarkList = new ArrayList<>();
                    for (LandMarkBeanCreator.LandMarkBeanProto item : stopLandMarks) {
                        stopLandMarkList.add(new LandMarkBean(item.getName(), item.getId()
                                , new PoseInfo(item.getPose().getX(), item.getPose().getY()
                                , item.getPose().getTheta())));
                    }

                    return new MapInfo(new MapIdBean(stopIdBean.getMapName(), stopIdBean.getMapId()), stopLandMarkList
                            , new MapContentBean(stopMapContent.getResolution(), stopMapContent.getOriginX()
                            , stopMapContent.getOriginY(), stopMapContent.getWidth(), stopMapContent.getHeight()
                            , stopMapContent.getData().toByteArray()));

            }
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }

        return null;
    }


    private synchronized void processResponseNoParam(CMDRequest cr, ResHeadCreator.ResHeadProto resHead) {
        if(SUCCESS == resHead.getResultCode()) {
            cr.getResponseListener().onResult(resHead.getResultCode(), resHead.getMsg(), null);
        } else {
            cr.getResponseListener().onError(resHead.getResultCode(), resHead.getMsg());
        }
        cmdApi.releaseAndRemoveCr(cr);
        cmdApi.removeCmdReqIdMap(cr);
    }

    private synchronized void processResponseWithStageHasParam(CMDRequest cr, ResHeadCreator.ResHeadProto resHead
            , ByteString param) {
        if (FAIL_NO_REASON == resHead.getResultCode()) {
            cr.getResponseListener().onError(resHead.getResultCode(), resHead.getMsg());
            cmdApi.releaseAndRemoveCr(cr);
            cmdApi.removeCmdReqIdMap(cr);
        } else if (SUCCESS == resHead.getResultCode()) {
            if (cr.getType() == EXECUTION) {
                if (resHead.getStage() == COMPLETE) {
                    cr.getResponseListener().onResult(resHead.getResultCode(), resHead.getMsg(),
                            generateParamWithStage(cr.cmdName, param));
                    cmdApi.releaseAndRemoveCr(cr);
                    cmdApi.removeCmdReqIdMap(cr);
                } else {
                    cr.getResponseListener().onStatusUpdate(resHead.getStage(), null);
                }
            } else if (cr.getType() == CANCEL) {
                if (resHead.getStage() == COMPLETE) {
                    cr.getResponseListener().onResult(resHead.getResultCode(), resHead.getMsg(), null);
                    cmdApi.releaseAndRemoveCr(cr);
                    //找到对应的取消的任务cr，release掉
                    CMDRequest crPairedTask = cmdApi.findRequestFromList(cr.getPairedReqId());
                    cmdApi.releaseAndRemoveCr(crPairedTask);
                    cmdApi.removeCmdReqIdMap(crPairedTask);
                } else {
                    cr.getResponseListener().onStatusUpdate(resHead.getStage(), null);
                }
            }
        }
    }

    private Object generateParamWithStage(String cmdName, ByteString param) {

        try {
            switch (cmdName) {
                case REQ_CHARGE_PILE_RECOGNIZE:
                    ResChargePileRecognizeCreator.ParamChargePileRecognize serialParam = ResChargePileRecognizeCreator.ParamChargePileRecognize.parseFrom(param);
                    return serialParam.getSerial();
                case REQ_CHARGE_PILE_SEARCH:
                    PoseBeanCreator.PoseBeanProto poseBean = PoseBeanCreator.PoseBeanProto.parseFrom(param);
                    return new PoseInfo(poseBean.getX(), poseBean.getY(), poseBean.getTheta());
                case REQ_SAVE_MAPPING_POSE:
                    ResSaveMappingPoseCreator.ParamResMappingPose paramResMappingPose = ResSaveMappingPoseCreator.ParamResMappingPose.parseFrom(param);
                    return paramResMappingPose.getPoseIndex();
                case REQ_GET_SENSOR_STATUS:
                    SensorBeanCreator.SensorBeanProto sensor = SensorBeanCreator.SensorBeanProto.parseFrom(param);
                    return new SensorStatus(sensor.getRadarSensor(), sensor.getOdom(),
                            sensor.getInfraredCamera(), sensor.getBackMonoImage(),
                            sensor.getBinocularImage(), sensor.getMiniSRgbd(), sensor.getMiniRgbd(), sensor.getUltraSound());
            }
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
        return null;
    }


    //TODO 回调里不要做耗时操作，可能阻塞处理
    private synchronized void processResponseWithStageNoParam(CMDRequest cr, ResHeadCreator.ResHeadProto resHead) {
        if (FAIL_NO_REASON == resHead.getResultCode()) {
            cr.getResponseListener().onError(resHead.getResultCode(), resHead.getMsg());
            cmdApi.releaseAndRemoveCr(cr);
            cmdApi.removeCmdReqIdMap(cr);
        } else if (SUCCESS == resHead.getResultCode()) {
            if (cr.getType() == EXECUTION) {
                if (resHead.getStage() == COMPLETE) {
                    cr.getResponseListener().onResult(resHead.getResultCode(), resHead.getMsg(), null);
                    cmdApi.releaseAndRemoveCr(cr);
                    cmdApi.removeCmdReqIdMap(cr);
                } else {
                    cr.getResponseListener().onStatusUpdate(resHead.getStage(), null);
                }
            } else if (cr.getType() == CANCEL) {
                cr.getResponseListener().onResult(resHead.getResultCode(), resHead.getMsg(), null);
                cmdApi.releaseAndRemoveCr(cr);
                //找到对应的取消的任务cr，release掉
                CMDRequest crPairedTask = cmdApi.findRequestFromList(cr.getPairedReqId());
                cmdApi.releaseAndRemoveCr(crPairedTask);
                cmdApi.removeCmdReqIdMap(crPairedTask);
            }
        }
    }

    private String generateResHeadStr(ResHeadCreator.ResHeadProto resHeadProto) {
        StringBuffer sb = new StringBuffer();
        sb.append("command = ");
        sb.append(resHeadProto.getCommand());
        sb.append("\nreqId = ");
        sb.append(resHeadProto.getReqId());
        sb.append("\nresultCode = ");
        sb.append(resHeadProto.getResultCode());
        sb.append("\nmsg = ");
        sb.append(resHeadProto.getMsg());
        sb.append("\nstage = ");
        sb.append(resHeadProto.getStage());
        sb.append("\ntimeStamp = ");
        sb.append(resHeadProto.getTimeStamp());
        return sb.toString();
    }
}
