package com.ainirobot.navigationservice.chassisAbility;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.hardware.RobotCore;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.navigationservice.beans.tk1.Pose;

import org.json.JSONException;
import org.json.JSONObject;

public class MapOutsideManager {
    private static final long DELAY_TIME = 5 * 1000;
    private static final int MAP_OUTSIDE_WARNING_OPEN = 1;
    private static final int BEING_PUSHED_WARNING_OPEN = 1;
    private final String TAG = "MapOutsideManager";

    private static final String PRODUCT_MODEL = RobotSettings.getProductModel();

    private enum Status {
        INSIDE_MAP("inside_map"),
        OUTSIDE_MAP("outside_map"),
        NONE("none"),
        CREATING_MAP("creating_map"),
        ;
        private String name;

        Status(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    private final int CLEAR_PUSHING_STATUS = 12;
    private final int SEND_MAP_OUTSIDE = 13;
    private final int MSG_CHECK_PERSISTENT_BEING_PUSHED = 14;
    private final int MSG_PERSISTENT_BEING_PUSHED_TIME_OUT = 15;

    private Handler mHandler;

    private static MapOutsideManager mInstance;

    /**
     * 实时推动状态，时间误差 100ms
     */
    private volatile boolean isBeingPush = false;

    private Status mCurrentStatus = Status.NONE;

    /**
     * 机器被持续推动时长限制，默认 0 秒
     */
    private static final long TIME_BEING_PUSHED_LIMIT = 0;

    /**
     * 被推动状态释放时长限制
     */
    private static final long TIME_BEING_PUSHED_RELEASE_LIMIT = 1 * 1000;

    /**
     * 机器是否处于被持续推动状态(在地图内)
     */
    private volatile boolean mBeingPushedPersistent = false;

    /**
     * 推动警告开关是否打开
     */
    private static boolean mBeingPushedWarningOpened = false;

    /**
     * 地图外警告开关是否打开
     */
    private static boolean mOutMapWarningOpened = false;

    private MapOutsideManager() {
        initOutMapWarningSetting();
        initBeingPushedWarningSetting();
        HandlerThread handlerThread = new HandlerThread(TAG);
        handlerThread.start();
        mHandler = new Handler(handlerThread.getLooper()) {
            @Override
            public void handleMessage(Message msg) {
                switch (msg.what) {
                    case CLEAR_PUSHING_STATUS:
                        isBeingPush = false;
                        break;
                    case SEND_MAP_OUTSIDE:
                        Log.d(TAG, "is allow map outside warning report " + mOutMapWarningOpened);
                        if (!mOutMapWarningOpened) {
                            return;
                        }
                        try {
                            JSONObject param = new JSONObject();
                            param.put("status", Definition.MAP_OUTSIDE);

                            sendStatusReport(Definition.STATUS_MAP_OUTSIDE, param.toString());
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        break;
                    case MSG_CHECK_PERSISTENT_BEING_PUSHED:
                        Log.d(TAG, "handleMessage MSG_CHECK_PERSISTENT_BEING_PUSHED:" +
                                " mCurrentStatus=" + mCurrentStatus +
                                " mBeingPushedPersistent=" + mBeingPushedPersistent);
                        if (!mBeingPushedPersistent &&
                                (mCurrentStatus == Status.INSIDE_MAP ||
                                        mCurrentStatus == Status.OUTSIDE_MAP)) {
                            mBeingPushedPersistent = true;
                            mHandler.sendEmptyMessageDelayed(MSG_PERSISTENT_BEING_PUSHED_TIME_OUT,
                                    TIME_BEING_PUSHED_RELEASE_LIMIT);
                            try {
                                JSONObject param = new JSONObject();
                                param.put("status", Definition.BEING_PUSHED);
                                Log.d(TAG, "handleMessage Send being pushed status!");
                                sendStatusReport(Definition.STATUS_ROBOT_BEING_PUSHED, param.toString());
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                        }
                        break;
                    case MSG_PERSISTENT_BEING_PUSHED_TIME_OUT:
                        Log.d(TAG, "handleMessage MSG_PERSISTENT_BEING_PUSHED_TIME_OUT:" +
                                " mBeingPushedPersistent=" + mBeingPushedPersistent);
                        if (mBeingPushedPersistent) {
                            mBeingPushedPersistent = false;
                            try {
                                JSONObject param = new JSONObject();
                                param.put("status", Definition.NOT_BEING_PUSHED);
                                Log.d(TAG, "handleMessage Send not being pushed status!");
                                sendStatusReport(Definition.STATUS_ROBOT_BEING_PUSHED, param.toString());
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                        }
                        break;
                    default:
                        break;
                }
            }
        };
    }

    private void initBeingPushedWarningSetting() {
        int setting = RobotSettingApi.getInstance()
                .getRobotInt(Definition.ROBOT_SETTING_BEING_PUSHED_WARNING);
        Log.d(TAG, "initBeingPushedWarningSetting: setting=" + setting);
        mBeingPushedWarningOpened = (setting == BEING_PUSHED_WARNING_OPEN);
    }

    private void initOutMapWarningSetting() {
        int setting = RobotSettingApi.getInstance()
                .getRobotInt(Definition.ROBOT_SETTING_MAP_OUTSIDE_WARNING);
        Log.d(TAG, "initOutMapWarningSetting: setting=" + setting);
        mOutMapWarningOpened = (setting == MAP_OUTSIDE_WARNING_OPEN);
    }

    public static synchronized MapOutsideManager getInstance() {
        if (mInstance == null) {
            mInstance = new MapOutsideManager();
        }
        return mInstance;
    }

    public void onPoseUpdate(Pose pose) {
        if (mCurrentStatus == Status.CREATING_MAP) {
            return;
        }

        switch (pose.getStatus()) {
            case Pose.MAP_STATUS_NORMAL_AREA:
                if (mCurrentStatus == Status.INSIDE_MAP) {
                    return;
                }

                Log.d(TAG, "mCurrentStatus from " + mCurrentStatus.getName() + " to " + Status.INSIDE_MAP.getName());

                if (mHandler.hasMessages(SEND_MAP_OUTSIDE)) {
                    mHandler.removeMessages(SEND_MAP_OUTSIDE);
                }

                try {
                    JSONObject param = new JSONObject();
                    param.put("status", Definition.MAP_INSIDE);
                    sendStatusReport(Definition.STATUS_MAP_OUTSIDE, param.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                }

                mCurrentStatus = Status.INSIDE_MAP;
                break;
            case Pose.MAP_STATUS_OUTSIDE_AREA:
                if (!isBeingPush || mCurrentStatus == Status.OUTSIDE_MAP) {
                    return;
                }

                Log.d(TAG, "mCurrentStatus from " + mCurrentStatus.getName() + " to " + Status.OUTSIDE_MAP.getName());

                mHandler.sendEmptyMessageDelayed(SEND_MAP_OUTSIDE, DELAY_TIME);
                mCurrentStatus = Status.OUTSIDE_MAP;
                break;
        }
    }

    public void onPushEventReport(boolean poseEstimate) {
        mHandler.removeMessages(CLEAR_PUSHING_STATUS);
        isBeingPush = true;
        mHandler.sendEmptyMessageDelayed(CLEAR_PUSHING_STATUS, 100);
        if (poseEstimate) {
            checkPersistentBeingPushedStatus();
        }
    }

    /**
     * 持续被推动状态检测
     */
    private void checkPersistentBeingPushedStatus() {
        if (!ProductInfo.isMiniProduct() && !ProductInfo.isMeissa2()) {
            return;
        }
        if (!mBeingPushedWarningOpened) {
            return;
        }
        if (mCurrentStatus != Status.INSIDE_MAP && mCurrentStatus != Status.OUTSIDE_MAP) {
            return;
        }

        if (!mBeingPushedPersistent) {
            if (!mHandler.hasMessages(MSG_CHECK_PERSISTENT_BEING_PUSHED)) {
                mHandler.sendEmptyMessageDelayed(MSG_CHECK_PERSISTENT_BEING_PUSHED,
                        TIME_BEING_PUSHED_LIMIT);
            }
        } else {
            mHandler.removeMessages(MSG_PERSISTENT_BEING_PUSHED_TIME_OUT);
            mHandler.sendEmptyMessageDelayed(MSG_PERSISTENT_BEING_PUSHED_TIME_OUT,
                    TIME_BEING_PUSHED_RELEASE_LIMIT);
        }
    }

    public void onMapCreate(boolean isCreating) {
        if (isCreating == (mCurrentStatus == Status.CREATING_MAP)) {
            return;
        }

        if (isCreating) {
            mCurrentStatus = Status.CREATING_MAP;
            mHandler.removeMessages(SEND_MAP_OUTSIDE);
        } else {
            mCurrentStatus = Status.NONE;
        }
        Log.d(TAG, "mCurrentStatus is " + mCurrentStatus.getName());
    }

    private void sendStatusReport(String type, String data) {
        if (ProductInfo.isMiniProduct() || ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
            RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, type, data);
        }
    }

    public void onMapOutsideWarningChanged(int value) {
        Log.d(TAG, "onMapOutsideWarningChanged value=" + value
                + " mCurrentStatus=" + mCurrentStatus);
        if (value == MAP_OUTSIDE_WARNING_OPEN && mCurrentStatus == Status.OUTSIDE_MAP) {
            mHandler.removeMessages(SEND_MAP_OUTSIDE);
            mHandler.sendEmptyMessageDelayed(SEND_MAP_OUTSIDE, DELAY_TIME);
        } else if (value != MAP_OUTSIDE_WARNING_OPEN && mCurrentStatus == Status.OUTSIDE_MAP) {
            try {
                JSONObject param = new JSONObject();
                param.put("status", Definition.MAP_INSIDE);
                sendStatusReport(Definition.STATUS_MAP_OUTSIDE, param.toString());
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        mOutMapWarningOpened = (value == MAP_OUTSIDE_WARNING_OPEN);
    }

    public void onBeingPushedWarningChanged(int value) {
        Log.d(TAG, "onBeingPushedWarningChanged value = " + value);
        if (mBeingPushedWarningOpened && value != BEING_PUSHED_WARNING_OPEN) {
            mHandler.removeMessages(MSG_PERSISTENT_BEING_PUSHED_TIME_OUT);
            mHandler.sendEmptyMessageDelayed(MSG_PERSISTENT_BEING_PUSHED_TIME_OUT,
                    TIME_BEING_PUSHED_RELEASE_LIMIT);
        }
        mBeingPushedWarningOpened = (value == BEING_PUSHED_WARNING_OPEN);
    }

}
