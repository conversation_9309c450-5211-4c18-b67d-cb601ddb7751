package com.ainirobot.navigationservice.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

public class SharedPrefUtil {

    private static SharedPreferences getSharedPreferences(Context context) {
        return context.getSharedPreferences("navigation_config", Context.MODE_PRIVATE);
    }

    public static void putString(Context context, String key, String value) {
        if (TextUtils.isEmpty(key)) {
            return;
        }
        SharedPreferences.Editor editor = getSharedPreferences(context).edit();
        editor.putString(key, value).apply();
    }

    public static String getString(Context context, String key, String defaultValue) {
        if (TextUtils.isEmpty(key)) {
            return "";
        }
        return getSharedPreferences(context).getString(key, defaultValue);
    }

    public static void putInt(Context context, String key, int value) {
        if (TextUtils.isEmpty(key)) {
            return;
        }
        SharedPreferences.Editor editor = getSharedPreferences(context).edit();
        editor.putInt(key, value).apply();
    }

    public static int getInt(Context context, String key, int defaultValue) {
        if (TextUtils.isEmpty(key)) {
            return 0;
        }
        return getSharedPreferences(context).getInt(key, defaultValue);
    }

    public static void putBoolean(Context context, String key, boolean value) {
        if (TextUtils.isEmpty(key)) {
            return;
        }
        SharedPreferences.Editor editor = getSharedPreferences(context).edit();
        editor.putBoolean(key, value).apply();
    }

    public static boolean getBoolean(Context context, String key, boolean defaultValue) {
        if (TextUtils.isEmpty(key)) {
            return false;
        }
        return getSharedPreferences(context).getBoolean(key, defaultValue);
    }

    public static void remove(Context context, String key) {
        if (TextUtils.isEmpty(key)) {
            return;
        }
        SharedPreferences.Editor editor = getSharedPreferences(context).edit();
        editor.remove(key).apply();
    }
}
