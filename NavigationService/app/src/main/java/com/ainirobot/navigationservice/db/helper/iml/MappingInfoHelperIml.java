package com.ainirobot.navigationservice.db.helper.iml;

import com.ainirobot.navigationservice.db.entity.MappingInfo;

import java.util.List;

public interface MappingInfoHelperIml extends BaseHelper<MappingInfo> {
    List<MappingInfo> getMappingInfoByMapName(String[] mapNames);

    void deleteMappingInfo(String mapName);

    void initMappingData(List<MappingInfo> mappingList);

    boolean updateMappingInfo(MappingInfo mappingInfo);

    List<MappingInfo> getMappingInfoByMapName(String mapName);
}
