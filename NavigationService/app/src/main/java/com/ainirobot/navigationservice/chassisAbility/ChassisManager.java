package com.ainirobot.navigationservice.chassisAbility;

import android.content.Context;

import com.ainirobot.navigationservice.Defs.Def;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.tk1.ChassisClientTk1Impl;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.waiter.ChassisClientWaiterImpl;
import com.ainirobot.navigationservice.chassisAbility.ota.client.IOtaClient;
import com.ainirobot.navigationservice.chassisAbility.ota.client.tk1.OtaClientTk1Impl;
import com.ainirobot.navigationservice.chassisAbility.ota.client.waiter.OtaClientWaiterImpl;
import com.ainirobot.navigationservice.chassisAbility.ota.client.x86.OtaClientX86Impl;
import com.ainirobot.navigationservice.commonModule.configuration.ConfigManager;

public class ChassisManager {
    private static ChassisManager mInstance;
    private IChassisClient chassisClient;
    private IOtaClient otaClient;
    private ChassisManager() {

    }

    public static synchronized ChassisManager getInstance() {
        if (mInstance == null) {
            mInstance = new ChassisManager();
        }
        return mInstance;
    }

    public void init(Context context) {
        switch(getChassisType()) {
            case Def.CLIENT_TK1:
                chassisClient = new ChassisClientTk1Impl();
                chassisClient.init(context);
                otaClient = new OtaClientTk1Impl();
                otaClient.init(context);
                break;
            case Def.CLIENT_X86:
//                chassisClient = new ChassisClientX86Impl();
                chassisClient = new ChassisClientWaiterImpl();
                chassisClient.init(context);
                otaClient = new OtaClientX86Impl();
                otaClient.init(context);
                break;
            case Def.CLIENT_WAITER:
                chassisClient = new ChassisClientWaiterImpl();
                chassisClient.init(context);
                otaClient = new OtaClientWaiterImpl();
                otaClient.init(context);
                break;
                default:
                    break;
        }
    }

    /**
     * 获取硬件信息
     * @return
     */
    private String getChassisType() {
            return ConfigManager.getInstance().getDeviceType();
    }

    /**
     * 获取硬件设备号
     *
     * @return
     */
    private int getChassisTypeNumber() {
        return ConfigManager.getInstance().getSubDeviceTypeNumber();
    }

    public IChassisClient getChassisClient() {
        if (chassisClient != null) {
            return chassisClient;
        } else {
            throw new RuntimeException("no chassisClient available");
        }
    }

    public IOtaClient getOtaClient() {
        if (otaClient != null) {
            return otaClient;
        } else {
            throw new RuntimeException("no otaClient available");
        }
    }
}
