package com.ainirobot.navigationservice.db.helper.objectbox;

import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.navigationservice.db.entity.PlaceInfo;
import com.ainirobot.navigationservice.db.entity.PlaceInfo_;
import com.ainirobot.navigationservice.db.helper.iml.PlaceInfoHelperIml;
import com.ainirobot.navigationservice.db.entity.PlaceName;
import com.ainirobot.navigationservice.db.entity.PlaceType;

import java.util.List;

import io.objectbox.Box;
import io.objectbox.BoxStore;
import io.objectbox.query.Query;
import io.objectbox.query.QueryBuilder;

public class PlaceInfoObjectHelper extends BaseObjectHelper<PlaceInfo> implements PlaceInfoHelperIml {
    public PlaceInfoObjectHelper(BoxStore boxStore) {
        super(boxStore);
    }

    @Nullable
    @Override
    public PlaceInfo getPlaceById(String placeId) {
        Query<PlaceInfo> placeInfoQuery = getBox()
                .query(PlaceInfo_.placeId.equal(placeId))
                .build();
        PlaceInfo placeInfo = placeInfoQuery.findFirst();
        placeInfoQuery.close();
        return placeInfo;
    }

    @Override
    public List<PlaceInfo> getPlaceInfoByMapName(String mapName) {
        long startTime = System.currentTimeMillis();
        Query<PlaceInfo> infoQuery = getPlaceInfoQuery(mapName);
        List<PlaceInfo> placeInfoList = infoQuery.find();
        Log.i(TAG, "获取PlaceInfo花费" + (System.currentTimeMillis() - startTime) + "毫秒");
        infoQuery.close();
        return placeInfoList;
    }

    @Override
    public List<PlaceInfo> getPlaceInfoByMapName(String[] mapNames) {
        long startTime = System.currentTimeMillis();
        Query<PlaceInfo> infoQuery = getBox()
                .query(PlaceInfo_.mapName.oneOf(mapNames)).build();
        List<PlaceInfo> placeInfoList = infoQuery.find();
        Log.i(TAG, "获取PlaceInfo花费" + (System.currentTimeMillis() - startTime) + "毫秒");
        infoQuery.close();
        return placeInfoList;
    }

    @Override
    public String[] getPlaceIdByMapName(String mapName) {
        Query<PlaceInfo> infoQuery = getPlaceInfoQuery(mapName);
        String[] placeIds = infoQuery
                .property(PlaceInfo_.placeId)
                .findStrings();
        infoQuery.close();
        return placeIds;
    }

    @NonNull
    @Override
    public List<PlaceInfo> getPlaceInfos(String mapName, String[] placeIdsInName) {
        QueryBuilder<PlaceInfo> queryBuilder = getBox().query();
        if (!TextUtils.isEmpty(mapName)) {
            queryBuilder.equal(PlaceInfo_.mapName, mapName, QueryBuilder.StringOrder.CASE_SENSITIVE);
        }
        if (null != placeIdsInName) {
            queryBuilder.in(PlaceInfo_.placeId, placeIdsInName, QueryBuilder.StringOrder.CASE_SENSITIVE);
        }
        Query<PlaceInfo> placeInfoQuery = queryBuilder.build();

        List<PlaceInfo> placeInfos = placeInfoQuery.find();
        placeInfoQuery.close();
        return placeInfos;
    }


    /**
     * 查找对应typeId的特殊点位，按照优先级升序获取
     */
    @Override
    public List<PlaceInfo> getPlacesByType(int typeId, String mapName) {
        Box<PlaceInfo> placeInfoBox = getBox();
        Query<PlaceInfo> placeInfoQuery = placeInfoBox
                .query(PlaceInfo_.typeId.equal(typeId)
                        .and(PlaceInfo_.mapName.equal(mapName)))
                .order(PlaceInfo_.priority)
                .build();
        List<PlaceInfo> placeInfoList = placeInfoQuery.find();
        placeInfoQuery.close();
        return placeInfoList;
    }

    /**
     * 根据多个typeId查找对应的特殊点位
     */
    @Override
    public List<PlaceInfo> getPlacesByTypeArr(int[] typeIdArr, String mapName) {
        Box<PlaceInfo> placeInfoBox = getBox();
        Query<PlaceInfo> placeInfoQuery = placeInfoBox
                .query().in(PlaceInfo_.typeId, typeIdArr)
                .build();
        List<PlaceInfo> placeInfoList = placeInfoQuery.find();
        placeInfoQuery.close();
        return placeInfoList;
    }

    /**
     * 查找对应的typeId和priority
     * 若没找到直接找优先级最高的
     * 暂时没被调用
     */
    @Override
    public PlaceInfo getPlaceByType(int typeId, int priority, String mapName) {
        Query<PlaceInfo> placeInfoQuery = getBox()
                .query(PlaceInfo_.typeId.equal(typeId)
                        .and(PlaceInfo_.mapName.equal(mapName))
                        .and(PlaceInfo_.priority.equal(priority)))
                .build();
        List<PlaceInfo> placeInfo = placeInfoQuery.find();
        // 找到了符合当前条件的特殊地点, 有且仅有一个符合条件
        if (placeInfo.size() == 1) {
            placeInfoQuery.close();
            return placeInfo.get(0);
        }
        // 没找到符合传入的priority，就查询优先级最高的0
        placeInfoQuery = getBox()
                .query(PlaceInfo_.typeId.equal(typeId)
                        .and(PlaceInfo_.priority.equal(0)))
                .build();
        List<PlaceInfo> placeInfos = placeInfoQuery.find();
        if (placeInfos.size() == 1) {
            placeInfoQuery.close();
            return placeInfos.get(0);
        }
        // 当前typeId下的特殊点位没有设置优先级，返回null
        placeInfoQuery.close();
        return null;
    }

    @Override
    public PlaceInfo getPlaceByTypeIdAndPriority(int typeId, int priority, String mapName) {
        if (mapName == null) {
            return null;
        }
        Query<PlaceInfo> placeInfoQuery = getBox()
                .query(PlaceInfo_.mapName.equal(mapName)
                        .and(PlaceInfo_.typeId.equal(typeId))
                        .and(PlaceInfo_.priority.equal(priority)))
                .build();
        PlaceInfo placeInfo = placeInfoQuery.findFirst();
        placeInfoQuery.close();
        return placeInfo;
    }

    public String[] getStrings(String[] placeIds, String mapName) {
        Query<PlaceInfo> infoQuery = getBox()
                .query(PlaceInfo_.placeId.oneOf(placeIds))
                .equal(PlaceInfo_.mapName, mapName, QueryBuilder.StringOrder.CASE_SENSITIVE)
                .build();
        String[] placeIdArr = infoQuery.property(PlaceInfo_.placeId).findStrings();
        infoQuery.close();
        return placeIdArr;
    }

    @Override
    public List<PlaceInfo> getPlaceInfoNonHighPriority(String mapName) {
        Box<PlaceInfo> placeInfoBox = getBox();
        Query<PlaceInfo> placeInfoQuery = placeInfoBox
            .query(
                PlaceInfo_.mapName.equal(mapName)
                .and(
                    PlaceInfo_.typeId.equal(Definition.NORMAL_POINT_TYPE)
                    .or(
                        PlaceInfo_.priority.notEqual(Definition.SPECIAL_PLACE_HIGH_PRIORITY)
                        .and(PlaceInfo_.typeId.notEqual(Definition.NORMAL_POINT_TYPE))
                    )
                )
            )
            .build();
        List<PlaceInfo> placeInfoList = placeInfoQuery.find();
        placeInfoQuery.close();
        return placeInfoList;
    }

    @Override
    public List<PlaceInfo> getPlaceInfoHighPriority(String mapName) {
        Box<PlaceInfo> placeInfoBox = getBox();
        Query<PlaceInfo> placeInfoQuery = placeInfoBox
                .query(PlaceInfo_.mapName.equal(mapName)
                        .and(PlaceInfo_.typeId.between(Definition.CHARGING_POINT_TYPE, Definition.GATE_OUTER_TYPE))
                        .and(PlaceInfo_.priority.equal(Definition.SPECIAL_PLACE_HIGH_PRIORITY)))
                .build();
        List<PlaceInfo> placeInfoList = placeInfoQuery.find();
        placeInfoQuery.close();
        return placeInfoList;
    }

    @Override
    public void updateTimeOneOfIdArr(String[] idArr) {
        Box<PlaceInfo> placeInfoBox = getBox();
        Query<PlaceInfo> infoQuery = placeInfoBox.query(PlaceInfo_.placeId.oneOf(idArr)).build();
        List<PlaceInfo> placeInfos = infoQuery.find();
        infoQuery.close();
        long updateTime = System.currentTimeMillis();
        for (PlaceInfo placeInfo : placeInfos) {
            placeInfo.setUpdateTime(updateTime);
        }
        placeInfoBox.put(placeInfos);
    }

    @Override
    public boolean updatePlaceInfo(List<PlaceInfo> placeList) {
        Box<PlaceInfo> placeInfoBox = getBox();
        placeInfoBox.put(placeList);
        return true;
    }

    @Override
    public void updatePlaceInfo(PlaceInfo placeInfo) {
        Box<PlaceInfo> placeInfoBox = getBox();
        placeInfoBox.put(placeInfo);
    }

    @Override
    public void initPlaceInfoData(List<PlaceInfo> placeList) {
        if (null == placeList || placeList.isEmpty()) {
            Log.d(TAG, "initPlaceInfoData: list is null");
            return;
        }
        Log.d(TAG, "initPlaceInfoData: start");
        Box<PlaceInfo> placeInfoBox = getBox();
        placeInfoBox.removeAll();
        placeInfoBox.put(placeList);
        Log.d(TAG, "initPlaceInfoData: " + placeInfoBox.count());
    }

    @Override
    public void deletePlaceByPlaceIds(String[] placeIdArr) {
        Query<PlaceInfo> placeInfoQuery = getBox()
                .query(PlaceInfo_.placeId.oneOf(placeIdArr))
                .build();
        long remove = placeInfoQuery.remove();
        placeInfoQuery.close();
    }

    @Override
    public String[] deletePlaceInfo(String mapName, String[] placeIds) {
        Query<PlaceInfo> placeInfoQuery = getBox()
                .query(PlaceInfo_.placeId.oneOf(placeIds))
                .equal(PlaceInfo_.mapName, mapName, QueryBuilder.StringOrder.CASE_SENSITIVE)
                .build();
        return deletePlaceAndGetPlaceIds(placeInfoQuery);
    }

    @Override
    public String[] deletePlaceByMapName(String mapName) {
        Query<PlaceInfo> placeInfoQuery = getBox()
                .query(PlaceInfo_.mapName.equal(mapName))
                .build();
        return deletePlaceAndGetPlaceIds(placeInfoQuery);
    }

    @Override
    public String[] deletePlaceByTypeId(String mapName, int typeId, int priority) {
        Query<PlaceInfo> placeInfoQuery = getBox()
                .query(PlaceInfo_.typeId.equal(typeId))
                .equal(PlaceInfo_.priority, priority)
                .equal(PlaceInfo_.mapName, mapName, QueryBuilder.StringOrder.CASE_SENSITIVE)
                .build();
        return deletePlaceAndGetPlaceIds(placeInfoQuery);
    }

    @Nullable
    private String[] deletePlaceAndGetPlaceIds(Query<PlaceInfo> placeInfoQuery) {
        String[] placeIds = placeInfoQuery.property(PlaceInfo_.placeId).findStrings();
        boolean remove = placeInfoQuery.remove() > 0;
        placeInfoQuery.close();
        Log.d(TAG, "getPlaceIds : " + remove);
        return placeIds;
    }

    private Query<PlaceInfo> getPlaceInfoQuery(String mapName) {
        return getBox()
                .query(PlaceInfo_.mapName.equal(mapName))
                .build();
    }

    public void connectTablePlaceType() {
        Log.d(TAG, "connectTablePlaceType");
        Box<PlaceInfo> placeInfoBox = getBox();
        // 1. 查询所有的PlaceType数据
        Query<PlaceType> placeTypeQuery = boxStore.boxFor(PlaceType.class)
                .query()
                .build();
        List<PlaceType> placeTypeList = placeTypeQuery.find();
        // 2. 将PlaceType中的特殊点位数据typeId填充到表中
        for (PlaceType placeType : placeTypeList) {
            List<PlaceName> placeNameList = placeType.placeNameToMany;
            if (placeNameList != null && placeNameList.size() != 0) {
                for (PlaceName placeName : placeNameList) {
                    String placeId = placeName.getPlaceId();
                    Query<PlaceInfo> placeInfoQuery = getBox()
                            .query(PlaceInfo_.placeId.equal(placeId))
                            .build();
                    PlaceInfo placeInfo = placeInfoQuery.findUnique();
                    if (placeInfo != null) {
                        //打印关联信息
                        Log.d(TAG, "connectTablePlaceType:Debug: " + placeName + " <-> " + placeType.getTypeId());
                        placeInfo.setTypeId(placeType.getTypeId());
                        placeInfoBox.put(placeInfo);
                        placeInfoQuery.close();
                    }
                }
            }
        }
        placeTypeQuery.close();
    }
}
