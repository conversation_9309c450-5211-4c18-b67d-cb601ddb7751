package com.ainirobot.navigationservice.chassisAbility.chassis.client.standard;

import android.content.Context;
import android.util.Log;

import com.ainirobot.navigationservice.beans.tk1.Laser;
import com.ainirobot.navigationservice.beans.tk1.NavAcceleration;
import com.ainirobot.navigationservice.beans.tk1.NavVelocity;
import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.tk1.TargetPose;
import com.ainirobot.navigationservice.beans.tk1.Velocity;
import com.ainirobot.navigationservice.beans.waiter.CameraBean;
import com.ainirobot.navigationservice.beans.waiter.MultiRobotConfigBean;
import com.ainirobot.navigationservice.beans.waiter.NaviPathDetail;
import com.ainirobot.navigationservice.beans.waiter.NaviPathInfo;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.tk1.customListener.CreateMapStop;
import com.ainirobot.navigationservice.roversdkhelper.maptype.NaviMapType;

import java.util.List;

import ninjia.android.proto.ChassisPacketProtoWrapper;
import ninjia.android.roversdk.Result;

import static com.ainirobot.navigationservice.Defs.Def.ResultCode.SUCCESS;

public class ChassisClientStandardImpl implements IChassisClient {
    @Override
    public void init(Context context) {

    }

    @Override
    public void setRoverConfig(RoverConfig roverConfig, ChassisResListener listener) {

    }

    @Override
    public void getRoverConfig(ChassisResListener listener) {

    }

    @Override
    public void setTime(long time, ChassisResListener listener) {

    }

    @Override
    public boolean isCommunicating() {
        return false;
    }

    @Override
    public boolean takeSnapshot(String logID, ChassisResListener listener) {
        return false;
    }

    @Override
    public boolean isServiceReady() {
        return false;
    }

    @Override
    public boolean isSocketConnected() {
        return false;
    }

    @Override
    public boolean isChassisReady() {
        return false;
    }

    @Override
    public void setEventListener(ChassisEventListener listener) {

    }

    @Override
    public Pose getCurrentPose() {
        return null;
    }

    @Override
    public Pose getRealtimePose() {
        return null;
    }

    @Override
    public ChassisPacketProtoWrapper.RealtimeObsMapProto getRealtimeObsMapProto() {
        return null;
    }

    @Override
    public Pose getCurrentPoseWithoutEstimate() {
        return null;
    }

    @Override
    public Velocity getVelocity() {
        return null;
    }

    @Override
    public Velocity getRealtimeVelocity() {
        return null;
    }

    @Override
    public List<Laser> getLasersData() {
        return null;
    }

    @Override
    public void updateMotionAvoidState(boolean withAvoid) {

    }

    @Override
    public boolean isPoseEstimate() {
        return false;
    }

    @Override
    public void checkCurNaviMap(ChassisResListener listener) {

    }

    @Override
    public boolean isMoving() {
        return false;
    }

    @Override
    public void switchMap(String mapName, ChassisResListener listener) {

    }

    @Override
    public void loadCurrentMap(boolean useCustomKeepPose, boolean keepPose, ChassisResListener listener) {

    }

    @Override
    public void startCreatingMap(NaviMapType naviMapType, ChassisResListener listener) {

    }

    @Override
    public void stopCreatingMap(String mapName, boolean save, String language, int type, int finishState, CreateMapStop listener) {

    }

    @Override
    public void stopExtendMap(String mapName, ChassisResListener listener) {

    }

    @Override
    public void go(TargetPose targetPose, NavVelocity navVelocity, NavAcceleration navAcc, ChassisResListener listener) {

    }

    @Override
    public void go(TargetPose targetPose, ChassisResListener listener) {

    }

    @Override
    public void cancelNavigation(ChassisResListener listener) {

    }

    @Override
    public void setPoseEstimate(Pose pose, ChassisResListener listener) {

    }

    @Override
    public void setFixedEstimate(Pose pose, ChassisResListener listener) {

    }

    @Override
    public void setForceEstimate(Pose pose, ChassisResListener listener) {

    }

    @Override
    public void setVisionEstimate(ChassisResListener listener) {

    }

    @Override
    public void resetPoseEstimate(ChassisResListener listener) {

    }

    @Override
    public void setChassisRelocation(int type, Pose pose, ChassisResListener listener) {

    }

    @Override
    public void stopMove() {

    }

    @Override
    public void motion(double angularSpeed, double linearSpeed, double acceleration, boolean hasAcceleration) {

    }

    @Override
    public void motionWithObstacles(double angularSpeed, double linearSpeed, double acceleration) {

    }

    @Override
    public void motion(double angularSpeed, double linearSpeed, double acceleration) {

    }

    @Override
    public void motionWithObstacles(double angularSpeed, double linearSpeed, double acceleration, double minDistance) {

    }

    @Override
    public void motionWithStaticObstacles(double angularSpeed, double linearSpeed, double minDistance) {

    }

    @Override
    public void motionWithOnceObstacle(double angularSpeed, double linearSpeed, boolean hasAcceleration) {

    }

    @Override
    public void motionControlWithObstacle(double angularSpeed, double linearSpeed, double minDistance) {

    }

    @Override
    public void motionSoft(double angularSpeed, double linearSpeed, boolean hasAcceleration) {

    }

    @Override
    public void turnLeft(double angle, double speed, double acceleration, boolean noNeedAcceleration, ChassisResListener listener) {

    }

    @Override
    public void turnRight(double angle, double speed, double acceleration, boolean noNeedAcceleration, ChassisResListener listener) {

    }

    @Override
    public void forward(double distance, double speed, double acceleration, ChassisResListener listener) {

    }

    @Override
    public void forward(double distance, double speed, double acceleration, boolean avoid, ChassisResListener listener) {

    }

    @Override
    public void backward(double distance, double speed, double acceleration, ChassisResListener listener) {

    }

    @Override
    public void rotateInPlace(int direction, double angle, double speed, ChassisResListener listener) {

    }

    @Override
    public void startExtendMap(ChassisResListener listener) {

    }

    @Override
    public void setRadarState(boolean state, ChassisResListener listener) {

    }

    @Override
    public void getRadarState(ChassisResListener listener) {

    }

    @Override
    public void switchChargeMode() {

    }

    @Override
    public void switchManualMode() {

    }

    @Override
    public void sendPrimitiveMovingSpeed(double angularSpeed, double linearSpeed) {

    }

    @Override
    public void getFullCheckStatus(ChassisResListener listener) {

    }

    @Override
    public void getSensorStatus(ChassisResListener listener) {

    }

    @Override
    public boolean getLogFile(long startTime, long endTime, String cmdType, String fileType, String path,  ChassisResListener listener) {
        return false;
    }

    @Override
    public void removeTkMap(String mapName, ChassisResListener listener) {

    }

    @Override
    public boolean packLogFile(long startTime, long endTime, ChassisResListener listener) {
        return false;
    }

    @Override
    public boolean startPlanRoute() {
        return false;
    }

    @Override
    public void savePlanRoute(String routeName, List<Pose> poseList) {

    }

    @Override
    public void getSystemInformation(ChassisResListener listener) {

    }

    @Override
    public boolean hasObstacle(double startAngle, double endAngle, double distance) {
        return false;
    }

    @Override
    public boolean hasObstacleInArea(double startAngle, double endAngle, double minDistance, double maxDistance) {
        return false;
    }

    @Override
    public void recoveryNavigation(ChassisResListener listener) {

    }

    @Override
    public boolean getHasVision() {
        return false;
    }

    @Override
    public void goCharge(boolean isFrontCamera, ChassisResListener listener) {

    }

    @Override
    public void stopCharge(ChassisResListener listener) {

    }

    @Override
    public String getMapStatus(String cmdType, String cmdParam) {
        return null;
    }

    @Override
    public void addMappingPose(Pose pose, ChassisResListener listener) {

    }

    @Override
    public void deleteMappingPose(int poseId, ChassisResListener listener) {

    }

    @Override
    public void setMinObstaclesDistance(double distance) {

    }

    @Override
    public void resetMinObstaclesDistance() {

    }

    @Override
    public void setSimpleEventListener(SimpleEventListener mSimpleEventListener) {

    }

    @Override
    public Velocity getFollowVelocity(double distance, double angle, double headAngleSpeed, Velocity velocity, double latency) {
        return null;
    }

    @Override
    public Velocity getBodyFollowVelocity(double distance, double angle, double latency, double headAngleSpeed, Velocity velocity, double maxLinSpeed, double maxAngSpeed, double safeDistance) {
        return null;
    }

    @Override
    public Velocity getBodyFollowVelocity(double distance, double angle, double headAngleSpeed, Velocity velocity, double latency) {
        return null;
    }

    @Override
    public Velocity getFollowVelocity(double angle,double latency) {
        return null;
    }

    @Override
    public void setCurrentWorkModeFree(ChassisResListener listener) {

    }

    @Override
    public void motionPid(double angularSpeed, double linearSpeed) {

    }

    @Override
    public void setCameraEnable(int cameraType, boolean enable, ChassisResListener listener) {

    }

    @Override
    public CameraBean queryCameraEnableState(int cameraType) {
        return null;
    }

    @Override
    public void setMultiRobotSettingConfigData(MultiRobotConfigBean configBean, ChassisResListener listener) {

    }

    @Override
    public void sendLoraMsgData(String dataInfo, ChassisResListener listener) {

    }

    @Override
    public boolean setNavigationPriority(int priority) {
        return false;
    }

    @Override
    public void sendLoraTestMsg(ChassisResListener listener) {

    }

    @Override
    public void setLoraTestMode(boolean enable, ChassisResListener listener) {

    }

    @Override
    public void resetLoraDefaultConfig(ChassisResListener listener) {

    }

    @Override
    public void setWheelControlMode(boolean isRelease, ChassisResListener listener) {

    }

    @Override
    public void calcNaviPathInfo(List<NaviPathInfo> pathInfos, ChassisResListener listener) {

    }

    @Override
    public void calcNaviPathDetail(List<NaviPathDetail> pathInfos, ChassisResListener listener) {

    }

    @Override
    public void setMultiRobotWriteExtraData(byte[] data, double time, ChassisResListener listener) {

    }

    @Override
    public void setMultiRobotWriteExternalData(byte[] data, double time, ChassisResListener listener) {

    }

    @Override
    public void enableReportLineData(boolean enable) {

    }

    @Override
    public void enableReportDepthImage(boolean enable, int deviceId) {

    }

    @Override
    public void enableReportIRImage(boolean enable, int deviceId) {

    }

    @Override
    public void startDataSetRecord(String sensorString) {

    }

    @Override
    public void stopDataSetRecord(boolean isLocalData) {

    }

    @Override
    public void uploadNaviDataSet(String sensorString) {

    }

    @Override
    public void autoDrawRoadGraph(String mapName, ChassisResListener listener) {

    }

    public void setOdomUpdateListener(OdomUpdate odomUpdate) {
    }

    @Override
    public void getNaviParams(String type, String params, ChassisResListener listener) {

    }

    @Override
    public void naviPause(boolean isPause, ChassisResListener listener) {

    }

    @Override
    public void gotoAlign(Pose pose, ChassisResListener listener) {

    }

    @Override
    public void cancelAlign(ChassisResListener listener) {

    }

    @Override
    public void startHumanFollowing(String followId, int lostFindTimeout, ChassisResListener listener) {

    }

    @Override
    public void stopHumanFollowing(ChassisResListener listener) {

    }

    @Override
    public void detectQrCodeByPic(String path, ChassisResListener listener) {

    }
}
