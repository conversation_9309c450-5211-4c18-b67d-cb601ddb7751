package com.ainirobot.navigationservice.business.rpc.algorithm;

import android.util.Log;

import com.ainirobot.navigationservice.business.rpc.SpeedBean;

import java.math.BigDecimal;

/**
 * Angular To Linear motion
 *
 * @version V1.0.0
 * @date 2019/11/15 15:00
 */
public class AngularMotion extends MotionAlgorithm {
    private static final int ACC_TIME = 50;

    public AngularMotion(SpeedBean target) {
        super(target);
    }

    @Override
    public void motion() {
        BigDecimal angularSpeed = target.getAngularSpeed();
        Log.i(TAG, "motion: target" + target);

        motion(0.0F, angularSpeed.floatValue());
        try {
            Thread.sleep(ACC_TIME);
        } catch (InterruptedException interruptedException) {
            Log.w(TAG, "motion: angular motion intercepted ");
        }
    }
}
