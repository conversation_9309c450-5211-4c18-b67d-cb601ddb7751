/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.utils;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.navigationservice.db.NavigationDataManager;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

public class NavigationConfig {
    private static final String TAG = NavigationConfig.class.getSimpleName();
    public static final String DEFAULT_NAVIGATION_IP = "127.0.0.1";
    public static final String DEFAULT_ROS_IP = "*************";

    // Display sdk report data, and DEFAULT_ROS_IP must be different
    public static final String DEFAULT_SDK_ROS_IP = "";

    private static final String CONFIG_DIR = "/robot/config";
    private static final String CONFIG_FILE = "navigation.properties";
    public static final String IP_NAVIGATION = "navigation";
    public static final String IP_ROS = "ros";
    public static final String IP_SDK_ROS = "sdk_ros";

    public static String getNavIp() {
        Log.d(TAG, "getNavIp: ");
        String navIp = NavigationDataManager.getInstance().getIpNavigation();
        Log.d(TAG, "getNavIp: navIp=" + navIp);
        return TextUtils.isEmpty(navIp) ? DEFAULT_NAVIGATION_IP : navIp;
    }

    public static String getSdkRosIp() {
        String sdkRosIp = NavigationDataManager.getInstance().getIpSdkRos();
        return sdkRosIp == null ? DEFAULT_SDK_ROS_IP : sdkRosIp;
    }
    /**
     * 局域网IP，即有线的以太网络接口
     * <p>
     * 只有最初821版本的豹小秘才有eth0接口，内部三块板子之间有局域网；
     * 其他产品都没有eth0接口，这里获取结果为null；
     */
    public static String getEth0Ip() {
        return getLocalIp("eth0");
    }

    /**
     * 无线局域网（Wi-Fi）IP，即wifi或者热点（不包括流量卡）
     */
    public static String getWlan0Ip() {
        return getLocalIp("wlan0");
    }

    public static String getLocalIp() {
        String ip = getEth0Ip();
        if (ip == null) {
            return getWlan0Ip();
        }
        return ip;
    }

    private static String getLocalIp(String ipType) {
        try {
            Enumeration<NetworkInterface> nets = NetworkInterface.getNetworkInterfaces();
            while (nets.hasMoreElements()) {
                NetworkInterface net = nets.nextElement();
                if (net.getName().equals(ipType)) {
                    Enumeration<InetAddress> addresses = net.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        InetAddress inetA = addresses.nextElement();
                        if (inetA instanceof Inet4Address && !inetA.isLoopbackAddress()) {
                            return inetA.getHostAddress();
                        }
                    }
                }
            }
            return null;
        } catch (SocketException e) {
            return null;
        }
    }

}
