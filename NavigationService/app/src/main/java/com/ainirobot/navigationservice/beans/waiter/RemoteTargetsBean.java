package com.ainirobot.navigationservice.beans.waiter;

import java.util.ArrayList;

public class RemoteTargetsBean {
    private int versionCode = 1;
    private ArrayList<Targets> targetsData;
    private ArrayList<TargetsErrorInfo> targetsErrorData;
    private ArrayList<TargetsSafeZone> targetsSafeZone;

    public RemoteTargetsBean() {

    }

    public RemoteTargetsBean(int versionCode, ArrayList<Targets> targetsData,
                             ArrayList<TargetsErrorInfo> targetsErrorData,
                             ArrayList<TargetsSafeZone> targetsSafeData) {
        this.versionCode = versionCode;
        this.targetsData = targetsData;
        this.targetsErrorData = targetsErrorData;
        this.targetsSafeZone = targetsSafeData;
    }

    public int getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(int versionCode) {
        this.versionCode = versionCode;
    }

    public ArrayList<Targets> getTargetsData() {
        return targetsData;
    }

    public void setTargetsData(ArrayList<Targets> targetsData) {
        this.targetsData = targetsData;
    }

    public ArrayList<TargetsErrorInfo> getTargetsErrorData() {
        return targetsErrorData;
    }

    public void setTargetsErrorData(ArrayList<TargetsErrorInfo> targetsErrorData) {
        this.targetsErrorData = targetsErrorData;
    }

    public ArrayList<TargetsSafeZone> getTargetSafeData() {
        return targetsSafeZone;
    }

    public void setTargetSafeData(ArrayList<TargetsSafeZone> targetsSafeZone) {
        this.targetsSafeZone = targetsSafeZone;
    }

    @Override
    public String toString() {
        return "RemoteTargetsBean{" +
                "versionCode=" + versionCode +
                ", targetsData=" + targetsData +
                ", targetsErrorData=" + targetsErrorData +
                ", targetsSafeZone=" + targetsSafeZone +
                '}';
    }
}
