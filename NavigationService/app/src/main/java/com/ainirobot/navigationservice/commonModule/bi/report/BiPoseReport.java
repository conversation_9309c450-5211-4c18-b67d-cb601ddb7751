package com.ainirobot.navigationservice.commonModule.bi.report;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

/**
 * 此埋点有两处，一个在NavigationService中，由底盘主动上报。
 * 另一个在MapTool中，每次进入设置界面手动ping多机状态上报。
 */
public class BiPoseReport extends BaseBiReport {

    private static final String TABLE_NAME = "base_robot_location";
    private static final String LOCATION_DATA = "location_data";
    private static final String CTIME = "ctime";

    public BiPoseReport() {
        super(TABLE_NAME);
        initData();
    }

    private void initData() {
        addData(LOCATION_DATA, false);
        addData(CTIME, "");
    }

    public BiPoseReport addLocationData(String poseData) {
        addData(LOCATION_DATA, poseData);
        return this;
    }

    private void addCTime() {
        addData(CTIME, System.currentTimeMillis());
    }

    @Override
    public void report() {
        addCTime();
        super.report();
    }


}
