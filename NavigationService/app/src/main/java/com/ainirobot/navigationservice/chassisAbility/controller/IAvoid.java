package com.ainirobot.navigationservice.chassisAbility.controller;

/**
 * Created by Orion on 2020/9/9.
 */
public interface IAvoid {

    void addObserver(BaseAvoidPolicy.AvoidObserver observer);

    boolean removeObserver(BaseAvoidPolicy.AvoidObserver observer);

    boolean getState();

    boolean getState(double distance);

    void setSafeDistance(double distance);

    void resetSafeDistance();

    int getScore();

}
