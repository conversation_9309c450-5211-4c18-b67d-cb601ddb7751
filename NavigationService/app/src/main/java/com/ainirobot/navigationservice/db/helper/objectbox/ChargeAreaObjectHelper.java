package com.ainirobot.navigationservice.db.helper.objectbox;

import static com.ainirobot.navigationservice.db.entity.ChargeArea_.areaId;

import android.util.Log;

import com.ainirobot.navigationservice.db.entity.ChargeArea;
import com.ainirobot.navigationservice.db.entity.ChargeArea_;
import com.ainirobot.navigationservice.db.entity.MappingInfo;
import com.ainirobot.navigationservice.db.helper.iml.ChargeAreaHelperIml;

import java.util.List;

import io.objectbox.Box;
import io.objectbox.BoxStore;
import io.objectbox.query.Query;

public class ChargeAreaObjectHelper extends BaseObjectHelper<ChargeArea> implements ChargeAreaHelperIml {
    public ChargeAreaObjectHelper(BoxStore boxStore) {
        super(boxStore);
    }

    @Override
    public ChargeArea getChargeAreaConfigByAreaId(int areaId) {
        Query<ChargeArea> chargeAreaInfoQuery = getBox()
                .query(ChargeArea_.areaId.equal(areaId)).build();
        ChargeArea chargeAreaInfo = chargeAreaInfoQuery.findFirst();
        chargeAreaInfoQuery.close();
        return chargeAreaInfo;
    }

    @Override
    public void initChargeAreaData(List<ChargeArea> infoList) {
    }

    @Override
    public List<ChargeArea> getChargeAreaConfig() {
        Query<ChargeArea> chargeAreaInfoQuery = getBox().query().build();
        List<ChargeArea> chargeAreaInfos = chargeAreaInfoQuery.find();
        chargeAreaInfoQuery.close();
        return chargeAreaInfos;
    }

    @Override
    public boolean updateChargeArea(List<ChargeArea> infoList) {
        Box<ChargeArea> chargeAreaInfoBox = getBox();
        chargeAreaInfoBox.put(infoList);
        return true;
    }

    @Override
    public boolean updateChargeArea(ChargeArea areaInfo) {
        Box<ChargeArea> chargeAreaInfoBox = getBox();
        return chargeAreaInfoBox.put(areaInfo) > 0;
    }

    @Override
    public boolean deleteChargeArea(ChargeArea info) {
        Box<ChargeArea> chargeAreaInfoBox = getBox();
        return chargeAreaInfoBox.remove(info);
    }
}
