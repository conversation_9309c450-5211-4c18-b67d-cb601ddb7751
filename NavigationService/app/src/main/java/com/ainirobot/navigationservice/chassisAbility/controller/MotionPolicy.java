/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.navigationservice.chassisAbility.controller;

import android.util.Log;

import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.beans.tk1.Velocity;

public class MotionPolicy {
    private static final String TAG = MotionPolicy.class.getSimpleName();

    //speed and accelerate
    private static final double MAX_LINEAR_SPEED = 0.9d;
    private static final double MAX_ANGULAR_SPEED = 1.0d;
    private static final double MIN_ANGULAR_SPEED = Math.toRadians(5);
    private static final double MAX_FOLLOW_LINEAR_SPEED = 1.0d;
    private static final double MAX_FOLLOW_ANGULAR_SPEED = 1.2d;
    private static final double FOLLOW_START_ANGLE = 2;
    private static final double FOLLOW_FAULT_ANGLE = 0;

    private static final double MAX_ANGULAR = Math.toRadians(40); //The deviation from the max of 50 degree
    private static final double START_ANGULAR = Math.toRadians(20); //The deviation is going to start at 10 degree

    //interval and distance
    private static final float DEFAULT_INTERVAL = 1.0f;

    //New
    private static final double DEC_ANGLE = 45; //The deviation from the max of 50 degree
    private static final double START_ANGLE = 5; //The deviation is going to start at 5 degree
    private static final double FAULT_ANGLE = 8;
    private static final double SAFE_DISTANCE = 1.0d;
    private static final double DEC_DISTANCE = 0.5d; //Start deceleration distance
    private static final double ANGULAR_FACTOR = 1.0d;

    //The navigation is reported a very small speed at the stationary state
    private static final double MIN_SPEED = 0.02;

    /**
     * 人体跟随开始减速距离 1+1=2 米
     */
    private static final double BODY_DEC_DISTANCE = 1d; //Start deceleration distance

    public static Velocity follow(double distance, double angle,
                                  double headAngleSpeed, Velocity velocity, double latency) {
        if (latency > 600 || Math.abs(angle) > 230 || distance < 0) {
            Log.d(TAG, "Wrong data , distance="
                    + distance + " angle=" + angle + " la: tency=" + latency);
            return new Velocity(0, 0);
        }

        double navLinearSpeed = (velocity.getX() < MIN_SPEED ? 0 : velocity.getX());
        double navAngularSpeed = (velocity.getZ() < MIN_SPEED ? 0 : velocity.getZ());
        double navAngleSpeed = Math.toDegrees(navAngularSpeed);
        double finalAngle = angle - (headAngleSpeed - navAngleSpeed) * latency / 1000;
        double angularFactor = evalAngularFactor(finalAngle);
        double angularSpeed = MAX_ANGULAR_SPEED * angularFactor;

        double finalDistance = (distance == 0 ? 0 : distance - navLinearSpeed * latency / 1000);
        double linearFactor = evalLinearFactor(finalDistance, angularFactor);
        double linearSpeed = MAX_LINEAR_SPEED * linearFactor;

        Log.d(TAG, "Follow : distance=" + distance + "  angle=" + angle + "  latency=" + latency
                + "  linear=" + linearSpeed + "  angular=" + angularSpeed);
        return new Velocity(linearSpeed, angularSpeed);
    }

    private static double evalAngularFactor(double angle) {
        double sign = -Math.signum(angle);
        angle = Math.abs(angle) - START_ANGLE;
        if (angle <= FAULT_ANGLE) {
            return 0.0;
        } else if (angle < DEC_ANGLE) {
            float fraction = (float) (angle / DEC_ANGLE);
            return (1.0 - Math.pow((1.0 - fraction), ANGULAR_FACTOR)) * sign;
        } else {
            return 1.0 * sign;
        }
    }

    private static double evalLinearFactor(double distance, double angularFactor) {
        distance = distance - SAFE_DISTANCE;
        angularFactor = 1.0 - Math.abs(angularFactor);
        if (distance <= 0) {
            return 0.0;
        } else if (distance < DEC_DISTANCE) {
            float fraction = (float) (distance / DEC_DISTANCE);
            return fraction * (1.0 + angularFactor) / 2;
        } else {
            return (1.0 + angularFactor) / 2;
        }
    }

    /**
     * 计算人体跟随速度
     */
    public static Velocity bodyFollow(double distance, double angle,
                                      double headAngleSpeed, Velocity velocity, double latency) {
        if (latency > 600 || Math.abs(angle) > 230 || distance < 0) {
            Log.d(TAG, "Wrong data , distance="
                    + distance + " angle=" + angle + " la: tency=" + latency);
            return new Velocity(0, 0);
        }

        double navLinearSpeed = (velocity.getX() < MIN_SPEED ? 0 : velocity.getX());
        double navAngularSpeed = (velocity.getZ() < MIN_SPEED ? 0 : velocity.getZ());
        double navAngleSpeed = Math.toDegrees(navAngularSpeed);
        double finalAngle = angle - (headAngleSpeed - navAngleSpeed) * latency / 1000;
//        double finalAngle = angle;
        double angularFactor = evalBodyAngularFactor(finalAngle);
        double angularSpeed = MAX_FOLLOW_ANGULAR_SPEED * angularFactor;

        double finalDistance = (distance == 0 ? 0 : distance - navLinearSpeed * latency / 1000);
//        double finalDistance = distance;
        double linearFactor = evalBodyLinearFactor(finalDistance, angularFactor);
        double linearSpeed = MAX_FOLLOW_LINEAR_SPEED * linearFactor;

        Log.d(TAG, "Body follow : distance=" + distance + "  angle=" + angle + "  latency=" + latency
                + "  linear=" + linearSpeed + "  angular=" + angularSpeed + ", finalDistance=" + finalDistance);
        return new Velocity(linearSpeed, angularSpeed);
    }

    private static double evalBodyAngularFactor(double angle) {
        double sign = -Math.signum(angle);
        angle = Math.abs(angle) - FOLLOW_START_ANGLE;
        if (angle <= FOLLOW_FAULT_ANGLE) {
            return 0.0;
        } else if (angle < DEC_ANGLE) {
            float fraction = (float) (angle / DEC_ANGLE);
            return (1.0 - Math.pow((1.0 - fraction), ANGULAR_FACTOR)) * sign;
        } else {
            return 1.0 * sign;
        }
    }

    private static double evalBodyLinearFactor(double distance, double angularFactor) {
        distance = distance - SAFE_DISTANCE;
        angularFactor = 1.0 - Math.abs(angularFactor);
        if (distance <= 0) {
            return 0.0;
        } else if (distance < BODY_DEC_DISTANCE) {
            float fraction = (float) (distance / BODY_DEC_DISTANCE);
            return fraction * (1.0 + angularFactor) / 2;
        } else {
            return (1.0 + angularFactor) / 2;
        }
    }

    /**
     * Algorithm of speed/timer/accelerate/distance
     *
     * @param distance
     * @param angle
     * @param headAngleSpeed
     * @param latency
     */
    public static Velocity follow(double distance, double angle,
                                  double headAngleSpeed, double latency) {

        Log.d(TAG, "move3 valuable: distance=" + distance + ",angle=" +
                angle + ",headAngleSpeed=" + headAngleSpeed);

        double newLinearSpeed = 0;
        double newAngularSpeed = 0;

        if (Math.abs(angle) > 180) {
            Log.d(TAG, "Wrong angle is " + angle);
            return null;
        }

        if (distance < 0) {
            return null;
        }

        double maxLinearSpeed = MAX_LINEAR_SPEED;

        //calculate final distance if maxSpeed == 0, no move, only rotate
        double finalDistance = (distance > DEFAULT_INTERVAL) ? (distance - DEFAULT_INTERVAL) : 0;

        //calculate final radian angle
        double finalAngle = Math.toRadians(-angle);
        //float finalAngle = 10f;
        double headSpeed = Math.toRadians(-headAngleSpeed);
        finalAngle = finalAngle - ((0 + headSpeed) * latency / 1000);
        double absRadianAngle = Math.abs(finalAngle) > START_ANGULAR ? Math.abs(finalAngle) : 0;

        //calculate max speeds
        maxLinearSpeed = (distance > 1) ? maxLinearSpeed : maxLinearSpeed / 4 * 3;
        double maxAngularSpeed = MAX_ANGULAR_SPEED *
                (absRadianAngle < MAX_ANGULAR ? absRadianAngle / MAX_ANGULAR : 1);

        double xTime = (maxLinearSpeed > 0) ? finalDistance / maxLinearSpeed : 0;
        double zTime = (maxAngularSpeed) > 0 ? absRadianAngle / maxAngularSpeed : 0;
        if (xTime == 0 && zTime == 0) {
            newAngularSpeed = 0;
            newLinearSpeed = 0;
        } else {
            newLinearSpeed = (xTime > zTime) ? maxLinearSpeed : finalDistance / zTime;
            //newLinearSpeed = (xTime > 0) ? finalDistance / xTime : 0;
            newAngularSpeed = (xTime > zTime) ? absRadianAngle / xTime : maxAngularSpeed;

            newAngularSpeed = Math.max(newAngularSpeed, MIN_ANGULAR_SPEED);

            //newAngularSpeed = absRadianAngle / zTime;
            newAngularSpeed = (absRadianAngle > 1) ? MAX_ANGULAR_SPEED : newAngularSpeed;
            newAngularSpeed = (finalAngle < 0) ? -newAngularSpeed : newAngularSpeed;
        }

        Log.d(TAG, "move3 valuable: newLinearSpeed=" + newLinearSpeed + ",newAngularSpeed=" +
                newAngularSpeed + ",finalDistance=" + finalDistance + ",absRadianAngle=" + absRadianAngle);

        return new Velocity(newLinearSpeed, newAngularSpeed);
    }

    public static Pose followWithNavigation(Pose currentPose, double distance, double angle,
                                            double headAngleSpeed, double latency) {
        if (currentPose == null) {
            return null;
        }
        double theta = currentPose.getTheta();
        double x = currentPose.getX();
        double y = currentPose.getY();
        double tX = 0;
        double tY = 0;
        double tTheta = 0;

        if (Math.signum(theta) == Math.signum(angle)) {
            tTheta = theta - Math.toRadians(angle);
            tX = x + distance * Math.cos(tTheta);
            tY = y + distance * Math.sin(tTheta);
        }

        if (theta >= 0 && angle < 0) {
            double diff = Math.toRadians(180) - theta + Math.toRadians(angle);
            tTheta = theta - Math.toRadians(angle);
            tX = x - distance * Math.cos(diff);
            tY = y + distance * Math.sin(diff);
        }

        if (theta < 0 && angle >= 0) {
            double diff = Math.toRadians(-180) - theta + Math.toRadians(angle);
            tTheta = theta - Math.toRadians(angle);
            tX = x + distance * Math.cos(diff);
            tY = y + distance * Math.sin(diff);
        }

        return new Pose((float) tX, (float) tY, (float) tTheta);
    }
}