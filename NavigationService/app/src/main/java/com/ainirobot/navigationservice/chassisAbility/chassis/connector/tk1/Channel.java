
/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.chassisAbility.chassis.connector.tk1;

import android.os.SystemClock;
import android.util.Log;

import com.ainirobot.navigationservice.utils.IOUtils;
import com.google.protobuf.Message;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.net.Socket;
import java.net.SocketTimeoutException;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import ninjia.android.proto.RoverPacketProtoWrapper;
import ninjia.android.proto.RoverPacketProtoWrapper.RoverPacketProto;
import ninjia.android.proto.SendHeartBeatingProtoWrapper;

import static com.ainirobot.navigationservice.Defs.Def.CHANNEL_COMMAND;
import static com.ainirobot.navigationservice.Defs.Def.CHANNEL_EVENT;
import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

public class Channel {
    private static final String TAG = TAGPRE + "Channel";

    private static final int STATUS_CONNECTED = 0;
    private static final int STATUS_CLOSED = 1;
    private static final int STATUS_CONNECTING = -1;

    private static final int COMMAND_PORT = 8080;
    private static final int EVENT_PORT = 8089;
    private static final String CLIENT_SYN = "rover client say hi";
    private static final String SERVER_ACK = "hello this is serversocket!";
    private static final long MIN_HEART_INTERVAL = 5000; // 3s
    private static final long DELAY_TIME = 5000; // Delay 5s reconnection
    // if not received any message in 30 seconds, reconnect
    private static final long RECEIVE_TIMEOUT = 30 * 1000;
    private static final long MAX_TIMEOUT = 24 * 60 * 60 * 1000;

    private static final Object RECONNECT_LOCK = new Object();

    private final String mHost;
    private final int mPort;
    private final String mChannelName;

    private Socket mSocket;
    private Timer mDelayTimer;
    private Timer mHeartTimer;
    private Emitter mEmitter;
    private BlockingQueue<Message> mMessageQueue = new LinkedBlockingQueue<>();
    private OnChannelListener mChannelListener;

    private SendThread mSendThread;
    private ReceiveThread mReceiveThread;
    private DataOutputStream mOut;
    private DataInputStream mIn;

    private long mSendTime = 0;
    private long mReceiveTime = 0;
    private int mStatus = STATUS_CONNECTING;

    private class SendThread extends Thread {
        @Override
        public void run() {
            try {
                while (isConnected()) {
                    Message message = mMessageQueue.take();
                    message.writeDelimitedTo(mOut);

                    mOut.flush();
                    mSendTime = SystemClock.uptimeMillis();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
                Log.e(TAG, "Send thread exception : " + mChannelName + " isInterrupted:" + isInterrupted());
                if (!isInterrupted()) {
                    reconnect();
                }
                Log.e(TAG, "Send thread exception : " + mChannelName);
            }
        }
    }

    private class ReceiveThread extends Thread {
        @Override
        public void run() {
            try {
                while (isConnected()) {
                    RoverPacketProto packet = RoverPacketProto.parseDelimitedFrom(mIn);
                    String header = packet.getHeader().getCode().toString();
                    if (packet == null) {
                        Log.e(TAG, "Read data exception : event , packet is null");
                        reconnect();
                        return;
                    }

                    if (isHeartbeat(packet)) {
                        onHeartbeat();
                    } else {
                        mEmitter.emit(packet);
                    }
                    mReceiveTime = SystemClock.uptimeMillis();
                }
            } catch (Exception e) {
                Log.e(TAG, "Read thread exception : " + mChannelName + " isInterrupted:" + isInterrupted());
                e.printStackTrace();
                if (!isInterrupted()) {
                    reconnect();
                }
            }
        }
    }

    public boolean isHeartbeat(RoverPacketProto packet) {
        RoverPacketProtoWrapper.RoverPacketCodeProto code = packet.getHeader().getCode();
        return code == RoverPacketProtoWrapper.RoverPacketCodeProto.SEND_HEART_BEATING;
    }

    public Channel(String channelName, Emitter emitter, String host) {
        mHost = host;
        mChannelName = channelName;
        mPort = CHANNEL_COMMAND.equals(mChannelName) ? COMMAND_PORT : EVENT_PORT;
        mEmitter = emitter;
    }

    public void setChannelListener(OnChannelListener listener) {
        this.mChannelListener = listener;
    }

    public void start() {
        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                connect();
            }
        });
        thread.start();
    }

    public boolean sendMessage(Message message) {
        return message != null && isConnected() && mMessageQueue.offer(message);
    }

    private void reconnect() {
        mStatus = STATUS_CONNECTING;
        synchronized (RECONNECT_LOCK) {
            Log.e(TAG, mChannelName + " start reconnect, status:" + mStatus);
            if (isConnected()) {
                return;
            }
            Log.e(TAG, mChannelName + " reconnect");
            if (mChannelListener != null) {
                mChannelListener.onDisconnected();
            }
            destroy();
            connect();
        }
    }

    private synchronized void connect() {
        if (isConnected()) {
            return;
        }
        try {
            Socket socket = new Socket(mHost, mPort);
            if (verifySocket(socket)) {
                Log.d(TAG, mChannelName + " is connected");
                mSocket = socket;
                mStatus = STATUS_CONNECTED;
                mReceiveThread = new ReceiveThread();
                mReceiveThread.start();

                mSendThread = new SendThread();
                mSendThread.start();

                if (mChannelListener != null) {
                    mChannelListener.onConnected();
                }

                startHeartbeat();
            } else {
                delayConnect();
            }
        } catch (IOException e) {
            e.printStackTrace();
            delayConnect();
        }
    }

    public String getName() {
        return mChannelName;
    }

    public boolean isConnected() {
        return mStatus == STATUS_CONNECTED;
    }

    public boolean isClosed() {
        return mStatus == STATUS_CLOSED;
    }

    public synchronized void close() {
        mStatus = STATUS_CLOSED;
        mMessageQueue.clear();
        destroy();
    }

    private void destroy() {
        IOUtils.close(mSocket);
        if (mSendThread != null) {
            mSendThread.interrupt();
            mSendThread = null;
        }

        if (mReceiveThread != null) {
            mReceiveThread.interrupt();
            mReceiveThread = null;
        }
    }

    private void delayConnect() {
        Log.d(TAG, mChannelName + "start delay reconnect");
        cancelDelayTimer();
        mDelayTimer = new Timer();
        mDelayTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.d(TAG, mChannelName + " delay reconnect");
                connect();
            }
        }, DELAY_TIME);
    }

    private void cancelDelayTimer() {
        if (mDelayTimer != null) {
            mDelayTimer.cancel();
            mDelayTimer = null;
        }
    }

    @SuppressWarnings("deprecation")
    private boolean verifySocket(Socket socket) throws IOException {
        try {
            socket.setSoTimeout(10000);
            mOut = new DataOutputStream(socket.getOutputStream());
            Log.d(TAG, "Rover say hi : " + mChannelName);
            mOut.write((CLIENT_SYN + "\n").getBytes());
            mOut.flush();

            mIn = new DataInputStream(socket.getInputStream());
            String response = mIn.readLine();
            Log.d(TAG, "Rover receive hello : " + mChannelName);
            return SERVER_ACK.equals(response);
        } catch (SocketTimeoutException e) {
            e.printStackTrace();
        } finally {
            socket.setSoTimeout(0);
        }
        return false;
    }

    private void startHeartbeat() {
        if (mHeartTimer != null) {
            mHeartTimer.cancel();
            mHeartTimer = null;
        }

        mReceiveTime = SystemClock.uptimeMillis();
        mHeartTimer = new Timer();
        mHeartTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                long currentTime = SystemClock.uptimeMillis();
                if (!isConnected()) {
                    return;
                }

                //if ((currentTime - mSendTime) > MIN_HEART_INTERVAL) {
                boolean isSucceed = sendHeartbeat();
                Log.d(TAG, "Send heartbeat : " + mChannelName + "  " + isSucceed);
                //}

                //The event channel has no heartbeat packet
                if (CHANNEL_EVENT.equals(mChannelName)) {
                    return;
                }

                long timeDiff = currentTime - mReceiveTime;
                if (timeDiff > MAX_TIMEOUT) {
                    mReceiveTime = SystemClock.uptimeMillis();
                    return;
                }

                if ((timeDiff) > RECEIVE_TIMEOUT) {
                    Log.e(TAG, "Heartbeat timeout : " + mChannelName);
                    reconnect();
                }
            }
        }, MIN_HEART_INTERVAL, MIN_HEART_INTERVAL);
    }

    private boolean sendHeartbeat() {
        Message message = SendHeartBeatingProtoWrapper.SendHeartBeatingProto.newBuilder()
                .setHeader(buildHeader(RoverPacketProtoWrapper.RoverPacketCodeProto.SEND_HEART_BEATING, null))
                .build();
        return sendMessage(message);
    }

    private RoverPacketProtoWrapper.RoverPacketHeaderProto buildHeader(
            RoverPacketProtoWrapper.RoverPacketCodeProto code, String text) {
        RoverPacketProtoWrapper.RoverPacketHeaderProto.Builder builder
                = RoverPacketProtoWrapper.RoverPacketHeaderProto
                .newBuilder()
                .setCode(code);

        if (text != null) {
            builder.setText(text);
        }
        return builder.build();
    }

    private void onHeartbeat() {
        Log.d(TAG, "Receive heartbeat : " + mChannelName);
    }

    public interface OnChannelListener {
        void onConnected();

        void onDisconnected();
    }
}
