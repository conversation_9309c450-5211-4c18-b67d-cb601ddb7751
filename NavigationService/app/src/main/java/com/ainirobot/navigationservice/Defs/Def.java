package com.ainirobot.navigationservice.Defs;

import com.ainirobot.coreservice.client.ProductInfo;

public class Def {
    public static final String TAGPRE = "NLOGS_";
    public static final String MAP_DB_PRE = "MAP_DB_";

    public static final String CLIENT_YUEFAN = "client_yuefan";
    public static final String CLIENT_TK1 = "client_tk1";
    public static final String CLIENT_X86 = "client_x86";
    public static final String CLIENT_WAITER = "client_waiter";
    public static final String CHANNEL_COMMAND = "command";
    public static final String CHANNEL_EVENT = "event";
    //底盘算法进程
    public static final String CHASSIS_REMOTE = "remote";

    public static class ResultCode {
        public final static int FAIL_NO_TASK_EXECUTING = -2;
        public final static int FAIL_NO_REASON = -1;
        public final static int SUCCESS = 0;
        public final static int START_EXPANSION_MAP_FAILED = -101;
        public final static int STOP_EXPANSION_MAP_FAILED = -111;
        public final static int DESTROY_NO_NEED = -111003;
        public final static int MOTION_AVOID_STOP = -111004;
        public final static int BASIC_MOTION_INTERRUPTED = -111005;
        public final static int CHASSIS_ALREADY_INITED = -111002;
    }

    public static class StandardDef {
        public static final String CMD_CHANNEL = "cmd_channel";
        public static final String EVENT_CHANNEL = "event_channel";
        public static final int PortCmd = 8887;
        public static final int PortEvent = 8889;
        public final static int EXECUTION = 1;
        public final static int CANCEL = 0;
        public final static int COMPLETE = 100;
    }

    public static class TK1Def {
        public static final String PORT = ":8088";
        public static final String URL_GET_MAP = "/map_manager/get_map_";
        public static final String URL_ADD_MAP = "/map_manager/add_map.do?mapName=";
        public static final String URL_MAP_NAVI = PORT + URL_GET_MAP;
        public static final String URL_MAP_LOCAL = PORT + URL_ADD_MAP;
        public static final String URL_MAP_NAVI_SUFFIX = ".do?mapName=";

        public static final String URL_GET_LOG = "/log_manager/get_error_log";
        public static final String URL_LOG_NAVI = PORT + URL_GET_LOG;
        public static final String URL_LOG_NAVI_SUFFIX = ".do?path=";

        public static final String URL_GET_FILE = "/file_manager/get_file";
        public static final String URL_FILE_NAVI = PORT + URL_GET_FILE;
        public static final String URL_GET_FILE_SUFFIX = ".do?path=";

        public static final String ERROR_LOG = "error_log";
        public static final String LOG_FILE = "log_file";
        public static final String SHOT_LOG = "shot_log";

        /*result code*/
        public static final int SUCCESS_SET_NAVI_MAP_HAS_VISION = 1;
        public static final int SUCCESS = 0;
        public static final int ERROR = -1;
        public static final int ERROR_ORIGIN_NOT_CONNECT = -100001;
        public static final int ERROR_REMOTE_ERROR = -100002;
        public static final int ERROR_INVALID_PROTO = -100003;

        public static final int ERROR_START_CREATE_MAP = -105000;
        public static final int ERROR_START_CREATE_MAP_INVALID_MAP_NAME = -105001;
        public static final int ERROR_START_CREATE_MAP_NOT_IN_WORKING_MODE_CREATE_MAP = -105002;
        public static final int ERROR_START_CREATE_MAP_NO_FEATURE_SPACE = -105003;
        public static final int ERROR_START_CREATE_MAP_GET_FEATURE_SPACE_FAILED = -105004;

        public static final int ERROR_DELETE_MAP = -102000;
        public static final int ERROR_DELETE_MAP_INVALID_MAP_NAME = -102001;

        public static final int ERROR_CANCEL_AUTO_MOVING = -108000;
        public static final int ERROR_CANCEL_AUTO_MOVING_NOT_IN_MOTION_MODE_AUTO_MOVING = -108001;
        public static final int ERROR_CANCEL_AUTO_MOVING_IN_WORKING_MODE_INITIAL = -108002;

        public static final int ERROR_SET_MOTION_MODE = -111000;
        public static final int ERROR_SET_MOTION_MODE_IN_WORKING_MODE_INITIAL = -111001;

        public static final int ERROR_SEND_PRIMITIVE_MOVING = -106000;
        public static final int ERROR_SEND_PRIMITIVE_MOVING_NOT_IN_MOTION_MODE_MANUAL_CONTROL = -106001;
        public static final int ERROR_SEND_PRIMITIVE_MOVING_IN_WORKING_MODE_INITIAL = -106002;

        public static final int ERROR_ADD_MAP = -101000;
        public static final int ERROR_ADD_MAP_INVALID_MAP_NAME = -101001;
        public static final int ERROR_ADD_MAP_INVALID_MAP_PATH = -101002;
        public static final int ERROR_SET_NAVI_MAP = -104000;
        public static final int ERROR_SET_NAVI_MAP_INVALID_MAP_NAME = -104001;

        public static final int ERROR_SET_GOAL = -107000;
        public static final int ERROR_SET_GOAL_NOT_IN_MOTION_MODE_AUTO_MOVING = -107001;
        public static final int ERROR_SET_GOAL_IN_WORKING_MODE_INITIAL = -107002;

        public static final int ERROR_RELOCATE = -110000;
        public static final int ERROR_RELOCATE_NOT_IN_WORKING_MODE_KNOWN_MAP_NAVIGATION = -110001;
        public static final int ERROR_RELOCATE_INVALID_PROTO = -110002;

        public static final int ERROR_SET_CONFIG = -109000;
        public static final int ERROR_SET_CONFIG_INVALID_PROTO = -109001;

        public static final int ERROR_SET_MOTION_MODE_SWITCH_LIDAR = -111002;
    }

    //导航任务默认线速度
    public static final float ROBOT_SETTING_DEFAULT_LINEAR_SPEED = 0.7F;
    //导航任务默认角速度
    public static final float ROBOT_SETTING_DEFAULT_ANGULAR_SPEED = 1.2F;
    //导航任务当未能到达指定位姿点时，需要到达位姿点的默认半径
    public static final double ROBOT_NAVIGATION_DEFAULT_DESTINATION_RANGE = 0.0D;
    /**
     * 最大避障距离默认值，距离目标的障碍物小于该值时，机器人停止
     */
    public static final double ROBOT_NAVIGATION_DEFAULT_OBS_DISTANCE = 0.75D;

    /**
     * 以下状态用于判断机器人导航任务下位姿是否发生变化，默认角度判断标准是10°(弧度为0.1745），距离判断是0.1米
     * 检测间隔是30秒
     */
    public static final double DEFAULT_ROBOT_NOT_MOVING_DISTANCE = 0.1D;
    public static final double DEFAULT_ROBOT_NOT_MOVING_ANGLE = 0.175D;
    public static final long DEFAULT_ROBOT_NOT_MOVING_TIME_INTERVAL = 30*1000L;

    public static final String CHASSIS_LOG_TYPE_NORMAL = "log_type_normal";
    public static final String CHASSIS_LOG_TYPE_SPECIAL = "log_type_special";
    public static final String CHASSIS_LOG_TYPE_SNAPSHOT = "log_type_snapshot";
    public static final String CHASSIS_LOG_TYPE_FOLLOWING = "log_type_following";

    public static final String NAVIGATION_STATUS_GO_STRAIGHT = "GoStraight";
    public static final String NAVIGATION_STATUS_TURN_LEFT = "TurnLeft";
    public static final String NAVIGATION_STATUS_TURN_RIGHT = "TurnRight";

    public static final String JSON_KEY_CACHE_ID = "cacheId";

    /** 导航任务默认线加速度 **/
    public static final float ROBOT_SETTING_DEFAULT_LINEAR_ACCELERATION = 0.7F;
    /** 导航任务默认角加速度 **/
    public static final float ROBOT_SETTING_DEFAULT_ANGULAR_ACCELERATION = 1.2F;
    /** 导航任务最小线加速度 **/
    public static final float ROBOT_SETTING_MIN_LINEAR_ACCELERATION = 0.7F;
    /** 导航任务最小角加速度 **/
    public static final float ROBOT_SETTING_MIN_ANGULAR_ACCELERATION = 1.2F;
    /** 导航任务最大线加速度 **/
    public static final float ROBOT_SETTING_MAX_LINEAR_ACCELERATION = 0.7F;
    /** 导航任务最大角加速度 **/
    public static final float ROBOT_SETTING_MAX_ANGULAR_ACCELERATION = 1.2F;

    /**
     *Lora 10秒无更新,Nan距离过近
     */
    public static final int LORA_NO_DATA = 53;

    /**
     * 机器人半径，
     * 招财豹 0.264
     * 小秘 0.244，
     * mini 0.205，
     * mini2 0.205 (100cm*41cm*41cm)
     */
    public static double sRobotRadius = 0.244f;

    static {
        if (ProductInfo.isMiniProduct()) {
            sRobotRadius = 0.205;
        } else if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
            sRobotRadius = 0.264;
        }
    }
}
