package com.ainirobot.navigationservice.chassisAbility.ota.connector.tk1;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.navigationservice.chassisAbility.ota.connector.tk1.bean.OtaReqMessage;
import com.ainirobot.navigationservice.chassisAbility.ota.connector.tk1.bean.OtaResMessage;
import com.ainirobot.navigationservice.commonModule.configuration.ConfigManager;
import com.ainirobot.navigationservice.utils.GsonUtil;
import com.ainirobot.navigationservice.utils.IOUtils;
import com.ainirobot.navigationservice.utils.NavigationConfig;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.Socket;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

public class ConnectServer {
    private final static String TAG = TAGPRE + ConnectServer.class.getSimpleName();

    private final int PORT = 9090;
    private final long RECONNECT_INTERVAL = 3000; // ms
    private static final long RECEIVE_TIMEOUT = 20 * 1000;
    private static final int EVENT_SOCKECT_CONNECT = 1;
    private static final int EVENT_SOCKECT_RECONNECT = 2;
    private HandlerThread mDeamonThread;
    private Deamon mDeamon;
    private String mHost;
    private int mPort;
    private volatile boolean isClosed = false;
    private volatile boolean isConnected = false;

    private Socket mSocket;
    private final String CMD_HEARTBEAT = "heartBeat";

    private Thread mReceiveThread;
    private Thread mSendThread;

    private BlockingQueue<String> mSendQueue = new LinkedBlockingQueue<>();

    private OtaConnectTk1Impl otaConnectTk1Impl;

    public ConnectServer(OtaConnectTk1Impl otaConnectTk1Impl) {
        this.mHost = ConfigManager.getInstance().getDeviceIP();
        this.mPort = PORT;
        this.otaConnectTk1Impl = otaConnectTk1Impl;
    }

    public void start() {
        mDeamonThread = new HandlerThread("OTA TK1 " + ConnectServer.class.getSimpleName());
        mDeamonThread.start();

        mDeamon = new Deamon(mDeamonThread.getLooper());
        mDeamon.sendEmptyMessage(EVENT_SOCKECT_CONNECT);
    }

    public void sendMessage(String msg) {
        if (TextUtils.isEmpty(msg)) {
            return;
        }

        try {
            mSendQueue.put(msg);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private class Deamon extends Handler {
        Deamon(Looper looper) {
            super(looper);
        }


        @Override
        public void handleMessage(Message msg) {
            Log.d(TAG, "Deamon handle message, what=" + msg.what);
            switch (msg.what) {
                case EVENT_SOCKECT_CONNECT:
                    connect();
                    break;

                case EVENT_SOCKECT_RECONNECT:
                    reconnect();
                    break;

                default:
                    break;
            }
        }

        private synchronized void reconnect() {
            isConnected = false;
            otaConnectTk1Impl.onDisConnectIn();
            if (isClosed) {
                return;
            }

            mDeamon.sendEmptyMessage(EVENT_SOCKECT_CONNECT);
        }

        private synchronized void connect() {
            if (isConnected) {
                otaConnectTk1Impl.onConnectIn();
                return;
            }
            try {
                mSocket = new Socket(mHost, mPort);
                Log.d(TAG, "OTA connected. Socket#" + mSocket);
                isConnected = true;

                if (mSendThread != null) {
                    mSendThread.interrupt();
                    mSendThread = null;
                }

                if (mReceiveThread != null) {
                    mReceiveThread.interrupt();
                    mReceiveThread = null;
                }

                mSendThread = new SendThread();
                mSendThread.start();

                mReceiveThread = new ReceiveThread();
                mReceiveThread.start();

                otaConnectTk1Impl.onConnectIn();
                mDeamon.sendEmptyMessageDelayed(EVENT_SOCKECT_RECONNECT, RECEIVE_TIMEOUT);
            } catch (IOException e) {
                Log.d(TAG, "OTA connect IOException. Socket#" + mSocket);
                e.printStackTrace();
                mDeamon.sendEmptyMessageDelayed(EVENT_SOCKECT_RECONNECT, RECONNECT_INTERVAL);
            }
        }
    }


    private class ReceiveThread extends Thread {
        private boolean isStop = false;

        @Override
        public void interrupt() {
            isStop = true;
            super.interrupt();
        }

        @Override
        public void run() {
            BufferedReader in = null;
            try {
                in = new BufferedReader(new InputStreamReader(mSocket.getInputStream()));
                while (!isInterrupted() && isConnected) {
                    String response = in.readLine();
                    if (response != null) {
                        Log.d(TAG, "Command response : " + response);
                        handleResponse(response);
                    }
                }
            } catch (Exception e) {
                Log.d(TAG,"ReceiveThread#" + Thread.currentThread().getName()
                        + " get exception. " + " Socket#" + mSocket + " isStop=" + isStop);
                e.printStackTrace();
                if (!isInterrupted() && !isStop) {
                    Log.d(TAG, "ReceiveThread#" + Thread.currentThread().getName()
                            + " reconnect");
                    mDeamon.sendEmptyMessage(EVENT_SOCKECT_RECONNECT);
                }
            } finally {
                IOUtils.close(in);
            }
        }
    }

    private class SendThread extends Thread {
        private boolean isStop = false;

        @Override
        public void interrupt() {
            isStop = true;
            super.interrupt();
        }

        @Override
        public void run() {
            DataOutputStream out = null;
            String message = null;
            try {
                out = new DataOutputStream(mSocket.getOutputStream());
                while (!isInterrupted() && isConnected) {
                    message = mSendQueue.take();
                    OtaReqMessage otaReqMessage = GsonUtil.fromJson(message, OtaReqMessage.class);
                    Log.d(TAG, "Command send : " + otaReqMessage.toString());
                    otaReqMessage.write(out);
                    out.flush();
                    message = null;
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
                handleException(message);
            } catch (IOException e) {
                Log.d(TAG, "SendThread#" + Thread.currentThread().getName() + " get exception. "
                        + " Socket#" + mSocket + "isStop=" + isStop);
                e.printStackTrace();
                if (!isStop) {
                    Log.d(TAG, "SendThread#" + Thread.currentThread().getName() + " reconnect");
                    mDeamon.sendEmptyMessage(EVENT_SOCKECT_RECONNECT);
                }
                handleException(message);
            } catch (Exception e) {
                e.printStackTrace();
                handleException(message);
            } finally {
                IOUtils.close(out);
            }
        }
    }

    private void handleException(String message) {
        if (TextUtils.isEmpty(message)) {
            return;
        }

        OtaResMessage otaResMessage = new OtaResMessage(OtaResMessage.ERROR_RES, message);
        otaConnectTk1Impl.onResponse(GsonUtil.toJson(otaResMessage));
        /*String command;
        if (message instanceof StringMessage) {
            command = ((StringMessage) message).getCommand();
        } else {
            command = CMD_START_UPDATE;
        }
        sendResult(command, "connect error");*/

    }

    private void handleResponse(String response) {
        if (TextUtils.isEmpty(response)) {
            return;
        }

        /*心跳处理，内部消化*/
        if (isHeartBeat(response)) {
            reSendReconnectMsg();
            return;
        }

        OtaResMessage otaResMessage = new OtaResMessage(OtaResMessage.SUC_RES, response);
        otaConnectTk1Impl.onResponse(GsonUtil.toJson(otaResMessage));
    }


    private boolean isHeartBeat(String response) {
        String command = "";
        try {
            JSONObject json = new JSONObject(response);
            command = json.getString("command");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return CMD_HEARTBEAT.equals(command);
    }

    private void reSendReconnectMsg() {
        if (mDeamon.hasMessages(EVENT_SOCKECT_RECONNECT)) {
            mDeamon.removeMessages(EVENT_SOCKECT_RECONNECT);
        }
        mDeamon.sendEmptyMessageDelayed(EVENT_SOCKECT_RECONNECT, RECEIVE_TIMEOUT);
    }
}
