package com.ainirobot.navigationservice.chassisAbility.controller;

import android.util.Log;

import com.ainirobot.navigationservice.beans.tk1.Laser;
import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.beans.tk1.Velocity;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.x86.WheelControlX86;
import com.ainirobot.navigationservice.utils.RLog;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Created by Orion on 2020/8/10.
 * 动态避停，用于基础运动
 */
public class DynamicStoppingPolicy extends BaseAvoidPolicy {
    private static final String TAG = "DynamicStoppingPolicy";

    //主动延长预估轨迹距离，可用来调节避停后距离障碍物距离(理论距离=膨胀半径+延长值+膨胀半径余量)
    private static final double PATH_DISTANCE_EXTENDED = 0.0;
    //膨胀半径余量，可用来调节窄道通过率
    private static final double SAFE_OFFSET = 0.1;
    //预估轨迹精度，即相邻两个点的最小距离
    private static final double PATH_PRECISION = 0.025;

    private ScheduledExecutorService mExecutor = Executors.newScheduledThreadPool(1);
    private Future mFuture;
    private IChassisClient mChassis;

    public DynamicStoppingPolicy(IChassisClient client) {
        super();
        init(client);
    }

    private void init(IChassisClient client) {
        this.mChassis = client;
        startDynamicAvoidTask();
    }

    private void startDynamicAvoidTask() {
        if (mFuture == null) {
            mFuture = mExecutor.scheduleAtFixedRate(new Runnable() {
                @Override
                public void run() {
                    try {
                        dynamicPathPlaning();
                    } catch (Exception e) {
                        Log.e(TAG, "startDynamicAvoidTask Exception : " + e.getMessage());
                    }
                }
            }, 0, 100, TimeUnit.MILLISECONDS);
        }
    }

    private void dynamicPathPlaning() {
//        Log.i(TAG, "dynamicPathPlaning : Start");
        long startTime = System.currentTimeMillis();
        Velocity velocity = mChassis.getRealtimeVelocity();
        if (velocity == null) {
            Log.i(TAG, "dynamicPathPlaning : Velocity null Error");
            return;
        }
        List<Laser> lasers = mChassis.getLasersData();
        if (lasers == null || lasers.size() <= 0) {
            Log.i(TAG, "dynamicPathPlaning : Lasers data null Error");
            return;
        }
        RLog.v(TAG, "dynamicPathPlaning : Velocity=" + velocity.toString() + ", Lasers size=" + lasers.size());

        if (Math.abs(velocity.getX()) < 0.1) {
            velocity = new Velocity(0.1, velocity.getZ());
        }
        if (Math.abs(velocity.getZ()) < 0.1) {
            double sign = Math.sin(velocity.getZ());
            velocity = new Velocity(velocity.getX(), sign * 0.1);
        }
        double extendDistance = ROBOT_RADIUS + PATH_DISTANCE_EXTENDED;
        double extendTime = extendDistance / Math.abs(velocity.getX());
        double linearDecTime = Math.abs(velocity.getX()) / getLinearDec();
        double pathTime = linearDecTime + extendTime;//pathTime尽量大，保证预估路径足够长
        double linearDecDistance = Math.pow(Math.abs(velocity.getX()), 2) / (2 * getLinearDec());
        double pathDistance = linearDecDistance + extendDistance;//用来限制预估路径实际长度
        int poseNumber = (int) Math.ceil(pathDistance / PATH_PRECISION);//点位个数
        double timeInterval = pathTime / poseNumber;
        double precision = timeInterval * velocity.getX();
//        Log.i(TAG, "dynamicPathPlaning : linearDecTime=" + linearDecTime + ", extendTime=" + extendTime + ", pathTime=" + pathTime
//                + " :: linearDecDistance=" + linearDecDistance + ", extendDistance=" + extendDistance + ", pathDistance=" + pathDistance
//                + " :: poseNumber=" + poseNumber + ", timeInterval=" + timeInterval + ", precision=" + precision);

        //计算预估路径Pose，以当前位置为坐标原点(0, 0, 0)
        List<Pose> planningPose = new ArrayList<>(poseNumber);
        Pose robot = new Pose(0, 0, 0);
        planningPose.add(robot);
//        Log.i(TAG, "dynamicPathPlaning : First pose : " + robot + " :: Distance to robot : " + robot.getDistance(robot));
        for (int i = 1; i < poseNumber; i++) {
            Pose pre = planningPose.get(i - 1);
            Pose cur = calculatePose(pre, velocity, timeInterval);
            double distanceToRobot = cur.getDistance(robot);
//            Log.i(TAG, "dynamicPathPlaning : Next pose=" + cur.toString() + " :: Distance to robot : " + distanceToRobot);
//            if (distanceToRobot >= pathDistance) break;//避免预估轨迹过长
            planningPose.add(cur);
        }

        //计算障碍物Pose，相对于当前位置
        List<LaserPose> laserPoseList = new ArrayList<>(lasers.size());
        for (Laser laser : lasers) {
            float xLaser = (float) (laser.getDistance() * Math.cos(laser.getAngle()));
            float yLaser = (float) (laser.getDistance() * Math.sin(laser.getAngle()));
            float thetaLaser = (float) laser.getAngle();
            Pose laserPose = new Pose(xLaser, yLaser, thetaLaser);
            laserPoseList.add(new LaserPose(laserPose, laser));
        }

        int score = scorePlanningPath(planningPose, laserPoseList, velocity.getX());
        RLog.v(TAG, "dynamicPathPlaning : Stop score=" + score + ", Cost time:" + (System.currentTimeMillis() - startTime));
        onScoreResult(score);
    }

    private int scorePlanningPath(List<Pose> planningPose, List<LaserPose> laserPose, double linearSpeed) {
        if (planningPose == null || planningPose.size() <= 0 || laserPose == null || laserPose.size() <= 0) {
            return OBSTACLES_SCORE_SAFE;
        }

//        Log.d(TAG, "scorePlanningPath , Planning pose size=" + planningPose.size()
//                + ", Laser pose size=" + laserPose.size() + "================================");
        for (Pose posePath : planningPose) {
            for (LaserPose poseLaser : laserPose) {
                double pathPoseToLaser = posePath.getDistance(poseLaser.getPose());
                if (pathPoseToLaser <= (ROBOT_RADIUS + SAFE_OFFSET)) {
                    double laserToRobot = poseLaser.getPose().getDistance(new Pose());
                    Log.d(TAG, " scorePlanningPath , in dangerous : laserToRobot="
                            + laserToRobot + "  pathPoseToLaser=" + pathPoseToLaser
                            + "  laser:" + poseLaser.getLaser().toString());
                    if ((linearSpeed < 0.8 && laserToRobot <= DEFAULT_DANGEROUS_DISTANCE_LOW_SPEED)
                            || (linearSpeed >= 0.8 && laserToRobot <= DEFAULT_DANGEROUS_DISTANCE_FAST_SPEED)) {
                        return OBSTACLES_SCORE_PERILOUS; //危险系数高，急停停止
                    }

                    return OBSTACLES_SCORE_DANGEROUS; //有障碍物，减速停止
                }
            }
        }
        return OBSTACLES_SCORE_SAFE; //可通过
    }

    /**
     * 计算预估路径上的每个点位
     *
     * @param pose     当前位姿,未定位情况下默认为(0,0,0)
     * @param velocity 当前速度,底盘上报
     * @param time     时间间隔,根据当前线速度和角速度算出速度降为0需要的时间(取最大值)
     * @return 目标 pose
     */
    private Pose calculatePose(Pose pose, Velocity velocity, double time) {
        if (time <= 0) {
            return pose;
        }
        double vel_linear_y = 0;

        double d_theta_half = velocity.getZ() * time / 2;
        double theta = pose.getTheta() + d_theta_half;
        double k = d_theta_half < 1e-6 ? 1 : Math.sin(d_theta_half) / d_theta_half;
        double d_x = velocity.getX() * k;
        double d_y = vel_linear_y * k;

        float xDes = (float) (pose.getX() + (d_x * Math.cos(theta) - d_y * Math.sin(theta)) * time);
        float yDes = (float) (pose.getY() + (d_x * Math.sin(theta) + d_y * Math.cos(theta)) * time);
        float thetaDes = (float) correctPoseTheta(theta + d_theta_half);

        return new Pose(xDes, yDes, thetaDes);
    }

    /**
     * 将目标角度转换到 -PI 到 PI 之间
     *
     * @param radian 弧度
     * @return
     */
    private double correctPoseTheta(double radian) {
        while (radian < -Math.PI) radian += 2 * Math.PI;
        while (radian > Math.PI) radian -= 2 * Math.PI;
        return radian;
    }


    //线-减速带
    private double getLinearDec() {
        return WheelControlX86.A_LINEAR_DEC;
    }

    private static class LaserPose {
        Pose pose; //障碍物对应pose
        Laser laser;

        public LaserPose(Pose pose, Laser laser) {
            this.pose = pose;
            this.laser = laser;
        }

        public Pose getPose() {
            return pose;
        }

        public void setPose(Pose pose) {
            this.pose = pose;
        }

        public Laser getLaser() {
            return laser;
        }

        public void setLaser(Laser laser) {
            this.laser = laser;
        }
    }

}
