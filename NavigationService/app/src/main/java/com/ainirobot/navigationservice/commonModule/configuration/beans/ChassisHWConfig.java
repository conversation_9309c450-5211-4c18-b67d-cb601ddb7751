package com.ainirobot.navigationservice.commonModule.configuration.beans;

import com.google.gson.annotations.SerializedName;

public class ChassisHWConfig {
    @SerializedName("底盘")
    private String deviceType;

    @SerializedName("底盘设备号")
    private int deviceTypeNumber;

    @SerializedName("启用子设备号")
    private boolean useSubDeviceTypeNum;

    @SerializedName("底盘IP")
    private String deviceIP;

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public int getDeviceTypeNumber() {
        return deviceTypeNumber;
    }

    public void setDeviceTypeNumber(int deviceTypeNumber) {
        this.deviceTypeNumber = deviceTypeNumber;
    }

    public boolean isUseSubDeviceTypeNum() {
        return useSubDeviceTypeNum;
    }

    public void setUseSubDeviceTypeNum(boolean useSubDeviceTypeNum) {
        this.useSubDeviceTypeNum = useSubDeviceTypeNum;
    }

    public String getDeviceIP() {
        return deviceIP;
    }

    public void setDeviceIP(String deviceIP) {
        this.deviceIP = deviceIP;
    }

    @Override
    public String toString() {
        return "ChassisHWConfig{" +
                "deviceType='" + deviceType + '\'' +
                ", deviceTypeNumber=" + deviceTypeNumber +
                ", useSubDeviceTypeNum=" + useSubDeviceTypeNum +
                ", deviceIP='" + deviceIP + '\'' +
                '}';
    }
}
