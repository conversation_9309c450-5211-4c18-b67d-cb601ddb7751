package com.ainirobot.navigationservice.beans.waiter;

import com.ainirobot.navigationservice.beans.tk1.Pose;

/**
 * 导航路线信息
 */
public class NaviPathInfo {

    public static final int STATE_POSE_NOT_EXIT = -1000;

    /**
     * 开始点位
     */
    private Pose startPose;

    /**
     * 结束点位
     */
    private Pose endPose;

    /**
     * 路线长度
     */
    private double pathLength;

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    private int state;


    public Pose getStartPose() {
        return startPose;
    }

    public void setStartPose(Pose startPose) {
        this.startPose = startPose;
    }

    public Pose getEndPose() {
        return endPose;
    }

    public void setEndPose(Pose endPose) {
        this.endPose = endPose;
    }

    public double getPathLength() {
        return pathLength;
    }

    public void setPathLength(double pathLength) {
        this.pathLength = pathLength;
    }
}
