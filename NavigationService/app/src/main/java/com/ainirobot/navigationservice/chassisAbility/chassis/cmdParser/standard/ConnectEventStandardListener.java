package com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard;

import android.os.Environment;
import android.util.Base64;
import android.util.Log;

import com.ainirobot.navigationservice.beans.standard.PoseInfo;
import com.ainirobot.navigationservice.chassisAbility.chassis.connector.standard.ConnectApi;
import com.ainirobot.navigationservice.protocol.bean.PoseBeanCreator;
import com.ainirobot.navigationservice.protocol.event.EventHeadCreator;
import com.ainirobot.navigationservice.protocol.event.EventPacketCreator;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Arrays;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.EventStandardDef.EVENT_POSE_UPDATE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.EventStandardDef.EVENT_REALTIME_MAP_DATA;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.EventStandardDef.EVENT_REMOTE_CONTROL_STATE;

public class ConnectEventStandardListener implements ConnectApi.EventListener {
    private final static String TAG = TAGPRE + ConnectResStandardListener.class.getSimpleName();

    private ChassisCommandStandardImpl cmdApi;

    public ConnectEventStandardListener(ChassisCommandStandardImpl cmdApi) {
        this.cmdApi = cmdApi;
    }

    @Override
    public void onEvent(Message message) {
        if (message instanceof EventPacketCreator.EventPacketProto) {
            EventPacketCreator.EventPacketProto eventPacketProto = (EventPacketCreator.EventPacketProto) message;
            EventHeadCreator.EventHeadProto eventHead = eventPacketProto.getHeader();
            Log.d(TAG, "onEvent :" + generateResHeadStr(eventHead));
            switch (eventHead.getCommand()) {
                case EVENT_REALTIME_MAP_DATA:
                    byte[] mapData = eventPacketProto.getParam().toByteArray();
                    Log.d(TAG, "mapData = " + Arrays.toString(mapData));
                    String string = null;
                    try {
                        string = new String(mapData, "UTF-8");
                    } catch (UnsupportedEncodingException e) {
                        e.printStackTrace();
                    }

                    Log.d(TAG, "map string = " + string);


                    try {
                        FileOutputStream write = new FileOutputStream(new File(getPngName()));
                        byte[] decoderBytes = Base64.decode(string, Base64.DEFAULT);
                        write.write(decoderBytes);
                        write.close();
                    } catch (IOException e) {
                        Log.e(TAG, "exception read");
                        e.printStackTrace();
                    }

                    cmdApi.getEventListener(EVENT_REALTIME_MAP_DATA).onEvent(eventHead.getType()
                            , eventHead.getMsg(), eventPacketProto.getParam());
                    break;
                case EVENT_REMOTE_CONTROL_STATE:
                    cmdApi.getEventListener(EVENT_REMOTE_CONTROL_STATE).onEvent(eventHead.getType()
                            , eventHead.getMsg(), null);
                    break;
                case EVENT_POSE_UPDATE:
                    try {
                        PoseBeanCreator.PoseBeanProto poseBeanProto = PoseBeanCreator.PoseBeanProto.parseFrom(eventPacketProto.getParam());
                        cmdApi.getEventListener(EVENT_POSE_UPDATE).onEvent(eventHead.getType()
                                , eventHead.getMsg(), new PoseInfo(poseBeanProto.getX(),
                                        poseBeanProto.getY(), poseBeanProto.getTheta()));
                    } catch (InvalidProtocolBufferException e) {
                        e.printStackTrace();
                    }
                    break;
                default:
                    break;
            }

        }
    }

    private final static String MAP_DIR = "/robot/map";
    private final static String ROBOT_MAP_DIR = Environment.getExternalStorageDirectory() + MAP_DIR;

    private String getPngName() {
        return Environment.getExternalStorageDirectory() + File.separator + "map.png";
    }

    private String generateResHeadStr(EventHeadCreator.EventHeadProto eventHeadProto) {
        StringBuffer sb = new StringBuffer();
        sb.append("command = ");
        sb.append(eventHeadProto.getCommand());
        sb.append("\ntype = ");
        sb.append(eventHeadProto.getType());
        sb.append("\nmsg = ");
        sb.append(eventHeadProto.getMsg());
        sb.append("\ntimeStamp = ");
        sb.append(eventHeadProto.getTimeStamp());
        return sb.toString();
    }

}
