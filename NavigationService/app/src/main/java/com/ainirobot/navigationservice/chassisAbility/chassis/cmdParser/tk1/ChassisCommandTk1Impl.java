package com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1;

import androidx.annotation.NonNull;
import android.util.Log;

import com.ainirobot.navigationservice.Defs.Def.ResultCode;
import com.ainirobot.navigationservice.beans.tk1.MotionMode;
import com.ainirobot.navigationservice.beans.tk1.RelocateMode;
import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.tk1.WorkMode;
import com.ainirobot.navigationservice.beans.tk1.WorkStateMode;
import com.ainirobot.navigationservice.chassisAbility.chassis.connector.tk1.IChassisConnect;
import com.ainirobot.navigationservice.commonModule.configuration.ConfigManager;
import com.ainirobot.navigationservice.roversdkhelper.MapFileHelper;
import com.google.protobuf.Message;
import com.koushikdutta.async.http.AsyncHttpClient;
import com.koushikdutta.async.http.AsyncHttpGet;
import com.koushikdutta.async.http.AsyncHttpPost;
import com.koushikdutta.async.http.AsyncHttpResponse;
import com.koushikdutta.async.http.body.MultipartFormDataBody;
import com.koushikdutta.async.http.callback.HttpConnectCallback;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import static com.ainirobot.navigationservice.Defs.Def.ResultCode.FAIL_NO_REASON;
import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.ERROR_LOG;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.LOG_FILE;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.SHOT_LOG;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.URL_FILE_NAVI;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.URL_GET_FILE_SUFFIX;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.URL_LOG_NAVI;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.URL_LOG_NAVI_SUFFIX;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.URL_MAP_LOCAL;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.URL_MAP_NAVI;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.URL_MAP_NAVI_SUFFIX;

public class ChassisCommandTk1Impl implements IChassisCommand {
    //TODO :TK1发送和接受的路径撘完了，下一步将TK1命令接口抽取出来
    private static final String TAG = TAGPRE + ChassisCommandTk1Impl.class.getSimpleName();

    private IChassisConnect connector;
    private ConcurrentHashMap<String, EventListener> eventListenerHashMap = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Protocol, List<ResponseListener>> mResponseListeners = new ConcurrentHashMap<>();
    private final List<Operations> mOperations = new ArrayList<>();
    private volatile boolean isSocketConnected = false;
    private OnCmdTk1CnnListener cnnListener;
    private String mNavIp;
    private volatile int mWorkModeState = WorkStateMode.CHASSIS_UNKOWN;

    @Override
    public void registerEventListener(String type, EventListener listener) {
        eventListenerHashMap.put(type, listener);
    }

    @Override
    public void unRegisterEventListener(String type) {
        eventListenerHashMap.remove(type);
    }

    public EventListener getEventListener(String type) {
        return eventListenerHashMap.get(type);
    }

    public ChassisCommandTk1Impl() {
    }

    @Override
    public void injectConnector(IChassisConnect connectApi) {
        connector = connectApi;
    }

    @Override
    public void init() {
        mNavIp = ConfigManager.getInstance().getDeviceIP();
        if (connector != null) {
            connector.registerConnectListener(connectListener);
            connector.registerEventListener(new ConnectEventTk1Listener(this));
            connector.registerResponseListener(new ConnectResTk1Listener(this));
            connector.init();
        }
    }

    @Override
    public void setCnnListener(OnCmdTk1CnnListener cnnListener) {
        this.cnnListener = cnnListener;
    }

    @Override
    public boolean addMappingPose(Double x, Double y, Double theta, ResponseListener listener) {
        return sendCommand(Protocol.ADD_TARGET, listener, x, y, theta);
    }

    @Override
    public boolean deleteMappingPose(int id, ResponseListener listener) {
        return sendCommand(Protocol.REMOVE_TARGET, listener, id);
    }

    @Override
    public boolean saveMap(ResponseListener listener) {
        return sendCommand(Protocol.SAVE_MAP, listener);
    }

    IChassisConnect.OnConnectListener connectListener = new IChassisConnect.OnConnectListener() {
        @Override
        public void onConnected()  {
            Log.d(TAG, "onConnected in");
            isSocketConnected = true;
            if (cnnListener != null) {
                cnnListener.onConnected();
            }
        }

        @Override
        public void onDisconnected(String channelName) {
            isSocketConnected = false;
            Log.d(TAG, "onDisConnected in");
            if (cnnListener != null) {
                cnnListener.onDisconnected(channelName);
            }
        }
    };

    @Override
    public void setWorkModeState(int state) {
        Log.d(TAG, "setWorkModeState = " + state);
        mWorkModeState = state;
    }

    @Override
    public boolean isWorkModeReady() {
        return mWorkModeState == WorkStateMode.CHASSIS_READY;
    }


    @Override
    public boolean isChannelConnected() {
        return isSocketConnected;
    }

    private void registerResponseListener(Protocol protocol, ResponseListener listener) {
        if (listener == null) {
            return;
        }
        if (mResponseListeners.containsKey(protocol)) {
            mResponseListeners.get(protocol).add(listener);
        } else {
            List<ResponseListener> list = new CopyOnWriteArrayList<>();
            list.add(listener);
            mResponseListeners.putIfAbsent(protocol, list);
        }
    }

    private void unregisterResponseListener(Protocol protocol) {
        if (mResponseListeners.containsKey(protocol)) {
            List<ResponseListener> list = mResponseListeners.remove(protocol);
        }
    }

    public ConcurrentHashMap<Protocol, List<ResponseListener>> getResponseListeners() {
        return mResponseListeners;
    }

    private boolean isCmdAllowSendWhenSetWorkMode(Protocol protocol) {
        return protocol == Protocol.GET_WORKING_MODE_STATE || protocol == Protocol.SET_TIME
                || protocol == Protocol.GET_MAP_LIST;
    }

    public boolean sendCommand(Protocol protocol, ResponseListener listener, Object... params) {
        if (!isCmdAllowSendWhenSetWorkMode(protocol) && !isSocketConnected) {
            Log.d(TAG, " chassis appear error, send command failed, Protocol = " + protocol.name());
            Log.e(TAG, Log.getStackTraceString(new Throwable("workModeReady = " + mWorkModeState)));
            listener.onResponse(false, FAIL_NO_REASON, "workMode not ready = " + mWorkModeState);
            return false;
        }

        Log.d(TAG, "Command send : " + protocol.name() + "   params : " + params);
        registerResponseListener(protocol, listener);
        Message message = protocol.create(params);
        boolean status = connector.request(message);
        Log.d(TAG, "Command send status: " + status);

        if (status && protocol == Protocol.SET_WORKING_MODE) {
            //如果发送成功的是SET_WORKING_MODE，则将模式置为switching状态。
            setWorkModeState(WorkStateMode.CHASSIS_SWITCHING);
        }

        return status;
    }

    private boolean sendCommand(Message msg) {
        boolean status = connector.request(msg);
        Log.d(TAG, "Command send status: " + status);
        return status;
    }

    @Override
    public boolean setWorkingMode(WorkMode workMode, ResponseListener listener) {
        unregisterResponseListener(Protocol.SET_WORKING_MODE);
        return sendCommand(Protocol.SET_WORKING_MODE, listener, workMode.getValue());
    }

    @Override
    public boolean getWorkingMode(ResponseListener listener) {
        return sendCommand(Protocol.GET_WORKING_MODE_STATE, listener);
    }

    @Override
    public boolean getMotionMode(ResponseListener listener) {
        return sendCommand(Protocol.GET_MOTION_MODE, listener);
    }

    @Override
    public void updatePoseState(ResponseListener listener) {
        sendCommand(Protocol.GET_POSE_STATE, listener);
    }

    @Override
    public boolean getSystemInformation(ResponseListener listener) {
        return sendCommand(Protocol.GET_SYSTEM_INFORMATION, listener);
    }

    @Override
    public boolean setMotionMode(MotionMode motionMode, ResponseListener listener) {
        unregisterResponseListener(Protocol.SET_MOTION_MODE);
        return sendCommand(Protocol.SET_MOTION_MODE, listener, motionMode.getValue());
    }

    @Override
    public boolean setRoverConfig(RoverConfig roverConfig, ResponseListener listener) {
        return sendCommand(Protocol.SET_ROVER_CONFIG, listener, roverConfig);
    }

    @Override
    public boolean getRoverConfig(ResponseListener listener) {
        return sendCommand(Protocol.GET_ROVER_CONFIG, listener);
    }

    /**
     * 没有回调，依赖于主动上报判断是否成功
     * @param mode
     * @param x
     * @param y
     * @param t
     * @return
     */
    @Override
    public boolean setRelocate(RelocateMode mode, double x, double y, double t) {
        Message message = Protocol.SET_RELOCATE.create(mode, x, y, t);
        return sendCommand(message);
    }

    @Override
    public boolean sendPrimitiveMovingCommand(double angular, double liner) {
        Message message = Protocol.SEND_PRIMITIVE_MOVING_COMMAND.create(angular, liner);
        return sendCommand(message);
    }

    @Override
    public boolean performOprations(Queue<Operations.Oper> opers, ResponseListener listener) {
        Log.e(TAG, "performOperations : " + opers.size() + "  " + mOperations.size());
        Operations operations = new Operations(this, opers, listener);
        operations.start();

        synchronized (mOperations) {
            mOperations.add(operations);
        }
        return true;
    }

    public void removeOperations(Operations operations) {
        Log.e(TAG, "remove operations : " + mOperations.size());
        synchronized (mOperations) {
            mOperations.remove(operations);
        }
    }

    public void getLogFile(String path, String fileType, final ResponseListener listener) {
        final File logFile = MapFileHelper.buildLogFile(path, fileType);
        Log.d(TAG, "Command get log file : " + logFile.getAbsolutePath());

        switch (fileType) {
            case LOG_FILE:
            case SHOT_LOG:
                download(path, URL_FILE_NAVI, URL_GET_FILE_SUFFIX,
                        "", logFile, listener);
                break;

            case ERROR_LOG:
                download(path, URL_LOG_NAVI, URL_LOG_NAVI_SUFFIX,
                        "", logFile, listener);
                break;

            default:
                Log.d(TAG, "unknown file type");
                break;
        }
    }

    //pgm
    public void getGlobalMapPgm(String mapName, String naviMapName, final ResponseListener
            listener) {
        final File pgmFile = MapFileHelper.getMapPgm(mapName);
        Log.d(TAG, "Command get global map pgm : " + pgmFile.getAbsolutePath());
        download(naviMapName, URL_MAP_NAVI, URL_MAP_NAVI_SUFFIX,
                "pgm", pgmFile, listener);
    }

    //data
    public void getGlobalMapData(String mapName, String naviMapName, final ResponseListener
            listener) {
        final File dataFile = MapFileHelper.getMapData(mapName);
        Log.d(TAG, "Command get global map data : " + dataFile.getAbsolutePath());
        download(naviMapName, URL_MAP_NAVI, URL_MAP_NAVI_SUFFIX,
                "data", dataFile, listener);
    }

    public void download(String fileName, String main, String suffix,
                         String fileType, final File saveFile, final ResponseListener listener) {

        String url = buildUrl(fileName, main, fileType, suffix);
        Log.d(TAG, "download url: " + url);
        AsyncHttpGet getRequest = new AsyncHttpGet(url);
        getRequest.setTimeout(300 * 1000);
        AsyncHttpClient.getDefaultInstance().executeFile(getRequest,
                saveFile.getAbsolutePath(), new AsyncHttpClient.FileCallback() {
                    @Override
                    public void onCompleted(Exception e, AsyncHttpResponse response, File result) {
                        boolean status = false;
                        if (e != null || response.code() == 404) {
                            Log.d(TAG, "Command download file failed");
                            status = false;
                            MapFileHelper.deleteDirectory(saveFile);
                        } else {
                            Log.d(TAG, "Command download file succeed");
                            status = true;
                        }
                        listener.onResponse(status, status ? ResultCode.SUCCESS : FAIL_NO_REASON,
                                status ? saveFile.getAbsolutePath() : null);
                    }
                });
    }

    @NonNull
    private String buildUrl(String fileName, String main, String fileType, String suffix) {
        return "http://" + mNavIp + main + fileType + suffix + fileName;
    }

    public void addGlobalMapPgm(String mapName, String naviMapName, final ResponseListener
            listener) {
        Log.d(TAG, "Command add global map pgm");
        File pgmFile = MapFileHelper.getMapPgm(mapName);
        if (!pgmFile.exists()) {
            throw new RuntimeException("Map not exists");
        }
        upload(naviMapName, pgmFile, listener);
    }

    public void addGlobalMapData(String mapName, String naviMapName, ResponseListener listener) {
        Log.d(TAG, "Command add global map data");
        File dataFile = MapFileHelper.getMapData(mapName);
        if (!dataFile.exists()) {
            throw new RuntimeException("Map not exists");
        }
        upload(naviMapName, dataFile, listener);
    }

    private void upload(String fileName, File file, final ResponseListener listener) {
        String url = "http://" + mNavIp + URL_MAP_LOCAL + fileName;
        AsyncHttpPost post = new AsyncHttpPost(url);
        MultipartFormDataBody body = new MultipartFormDataBody();
        body.addFilePart(fileName, file);
        post.setBody(body);
        AsyncHttpClient.getDefaultInstance().execute(post, new HttpConnectCallback() {
            @Override
            public void onConnectCompleted(Exception e, AsyncHttpResponse response) {
                boolean status = false;
                if (e != null) {
                    Log.d(TAG, "Command upload file failed");
                    status = false;
                    e.printStackTrace();
                } else {
                    Log.d(TAG, "Command upload file succeed");
                    status = true;
                }
                listener.onResponse(status, status ? ResultCode.SUCCESS : FAIL_NO_REASON,
                        null);
            }
        });
    }
}
