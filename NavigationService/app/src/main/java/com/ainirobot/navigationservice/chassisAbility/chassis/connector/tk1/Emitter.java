/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.chassisAbility.chassis.connector.tk1;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import ninjia.android.proto.RoverPacketProtoWrapper.RoverPacketProto;

import static com.ainirobot.navigationservice.Defs.Def.CHANNEL_COMMAND;
import static com.ainirobot.navigationservice.Defs.Def.CHANNEL_EVENT;
import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

/**
 * The type emitter
 */
public class Emitter {

    private static final String TAG = TAGPRE + Emitter.class.getSimpleName();
    private ExecutorService mExecutor;
    private String name;
    private ChassisConnectTk1Impl connectApiTk1;

    public Emitter(String name, ChassisConnectTk1Impl connectApiTk1) {
        this.mExecutor = Executors.newCachedThreadPool();
        this.name = name;
        this.connectApiTk1 = connectApiTk1;
    }


    public void emit(RoverPacketProto packet) {
        if (packet == null) {
            return;
        }

        EmitRunnable runnable = new EmitRunnable(packet);
        mExecutor.submit(runnable);
    }

    private class EmitRunnable implements Runnable {
        RoverPacketProto packet;

        EmitRunnable(RoverPacketProto packet) {
            this.packet = packet;
        }

        @Override
        public void run() {
            switch (name) {
                case CHANNEL_COMMAND:
                    connectApiTk1.onEmitResponse(packet);
                    break;
                case CHANNEL_EVENT:
                    connectApiTk1.onEmitEvent(packet);
                    break;
                    default:
                        break;
            }
        }
    }
}
