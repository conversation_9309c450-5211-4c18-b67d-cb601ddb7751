package com.ainirobot.navigationservice.chassisAbility.controller;

import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.navigationservice.utils.LogUtils;

import java.util.Vector;

/**
 * Created by Orion on 2019/12/19.
 */
public class BaseAvoidPolicy implements IAvoid {
    protected static String TAG = BaseAvoidPolicy.class.getClass().getSimpleName();

    private Vector<AvoidObserver> mObservables = new Vector<>();
    protected volatile boolean mHasObstacle = false;
    protected volatile int mScore = OBSTACLES_SCORE_SAFE;

    /**
     * 机器人半径，
     * 招财豹 Pro 0.285
     * 招财豹 0.305
     * 小秘 0.244
     * mini 0.205 (100cm*41cm*41cm)
     */
    public static double ROBOT_RADIUS = 0.205f;

    /**
     * 障碍物危险度评估分数
     */
    public static final int OBSTACLES_SCORE_SAFE = 0;
    public static final int OBSTACLES_SCORE_DANGEROUS = 1;
    public static final int OBSTACLES_SCORE_PERILOUS = 2;

    //默认障碍物危险距离
    protected static final double DEFAULT_DANGEROUS_DISTANCE_LOW_SPEED = 0.3f;
    protected static final double DEFAULT_DANGEROUS_DISTANCE_FAST_SPEED = 0.4f;

    public BaseAvoidPolicy() {
        String model = RobotSettings.getProductModel();
        Log.d(TAG, "base avoid policy construct: product model=" + model);
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
            ROBOT_RADIUS = 0.264;
        }
        Log.d(TAG, "base avoid policy construct: ROBOT_RADIUS=" + ROBOT_RADIUS);
    }

    protected void onScoreResult(int score) {
        boolean obstacle = (score != OBSTACLES_SCORE_SAFE);
        LogUtils.printLog(LogUtils.TYPE_LASER, TAG,
                "Has obstacle=" + obstacle
                        + ", Pre state=" + mHasObstacle
                        + ", score=" + score,
                1000);

        if (score != mScore || obstacle != mHasObstacle) {
            this.mScore = score;
            mHasObstacle = obstacle;
            notifyAllObservers();
        }
    }

    public interface AvoidObserver {
        void onStateUpdate(boolean avoid, int score);
    }

    private void notifyAllObservers() {
        for (AvoidObserver observer : mObservables) {
            observer.onStateUpdate(mHasObstacle, mScore);
        }
    }

    public void addObserver(AvoidObserver observer) {
        if (mObservables.contains(observer)) {
            Log.e(TAG, "Already in observables");
            return;
        }
        mObservables.addElement(observer);
        notifyAllObservers();
    }

    public boolean removeObserver(AvoidObserver observer) {
        return mObservables.removeElement(observer);
    }

    public boolean getState() {
        return mHasObstacle;
    }

    public boolean getState(double distance) {
        return false;
    }

    public void setSafeDistance(double distance) {
    }

    public void resetSafeDistance() {
    }

    public int getScore() {
        return mScore;
    }

}
