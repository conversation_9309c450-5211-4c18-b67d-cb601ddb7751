package com.ainirobot.navigationservice.business.rpc;

import java.math.BigDecimal;

/**
 * speed and motion type check
 *
 * @version V1.0.0
 * @date 2019/11/15 15:00
 */
public class SpeedChecker {
    private static final SpeedBean STOPPED = new SpeedBean();

    public static boolean isAngularMotion(SpeedBean cur, SpeedBean target) {
        return isSingleAS(cur) && isSingleAS(target);
    }

    public static boolean isAngularToLinearMotion(SpeedBean cur, SpeedBean target) {
        return isSingleAS(cur) && isSingleLS(target);
    }

    public static boolean isLinearMotion(SpeedBean cur, SpeedBean target) {
        return isSingleLS(cur) && isSingleLS(target);
    }

    public static boolean isLinearToAngularMotion(SpeedBean cur, SpeedBean target) {
        return isSingleLS(cur) && isSingleAS(target);
    }

    public static boolean isMixMotion(SpeedBean cur, SpeedBean target) {
        return isMixSpeed(cur) || isMixSpeed(target);
    }

    public static boolean isMixSpeed(SpeedBean speed) {
        return !isSingleAS(speed) && !isSingleLS(speed);
    }

    public static boolean isSameDirectionLinearMotion(SpeedBean cur, SpeedBean target) {
        return isLinearMotion(cur, target)
                && isSameDirectionLinearSpeed(cur, target);
    }

    public static boolean isSameDirectionLinearSpeed(SpeedBean cur, SpeedBean target) {
        BigDecimal linearSpeed = cur.getLinearSpeed();
        BigDecimal angularSpeed = target.getAngularSpeed();
        return (linearSpeed.compareTo(BigDecimal.ZERO) < 0 && angularSpeed.compareTo(BigDecimal.ZERO) < 0)
                || (linearSpeed.compareTo(BigDecimal.ZERO) >= 0 && angularSpeed.compareTo(BigDecimal.ZERO) >= 0);
    }

    public static boolean isSingleAS(SpeedBean speed) {
        if (speed == null) {
            return false;
        }
        return speed.getLinearSpeed().compareTo(BigDecimal.ZERO) == 0;
    }

    public static boolean isSingleLS(SpeedBean speed) {
        if (speed == null) {
            return false;
        }
        return speed.getAngularSpeed().compareTo(BigDecimal.ZERO) == 0;
    }
}
