package com.ainirobot.navigationservice.utils;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

/**
 * Created by Orion on 2020/12/7.
 */
public class ThreadUtils {
    private static final String TAG = ThreadUtils.class.getSimpleName();

    private static final int CPU_COUNT = Runtime.getRuntime().availableProcessors();

    private static ExecutorService sCpuService = Executors.newFixedThreadPool(CPU_COUNT + 1,
            new ThreadFactory() {
                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r);
                    thread.setName("ThreadUtils:CPU:");
                    return thread;
                }
            });

    private static ExecutorService sIoService = Executors.newFixedThreadPool(2 * CPU_COUNT + 1,
            new ThreadFactory() {
                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r);
                    thread.setName("ThreadUtils:IO:");
                    return thread;
                }
            });

    public static ExecutorService getCpuService() {
        return sCpuService;
    }

    public static ExecutorService getIoService() {
        return sIoService;
    }

}
