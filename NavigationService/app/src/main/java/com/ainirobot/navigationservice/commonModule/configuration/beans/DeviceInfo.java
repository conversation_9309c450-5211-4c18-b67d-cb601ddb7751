/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.navigationservice.commonModule.configuration.beans;

import android.util.Log;

import androidx.annotation.NonNull;

import com.ainirobot.navigationservice.utils.MapUtils;

import java.util.Properties;
import java.lang.reflect.Field;

// 相机字段描述，参考文档 https://orionstar.feishu.cn/docx/OCFbd37BNoK9ScxAJ9OcQ3WKnie
// 结构参数描述，参考文档 https://orionstar.feishu.cn/docx/NjCnd19HRosbY8xh6R8cAi8znog
public class DeviceInfo {
    private static final String TAG = DeviceInfo.class.getSimpleName();
    @DeviceProperty("TopCamera")
    private TopCameraType topCamera;
    @DeviceProperty("FrontCamera")
    private FrontCameraType frontCamera;
    @DeviceProperty("BackCamera")
    private BackCameraType backCamera;
    @DeviceProperty("MajorDepth")
    private MajorDepthType majorDepth;
    @DeviceProperty("DownDepth")
    private DownDepthType downDepth;
    @DeviceProperty("Structure")
    private StructureType structure;

    public enum TopCameraType {
        HP_636d_636c, //双通 topMono
        CX_6369_0c45,
        HP_6369_0c45
    }

    public enum FrontCameraType {
        HP_6341_0c45, KMJ_2076_0edc, HY_0b15_1bcf
    }

    public enum BackCameraType {
        HP_6340_0c45
    }

    public enum MajorDepthType {
        YXSK_WF, ASJ_XB40, ASJ_XB100, INTEL_RS430
    }

    public enum DownDepthType {
        ASJ_XB100, SY_HST007
    }

    //10.1以后支持
    public enum StructureType {
        // 招财豹Base
        // 1. 招财豹原始版本
        //    Structure = Waiter
        // 2. 招财豹减震版
        //    Structure = Waiter_SA
        // 3. 招财豹减震加高版
        //    Structure = Waiter_SA_H
        // 4. 招财豹减震加高加宽版
        //    Structure = Waiter_SA_H_LW
        // 5. 招财豹减震加宽版
        //    Structure = Waiter_SA_LW
        Waiter, Waiter_SA, Waiter_SA_H, Waiter_SA_H_LW, Waiter_SA_LW,
        // 招财豹PRO Base
        // 1. 招财豹PRO基础
        //    Structure = WaiterPro
        // 2. 豹小密2
        //    Structure = Meissa2
        // 3. 电动门
        //    Structure = WaiterPro_EGate
        // 4. 顶部mono
        //    Structure = WaiterPro_TopMono
        // 5. 招财豹Pro Carry
        //    Structure = WaiterPro_Carry
        // 6. 招财豹工厂头部收缩版本，解决65cm通过性问题
        //    Structure = WaiterPro_Carry_HS
        WaiterPro, Meissa2, WaiterPro_EGate, WaiterPro_TopMono, WaiterPro_Carry, WaiterPro_Carry_HS,
        // MINI Base
        // 1. Mini基础
        //    Structure = Mini2
        // 2. Mini 二供mono（修改了imu位置）
        //    Structure = Mini2_SSMono
        Mini2, Mini2_SSMono, Mini2_TopMono,
        // Slim Base
        // 1. Slim基础
        //    Structure = Slim
        // 2. Slim电动门
        //    Structure = Slim_EGate
        Slim, Slim_EGate
    }

    public TopCameraType getTopCamera() {
        return topCamera;
    }

    public void setTopCamera(TopCameraType topCamera) {
        this.topCamera = topCamera;
    }

    public FrontCameraType getFrontCamera() {
        return frontCamera;
    }

    public void setFrontCamera(FrontCameraType frontCamera) {
        this.frontCamera = frontCamera;
    }

    public BackCameraType getBackCamera() {
        return backCamera;
    }

    public void setBackCamera(BackCameraType backCamera) {
        this.backCamera = backCamera;
    }

    public MajorDepthType getMajorDepth() {
        return majorDepth;
    }

    public void setMajorDepth(MajorDepthType majorDepth) {
        this.majorDepth = majorDepth;
    }

    public DownDepthType getDownDepth() {
        return downDepth;
    }

    public void setDownDepth(DownDepthType downDepth) {
        this.downDepth = downDepth;
    }

    public StructureType getStructure() {
        return structure;
    }

    public void setStructure(StructureType structure) {
        this.structure = structure;
    }

    public boolean isNewCameraInfo() {
        return !(topCamera == null && frontCamera == null && backCamera == null && majorDepth == null && downDepth == null);
    }

    public boolean isEmptyDeviceInfo() {
        return topCamera == null && frontCamera == null && backCamera == null && majorDepth == null && downDepth == null && structure == null;
    }

    public boolean hasStructureType() {
        return structure != null;
    }

    public static DeviceInfo fromProperties(Properties properties) {
        DeviceInfo cameraInfo = new DeviceInfo();

        for (Field field : DeviceInfo.class.getDeclaredFields()) {
            if (field.isAnnotationPresent(DeviceProperty.class) && field.getType().isEnum()) {
                DeviceProperty deviceProperty = field.getAnnotation(DeviceProperty.class);
                String fieldName = deviceProperty.value();
                String value = properties.getProperty(fieldName);
                Log.d(TAG, "fieldName:" + fieldName + ", value:" + value);
                if (value != null) {
                    try {
//                        Class<? extends Enum<?>> enumType = (Class<? extends Enum<?>>) field.getType();
//                        Enum<?> type = Enum.valueOf(enumType, value);
                        Enum<?> type = Enum.valueOf((Class<? extends Enum>) field.getType(), value);
                        field.set(cameraInfo, type);
                    } catch (IllegalAccessException | IllegalArgumentException |
                             NullPointerException e) {
//                        e.printStackTrace();
                        Log.e(TAG, "fieldName:" + fieldName
                                + ", value:" + value + "  is not an enum type.");
                    }
                }
            }
        }
        return cameraInfo;
    }

    @NonNull
    @Override
    public String toString() {
        return "DeviceInfo:{" +
                "topCamera=" + topCamera +
                ", frontCamera=" + frontCamera +
                ", backCamera=" + backCamera +
                ", majorDepth=" + majorDepth +
                ", downDepth=" + downDepth +
                ", structure=" + structure +
                "}";
    }
}