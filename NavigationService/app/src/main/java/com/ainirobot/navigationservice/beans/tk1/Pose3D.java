/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.tk1;

import java.io.DataOutput;
import java.io.IOException;

/**
 * This represents a pose in free space, composed of position and orientation.
 */
public class Pose3D extends Message {

    private Point position;
    private Quaternion orientation;

    public Pose3D() {
        this(new Point(), new Quaternion());
    }

    public Pose3D(Point position, Quaternion orientation) {
        super(Message.MSG_TYPE_POSE);
        this.position = position;
        this.orientation = orientation;
    }

    public Point getPosition() {
        return position;
    }

    public Quaternion getOrientation() {
        return orientation;
    }

    @Override
    public void writeTo(DataOutput out) throws IOException {
        position.writeTo(out);
        orientation.writeTo(out);
    }

    @Override
    public void readData(ReverseInputStream in) throws IOException {
        position = new Point();
        position.readData(in);

        orientation = new Quaternion();
        orientation.readData(in);
    }
}
