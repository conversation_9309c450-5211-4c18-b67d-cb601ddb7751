package com.ainirobot.navigationservice.db.helper.iml;

import com.ainirobot.navigationservice.db.entity.PlaceInfo;

import java.util.List;

public interface PlaceInfoHelperIml extends BaseHelper<PlaceInfo> {
    PlaceInfo getPlaceById(String placeId);

    List<PlaceInfo> getPlaceInfoByMapName(String mapName);

    List<PlaceInfo> getPlaceInfoByMapName(String[] mapNames);

    String[] getPlaceIdByMapName(String mapName);

    List<PlaceInfo> getPlaceInfos(String mapName, String[] placeIdsInName);

    String[] getStrings(String[] placeIds, String mapName);

    List<PlaceInfo> getPlaceInfoNonHighPriority(String mapName);

    List<PlaceInfo> getPlaceInfoHighPriority(String mapName);

    void updateTimeOneOfIdArr(String[] idArr);

    boolean updatePlaceInfo(List<PlaceInfo> placeList);

    void updatePlaceInfo(PlaceInfo placeInfo);

    void initPlaceInfoData(List<PlaceInfo> placeList);

    void deletePlaceByPlaceIds(String[] placeIdArr);

    String[] deletePlaceInfo(String mapName, String[] placeIds);

    String[] deletePlaceByMapName(String mapName);

    String[] deletePlaceByTypeId(String mapName, int typeId, int priority);

    PlaceInfo getPlaceByType(int typeId, int priority, String mapName);

    PlaceInfo getPlaceByTypeIdAndPriority(int typeId, int priority, String mapName);

    List<PlaceInfo> getPlacesByType(int typeId, String mapName);

    List<PlaceInfo> getPlacesByTypeArr(int[] typeId, String mapName);

    void connectTablePlaceType();
}
