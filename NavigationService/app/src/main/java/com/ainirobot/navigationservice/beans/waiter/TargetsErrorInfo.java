package com.ainirobot.navigationservice.beans.waiter;

public class TargetsErrorInfo {

    private int id;
    /**
     *   kUNKNOW = 100;                   //未知
     *   kOberserveLess = 0;            // 观测次数太少
     *   kReprojectionErrorLarge = 1;   // 优化后重投影误差较大
     *   kTiltAngleLarge = 2;           // Target或平面特征与全局坐标xy平面倾角过大
     *   kTagRepeated = 3;              // Tag 重复
     *   kTagPoorQuality = 4;           // 粘贴质量或者Tag出厂质量不好gitk
     */
    private int errorCode;
    private String errorMsg;

    public TargetsErrorInfo(){

    }

    public TargetsErrorInfo(int id, int errorCode, String errorMsg){
        this.id = id;
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    @Override
    public String toString() {
        return "TargetsErrorInfo{" +
                "id=" + id +
                ", errorCode=" + errorCode +
                ", errorMsg='" + errorMsg + '\'' +
                '}';
    }
}
