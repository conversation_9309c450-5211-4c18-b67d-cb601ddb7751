package com.ainirobot.navigationservice.utils;

import android.util.Log;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;


public class DistanceUtils {
    private static final String TAG = TAGPRE + DistanceUtils.class.getSimpleName();
    private static int calculationCount = 0;
    private static double oneY;
    private static double oneX;
    private static int pushMaxDistance = 2;

    /**
     * mLinearSpeed == 0 遥控模式
     * CREATING_MAP 建图模式
     * isNavigationing 非导航状态
     */
    public static void pushRobotTooFarAway(double x, double y, MoveListener moveListener) {
        ++calculationCount;
        if (calculationCount == 1) {
            oneX = x;
            oneY = y;
        }
        //Log.i(TAG, "pushRobotTooFarAway: calculationCount " + calculationCount);
        if (calculationCount == 50) {
            int distance = calculationDistance(x, y);
            if (pushMaxDistance <= distance) {
                //5秒内被移动了2米
                moveListener.pushExceedDistance(String.valueOf(distance));
            }
            calculationCount = 0;
            oneX = 0;
            oneY = 0;
        }
    }

    private static int calculationDistance(double x, double y) {
        Log.i(TAG, "calculationDistance: oneX " + oneX + " oneY " + oneY + " x "+ x+ " y "+ y);

        int distance = (int) Math.sqrt(Math.pow(Math.abs(oneX - x), 2) + Math.pow(Math.abs(oneY - y), 2));
        Log.i(TAG, "calculationDistance: distance " + distance);
        return distance;
    }

    public static void resetCalculationCount(){
        Log.i(TAG, "resetCalculationCount: cur " +calculationCount);
        calculationCount = 0;
    }


    public interface MoveListener {
        void pushExceedDistance(String distance);
    }

}
