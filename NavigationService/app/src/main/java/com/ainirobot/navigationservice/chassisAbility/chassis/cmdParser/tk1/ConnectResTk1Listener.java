package com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1;

import android.util.Log;

import com.ainirobot.navigationservice.beans.tk1.WorkStateMode;
import com.ainirobot.navigationservice.chassisAbility.chassis.connector.tk1.IChassisConnect;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.IChassisCommand.ResponseListener;
import com.google.protobuf.ByteString;
import com.google.protobuf.Message;

import java.util.List;

import ninjia.android.proto.RoverPacketProtoWrapper.RoverPacketProto;

import static com.ainirobot.navigationservice.Defs.Def.ResultCode.SUCCESS;
import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

public class ConnectResTk1Listener implements IChassisConnect.ResListener {
    private static final String TAG = TAGPRE + ChassisCommandTk1Impl.class.getSimpleName();

    private ChassisCommandTk1Impl cmdTk1;

    public ConnectResTk1Listener(ChassisCommandTk1Impl cmdTk1) {
        this.cmdTk1 = cmdTk1;
    }

    @Override
    public void onResponse(Message message) {
        if (message instanceof RoverPacketProto) {
            RoverPacketProto packet = (RoverPacketProto) message;
            String event = packet.getHeader().getCode().name();
            boolean status = packet.getHeader().getStatus();
            int resultCode = packet.getHeader().getResult();
            String params = packet.getHeader().getText();
            ByteString unknownFields = packet.getUnknownFields().toByteString();
            ByteString content = packet.getBody().concat(unknownFields);
            Protocol protocol = Protocol.valueOf(event);
            Log.d(TAG, "onResponse in = " + protocol);

            if (resultCode == SUCCESS && protocol == Protocol.SET_WORKING_MODE) {
                cmdTk1.setWorkModeState(WorkStateMode.CHASSIS_READY);
            }

            if (!cmdTk1.getResponseListeners().containsKey(protocol)) {
                Log.d(TAG, "onResponse no listener");
                return;
            }

            List<ResponseListener> list = cmdTk1.getResponseListeners().remove(protocol);
            for (ResponseListener listener : list) {
                listener.onResponse(status, resultCode, protocol.parse(params, content));
            }
        }
    }
}
