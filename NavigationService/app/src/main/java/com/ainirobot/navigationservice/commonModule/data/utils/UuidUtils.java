package com.ainirobot.navigationservice.commonModule.data.utils;

import android.util.Log;

import com.ainirobot.navigationservice.db.entity.LocalPlaceInfo;
import com.ainirobot.navigationservice.db.entity.PlaceInfo;

import java.util.Random;
import java.util.UUID;

/**
 * @version V1.0.0
 * @date 2020/8/4 20:31
 */
public class UuidUtils {

    private static final String TAG = "UuidUtils";
    public static String createPlaceId(PlaceInfo placeInfo) {
        Log.i(TAG, "createPlaceId: " + placeInfo);
        return UUID.nameUUIDFromBytes((placeInfo.getMapName()
                + placeInfo.getPointX()
                + placeInfo.getPointY()
                + placeInfo.getPointTheta()
                + getRandomChar(14)
                + System.currentTimeMillis()).
                getBytes()).toString().replace("-", "");

    }

    public static String createMapId(String mapName) {
        Log.i(TAG, "createMapId: " + mapName);
        return UUID.nameUUIDFromBytes((mapName + System.currentTimeMillis()+"sn").getBytes())
                .toString()
                .replace("-", "");

    }

    //生成随机字符串
    public static String getRandomChar(int length) {
        char[] chr = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J',
                'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T',
                'U', 'V', 'W', 'X', 'Y', 'Z'};
        Random random = new Random();
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < length; i++) {
            buffer.append(chr[random.nextInt(36)]);
        }
        return buffer.toString();
    }

    public static String createLocalPlaceId(LocalPlaceInfo placeInfo) {
        Log.i(TAG, "createPlaceId: " + placeInfo);
        return UUID.nameUUIDFromBytes((placeInfo.getMapName()
                + placeInfo.getPointX()
                + placeInfo.getPointY()
                + placeInfo.getPointTheta()
                + getRandomChar(14)
                + System.currentTimeMillis()).
                getBytes()).toString().replace("-", "");

    }
}
