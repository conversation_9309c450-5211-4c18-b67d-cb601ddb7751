package com.ainirobot.navigationservice.Defs;

public class NavigationResult {

    private static final int BASE_NAVIGATION_RESULT = 30010000;
    public static final int RESULT_NAVI_EVENT_RESULT_ARRIVED = BASE_NAVIGATION_RESULT +1;
    public static final int RESULT_NAVI_EVENT_RESULT_ABORTED = BASE_NAVIGATION_RESULT +2;
    public static final int RESULT_NAVI_EVENT_RESULT_CANCELED = BASE_NAVIGATION_RESULT +3;
    public static final int RESULT_NAVI_EVENT_RESULT_FAILED = BASE_NAVIGATION_RESULT +4;
    public static final int RESULT_NAVI_EVENT_RESULT_REPLACE = BASE_NAVIGATION_RESULT +5;
    /**
     * Lora多机，导航优先级设置成功。
     */
    public static final int RESULT_NAVI_SET_PRIORITY_SUCCESS = BASE_NAVIGATION_RESULT +6;
    /**
     * Lora多机导航优先级设置失败
     */
    public static final int RESULT_NAVI_SET_PRIORITY_FAILED = BASE_NAVIGATION_RESULT +7;
    /**
     * 多机导航未打开巡线开关
     */
    public static final int RESULT_NAVI_LINE_TRACKING_CLOSED = BASE_NAVIGATION_RESULT +8;
}
