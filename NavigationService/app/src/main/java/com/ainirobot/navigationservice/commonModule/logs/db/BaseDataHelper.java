package com.ainirobot.navigationservice.commonModule.logs.db;


import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;


public abstract class BaseDataHelper {
    private Context mContext;

    BaseDataHelper(Context context) {
        mContext = context;
    }

    public Context getContext() {
        return mContext;
    }

    public abstract SQLiteTable getTable();

    private Uri getContentUri() {
        return getTable().getContentUri();
    }

    public void notifyChange() {
        mContext.getContentResolver().notifyChange(getContentUri(), null);
    }

    private final Cursor query(String[] projection, String selection, String[] selectionArgs,
                               String sortOrder) {
        return mContext.getContentResolver().query(getContentUri(), projection, selection,
                selectionArgs, sortOrder);
    }

    final Cursor query(String selection, String[] selectionArgs) {
        return query(null, selection, selectionArgs, null);
    }

    final Uri insert(ContentValues values) {
        return mContext.getContentResolver().insert(getContentUri(), values);
    }

    public final int bulkInsert(ContentValues[] values) {
        try {
            return mContext.getContentResolver().bulkInsert(getContentUri(), values);
        } catch (Exception e) {
            return -1;
        }
    }

    final int update(ContentValues values, String where, String[] whereArgs) {
        return mContext.getContentResolver().update(getContentUri(), values, where, whereArgs);
    }

    final int delete(String selection, String[] selectionArgs) {
        return mContext.getContentResolver().delete(getContentUri(), selection, selectionArgs);
    }

    public final int deleteAll(String tableName) {
        synchronized (LogDataProvider.DBLock) {
            LogDataProvider.DBHelper mHelper = LogDataProvider.getDBHelper();
            SQLiteDatabase db = mHelper.getWritableDatabase();
            return db.delete(tableName, null, null);
        }
    }

}
