package com.ainirobot.navigationservice.business.rpc.algorithm;

import com.ainirobot.navigationservice.business.rpc.SpeedBean;

/**
 * Angular To Linear motion
 *
 * @version V1.0.0
 * @date 2019/11/15 15:00
 */
public class AngularToLinearMotion extends MotionAlgorithm {

    public AngularToLinearMotion(SpeedBean target) {
        super(target);
    }

    @Override
    public void motion() {
        SpeedBean targetSpeed = target;
        AngularMotion angularMotion = new AngularMotion(new SpeedBean());
        angularMotion.motion();
        LinearMotion linearMotion = new LinearMotion(targetSpeed);
        linearMotion.motion();
    }
}
