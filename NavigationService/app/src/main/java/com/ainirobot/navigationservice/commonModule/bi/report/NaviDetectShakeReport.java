package com.ainirobot.navigationservice.commonModule.bi.report;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class NaviDetectShakeReport extends BaseBiReport {
    private final static String TABLE_NAME = "gb_chassis_statistics";
    private final static String CTIME = "ctime";
    private final static String TYPE = "type";
    private final static String DATA_DES = "bump";
    public NaviDetectShakeReport() {
            super(TABLE_NAME);
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        addData(TYPE, DATA_DES);
        super.report();
    }
}
