/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file elinearcept in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either elinearpress or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.tk1;
import java.io.DataOutput;
import java.io.IOException;


public class NavVelocity extends Message {

    private double linear, angular;

    public NavVelocity() {
        this(Message.MSG_TYPE_NAV_VELOCITY);
    }

    public NavVelocity(int type, double linear, double angular) {
        super(type);
        this.linear = linear;
        this.angular = angular;
    }

    public NavVelocity(double linear, double angular) {
        this(Message.MSG_TYPE_NAV_VELOCITY, linear, angular);
    }

    public NavVelocity(int type) {
        super(type);
    }

    public double getLinear() {
        return linear;
    }

    public double getAngular() {
        return angular;
    }

    public void setLinear(double linear){
        this.linear = linear;
    }

    public void setAngular(double angular){
        this.angular = angular;
    }

    @Override
    public void readData(ReverseInputStream in) throws IOException {
        linear = in.readDouble();
        angular = in.readDouble();
    }

    @Override
    public void writeTo(DataOutput out) throws IOException {
        out.writeDouble(linear);
        out.writeDouble(angular);
    }

    @Override
    public String toString() {
        return "linear=" + linear + "  angular=" + angular;
    }

}
