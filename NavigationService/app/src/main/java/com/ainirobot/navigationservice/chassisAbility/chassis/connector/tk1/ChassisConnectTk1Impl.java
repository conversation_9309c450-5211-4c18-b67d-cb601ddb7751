package com.ainirobot.navigationservice.chassisAbility.chassis.connector.tk1;

import android.util.Log;

import com.ainirobot.navigationservice.commonModule.configuration.ConfigManager;
import com.ainirobot.navigationservice.utils.NavigationConfig;
import com.google.protobuf.Message;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import static com.ainirobot.navigationservice.Defs.Def.CHANNEL_COMMAND;
import static com.ainirobot.navigationservice.Defs.Def.CHANNEL_EVENT;
import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

public class ChassisConnectTk1Impl implements IChassisConnect {
    private final static String TAG = TAGPRE + ChassisConnectTk1Impl.class.getSimpleName();

    private Channel mCommandChannel;
    private Channel mEventChannel;
    private OnConnectListener mConnectListener;//连接成功通知
    private ConcurrentMap<String, Boolean> mChannelStatus;//Channel状态记录
    private EventListener mEventListener;//所有Event转换为protoBuf上报
    private ResListener mResListener;//所有response转换为protoBuf上报
    private String mNavIp;


    public ChassisConnectTk1Impl() {
        mNavIp = ConfigManager.getInstance().getDeviceIP();
        mChannelStatus = new ConcurrentHashMap<>();
        mCommandChannel = newChannel(CHANNEL_COMMAND);
        mEventChannel = newChannel(CHANNEL_EVENT);
    }

    @Override
    public void init() {
        if (mCommandChannel != null && mEventChannel != null) {
            mCommandChannel.start();
            mEventChannel.start();
        } else {
            throw new NullPointerException("Tk1 connect channel is null");
        }
    }

    private Channel newChannel(final String channelName) {
        Channel channel = new Channel(channelName, new Emitter(channelName, this), mNavIp);
        channel.setChannelListener(new Channel.OnChannelListener() {
            @Override
            public void onConnected() {
                mChannelStatus.put(channelName, true);
                if (mConnectListener != null
                        && getChannelState(CHANNEL_COMMAND)
                        && getChannelState(CHANNEL_EVENT)) {
                    mConnectListener.onConnected();
                }
            }

            @Override
            public void onDisconnected() {
                mChannelStatus.put(channelName, false);
                mConnectListener.onDisconnected(channelName);
            }
        });
        return channel;
    }

    private boolean getChannelState(String channelName) {
        if (mChannelStatus.containsKey(channelName)) {
            return mChannelStatus.get(channelName);
        }
        return false;
    }

    @Override
    public boolean request(Message message) {
        boolean sendRslt =  (mCommandChannel != null && mCommandChannel.sendMessage(message));
        Log.d(TAG, "request send " + sendRslt);
        return sendRslt;
    }

    @Override
    public void registerEventListener(EventListener listener) {
        addEventListener(listener);
    }

    private synchronized void addEventListener(EventListener listener) {
        mEventListener = listener;
    }

    @Override
    public synchronized void registerConnectListener(OnConnectListener listener) {
        mConnectListener = listener;
    }

    @Override
    public synchronized void registerResponseListener(ResListener listener) {
        mResListener = listener;
    }


    public void onEmitResponse(Message resMsg) {
        if (mResListener != null) {
            mResListener.onResponse(resMsg);
        }
    }

    public void onEmitEvent(Message eventMsg) {
        if (mEventListener != null) {
            mEventListener.onEvent(eventMsg);
        }
    }
}
