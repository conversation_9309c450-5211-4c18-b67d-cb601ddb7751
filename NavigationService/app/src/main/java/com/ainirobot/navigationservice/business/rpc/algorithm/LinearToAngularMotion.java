package com.ainirobot.navigationservice.business.rpc.algorithm;

import com.ainirobot.navigationservice.business.rpc.SpeedBean;

/**
 * Linear To Angular motion Algorithm
 *
 * @version V1.0.0
 * @date 2019/11/15 15:00
 */
public class LinearToAngularMotion extends MotionAlgorithm {
    public LinearToAngularMotion(SpeedBean target) {
        super(target);
    }

    @Override
    public void motion() {
        SpeedBean targetSpeed = target;
        LinearMotion linearMotion = new LinearMotion(new SpeedBean());
        linearMotion.motion();
        AngularMotion angularMotion = new AngularMotion(targetSpeed);
        angularMotion.motion();
    }
}
