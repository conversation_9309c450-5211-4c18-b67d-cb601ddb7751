package com.ainirobot.navigationservice.db.helper.sqlite;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import com.ainirobot.navigationservice.db.entity.PlaceName;
import com.ainirobot.navigationservice.db.helper.iml.PlaceNameHelperIml;
import com.ainirobot.navigationservice.db.sqlite.TableInfoDef;
import com.ainirobot.navigationservice.db.sqlite.SqliteDbMigrate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class PlaceNameSqliteHelper extends BaseSqliteHelper<PlaceName> implements PlaceNameHelperIml {
    public PlaceNameSqliteHelper(SqliteDbMigrate sqliteDbMigrate) {
        super(sqliteDbMigrate, TableInfoDef.TABLE_NAME_PLACE_NAME);
    }

    @Override
    protected Map<String, Integer> updateCursorIndexMap(Cursor cursor) {
        return sqliteDbMigrate.getPlaceNameIndex(cursor);
    }

    @Override
    public String[] getPlaceIdsByName(String placeName) {
        SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
        List<String> placeIds = new ArrayList<>();
        try (Cursor cursor = readDb.query(mTableName, new String[]{TableInfoDef.COLUMN_PLACE_ID}, TableInfoDef.COLUMN_PLACE_NAME + " = ?", new String[]{placeName}, null, null, null)) {
            if (cursor != null) {
                int placeIdIndex = getCursorIndex(getCursorIndexMap(cursor), cursor, TableInfoDef.COLUMN_PLACE_ID);
                while (cursor.moveToNext()) {
                    placeIds.add(cursor.getString(placeIdIndex));
                }
            }
        }
        return placeIds.toArray(new String[0]);
    }

    @Override
    public String[] getPlaceIdsByNameList(String[] nameArr) {
        SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
        List<String> placeIds = new ArrayList<>();
        Cursor cursor = null;
        try {
            StringBuilder placeholders = new StringBuilder();
            for (int i = 0; i < nameArr.length; i++) {
                placeholders.append("?");
                if (i < nameArr.length - 1) {
                    placeholders.append(",");
                }
            }
            cursor = readDb.query(mTableName, new String[]{TableInfoDef.COLUMN_PLACE_ID}, TableInfoDef.COLUMN_PLACE_NAME + " IN (" + placeholders + ")", nameArr, null, null, null);
            if (cursor != null) {
                int placeIdIndex = getCursorIndex(getCursorIndexMap(cursor), cursor, TableInfoDef.COLUMN_PLACE_ID);
                while (cursor.moveToNext()) {
                    placeIds.add(cursor.getString(placeIdIndex));
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return placeIds.toArray(new String[0]);
    }

    @Override
    public List<PlaceName> getPlaceNameByPlaceId(String[] placeIds) {
        SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
        List<PlaceName> placeNames = new ArrayList<>();
        Cursor cursor = null;
        try {
            StringBuilder placeholders = new StringBuilder();
            for (int i = 0; i < placeIds.length; i++) {
                placeholders.append("?");
                if (i < placeIds.length - 1) {
                    placeholders.append(",");
                }
            }
            cursor = readDb.query(mTableName, null, TableInfoDef.COLUMN_PLACE_ID + " IN (" + placeholders + ")", placeIds, null, null, null);
            if (cursor != null) {
                Map<String, Integer> map = getCursorIndexMap(cursor);
                while (cursor.moveToNext()) {
                    PlaceName placeName = sqliteDbMigrate.cursorToPlaceName(cursor, map);
                    placeNames.add(placeName);
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        Log.d(TAG, "getPlaceNameByPlaceId: " + placeNames.size() + " records found.");
        return placeNames;
    }

    @Override
    public void updatePlace(String[] idArr, List<String> newNameList, String language) {
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        writeDb.beginTransaction();
        try {
            for (int i = 0; i < idArr.length; i++) {
                ContentValues values = new ContentValues();
                values.put(TableInfoDef.COLUMN_PLACE_NAME, newNameList.get(i));
                values.put(TableInfoDef.COLUMN_LANGUAGE_TYPE, language);
                // 尝试更新数据
                int rowsUpdated = writeDb.update(
                        mTableName,
                        values,
                        TableInfoDef.COLUMN_PLACE_ID + " = ?",
                        new String[]{idArr[i]});
                // 如果更新失败（没有记录被更新），尝试插入新记录
                if (rowsUpdated <= 0) {
                    values.put(TableInfoDef.COLUMN_PLACE_ID, idArr[i]); // 插入时需要包含 PLACE_ID
                    long rowId = writeDb.insert(mTableName, null, values
                    );
                    if (rowId == -1) {
                        Log.d(TAG, "updatePlace: insert failed for placeId = " + idArr[i]);
                    } else {
                        Log.d(TAG, "updatePlace: insert succeeded for placeId = " + idArr[i]);
                    }
                } else {
                    Log.d(TAG, "updatePlace: update succeeded for placeId = " + idArr[i]);
                }
            }
            writeDb.setTransactionSuccessful();
        } finally {
            writeDb.endTransaction();
        }
    }

    @Override
    public void initPlaceNameData(List<PlaceName> nameList) {
        updatePlaceNames(nameList);
    }

    @Override
    public boolean updatePlaceNames(List<PlaceName> placeNames) {
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        writeDb.beginTransaction();
        boolean allUpdated = true;
        try {
            for (PlaceName placeName : placeNames) {
                ContentValues values = sqliteDbMigrate.placeNameToContentValues(placeName);
                // 尝试更新数据
                int rowsUpdated = writeDb.update(
                        mTableName,
                        values,
                        TableInfoDef.COLUMN_PLACE_ID + " = ? AND " + TableInfoDef.COLUMN_LANGUAGE_TYPE + " = ?",
                        new String[]{placeName.getPlaceId(), placeName.getLanguageType()});
                // 如果更新失败（没有记录被更新），尝试插入新记录
                if (rowsUpdated <= 0) {
                    long rowId = writeDb.insert(mTableName, null, values);
                    if (rowId == -1) {
                        allUpdated = false;
                        Log.d(TAG, "updatePlaceNames: insert failed for placeId = " + placeName.getPlaceId());
                    } else {
                        Log.d(TAG, "updatePlaceNames: insert succeeded for placeId = " + placeName.getPlaceId());
                    }
                } else {
                    Log.d(TAG, "updatePlaceNames: update succeeded for placeId = " + placeName.getPlaceId());
                }
            }
            if (allUpdated) {
                writeDb.setTransactionSuccessful();
            }
        } catch (Exception e) {
            Log.e(TAG, "updatePlaceNames: exception occurred", e);
            allUpdated = false;
        } finally {
            writeDb.endTransaction();
        }
        return allUpdated;
    }


    @Override
    public void deletePlaceNameByPlaceId(String[] placeIds) {
        if (placeIds == null || placeIds.length == 0) {
            Log.d(TAG, "deletePlaceNameByPlaceId: placeIds array is null or empty");
            return;
        }
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        int rowsDeleted = 0;
        writeDb.beginTransaction();
        try {
            StringBuilder whereClause = new StringBuilder(TableInfoDef.COLUMN_PLACE_ID + " IN (");
            for (int i = 0; i < placeIds.length; i++) {
                whereClause.append("?");
                if (i < placeIds.length - 1) {
                    whereClause.append(",");
                }
            }
            whereClause.append(")");
            // 执行删除操作
            rowsDeleted = writeDb.delete(mTableName, whereClause.toString(), placeIds);
            writeDb.setTransactionSuccessful();
        } catch (Exception e) {
            Log.e(TAG, "deletePlaceNameByPlaceId: exception occurred", e);
        } finally {
            writeDb.endTransaction();
        }
        Log.d(TAG, "deletePlaceNameByPlaceId: " + rowsDeleted);
    }

    @Override
    public boolean hasPlaceNameData() {
        return false;
    }

    private int getCursorIndex(Map<String, Integer> indexMap, Cursor cursor, String columnName) {
        Integer index;
        if (indexMap != null && (index = indexMap.get(columnName)) != null) {
            return index;
        }
        return cursor.getColumnIndex(columnName);
    }
}