package com.ainirobot.navigationservice.commonModule.bi.report;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class BiCorrectLocationReport extends BaseBiReport {
    private static final String TABLE_NAME = "base_robot_vision_correct_localtion";
    private static final String GUESS_POSE = "guess_pose";
    private static final String VISION_POSE = "vision_pose";
    private static final String VISION_REFINE_POSE = "vision_refine_pose";
    private static final String LOCATE_TYPE = "locate_type";
    private static final String LOST_DISTANCE = "lost_distance";
    private static final String ARG1 = "arg1";
    private static final String ARG2 = "arg2";
    private static final String ARG3 = "arg3";
    private static final String ARG4 = "arg4";
    private static final String MESSAGE = "message";

    private static final String CTIME = "ctime";

    public BiCorrectLocationReport() {
        super(TABLE_NAME);
    }

    public BiCorrectLocationReport addGuessPose(String poseInfo) {
        addData(GUESS_POSE, poseInfo);
        return this;
    }

    public BiCorrectLocationReport addVisionPose(String poseInfo) {
        addData(VISION_POSE, poseInfo);
        return this;
    }

    public BiCorrectLocationReport addVisionRefinePose(String poseInfo) {
        addData(VISION_REFINE_POSE, poseInfo);
        return this;
    }

    public BiCorrectLocationReport addLocateType(int locateType) {
        addData(LOCATE_TYPE, locateType);
        return this;
    }

    public BiCorrectLocationReport addLostDistance(double lostDistance) {
        addData(LOST_DISTANCE, lostDistance);
        return this;
    }

    public BiCorrectLocationReport addArg1(int arg) {
        addData(ARG1, arg);
        return this;
    }

    public BiCorrectLocationReport addArg2(int arg) {
        addData(ARG2, arg);
        return this;
    }

    public BiCorrectLocationReport addArg3(double arg) {
        addData(ARG3, arg);
        return this;
    }

    public BiCorrectLocationReport addArg4(double arg) {
        addData(ARG4, arg);
        return this;
    }

    public BiCorrectLocationReport addMessage(String msg) {
        addData(MESSAGE, msg);
        return this;
    }

    private void addCTime() {
        addData(CTIME, System.currentTimeMillis());
    }

    @Override
    public void report() {
        addCTime();
        super.report();
    }


}
