/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.navigationservice.chassisAbility.chassis.client.waiter.socket;

import android.text.TextUtils;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

public class AwareMessage {

    //Json Key
    private static final String JSON_COMMAND = "command";
    private static final String JSON_PARAMS = "params";

    private static JsonParser sJsonParser = new JsonParser();

    public static String buildMessage(String command, String params) {
        if (command == null) {
            return "";
        }

        JsonElement jsonElement = null;
        if (params != null) {
            try {
                jsonElement = sJsonParser.parse(params);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return buildMessage(command, jsonElement);
    }

    private static String buildMessage(String command, JsonElement element) {
        JsonObject message = new JsonObject();
        message.addProperty(JSON_COMMAND, command);
        if (element != null) {
            message.add(JSON_PARAMS, element);
        }
        return message.toString();
    }

    public static String parse(String content) {

        return content;
    }

    public static boolean verifyMessagePlatform(String text) {
        if (TextUtils.isEmpty(text)) {
            return false;
        }

        return true;
    }
}
