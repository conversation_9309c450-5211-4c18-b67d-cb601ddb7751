package com.ainirobot.navigationservice.beans.tk1;

public class BaseEvent {

    private int code;
    private String message;

    public BaseEvent(int code, String message){
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "BaseEvent{" +
                ", code=" + code +
                ", message='" + message +
                '}';
    }
}
