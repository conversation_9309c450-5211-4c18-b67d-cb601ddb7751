package com.ainirobot.navigationservice.beans.standard;

public class MapContentBean {
    double resolution;
    double origin_x;
    double origin_y;
    int width;
    int height;
    byte[] data;

    @Override
    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append("resolution = ");
        sb.append(this.resolution);
        sb.append(", x = ");
        sb.append(this.origin_x);
        sb.append(", y = ");
        sb.append(this.origin_y);
        sb.append(", width = ");
        sb.append(this.width);
        sb.append(", height = ");
        sb.append(this.height);
        return sb.toString();
    }

    public MapContentBean(double resolution, double origin_x, double origin_y, int width, int height, byte[] data) {
        this.resolution = resolution;
        this.origin_x = origin_x;
        this.origin_y = origin_y;
        this.width = width;
        this.height = height;
        this.data = data;
    }

    public double getResolution() {
        return resolution;
    }

    public void setResolution(double resolution) {
        this.resolution = resolution;
    }

    public double getOrigin_x() {
        return origin_x;
    }

    public void setOrigin_x(double origin_x) {
        this.origin_x = origin_x;
    }

    public double getOrigin_y() {
        return origin_y;
    }

    public void setOrigin_y(double origin_y) {
        this.origin_y = origin_y;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }
}
