package com.ainirobot.navigationservice.db.helper.sqlite;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ainirobot.navigationservice.db.entity.PlaceInfo;
import com.ainirobot.navigationservice.db.helper.iml.PlaceInfoHelperIml;
import com.ainirobot.navigationservice.db.sqlite.TableInfoDef;
import com.ainirobot.navigationservice.db.sqlite.SqliteDbMigrate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class PlaceInfoSqliteHelper extends BaseSqliteHelper<PlaceInfo> implements PlaceInfoHelperIml {

    public PlaceInfoSqliteHelper(SqliteDbMigrate sqliteDbMigrate) {
        super(sqliteDbMigrate, TableInfoDef.TABLE_NAME_PLACE_INFO);
    }

    @Override
    protected Map<String, Integer> updateCursorIndexMap(Cursor cursor) {
        return sqliteDbMigrate.getPlaceInfoIndex(cursor);
    }

    @Nullable
    @Override
    public PlaceInfo getPlaceById(String placeId) {
        SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
        Cursor cursor = null;
        PlaceInfo placeInfo = null;
        try {
            cursor = readDb.query(mTableName, null, TableInfoDef.COLUMN_PLACE_ID + " = ?", new String[]{placeId}, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                placeInfo = sqliteDbMigrate.cursorToPlaceInfo(cursor, getCursorIndexMap(cursor));
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return placeInfo;
    }

    @Override
    public List<PlaceInfo> getPlaceInfoByMapName(String mapName) {
        SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
        List<PlaceInfo> placeInfoList = new ArrayList<>();
        try (Cursor cursor = readDb.query(mTableName, null, TableInfoDef.COLUMN_MAP_NAME + " = ?", new String[]{mapName}, null, null, null)) {
            if (cursor != null) {
                Map<String, Integer> map = getCursorIndexMap(cursor);
                while (cursor.moveToNext()) {
                    PlaceInfo placeInfo = sqliteDbMigrate.cursorToPlaceInfo(cursor, map);
                    placeInfoList.add(placeInfo);
                }
            }
        }
        return placeInfoList;
    }

    @Override
    public List<PlaceInfo> getPlaceInfoByMapName(String[] mapNames) {
        SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
        List<PlaceInfo> placeInfoList = new ArrayList<>();
        Cursor cursor = null;
        try {
            if (mapNames == null || mapNames.length == 0) {
                cursor = readDb.query(mTableName, null, null, null, null, null, null);
            } else {
                String placeholders = new String(new char[mapNames.length - 1]).replace("\0", "?,") + "?";
                cursor = readDb.query(mTableName, null, TableInfoDef.COLUMN_MAP_NAME + " IN (" + placeholders + ")", mapNames, null, null, null);
            }
            if (cursor != null) {
                Map<String, Integer> map = getCursorIndexMap(cursor);
                while (cursor.moveToNext()) {
                    PlaceInfo placeInfo = sqliteDbMigrate.cursorToPlaceInfo(cursor, map);
                    placeInfoList.add(placeInfo);
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return placeInfoList;
    }

    @Override
    public String[] getPlaceIdByMapName(String mapName) {
        SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
        List<String> placeIds = new ArrayList<>();
        try (Cursor cursor = readDb.query(mTableName, new String[]{TableInfoDef.COLUMN_PLACE_ID}, TableInfoDef.COLUMN_MAP_NAME + " = ?", new String[]{mapName}, null, null, null)) {
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    placeIds.add(cursor.getString(cursor.getColumnIndex(TableInfoDef.COLUMN_PLACE_ID)));
                }
            }
        }
        return placeIds.toArray(new String[0]);
    }

    @NonNull
    @Override
    public List<PlaceInfo> getPlaceInfos(String mapName, String[] placeIdsInName) {
        SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
        List<PlaceInfo> placeInfoList = new ArrayList<>();
        Cursor cursor = null;
        try {
            if (placeIdsInName == null || placeIdsInName.length == 0) {
                cursor = readDb.query(mTableName, null, TableInfoDef.COLUMN_MAP_NAME + " = ?", new String[]{mapName}, null, null, null);
            } else {
                String placeholders = new String(new char[placeIdsInName.length - 1]).replace("\0", "?,") + "?";
                cursor = readDb.query(mTableName, null, TableInfoDef.COLUMN_MAP_NAME + " = ? AND " + TableInfoDef.COLUMN_PLACE_ID + " IN (" + placeholders + ")", concatenate(new String[]{mapName}, placeIdsInName), null, null, null);
            }
            if (cursor != null) {
                Map<String, Integer> map = getCursorIndexMap(cursor);
                while (cursor.moveToNext()) {
                    PlaceInfo placeInfo = sqliteDbMigrate.cursorToPlaceInfo(cursor, map);
                    placeInfoList.add(placeInfo);
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return placeInfoList;
    }

    @Override
    public String[] getStrings(String[] placeIds, String mapName) {
        if (placeIds == null || placeIds.length == 0) {
            return new String[0];
        }
        SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
        List<String> placeIdList = new ArrayList<>();
        Cursor cursor = null;
        try {
            StringBuilder queryBuilder = getQuerySqlByPlaceIds(placeIds);
            String[] selectionArgs = new String[placeIds.length + 1];
            selectionArgs[0] = mapName;
            System.arraycopy(placeIds, 0, selectionArgs, 1, placeIds.length);
            cursor = readDb.rawQuery(queryBuilder.toString(), selectionArgs);
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    String placeId = cursor.getString(cursor.getColumnIndexOrThrow(TableInfoDef.COLUMN_PLACE_ID));
                    placeIdList.add(placeId);
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return placeIdList.toArray(new String[0]);
    }

    @Override
    public List<PlaceInfo> getPlaceInfoNonHighPriority(String mapName) {
        return null;
    }

    @Override
    public List<PlaceInfo> getPlaceInfoHighPriority(String mapName) {
        return null;
    }

    @Override
    public void updateTimeOneOfIdArr(String[] idArr) {
        if (idArr == null || idArr.length == 0) {
            Log.d(TAG, "updateTimeOneOfIdArr: idArr is null or empty");
            return;
        }
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        ContentValues values = new ContentValues();
        values.put(TableInfoDef.COLUMN_UPDATE_TIME, System.currentTimeMillis());
        for (String id : idArr) {
            writeDb.update(mTableName, values, TableInfoDef.COLUMN_PLACE_ID + " = ?", new String[]{id});
        }
    }

    @Override
    public boolean updatePlaceInfo(List<PlaceInfo> placeList) {
        if (placeList == null || placeList.isEmpty()) {
            Log.d(TAG, "updatePlaceInfo: placeList is null or empty");
            return false;
        }
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        writeDb.beginTransaction();
        boolean allUpdated = true;
        try {
            for (PlaceInfo placeInfo : placeList) {
                ContentValues values = sqliteDbMigrate.placeInfoToContentValues(placeInfo);
                // 尝试更新数据
                int rowsUpdated = writeDb.update(
                        mTableName,
                        values,
                        TableInfoDef.COLUMN_PLACE_ID + " = ?",
                        new String[]{placeInfo.getPlaceId()});
                // 如果更新失败（没有记录被更新），尝试插入新记录
                if (rowsUpdated <= 0) {
                    long rowId = writeDb.insert(mTableName, null, values);
                    if (rowId == -1) {
                        allUpdated = false;
                        Log.d(TAG, "updatePlaceInfo: insert failed for placeId = " + placeInfo.getPlaceId());
                    }
                }
            }
            // 如果全部更新或插入成功，提交事务
            if (allUpdated) {
                writeDb.setTransactionSuccessful();
            }
        } catch (Exception e) {
            Log.e(TAG, "updatePlaceInfo: exception occurred", e);
            allUpdated = false;
        } finally {
            writeDb.endTransaction();
        }
        return allUpdated;
    }

    @Override
    public void updatePlaceInfo(PlaceInfo placeInfo) {
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        ContentValues values = sqliteDbMigrate.placeInfoToContentValues(placeInfo);
        // 尝试更新数据
        int rowsUpdated = writeDb.update(
                mTableName,
                values,
                TableInfoDef.COLUMN_PLACE_ID + " = ?",
                new String[]{placeInfo.getPlaceId()}
        );
        // 如果更新失败（没有记录被更新），尝试插入新记录
        if (rowsUpdated <= 0) {
            long rowId = writeDb.insert(mTableName, null, values);
            if (rowId == -1) {
                Log.d(TAG, "updatePlaceInfo: insert failed for placeId = " + placeInfo.getPlaceId());
            } else {
                Log.d(TAG, "updatePlaceInfo: insert succeeded for placeId = " + placeInfo.getPlaceId());
            }
        } else {
            Log.d(TAG, "updatePlaceInfo: update succeeded for placeId = " + placeInfo.getPlaceId());
        }
    }

    @Override
    public void initPlaceInfoData(List<PlaceInfo> placeList) {
        updatePlaceInfo(placeList);
    }

    @Override
    public void deletePlaceByPlaceIds(String[] placeIdArr) {
        if (placeIdArr == null || placeIdArr.length == 0) {
            Log.d(TAG, "deletePlaceByPlaceIds: placeIdArr is null or empty");
            return;
        }
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        int rowsDeleted = 0;
        for (String placeId : placeIdArr) {
            rowsDeleted += writeDb.delete(mTableName, TableInfoDef.COLUMN_PLACE_ID + " = ?", new String[]{placeId});
        }
        Log.d(TAG, "deletePlaceByPlaceIds: " + rowsDeleted);
    }

    @Override
    public String[] deletePlaceInfo(String mapName, String[] placeIds) {
        if (placeIds == null || placeIds.length == 0) {
            Log.d(TAG, "deletePlaceInfo: placeIds array is null or empty");
            return new String[0];
        }
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        List<String> deletedPlaceIds = new ArrayList<>();
        writeDb.beginTransaction();
        try {
            StringBuilder whereClause = new StringBuilder(TableInfoDef.COLUMN_MAP_NAME + " = ? AND " + TableInfoDef.COLUMN_PLACE_ID + " IN (");
            for (int i = 0; i < placeIds.length; i++) {
                whereClause.append("?");
                if (i < placeIds.length - 1) {
                    whereClause.append(",");
                }
            }
            whereClause.append(")");
            // 构建参数数组
            String[] args = new String[placeIds.length + 1];
            args[0] = mapName;
            System.arraycopy(placeIds, 0, args, 1, placeIds.length);
            // 执行删除操作
            int rowsDeleted = writeDb.delete(mTableName, whereClause.toString(), args);
            // 检查哪些 placeIds 实际被删除了
            if (rowsDeleted > 0) {
                for (String placeId : placeIds) {
                    // 检查每个 placeId 是否仍然存在
                    Cursor cursor = writeDb.query(mTableName,
                            new String[]{TableInfoDef.COLUMN_PLACE_ID},
                            TableInfoDef.COLUMN_MAP_NAME + " = ? AND " + TableInfoDef.COLUMN_PLACE_ID + " = ?",
                            new String[]{mapName, placeId},
                            null, null, null);
                    if (cursor.getCount() == 0) {
                        deletedPlaceIds.add(placeId);
                    }
                    cursor.close();
                }
            }
            writeDb.setTransactionSuccessful();
        } catch (Exception e) {
            Log.e(TAG, "deletePlaceInfo: exception occurred", e);
        } finally {
            writeDb.endTransaction();
        }
        return deletedPlaceIds.toArray(new String[0]);
    }

    @Override
    public String[] deletePlaceByMapName(String mapName) {
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        List<String> deletedPlaceIds = new ArrayList<>();
        try (Cursor cursor = writeDb.query(mTableName, new String[]{TableInfoDef.COLUMN_PLACE_ID}, TableInfoDef.COLUMN_MAP_NAME + " = ?", new String[]{mapName}, null, null, null)) {
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    String placeId = cursor.getString(cursor.getColumnIndex(TableInfoDef.COLUMN_PLACE_ID));
                    int rowsDeleted = writeDb.delete(mTableName, TableInfoDef.COLUMN_MAP_NAME + " = ? AND " + TableInfoDef.COLUMN_PLACE_ID + " = ?", new String[]{mapName, placeId});
                    if (rowsDeleted > 0) {
                        deletedPlaceIds.add(placeId);
                    }
                }
            }
        }
        return deletedPlaceIds.toArray(new String[0]);
    }

    @Override
    public String[] deletePlaceByTypeId(String mapName, int typeId, int priority) {
        return new String[0];
    }

    @Override
    public PlaceInfo getPlaceByType(int typeId, int priority, String mapName) {
        return null;
    }

    @Override
    public PlaceInfo getPlaceByTypeIdAndPriority(int typeId, int priority, String mapName) {
        return null;
    }

    @Override
    public List<PlaceInfo> getPlacesByType(int typeId, String mapName) {
        return null;
    }

    @Override
    public List<PlaceInfo> getPlacesByTypeArr(int[] typeId, String mapName) {
        return Collections.emptyList();
    }

    @Override
    public void connectTablePlaceType() {

    }

    @NonNull
    private StringBuilder getQuerySqlByPlaceIds(String[] placeIds) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("SELECT ").append(TableInfoDef.COLUMN_PLACE_ID)
                .append(" FROM ").append(mTableName)
                .append(" WHERE ").append(TableInfoDef.COLUMN_MAP_NAME).append(" = ?")
                .append(" AND ").append(TableInfoDef.COLUMN_PLACE_ID).append(" IN (");
        for (int i = 0; i < placeIds.length; i++) {
            queryBuilder.append("?");
            if (i < placeIds.length - 1) {
                queryBuilder.append(",");
            }
        }
        queryBuilder.append(")");
        return queryBuilder;
    }

    private String[] concatenate(String[] first, String[] second) {
        String[] result = new String[first.length + second.length];
        System.arraycopy(first, 0, result, 0, first.length);
        System.arraycopy(second, 0, result, first.length, second.length);
        return result;
    }
}