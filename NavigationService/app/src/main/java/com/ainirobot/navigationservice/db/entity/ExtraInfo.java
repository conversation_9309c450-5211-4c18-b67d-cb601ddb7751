package com.ainirobot.navigationservice.db.entity;

import io.objectbox.annotation.Entity;
import io.objectbox.annotation.Id;
import io.objectbox.annotation.Index;

@Entity
public class ExtraInfo {
    @Id
    public long id;
    @Index
    private String mapName;
    private String extraId;
    private String extraMd5;

    public ExtraInfo() {
    }

    public ExtraInfo(String mapName) {
        this.mapName = mapName;
    }

    public ExtraInfo(String mapName, String extraId, String extraMd5) {
        this.mapName = mapName;
        this.extraId = extraId;
        this.extraMd5 = extraMd5;
    }

    public String getMapName() {
        return mapName;
    }

    public String getExtraId() {
        return extraId;
    }

    public String getExtraMd5() {
        return extraMd5;
    }

    @Override
    public String toString() {
        return "ExtraInfo{" +
                "mapName='" + mapName + '\'' +
                ", extraId='" + extraId + '\'' +
                ", extraMd5='" + extraMd5 + '\'' +
                '}';
    }
}
