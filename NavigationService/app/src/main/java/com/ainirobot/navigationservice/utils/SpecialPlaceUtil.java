package com.ainirobot.navigationservice.utils;

import android.content.Context;
import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.navigationservice.Defs.Def;
import com.ainirobot.navigationservice.R;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * Created by Orion on 2021/3/22.
 */
public class SpecialPlaceUtil {
    private static final String TAG = Def.TAGPRE + "SpecialPlaceUtil";

    /**
     * 特殊点位多语言名称列表，特殊点位配置的语言类型全集，便于本地增加新语言支持后的地图兼容
     */
    private static HashMap<String, List<String>> sSpecialPlace = new HashMap<>();
    private Context sContext;

    private static class ModelPrefix {
        private static final String MINI_TOB = "MiniTob_SpecialPlace_";
        private static final String SAIPH = "Saiph_SpecialPlace_";
        private static final String DEFAULT = "SpecialPlace_";
        private static final String SAIPH_ELEVATOR = "Saiph_Elevator_SpecialPlace_";
    }

    private SpecialPlaceUtil() {
    }

    private void initSpecialPlaceLangName() {
        List<String> languageList = Arrays.asList(sContext.getResources().
                getStringArray(R.array.special_place_lang));
        String prefix = getStringArrayNamePrefix();
        Log.d(TAG, "initSpecialPlaceLangName: languageList=" + languageList.toString()
                + " prefix=" + prefix);
        for (String language : languageList) {
            try {
                int arrayId = getArrayId(prefix + language);
                List<String> names = Arrays.asList(sContext.getResources().getStringArray(arrayId));
                if (names == null && names.size() <= 0) {
                    Log.e(TAG, "initSpecialPlaceLangName: Language " + language + " config null!");
                    continue;
                }
                Log.d(TAG, "initSpecialPlaceLangName: language=" + language + " names=" + names);
                sSpecialPlace.put(language, names);
            } catch (Exception e) {
                e.printStackTrace();
                Log.e(TAG, "initSpecialPlaceLangName: Resource not found exception: language=" + language);
            }
        }
    }

    private int getArrayId(String name) {
        try {
            Field field = R.array.class.getField(name);
            return field.getInt(null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    public static SpecialPlaceUtil getInstance() {
        return Singleton.INSTANCE;
    }

    public void init(Context context) {
        this.sContext = context;
        initSpecialPlaceLangName();
    }

    private static class Singleton {
        static SpecialPlaceUtil INSTANCE = new SpecialPlaceUtil();
    }

    public HashMap<String, List<String>> getLangSpecialPlace() {
        return sSpecialPlace;
    }

    private String getStringArrayNamePrefix() {
        if (ProductInfo.isElevatorCtrlProduct()) {
            return ModelPrefix.SAIPH_ELEVATOR;
        } else if (ProductInfo.isMiniProduct() || ProductInfo.isMeissa2()) {
            return ModelPrefix.MINI_TOB;
        } else if (ProductInfo.isDeliveryProduct()) {
            return ModelPrefix.SAIPH;
        } else {
            //小秘PLUS 机型,走默认的ModelPrefix类型, 接待点要作为特殊点位.
            return ModelPrefix.DEFAULT;
        }
    }

    public String getAfterDash(String specialPlaceName) {
        int index = specialPlaceName.indexOf("-");
        if (index != -1) {
            return specialPlaceName.substring(index + 1);
        }
        return "error";
    }

    public String replaceAfterDash(String originalName, String newName) {
        if (originalName != null && originalName.contains("-")) {
            int index = originalName.indexOf("-");
            if (index != -1) {
                return originalName.substring(0, index + 1) + newName;
            }
        }
        return "error";
    }

    public String getBeforeDash(String str) {
        if (str == null) return "error";
        int index = str.indexOf("-");
        if (index != -1) {
            return str.substring(0, index); // 截取第一个“-”前的部分
        }
        return "error"; // 没有“-”时返回错误
    }

    public String replaceBeforeDash(String original, String newPrefix) {
        if (original == null || !original.contains("-")) {
            return "error"; // 原字符串没有“-”时返回错误
        }
        int index = original.indexOf("-");
        if (index != -1) {
            return newPrefix + original.substring(index);
        }
        return "error";
    }

}
