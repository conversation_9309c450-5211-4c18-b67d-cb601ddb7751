/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.tk1;

import java.io.DataOutput;
import java.io.IOException;

/**
 * This represents a pose in free space with uncertainty. Used to parse the message send from
 * the navigation for ROS , later deleted.
 */
public class PoseWithCovariance extends Message {

    public static final int COVARIANCE_ROWS = 6;

    public static final int COVARIANCE_COLUMNS = 6;

    public static final int COVARIANCE_SIZE = PoseWithCovariance.COVARIANCE_ROWS
            * PoseWithCovariance.COVARIANCE_COLUMNS;

    private Pose3D pose3D;
    private double[] covariance;
    private double[][] covarianceMatrix;

    public PoseWithCovariance() {
        super(Message.MSG_TYPE_POSE_COVARIANCE);
    }


    @Override
    public void writeTo(DataOutput out) throws IOException {
        pose3D.writeTo(out);
        for (double value : covariance) {
            out.writeDouble(value);
        }
    }

    @Override
    public void readData(ReverseInputStream in) throws IOException {
        pose3D = new Pose3D();
        pose3D.readData(in);

        covariance = new double[COVARIANCE_SIZE];
        for (int i = 0; i < COVARIANCE_SIZE; i++) {
            covariance[i] = in.readDouble();
        }

//        for(int row = 0; row < COVARIANCE_ROWS; row++){
//
//        }
    }
}
