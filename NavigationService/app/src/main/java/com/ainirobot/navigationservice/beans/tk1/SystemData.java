package com.ainirobot.navigationservice.beans.tk1;

import ninjia.android.proto.GetSystemInformationProtoWrapper;

public class SystemData {
    String apkVersion;
    String romVersion;
    String sn;
    String boardId;

    public SystemData() {
    }

    public SystemData(GetSystemInformationProtoWrapper.SystemInformationProto proto) {
        if (proto != null) {
            this.apkVersion = proto.getApkVersion();
            this.romVersion = proto.getRomVersion();
            this.sn = proto.getSerialId();
            this.boardId = proto.getBoardId();
        }
    }

    public String getApkVersion() {
        return apkVersion;
    }

    public void setApkVersion(String apkVersion) {
        this.apkVersion = apkVersion;
    }

    public String getRomVersion() {
        return romVersion;
    }

    public void setRomVersion(String romVersion) {
        this.romVersion = romVersion;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getBoardId() {
        return boardId;
    }

    public void setBoardId(String boardId) {
        this.boardId = boardId;
    }

    @Override
    public String toString() {
        return "SystemData{" +
                "apkVersion='" + apkVersion + '\'' +
                ", romVersion='" + romVersion + '\'' +
                ", sn='" + sn + '\'' +
                ", boardId='" + boardId + '\'' +
                '}';
    }
}
