package com.ainirobot.navigationservice.beans.standard;

public class MapIdBean {
    String name;
    String id;

    public MapIdBean(String name, String id) {
        this.name = name;
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append("name = ");
        sb.append(this.name);
        sb.append(", id = ");
        sb.append(this.id);

        return sb.toString();
    }
}
