package com.ainirobot.navigationservice.utils;

import android.util.Log;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;

public class TempCode {
    private static final String TAG = "TempCode";

    public static boolean CopyFile(String src_path, String dest_path) {
        File src_file = new File(src_path);
        if (!src_file.exists()) {
            Log.e(TAG, "copy error: src not exists");
            return false;
        }
        return RunCommand("cp -rf \"" + src_path + "\" \"" + dest_path + "\"");
    }

    public static boolean RunCommand(String command) {
        Log.d(TAG, "RunCommand: " + command);
        try {
            String[] commands = {"/bin/sh", "-c", command};
            Log.d(TAG, "final command: " + command);
            Process process = Runtime.getRuntime().exec(commands);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder msg = new StringBuilder();
            BufferedReader err_reader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            StringBuilder err_msg = new StringBuilder();
            for (String line; (line = reader.readLine()) != null; ) {
                msg.append(line).append('\n');
            }
            for (String line; (line = err_reader.readLine()) != null; ) {
                err_msg.append(line).append('\n');
            }
            int exit_code = process.waitFor();
            if (exit_code == 0) {
                Log.d(TAG, "RunCommand success msg: " + msg);
                return true;
            } else {
                Log.e(TAG, "RunCommand error " +
                        " ret: " + exit_code +
                        " msg: " + msg +
                        " err_msg: " + err_msg);
            }
        } catch (IOException e) {
            Log.e(TAG, "Upload Failed: io exception" +
                    " msg: " + e.getMessage());
        } catch (InterruptedException e) {
        }
        return false;
    }

    public static boolean needMigrateData(String version1, String version2) {
        String[] parts1 = version1.split("\\.");
        String[] parts2 = version2.split("\\.");

        int length = Math.max(parts1.length, parts2.length);
        for (int i = 0; i < length; i++) {
            int num1 = i < parts1.length ? Integer.parseInt(parts1[i]) : 0;
            int num2 = i < parts2.length ? Integer.parseInt(parts2[i]) : 0;

            if (num1 < num2) {
                return true;  // Need to migrate if version1 < version2
            }
            if (num1 > num2) {
                return false; // No migration needed if version1 > version2
            }
        }
        return false; // No migration needed if versions are equal
    }

}
