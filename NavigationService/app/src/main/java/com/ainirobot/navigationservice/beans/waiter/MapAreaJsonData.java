package com.ainirobot.navigationservice.beans.waiter;

import java.util.ArrayList;

public class MapAreaJsonData {
    private ArrayList<MapRuleAreasData> areas;

    public MapAreaJsonData() {
    }

    public MapAreaJsonData(ArrayList<MapRuleAreasData> areas) {
        this.areas = areas;
    }

    public ArrayList<MapRuleAreasData> getAreas() {
        return areas;
    }

    public void setAreas(ArrayList<MapRuleAreasData> areas) {
        this.areas = areas;
    }

    @Override
    public String toString() {
        return "MapAreaJsonData{" +
                "areas=" + areas +
                '}';
    }
}
