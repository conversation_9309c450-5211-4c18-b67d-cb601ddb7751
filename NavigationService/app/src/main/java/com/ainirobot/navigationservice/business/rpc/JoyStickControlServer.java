package com.ainirobot.navigationservice.business.rpc;

import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.IInspectCallBack;
import com.ainirobot.coreservice.bean.Command;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.hardware.HWService;
import com.ainirobot.coreservice.config.ServiceConfig;
import com.ainirobot.navigationservice.chassisAbility.ChassisManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

public class JoyStickControlServer extends HWService {
    private static final String TAG = TAGPRE + JoyStickControlServer.class.getSimpleName();

    public JoyStickControlServer() {
    }

    @Override
    public boolean startStatusSocket(String s, int i) throws RemoteException {
        return false;
    }

    @Override
    public boolean closeStatusSocket(String s, int i) throws RemoteException {
        return false;
    }

    @Override
    public List<Command> onMount(String s, ServiceConfig serviceConfig) {
        return null;
    }

    @Override
    public void onInspectStart(IInspectCallBack iInspectCallBack) {

    }

    @Override
    public boolean onUpgrade(String s, String s1) {
        return false;
    }

    @Override
    public void onReset() {

    }

    @Override
    public void onAsyncCommand(String s, String s1, String language) {
        motion(s1);
    }

    @Override
    public String onSyncCommand(String s, String s1, String language) {
        return null;
    }

    @Override
    public String getBoardName() throws RemoteException {
        return null;
    }

    @Override
    public boolean isNeedUpgrade(String s, String s1) throws RemoteException {
        return false;
    }

    @Override
    public String getVersion(String s) throws RemoteException {
        return null;
    }

    private boolean motion(String params) {
        try {
            JSONObject json = new JSONObject(params);
            double angularSpeed = json.getDouble(Definition.JSON_NAVI_ANGULAR_SPEED);
            double linearSpeed = json.getDouble(Definition.JSON_NAVI_LINEAR_SPEED);
            boolean hasAcceleration = json.optBoolean(Definition.JSON_NAVI_ACCELERATION, true);
            ChassisManager.getInstance().getChassisClient().motion(angularSpeed, linearSpeed, 0, hasAcceleration);
            return true;
        } catch (JSONException e) {
            e.printStackTrace();
            return false;
        }
    }
}
