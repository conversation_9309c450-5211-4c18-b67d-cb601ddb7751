package com.ainirobot.navigationservice.commonModule.bi.report;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

/**
 * navigation log bi report
 *
 * @version V1.0.0
 * @date 2019/3/6 14:31
 */
public class BiNavigationLogReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_chassis_logcat";
    private static final String TYPE = "type";
    private static final String INT1 = "int1";
    private static final String INT2 = "int2";
    private static final String INT3 = "int3";
    private static final String INT4 = "int4";
    private static final String DOUBLE1 = "double1";
    private static final String DOUBLE2 = "double2";
    private static final String DOUBLE3 = "double3";
    private static final String DOUBLE4 = "double4";
    private static final String STRVALUE = "strvalue";
    private static final String CTIME = "ctime";

    public BiNavigationLogReport() {
        super(TABLE_NAME);
        initData();
    }

    private void initData() {
        addData(TYPE, "");
        addData(INT1, "");
        addData(INT2, "");
        addData(INT3, "");
        addData(INT4, "");
        addData(DOUBLE1, "");
        addData(DOUBLE2, "");
        addData(DOUBLE3, "");
        addData(DOUBLE4, "");
        addData(STRVALUE, "");
        addData(CTIME, "");
    }

    public BiNavigationLogReport addType(int type) {
        addData(TYPE, type);
        return this;
    }

    public BiNavigationLogReport addInt1(int int1) {
        addData(INT1, int1);
        return this;
    }

    public BiNavigationLogReport addInt2(int int2) {
        addData(INT2, int2);
        return this;
    }

    public BiNavigationLogReport addInt3(int int3) {
        addData(INT3, int3);
        return this;
    }

    public BiNavigationLogReport addInt4(int int4) {
        addData(INT4, int4);
        return this;
    }

    public BiNavigationLogReport addDouble1(double double1) {
        addData(DOUBLE1, double1);
        return this;
    }

    public BiNavigationLogReport addDouble2(double double2) {
        addData(DOUBLE2, double2);
        return this;
    }

    public BiNavigationLogReport addDouble3(double double3) {
        addData(DOUBLE3, double3);
        return this;
    }

    public BiNavigationLogReport addDouble4(double double4) {
        addData(DOUBLE4, double4);
        return this;
    }

    public BiNavigationLogReport addStrValue(String strValue) {
        addData(STRVALUE, strValue);
        return this;
    }

    private void addCTime() {
        addData(CTIME, System.currentTimeMillis());
    }

    @Override
    public void report() {
        addCTime();
        super.report();
    }


}
