package com.ainirobot.navigationservice.beans;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.navigationservice.Defs.Def;
import com.ainirobot.navigationservice.db.NavigationDataManager;
import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.commonModule.data.DataManager;
import com.ainirobot.navigationservice.db.entity.MapInfo;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 映射点位列表维护，边建图边设点时维护点位列表在内存中，建图成功结束后存储到本地
 */
public class MappingPose {

    private static final String TAG = MappingPose.class.getSimpleName();

    private static CopyOnWriteArrayList<MappingPose> sPoseList = new CopyOnWriteArrayList<>();

    /**
     * id是底盘上报的，只在边建图边设点时候用，建图完端上和底盘都不会保存
     */
    private int id;
    private boolean _valid;
    private Pose pose;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public boolean isValid() {
        return _valid;
    }

    public void setValid(boolean _valid) {
        this._valid = _valid;
    }

    public Pose getPose() {
        return pose;
    }

    public void setPose(Pose pose) {
        this.pose = pose;
    }

    public static void init() {
        sPoseList.clear();
    }

    public static void destroy() {
        sPoseList.clear();
    }

    public boolean checkExists() {
        return false;
    }

    /**
     * updatePoses只更新位姿信息，即位姿信息信任底盘上报，不做任何判断；
     * 增删点位操作只在addPose和deletePose处理，即增删点位操作信任add和remove接口调用结果，不做任何判断；
     */
    public synchronized static void updatePoses(List<MappingPose> list) {
        Log.d(TAG, "PlaceDataManager - updatePoses: " + "list: " + (list != null ? list.size() : "null"));
        //list中_valid、pose.x、pose.x、pose.theta 替换 sPoseList中id相同的元素对应的值
        for (MappingPose pose : list) {
            for (MappingPose poseT : sPoseList) {
                if (poseT.getId() == pose.getId()) {
                    poseT.setValid(pose.isValid());
                    poseT.getPose().setX(pose.getPose().getX());
                    poseT.getPose().setY(pose.getPose().getY());
                    poseT.getPose().setTheta(pose.getPose().getTheta());
                }
            }
        }
    }

    /**
     * 新增一个类型对应多个特殊点，支持业务层建图时可以修改优先级。
     * 需要在保存点位到数据库前做一次优先级更新。
     */
    public synchronized static void updateSpecialPoses(List<com.ainirobot.coreservice.client.actionbean.Pose> poseList) {
        for (com.ainirobot.coreservice.client.actionbean.Pose pose: poseList) {
            for (MappingPose poseT: sPoseList) {
                if (poseT.getId() == pose.getPoseId() &&
                    poseT.getPose().getTypeId() == pose.getTypeId()) {
                    poseT.getPose().setPriority(pose.getPriority());
                }
                // 专门需要处理回充点, 回充点需要名字去定位 名字格式为：1. 回充点-(对应充电桩的名字) 2. 回充点 充电桩
                if (poseT.getPose().getName().
                    equals(Definition.START_BACK_CHARGE_POSE + "-" + pose.getName()) ||
                    (poseT.getPose().getName().equals(Definition.START_BACK_CHARGE_POSE) &&
                            pose.getName().equals(Definition.START_CHARGE_PILE_POSE))) {
                    poseT.getPose().setPriority(pose.getPriority());
                }
            }
        }
    }

    public static void addPose(MappingPose pose) {
        Log.d(TAG, "PlaceDataManager - addPose: " + "pose: " + pose.toJson());
        for (MappingPose poseT : sPoseList) {
            if (poseT.getId() == pose.getId()
                    && TextUtils.isEmpty(poseT.getPose().getName())
                    && !TextUtils.isEmpty(pose.getPose().getName())) {
                //处理onMappingPoseUpdate先于addTarget上报点位导致的name为null问题
                Log.e(TAG, "PlaceDataManager - nameNullError: " + poseT.toJson());
                sPoseList.remove(poseT);
                poseT.getPose().setName(pose.getPose().getName());
                poseT.getPose().setTypeId(pose.getPose().getTypeId());
                poseT.getPose().setPriority(pose.getPose().getPriority());
                sPoseList.add(poseT);
                return;
            }
        }

        sPoseList.add(pose);
    }

    public static List<MappingPose> getPoseList() {
        return (List<MappingPose>) sPoseList.clone();
    }

    public static String getMappingPoseName(int id) {
        for (MappingPose pose : sPoseList) {
            if (pose.getId() == id) {
                return pose.getPose().getName();
            }
        }
        return null;
    }

    public static Pose getMappingPose(int id) {
        for (MappingPose pose : sPoseList) {
            if (pose.getId() == id) {
                return pose.getPose();
            }
        }
        return null;
    }

    public String toJson() {
        try {
            JSONObject object = new JSONObject();
            object.put("poseX", pose.getX());
            object.put("poseY", pose.getY());
            object.put("poseT", pose.getTheta());
            object.put("valid", isValid());
            object.put("id", getId());
            object.put("name", pose.getName());
            object.put("typeId", pose.getTypeId());
            object.put("priority", pose.getPriority());
            object.put("ignoreDistance", pose.getIgnoreDistance());
            object.put("safeDistance", pose.getSafeDistance());
            object.put("noDirectionalParking", pose.getNoDirectionalParking());
            return object.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getMappingPoseListJson() {
        if (sPoseList == null || sPoseList.size() == 0) {
            return "";
        }
        JSONArray array = new JSONArray();
        for (MappingPose mappingPose : sPoseList) {
            try {
                JSONObject object = new JSONObject();
                object.put("poseX", mappingPose.getPose().getX());
                object.put("poseY", mappingPose.getPose().getY());
                object.put("poseT", mappingPose.getPose().getTheta());
                object.put("valid", mappingPose.isValid());
                object.put("id", mappingPose.getId());
                object.put("name", mappingPose.getPose().getName());
                object.put("typeId", mappingPose.getPose().getTypeId());
                object.put("priority", mappingPose.getPose().getPriority());
                array.put(object);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return array.toString();
    }

    public static void deletePose(int poseId) {
        Log.d(TAG, "PlaceDataManager:deletePose: " + "poseId: " + poseId);
        if (sPoseList == null || sPoseList.size() == 0) {
            return;
        }
        for (MappingPose mappingPose : sPoseList) {
            if (mappingPose.getId() == poseId) {
                sPoseList.remove(mappingPose);
                return;
            }
        }
    }

    /**
     * 存储点位到本地数据库
     */
    public static void saveMappingPoseToLocal(MapInfo mapInfo) {
        Log.d(TAG, "PlaceDataManager:saveMappingPoseToLocal: " + "poseList: " + sPoseList.toString());
        if (sPoseList == null || sPoseList.size() == 0 || mapInfo == null) {
            return;
        }
        final String language = mapInfo.getMapLanguage();
        Log.d(TAG, "saveMappingPoseToLocal: mapInfo=" + mapInfo.toString() +
                " language=" + language);
        List<Pose> validPose = new ArrayList<>();
        for (MappingPose mappingPose : sPoseList) {
            if (mappingPose.isValid() ||
                    DataManager.getInstance().
                            isSpecialPlace(mappingPose.getPose().getName())) {//特殊点位不校验valid
                Pose pose = mappingPose.getPose();
                validPose.add(pose);
            }
        }
        Log.d(TAG, "saveMappingPoseToLocal: validPose=" + validPose.toString());
        NavigationDataManager.getInstance().updatePlaceInfoList(validPose, language, mapInfo);
    }

    @Override
    public String toString() {
        return "MappingPose{" +
                "id=" + id +
                ", _valid=" + _valid +
                ", pose=" + pose.toString() +
                '}';
    }
}