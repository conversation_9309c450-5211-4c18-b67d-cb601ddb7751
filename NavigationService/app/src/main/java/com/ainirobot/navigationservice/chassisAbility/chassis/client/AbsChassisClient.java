package com.ainirobot.navigationservice.chassisAbility.chassis.client;

import android.content.Context;

import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.beans.tk1.Velocity;
import com.ainirobot.navigationservice.commonModule.bi.BiManager;
import com.ainirobot.navigationservice.commonModule.logs.LogManager;
import com.ainirobot.navigationservice.commonModule.settings.SettingManager;
import com.google.gson.Gson;

import java.util.concurrent.atomic.AtomicReference;

public abstract class AbsChassisClient implements IChassisClient {
    protected AtomicReference<Pose> mCurPose;
    protected AtomicReference<Velocity> mVelocity;
    protected BiManager biManager;
    protected LogManager logManager;
    protected SettingManager settingManager;
    protected Context mContext;
    protected Gson mGson;

    public AbsChassisClient() {
        mCurPose = new AtomicReference<>();
        mVelocity = new AtomicReference<>();
        mGson = new Gson();
    }

    @Override
    public void init(Context context) {
        mContext = context;
        this.biManager = BiManager.getInstance();
        this.logManager = LogManager.getInstance();
        this.settingManager = SettingManager.getInstance();
    }
}

