/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.waiter;

public class TargetsSafeZone {

    private int id;
    private double enable_distance;

    public TargetsSafeZone(){

    }

    public TargetsSafeZone(int id, double enable_distance){
        this.id = id;
        this.enable_distance = enable_distance;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public double getEnableDistance() {
        return enable_distance;
    }

    public void setEnableDistance(double enable_distance) {
        this.enable_distance = enable_distance;
    }

    @Override
    public String toString() {
        return "Targets{" +
                "id=" + id +
                ", enable_distance=" + enable_distance +
                '}';
    }
}
