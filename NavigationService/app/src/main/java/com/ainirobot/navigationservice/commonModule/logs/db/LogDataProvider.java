package com.ainirobot.navigationservice.commonModule.logs.db;


import android.content.ContentProvider;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.database.sqlite.SQLiteQueryBuilder;
import android.net.Uri;
import android.provider.BaseColumns;
import androidx.annotation.NonNull;
import android.util.Log;

import com.ainirobot.navigationservice.ApplicationWrapper;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class LogDataProvider extends ContentProvider {

    private static final String TAG = "LogDataProvider";
    static final Object DBLock = new Object();
    public static final String AUTHORITY = "com.ainirobot.navigation.dataprovider";

    private static final UriMatcher sUriMatcher;
    private static final Map<Integer, SQLiteTable> sTables = new ConcurrentHashMap<>();

    static {
        sUriMatcher = new UriMatcher(UriMatcher.NO_MATCH);
        for (SQLiteTable table : SQLiteTable.values()) {
            if (!AUTHORITY.equals(table.getAuthority())) {
                continue;
            }
            sUriMatcher.addURI(AUTHORITY, table.getTableName(), table.ordinal());
            sTables.put(table.ordinal(), table);
        }
    }

    private static DBHelper mDBHelper;

    public static DBHelper getDBHelper() {
        if (mDBHelper == null) {
            mDBHelper = new DBHelper(ApplicationWrapper.getContext());
        }
        return mDBHelper;
    }

    @Override
    public String getType(@NonNull Uri uri) {
        int code = sUriMatcher.match(uri);
        SQLiteTable table = sTables.get(code);
        if (table != null) {
            return table.getContentType();
        }
        return null;
    }

    private String matchTable(Uri uri) {
        int code = sUriMatcher.match(uri);
        SQLiteTable table = sTables.get(code);
        if (table != null) {
            return table.getTableName();
        }
        return null;
    }

    @Override
    public boolean onCreate() {
        return true;
    }

    @Override
    public Cursor query(@NonNull Uri uri, String[] projection,
                        String selection, String[] selectionArgs, String sortOrder) {
        Log.d(TAG, "query ");
        synchronized (DBLock) {
            SQLiteQueryBuilder queryBuilder = new SQLiteQueryBuilder();
            String table = matchTable(uri);
            queryBuilder.setTables(table);

            SQLiteDatabase db = getDBHelper().getReadableDatabase();
            Cursor cursor = queryBuilder.query(db,
                    projection,
                    selection,
                    selectionArgs,
                    null,
                    null,
                    sortOrder
            );

            if (getContext() == null) {
                return null;
            }
            cursor.setNotificationUri(getContext().getContentResolver(), uri);
            return cursor;
        }
    }


    @Override
    public Uri insert(@NonNull Uri uri, ContentValues values) throws SQLException {
        Log.d(TAG, "real insert ");
        synchronized (DBLock) {
            String table = matchTable(uri);
            SQLiteDatabase db = getDBHelper().getWritableDatabase();
            long rowId = 0;
            db.beginTransaction();
            try {
                rowId = db.insertWithOnConflict(table, null, values, SQLiteDatabase.CONFLICT_REPLACE);
                db.setTransactionSuccessful();
            } catch (Exception e) {
                Log.e(TAG, e.getMessage());
            } finally {
                db.endTransaction();
            }
            if (rowId > 0) {
                Uri returnUri = ContentUris.withAppendedId(uri, rowId);
                if (getContext() == null) {
                    return null;
                }
                getContext().getContentResolver().notifyChange(uri, null);
                return returnUri;
            }
            throw new SQLException("Failed to insert row into " + uri);
        }
    }

    @Override
    public int bulkInsert(@NonNull Uri uri, @NonNull ContentValues[] values) {
        Log.d(TAG, "bulkInsert ");
        synchronized (DBLock) {
            String table = matchTable(uri);
            SQLiteDatabase db = getDBHelper().getWritableDatabase();
            db.beginTransaction();
            try {
                for (ContentValues contentValues : values) {
                    db.insertWithOnConflict(table, BaseColumns._ID, contentValues,
                            SQLiteDatabase.CONFLICT_IGNORE);
                }
                db.setTransactionSuccessful();
                if (getContext() == null) {
                    return -1;
                }
                getContext().getContentResolver().notifyChange(uri, null);
                return values.length;
            } catch (Exception e) {
                Log.e(TAG, e.getMessage());
            } finally {
                db.endTransaction();
            }
            throw new SQLException("Failed to insert row into " + uri);
        }
    }

    @Override
    public int delete(@NonNull Uri uri, String selection, String[] selectionArgs) {
        Log.d(TAG, "delete ");
        synchronized (DBLock) {
            SQLiteDatabase db = getDBHelper().getWritableDatabase();

            int count = 0;
            String table = matchTable(uri);
            db.beginTransaction();
            try {
                count = db.delete(table, selection, selectionArgs);
                db.setTransactionSuccessful();
            } finally {
                db.endTransaction();
            }

            if (getContext() == null) {
                return -1;
            }
            getContext().getContentResolver().notifyChange(uri, null);
            return count;
        }
    }

    @Override
    public int update(@NonNull Uri uri, ContentValues values,
                      String selection, String[] selectionArgs) {
        Log.d(TAG, "update ");
        synchronized (DBLock) {
            SQLiteDatabase db = getDBHelper().getWritableDatabase();
            int count;
            String table = matchTable(uri);
            db.beginTransaction();
            try {
                count = db.update(table, values, selection, selectionArgs);
                db.setTransactionSuccessful();
            } finally {
                db.endTransaction();
            }

            if (getContext() == null) {
                return -1;
            }
            getContext().getContentResolver().notifyChange(uri, null);
            return count;
        }
    }

    static class DBHelper extends SQLiteOpenHelper {
        private static final String DB_NAME = "log.db";

        private static final int VERSION = 2;
        private Context mContext;

        private DBHelper(Context context) {
            super(context, DB_NAME, null, VERSION);
            mContext = context;
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            for (SQLiteTable table : sTables.values()) {
                table.create(db);
            }
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            Log.d(TAG, "onUpgrade old:" + oldVersion + " new:" +newVersion);
            for (SQLiteTable table : sTables.values()) {
                if (oldVersion == 1 && newVersion == 2){
                    Log.d(TAG, "onUpgrade deleteTable:" + table.getTableName());
                    table.delete(db);
                }
                table.create(db);
            }
        }

        @Override
        public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            for (SQLiteTable table : sTables.values()) {
                table.delete(db);
                table.create(db);
            }
        }
    }
}
