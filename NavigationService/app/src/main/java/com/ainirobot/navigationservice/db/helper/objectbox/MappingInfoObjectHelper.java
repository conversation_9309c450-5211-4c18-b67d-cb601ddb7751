package com.ainirobot.navigationservice.db.helper.objectbox;

import android.util.Log;

import androidx.annotation.NonNull;

import com.ainirobot.navigationservice.db.entity.MappingInfo;
import com.ainirobot.navigationservice.db.entity.MappingInfo_;
import com.ainirobot.navigationservice.db.helper.iml.MappingInfoHelperIml;

import java.util.List;

import io.objectbox.Box;
import io.objectbox.BoxStore;
import io.objectbox.query.Query;

public class MappingInfoObjectHelper extends BaseObjectHelper<MappingInfo> implements MappingInfoHelperIml {
    public MappingInfoObjectHelper(BoxStore boxStore) {
        super(boxStore);
    }

    @NonNull
    @Override
    public List<MappingInfo> getMappingInfoByMapName(String[] mapNames) {
        long startTime = System.currentTimeMillis();
        Query<MappingInfo> mappingQuery = getBox()
                .query(MappingInfo_.mapName.oneOf(mapNames))
                .build();
        List<MappingInfo> mappingInfos = mappingQuery.find();
        Log.i(TAG, "获取Mapping花费" + (System.currentTimeMillis() - startTime) + "毫秒");
        mappingQuery.close();
        return mappingInfos;
    }

    @Override
    public void deleteMappingInfo(String mapName) {
        Query<MappingInfo> mappingInfoQuery = getMappingInfoQuery(mapName);
        boolean remove = mappingInfoQuery.remove() > 0;
        mappingInfoQuery.close();
        Log.d(TAG, "deleteMappingInfo : " + remove);
    }

    @Override
    public void initMappingData(List<MappingInfo> mappingList) {
        if (null == mappingList || mappingList.isEmpty()) {
            Log.d(TAG, "initMappingData: list is null");
            return;
        }
        Log.d(TAG, "initMappingData: start");
        Box<MappingInfo> mappingInfoBox = getBox();
        mappingInfoBox.removeAll();
        mappingInfoBox.put(mappingList);
        Log.d(TAG, "initMappingData: " + mappingInfoBox.count());
    }

    @Override
    public boolean updateMappingInfo(MappingInfo mappingInfo) {
        Box<MappingInfo> mappingInfoBox = getBox();
        return mappingInfoBox.put(mappingInfo) > 0;
    }

    @Override
    public List<MappingInfo> getMappingInfoByMapName(String mapName) {
        long startTime = System.currentTimeMillis();
        Query<MappingInfo> mappingQuery = getMappingInfoQuery(mapName);
        List<MappingInfo> mappingInfos = mappingQuery.find();
        Log.i(TAG, "获取Mapping花费" + (System.currentTimeMillis() - startTime) + "毫秒");
        mappingQuery.close();
        return mappingInfos;
    }

    private Query<MappingInfo> getMappingInfoQuery(String mapName) {
        return getBox()
                .query(MappingInfo_.mapName.equal(mapName)).build();
    }
}
