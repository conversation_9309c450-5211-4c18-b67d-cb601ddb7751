package com.ainirobot.navigationservice.utils;

import java.text.SimpleDateFormat;
import java.util.Locale;

public class TimeUtils {

    public static String date2TimeStamp(String date_str, String format) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.US);
            return String.valueOf(sdf.parse(date_str).getTime() / 1000);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
}
