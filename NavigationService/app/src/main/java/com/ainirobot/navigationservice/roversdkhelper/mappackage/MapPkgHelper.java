package com.ainirobot.navigationservice.roversdkhelper.mappackage;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.base.analytics.utils.Md5Util;
import com.ainirobot.navigationservice.db.NavigationDataManager;
import com.ainirobot.navigationservice.roversdkhelper.MapFileHelper;
import com.ainirobot.navigationservice.utils.FileUtils;
import com.ainirobot.navigationservice.utils.GsonUtil;
import com.ainirobot.navigationservice.utils.MapUtils;
import com.ainirobot.navigationservice.utils.ThreadUtils;
import com.ainirobot.navigationservice.utils.ZipUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 地图包结构管理，旧包结构兼容
 *
 * <p> 本地使用新包结构，上传服务端保持旧包结构：
 * >上传地图打包，把新包结构的文件按照旧结构打包，保证服务端都是统一的旧包结构
 * >下载地图，先修改为新的包结构，保证机器端是统一的新包结构
 */
public class MapPkgHelper {
    private static final String TAG = "MapPkgHelper";

    private static final String PGM_ZIP = "pgm.zip";
    private static final String DATA_ZIP = "data.zip";

    /**
     * 初始化旧包结构全集，用于上传地图打包依据，最新版本的全集。
     * 打包上传时候根据mapConfig.json
     */
    private static MapPkgDes mMapPkg = new MapPkgDes();

    static {
        mMapPkg.getPgmZip().add(new MapFileDes().setName("map.pgm").setRelPath("navi_data/map.pgm"));
        mMapPkg.getPgmZip().add(new MapFileDes().setName("targets.data").setRelPath("navi_data/targets.data"));

        mMapPkg.getDataZip().add(new MapFileDes().setName("config.xml").setRelPath("navi_data/config.xml"));
        mMapPkg.getDataZip().add(new MapFileDes().setName("config.json").setRelPath("navi_data/config.json"));
        mMapPkg.getDataZip().add(new MapFileDes().setName("frame.data").setRelPath("navi_data/frame.data"));
        mMapPkg.getDataZip().add(new MapFileDes().setName("mapping_track.data").setRelPath("navi_data/mapping_track.data"));
        mMapPkg.getDataZip().add(new MapFileDes().setName("probabilitymap.data").setRelPath("navi_data/probabilitymap.data"));
        mMapPkg.getDataZip().add(new MapFileDes().setName("probpyramids.data").setRelPath("navi_data/probpyramids.data"));
        mMapPkg.getDataZip().add(new MapFileDes().setName("visual_map.data").setRelPath("navi_data/visual_map.data"));
        mMapPkg.getDataZip().add(new MapFileDes().setName("road_graph.data").setRelPath("navi_data/road_graph.data"));
        mMapPkg.getDataZip().add(new MapFileDes().setName("vision_map.data").setRelPath("navi_data/vision_map.data"));

        mMapPkg.getMapPkg().add(new MapFileDes().setName(PGM_ZIP).setRelPath(PGM_ZIP));
        mMapPkg.getMapPkg().add(new MapFileDes().setName(DATA_ZIP).setRelPath(DATA_ZIP));
        mMapPkg.getMapPkg().add(new MapFileDes().setName("road.json").setRelPath("road.json"));
        mMapPkg.getMapPkg().add(new MapFileDes().setName("target.json").setRelPath("target.json"));
        mMapPkg.getMapPkg().add(new MapFileDes().setName("mapping_track.json").setRelPath("mapping_track.json"));
        mMapPkg.getMapPkg().add(new MapFileDes().setName("mapinfo.json").setRelPath("mapinfo.json"));
        mMapPkg.getMapPkg().add(new MapFileDes().setName("place.json").setRelPath("place.json"));
        mMapPkg.getMapPkg().add(new MapFileDes().setName("place.properties").setRelPath("place.properties"));
        mMapPkg.getMapPkg().add(new MapFileDes().setName("mapConfig.json").setRelPath("mapConfig.json"));
        mMapPkg.getMapPkg().add(new MapFileDes().setName("map_area.json").setRelPath("map_area.json"));

        mMapPkg.getExtraFiles().add(new MapFileDes().setName("vision_map_raw_images.data").setRelPath("navi_data/vision_map_raw_images.data"));
    }

    public static MapPkgDes getDefaultMapPkg() {
        return mMapPkg;
    }

    public static void localPkg2Server(String mapName, Callback callback) {
        Log.d(TAG, "localPkg2Server: mapName=" + mapName);
        if(TextUtils.isEmpty(mapName)){
            Log.d(TAG, "localPkg2Server: Map name empty!");
            callback.onFinished(false, "", "");
            return;
        }
        File mapDir = MapFileHelper.getMapDir(mapName);
        if (!mapDir.exists() || mapDir.length() <= 0) {
            Log.d(TAG, "localPkg2Server: Map dir not exists or empty!");
            callback.onFinished(false, "", "");
            return;
        }
        ThreadUtils.getIoService().submit(() -> {
            Log.d(TAG, "localPkg2Server: Start!");
            long startTime = System.currentTimeMillis();
            String mapPath = MapFileHelper.getMapFilePath(mapName);
            Log.d(TAG, "localPkg2Server: mapPath=" + mapPath);
            MapPkgDes mapConfig = getMapConfig(mapName);
            //--打包 pgm.zip
            List<String> pgmFiles = new ArrayList<>();
            for (MapFileDes des : mapConfig.getPgmZip()) {
                pgmFiles.add(mapPath + des.getRelPath());
            }
            boolean pgmZipResult = ZipUtil.zipFiles(pgmFiles, mapPath + PGM_ZIP, "");
            Log.d(TAG, "localPkg2Server: pgmZipResult=" + pgmZipResult);
            if (!pgmZipResult) {
                Log.d(TAG, "localPkg2Server: Zip pgm.zip fail!");
                callback.onFinished(false, "", "");
                return;
            }
            //--打包 data.zip
            List<String> dataFiles = new ArrayList<>();
            for (MapFileDes des : mapConfig.getDataZip()) {
                dataFiles.add(mapPath + des.getRelPath());
            }
            boolean dataZipResult = ZipUtil.zipFiles(dataFiles, mapPath + DATA_ZIP, "");
            Log.d(TAG, "localPkg2Server: dataZipResult=" + dataZipResult);
            if (!dataZipResult) {
                Log.d(TAG, "localPkg2Server: Zip data.zip fail!");
                callback.onFinished(false, "", "");
                return;
            }
            //重新打包 mapinfo.json
            MapUtils.saveMapInfoJson(mapName, NavigationDataManager.getInstance().getMapByName(mapName));//localPkg2Server
            //如果不存在 mapConfig.json，则创建
            if (MapFileHelper.isMapConfigJsonExists(mapName)) {
                Log.d(TAG, "localPkg2Server: Save map config json file!");
                MapUtils.saveMapConfig(mapName);//localPkg2Server
            }
            // 数据库迁移版本，结束建图时丢失mapping_track.json；增加兜底，如果没有json文件会重新转换，转换工具类本身有对mapping_track.data的判空，此处不处理
            if (!MapFileHelper.isTrackJsonExists(mapName)) {
                int resultCode = MapUtils.transferTrackData2Json(mapName);
                Log.d(TAG, "transferTrackData2Json: mapName=" + mapName + " result=" + resultCode);
            }
            //--打包地图整包 mapName.zip
            List<String> mapFiles = new ArrayList<>();
            for (MapFileDes des : mapConfig.getMapPkg()) {
                mapFiles.add(mapPath + des.getRelPath());
            }
            String mapZipPath = MapFileHelper.getMapZipPath(mapName);
            boolean mapZipResult = ZipUtil.zipFiles(mapFiles, mapZipPath, mapName);
            Log.d(TAG, "localPkg2Server: mapZipResult=" + mapZipResult);
            //地图整包打包完成后，删除pgm.zip和data.zip
            deletePgmAndDataZip(mapName);//localPkg2Server
            if (!mapZipResult) {
                Log.d(TAG, "localPkg2Server: Zip mapName.zip fail!");
                callback.onFinished(false, "", "");
                return;
            }
            callback.onFinished(true, mapZipPath, "");
            Log.d(TAG, "localPkg2Server: Success! Cost time=" +
                    (System.currentTimeMillis() - startTime) + "(ms)");
        });
    }

    public static void serverPkg2Local(String mapName, Callback callback) {
        ThreadUtils.getIoService().submit(new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "serverPkg2Local: mapName=" + mapName);
                File mapZip = MapFileHelper.getMapZipFile(mapName);
                if (!mapZip.exists()) {
                    Log.d(TAG, "serverPkg2Local: mapZip not exists!");
                    callback.onFinished(false, "", "");
                    return;
                }
                String mapZipPath = MapFileHelper.getMapZipPath(mapName);
                String mapPath = MapFileHelper.getMapFilePath(mapName);
                String naviDataPath = MapFileHelper.getNaviDataPath(mapName);
                Log.d(TAG, "serverPkg2Local: mapZipPath=" + mapZipPath +
                        " mapPath=" + mapPath + " naviDataPath=" + naviDataPath);
                //解压整包 mapName.zip 到 mapName/
                boolean unZipMapPkg = ZipUtil.unZipFolder(mapZipPath, MapFileHelper.getRobotMapPath());
                if (!unZipMapPkg) {
                    Log.d(TAG, "serverPkg2Local: unZipMapPkg fail!");
                    callback.onFinished(false, "", "");
                    return;
                }
                //解压 pgm.zip 到 navi_path/
                boolean unZipPgm = ZipUtil.unZipFolder(mapPath + PGM_ZIP, naviDataPath);
                if (!unZipPgm) {
                    Log.d(TAG, "serverPkg2Local: unZipPgm fail!");
                    callback.onFinished(false, "", "");
                    return;
                }
                //解压 data.zip 到 navi_path/
                boolean unZipData = ZipUtil.unZipFolder(mapPath + DATA_ZIP, naviDataPath);
                if (!unZipData) {
                    Log.d(TAG, "serverPkg2Local: unZipData fail!");
                    callback.onFinished(false, "", "");
                    return;
                }
                //pgm.zip 和 data.zip 解压缩后就删除
                deletePgmAndDataZip(mapName);//serverPkg2Local
                callback.onFinished(true, mapZipPath, "");
                Log.d(TAG, "serverPkg2Local: Success!");
            }
        });
    }

    private static void deletePgmAndDataZip(String mapName) {
        Log.d(TAG, "deletePgmAndDataZip: mapName=" + mapName);
        String mapPath = MapFileHelper.getMapFilePath(mapName);
        String pgmZipPath = mapPath + PGM_ZIP;
        String dataZipPath = mapPath + DATA_ZIP;
        File pgmZipFile = new File(pgmZipPath);
        File dataZipFile = new File(dataZipPath);
        if (pgmZipFile.exists()) pgmZipFile.delete();
        if (dataZipFile.exists()) dataZipFile.delete();
    }

    public static void hasNavigationExtraData(String mapName, Callback callback) {
        ThreadUtils.getIoService().submit(() -> {
            List<MapFileDes> extraFileList = getMapConfig(mapName).getExtraFiles();
            if (null == extraFileList) {
                Log.d(TAG, "hasNavigationExtraData: extraFileList is null ");
                callback.onFinished(false, "", "");
                return;
            }
            boolean hasExtra = !extraFileList.isEmpty();
            for (MapFileDes fileBean : extraFileList) {
                File extraFile = new File(MapFileHelper.getMapFilePath(mapName), fileBean.getRelPath());
                Log.d(TAG, "hasNavigationExtraData: " + extraFile.getAbsolutePath());
                try {
                    boolean exists = extraFile.exists();
                    String md5 = "";
                    if (!exists || !fileBean.getMd5().equalsIgnoreCase(md5 = Md5Util.getFileMD5(extraFile))) {
                        hasExtra = false;
                        Log.d(TAG, "hasNavigationExtraData: " + exists + " md5:" + md5);
                        break;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            Log.d(TAG, "hasNavigationExtraData: hasExtra=" + hasExtra + " mapName=" + mapName);
            callback.onFinished(hasExtra, MapFileHelper.getTempExtraFilePath(mapName), "");
        });
    }

    /**
     * 把机器端本地地图转化为新的包结构，
     * 纯文件操作，只根据旧的包接口目录和文件判断是否更新
     */
    public static void transferLocalMap2NewDirectoryStructure() {
        Log.d(TAG, "transferLocalMap2NewDirectoryStructure: Start!");
        long startTime = System.currentTimeMillis();
        File robotMapDir = MapFileHelper.getRobotMapDir();
        File[] files = robotMapDir.listFiles();
        for (File file : files) {
            if (file.isFile() || file.length() <= 0) {
                continue;
            }
            String mapName = file.getName();
            Log.d(TAG, "transferLocalMap2NewDirectoryStructure: mapName=" + mapName);
            File[] subFiles = file.listFiles();
            for (File file1 : subFiles) {
                if (file1.isDirectory() || file1.length() <= 0) {
                    continue;
                }
                String name = file1.getName();
                if (PGM_ZIP.equals(name) || DATA_ZIP.equals(name)) {
                    String naviDataPath = MapFileHelper.getNaviDataPath(mapName);
                    File naviDataFile = new File(naviDataPath);
                    if (!naviDataFile.exists()) naviDataFile.mkdirs();
                    Log.d(TAG, "transferLocalMap2NewDirectoryStructure: Unzip " +
                            name + " to navi_data/");
                    boolean unzipResult = ZipUtil.unZipFolder(file1.getAbsolutePath(), naviDataPath);
                    boolean deleteResult = file1.delete();
                    Log.d(TAG, "transferLocalMap2NewDirectoryStructure: unzipResult=" +
                            unzipResult + " deleteResult=" + deleteResult);
                }
            }
        }
        Log.d(TAG, "transferLocalMap2NewDirectoryStructure: Done! Cost time=" +
                (System.currentTimeMillis() - startTime) + "(ms)");
    }

    public static void zipNavigationExtraData(String mapName, MapPkgHelper.Callback callback) {
        List<MapFileDes> extraFileList = getMapConfig(mapName).getExtraFiles();
        if (null == extraFileList) {
            Log.d(TAG, "zipNavigationExtraData extraFileList is null ");
            return;
        }
        List<File> list = new ArrayList<>();
        for (MapFileDes fileBean : extraFileList) {
            File file = new File(MapFileHelper.getMapFilePath(mapName), fileBean.getRelPath());
            if (file.exists()) {
                list.add(file);
            }
        }
        ZipUtil.zipFiles(list, MapFileHelper.getTempExtraFilePath(mapName), callback);
    }

    public static void unzipNavigationExtraData(String mapName, Callback callback) {
        ZipUtil.unZipFile(MapFileHelper.getTempExtraFilePath(mapName), MapFileHelper.getNaviDataPath(mapName), callback);
    }

    public interface Callback {
        void onFinished(boolean success, String path, String extraData);
    }

    private static MapPkgDes getMapConfig(String mapName) {
        String config = com.ainirobot.base.util.FileUtils.stringFromFile(MapFileHelper.getMapConfig(mapName));
        MapPkgDes mapPkgDes = GsonUtil.fromJson(config, MapPkgDes.class);
        return mapPkgDes == null ? getDefaultMapPkg() : mapPkgDes;
    }

    public static void copyImportMapFile(String mapName, Callback callback) {
        ThreadUtils.getIoService().submit(new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "copyImportMapFile: mapName=" + mapName);
                if (!MapFileHelper.isImportMapExist(mapName)) {
                    Log.d(TAG, "copyImportMapFile: Map dir not exists!");
                    callback.onFinished(false, "", "");
                    return;
                }

                String importMapDir = MapFileHelper.getImportMapFilePath(mapName);
                String targetMapDir = MapFileHelper.getMapFilePath(mapName);
                Log.d(TAG, "copyImportMapFile: importMapDir=" + importMapDir + " targetMapDir=" + targetMapDir);

                //如果targetMapDir存在则删除
                MapFileHelper.removeMapDir(mapName);

                //importMapDir -> targetMapDir，拷贝
                boolean result = FileUtils.renameFileTo(importMapDir, targetMapDir);
                if (!result) {
                    Log.d(TAG, "copyImportMapFile: Rename fail!");
                    callback.onFinished(false, "", "");
                    return;
                }

                callback.onFinished(true, targetMapDir, "");
                Log.d(TAG, "copyImportMapFile: Success!");
            }
        });
    }

}
