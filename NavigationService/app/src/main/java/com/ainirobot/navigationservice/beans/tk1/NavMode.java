/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file elinearcept in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either elinearpress or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.tk1;

import java.io.DataOutput;
import java.io.IOException;


public class NavMode extends Message {

    private int startModeLevel, brakeModeLevel;

    public NavMode() {
        this(Message.MSG_TYPE_NAV_MODE);
    }

    public NavMode(int type, int startModeLevel, int brakeModeLevel) {
        super(type);
        this.startModeLevel = startModeLevel;
        this.brakeModeLevel = brakeModeLevel;
    }

    public NavMode(int startModeLevel, int brakeModeLevel) {
        this(Message.MSG_TYPE_NAV_MODE, startModeLevel, brakeModeLevel);
    }

    public NavMode(int type) {
        super(type);
    }

    public int getStartModeLevel() {
        return startModeLevel;
    }

    public int getBrakeModeLevel() {
        return brakeModeLevel;
    }

    public void setStartModeLevel(int startModeLevel) {
        this.startModeLevel = startModeLevel;
    }

    public void setBrakeModeLevel(int brakeModeLevel) {
        this.brakeModeLevel = brakeModeLevel;
    }

    @Override
    public void readData(ReverseInputStream in) throws IOException {
        startModeLevel = in.readInt();
        brakeModeLevel = in.readInt();
    }

    @Override
    public void writeTo(DataOutput out) throws IOException {
        out.writeDouble(startModeLevel);
        out.writeDouble(brakeModeLevel);
    }

    @Override
    public String toString() {
        return "startModeLevel=" + startModeLevel + "  brakeModeLevel=" + brakeModeLevel;
    }

}
