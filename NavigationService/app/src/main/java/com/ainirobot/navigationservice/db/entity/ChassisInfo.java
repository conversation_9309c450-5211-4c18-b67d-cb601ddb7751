package com.ainirobot.navigationservice.db.entity;

import io.objectbox.annotation.Entity;
import io.objectbox.annotation.Id;

/**
 * @version V1.0.0
 * @date 2020/7/21 14:15
 */
@Entity
public class ChassisInfo {
    @Id
    public long id;
    private String roverConfig;
    private String ipNavigation;
    private String ipRos;
    private String ipSdkRos;
    /**
     * 目前只有多机版本使用该字段
     */
    private String serverIp;
    private String multiRobotConfig;

    public String getRoverConfig() {
        return roverConfig;
    }

    public ChassisInfo setRoverConfig(String roverConfig) {
        this.roverConfig = roverConfig;
        return this;
    }

    public String getIpNavigation() {
        return ipNavigation;
    }

    public ChassisInfo setIpNavigation(String ipNavigation) {
        this.ipNavigation = ipNavigation;
        return this;
    }

    public String getIpRos() {
        return ipRos;
    }

    public ChassisInfo setIpRos(String ipRos) {
        this.ipRos = ipRos;
        return this;
    }

    public String getIpSdkRos() {
        return ipSdkRos;
    }

    public ChassisInfo setIpSdkRos(String ipSdkRos) {
        this.ipSdkRos = ipSdkRos;
        return this;
    }

    public String getServerIp() {
        return serverIp;
    }

    public ChassisInfo setServerIp(String serverIp) {
        this.serverIp = serverIp;
        return this;
    }

    public ChassisInfo setMultiRobotConfig(String multiRobotConfig){
        this.multiRobotConfig = multiRobotConfig;
        return this;
    }

    public String getMultiRobotConfig() {
        return multiRobotConfig;
    }

    @Override
    public String toString() {
        return "ChassisInfo{" +
                "roverConfig='" + roverConfig + '\'' +
                ", ipNavigation='" + ipNavigation + '\'' +
                ", ipRos='" + ipRos + '\'' +
                ", ipSdkRos='" + ipSdkRos + '\'' +
                ", serverIp='" + serverIp + '\'' +
                ", multiRobotConfig='" + multiRobotConfig + '\'' +
                '}';
    }
}
