/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.navigationservice.chassisAbility.controller;

import android.util.Log;

import com.ainirobot.navigationservice.beans.tk1.Velocity;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.x86.WheelControlX86;

/**
 * <p>
 * 没有水平云台，不需要计算底盘和水平云台的速度差；
 * <p>
 * mini视场角：-40到40,添加畸后可达-45到45
 */
public class MotionPolicyMini {
    private static final String TAG = MotionPolicyMini.class.getSimpleName();

    private enum LinearState {
        STATIC,
        ACCELERATION,
        MAX
    }

    private static LinearState mLinearState = LinearState.STATIC;

    //speed and accelerate
    private static final double MAX_LINEAR_SPEED = 0.9d;
    private static final double MAX_ANGULAR_SPEED = 1.5d;
    private static final double MAX_FOLLOW_LINEAR_SPEED = 1.0d;
    private static final double MAX_FOLLOW_ANGULAR_SPEED = 1.2d;
    private static final double FOLLOW_START_ANGLE = 0;
    private static final double FOLLOW_FAULT_ANGLE = 5;
    private static final double OFFSET_DISTANCE = 0.1d;

    //New
    private static final double DEC_ANGLE = 45; //The deviation from the max of 45 degree
    private static final double START_ANGLE = 0; //The deviation is going to start at 5 degree
    private static final double FAULT_ANGLE = 5;
    private static final double FAULT_ANGLE_SWING = 13;
    private static final double SAFE_DISTANCE = 1.0d;
    private static final double DEC_DISTANCE = 1.0d; //Start deceleration distance
    private static final double ANGULAR_FACTOR = 2.0d;

    //The navigation is reported a very small speed at the stationary state
    private static final double MIN_SPEED = 0.02;

    public static Velocity follow(double distance, double angle, double latency, Velocity velocity) {
        if (latency > 600 || Math.abs(angle) > 230 || distance < 0) {
            Log.d(TAG, "Wrong data , distance="
                    + distance + " angle=" + angle + " latency=" + latency);
            return new Velocity(0, 0);
        }

        double navLinearSpeed = (velocity.getX() < MIN_SPEED ? 0 : velocity.getX());
        double navAngularSpeed = (velocity.getZ() < MIN_SPEED ? 0 : velocity.getZ());

        double finalAngle = angle - Math.toDegrees(navAngularSpeed) * latency / 1000;
        double angularFactor = evalAngularFactor(finalAngle, FAULT_ANGLE);
        double angularSpeed = MAX_ANGULAR_SPEED * angularFactor;

        double finalDistance = (distance == 0 ? 0 : distance - navLinearSpeed * latency / 1000);
        double linearFactor = evalLinearFactor(finalDistance, angularFactor);
        double linearSpeed = MAX_LINEAR_SPEED * linearFactor;

        Log.d(TAG, "Focus follow : distance=" + distance + "  angle=" + angle + "  latency=" + latency
                + "  linear=" + linearSpeed + "  angular=" + angularSpeed
                + ", finalDistance=" + finalDistance + ", finalAngle=" + finalAngle);
        return new Velocity(linearSpeed, angularSpeed);
    }

//    private enum AngleState {
//        STATIC,
//        SWING,
//        ACCELERATION
//    }
//
//    private static AngleState mPreState = AngleState.STATIC;
//
//    private static AngleState getAngleState(double angle) {
//        double angleAbs = Math.abs(angle);
//        if (angleAbs <= 5) {
//            return AngleState.STATIC;
//        } else if (angleAbs <= 13) {
//            return AngleState.SWING;
//        } else {
//            return AngleState.ACCELERATION;
//        }
//    }

    public static double sFaultAngle = FAULT_ANGLE;
    public static double sMinAngleFactor = 0.2;

//    private static boolean isAngleIncrease(double angle) {
//        AngleState newState = getAngleState(angle);
//        switch (newState) {
//            case STATIC: //停止
//                sFaultAngle = FAULT_ANGLE;
//                mPreState = AngleState.STATIC;
//                break;
//            case ACCELERATION: //加速
//                sFaultAngle = FAULT_ANGLE_SWING;
//                mPreState = AngleState.ACCELERATION;
//                break;
//            case SWING: //摇摆区间：正负5 到 正负13
//                if (mPreState == AngleState.STATIC) {
//                    //start acc
//                } else if (mPreState == AngleState.ACCELERATION) { //问题：5-13停下，后立即又转到5以内，有停顿
////                    if (WheelControlX86.getInstance().isStill()) {//没停下来一直给0，停下来改为停止状态，不应该在5-13区间停下
////                        sFaultAngle = FAULT_ANGLE;
////                        mPreState = AngleState.STATIC;
////                    }
//                    //start dec
//                }
//                break;
//            default:
//                break;
//        }
//        return sFaultAngle == FAULT_ANGLE;
//    }

    private static double evalAngularFactor(double angle, double faultAngle) {
//        sMinAngleFactor = isAngleIncrease(angle) ? 0.2 : 0;
//        Log.d(TAG, "Focus follow evalAngularFactor : sMinAngleFactor=" + sMinAngleFactor);

        double sign = -Math.signum(angle);
        angle = Math.abs(angle) - START_ANGLE;
        if (angle <= faultAngle) {
            return 0.0;
        } else if (angle < DEC_ANGLE) {
            double fraction = (angle - faultAngle) / (DEC_ANGLE - faultAngle);
            fraction = (1.0 - Math.pow((1.0 - fraction), ANGULAR_FACTOR));
            if (fraction < sMinAngleFactor) {
                fraction = sMinAngleFactor;
            }
            return fraction * sign;
        } else {
            return 1.0 * sign;
        }
    }

    private static double evalLinearFactor(double distance, double angularFactor) {
        distance = distance - SAFE_DISTANCE;
        angularFactor = 1.0 - Math.abs(angularFactor);
        if (distance <= 0) {
            return 0.0;
        } else if (distance < DEC_DISTANCE) {
            float fraction = (float) (distance / DEC_DISTANCE);
            return fraction * (1.0 + angularFactor) / 2;
        } else {
            return (1.0 + angularFactor) / 2;
        }
    }

    public static Velocity bodyFollow(double distance, double angle, double latency,
                                      Velocity velocity) {
        return bodyFollow(distance, angle, latency, velocity, MAX_FOLLOW_LINEAR_SPEED, MAX_FOLLOW_ANGULAR_SPEED);
    }

    public static Velocity bodyFollow(double distance, double angle, double latency,
                                      Velocity velocity,
                                      double maxLinSpeed, double maxAngSpeed) {
        return bodyFollow(distance, angle, latency, velocity, maxLinSpeed, maxAngSpeed, SAFE_DISTANCE);
    }

    /**
     * 计算人体跟随速度
     */
    public static Velocity bodyFollow(double distance, double angle, double latency,
                                      Velocity velocity,
                                      double maxLinSpeed, double maxAngSpeed, double safeDistance) {
        if (latency > 600 || Math.abs(angle) > 230 || distance < 0) {
            Log.d(TAG, "Wrong data , distance="
                    + distance + " angle=" + angle + " latency=" + latency);
            return new Velocity(0, 0);
        }

        double navLinearSpeed = (velocity.getX() < MIN_SPEED ? 0 : velocity.getX());
        double navAngularSpeed = (velocity.getZ() < MIN_SPEED ? 0 : velocity.getZ());
        double finalAngle = angle - Math.toDegrees(navAngularSpeed) * latency / 1000;
        double finalDistance = (distance == 0 ? 0 : distance - navLinearSpeed * latency / 1000);

        double linearFactor = evalBodyLinearFactorOffset(finalDistance, safeDistance);
        double linearSpeed = maxLinSpeed * linearFactor;

        double angularFactor;
        double angularSpeed;
        if (WheelControlX86.getInstance().isLinearStill(new Velocity(linearSpeed, 0))) {
            angularFactor = evalAngularFactor(finalAngle, FAULT_ANGLE);
            angularSpeed = maxAngSpeed * angularFactor;
        } else {
            angularFactor = evalBodyAngularFactor(finalAngle);
            angularSpeed = maxAngSpeed * angularFactor;
        }

        Log.d(TAG, "Body follow : distance=" + distance + "  angle=" + angle + "  latency=" + latency
                + ", finalDistance=" + finalDistance + ", finalAngle=" + finalAngle
                + ", maxLinSpeed=" + maxLinSpeed + ", maxAngSpeed=" + maxAngSpeed
                + ", safeDistance=" + safeDistance
                + "  linear=" + linearSpeed + "  angular=" + angularSpeed);
        return new Velocity(linearSpeed, angularSpeed);
    }

    private static double evalBodyAngularFactor(double angle) {
        double sign = -Math.signum(angle);
        angle = Math.abs(angle) - FOLLOW_START_ANGLE;
        if (angle <= FOLLOW_FAULT_ANGLE) {
            return 0.0;
        } else if (angle < DEC_ANGLE) {
//            double fraction = (angle - FOLLOW_FAULT_ANGLE) / (DEC_ANGLE - FOLLOW_FAULT_ANGLE);
//            fraction = (1.0 - Math.pow((1.0 - fraction), ANGULAR_FACTOR));
//            return fraction * sign;
            float fraction = (float) (angle / DEC_ANGLE);
            return (1.0 - Math.pow((1.0 - fraction), ANGULAR_FACTOR)) * sign;
        } else {
            return 1.0 * sign;
        }
    }

    private static final double DEC_AREA_FACTOR = 0.8;//加速区间因子，决定加速区间与启动区间倍数关系
    private static final float MIN_DEC_FRACTION = 0.2f;//线速度最小加速因子，防“蠕动”

    private static double evalBodyLinearFactorOffset(double distance,
                                                     double safeDistance) {
        double safeDist = safeDistance;//启动区间
        double decDist = safeDist * DEC_AREA_FACTOR;//加速区间
        distance = distance - safeDist; //stop
        if (distance <= 0) {
            mLinearState = LinearState.STATIC;
            return 0.0;
        } else if (distance < decDist && decDist > 0) {//acceleration
            if (mLinearState == LinearState.STATIC && (distance < OFFSET_DISTANCE)) {//防抖
                return 0.0;
            }
            mLinearState = LinearState.ACCELERATION;
            float fraction = (float) (distance / decDist);
            if (fraction < MIN_DEC_FRACTION) {
                fraction = MIN_DEC_FRACTION;
            }
            return fraction;
        } else {//max
            mLinearState = LinearState.MAX;
            return 1.0;
        }
    }

}