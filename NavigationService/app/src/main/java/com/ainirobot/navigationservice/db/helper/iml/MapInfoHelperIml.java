package com.ainirobot.navigationservice.db.helper.iml;

import com.ainirobot.navigationservice.db.DataChangeListener;
import com.ainirobot.navigationservice.db.entity.MapInfo;

import java.util.List;

public interface MapInfoHelperIml extends BaseHelper<MapInfo> {
    MapInfo getMapByName(String mapName);

    void deleteMapByName(String mapName);

    MapInfo getNavMapInfo();

    void clearUseState();

    void updateUseState(MapInfo mapInfo);

    List<MapInfo> getMapInfos(String[] mapName);

    void initMapInfoData(List<MapInfo> mapInfoList);

    boolean updateMapInfo(MapInfo mapInfo);

    void updateMapInfo(List<MapInfo> mapInfos);

    void setOnDataChangeListener(DataChangeListener listener);
}
