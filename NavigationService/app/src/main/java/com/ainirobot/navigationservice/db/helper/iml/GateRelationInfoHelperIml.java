package com.ainirobot.navigationservice.db.helper.iml;

import com.ainirobot.navigationservice.db.entity.GateRelationInfo;
import java.util.List;

/**
 * 数据访问接口
 */
public interface GateRelationInfoHelperIml extends BaseHelper<GateRelationInfo>{

    List<GateRelationInfo> findByGateIds(List<String> validGateIds);

    List<GateRelationInfo> getAllGateRelationData();

    List<GateRelationInfo> findByLineIds(List<Integer> validGateLineIds);


    void batchInsertOrUpdateGate(List<GateRelationInfo> gateRelationInfos);

    long deleteByLineIds(List<Integer> validGateLineIds);

    long deleteByGateIds(List<String> validGateIds);

    List<GateRelationInfo> deleteExceptLineIds(List<Integer> validGateLineIds);

}
