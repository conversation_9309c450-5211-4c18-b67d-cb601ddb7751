package com.ainirobot.navigationservice.chassisAbility.chassis.client;

import android.content.Context;

import com.ainirobot.navigationservice.beans.MappingPose;
import com.ainirobot.navigationservice.beans.tk1.Laser;
import com.ainirobot.navigationservice.beans.tk1.Map;
import com.ainirobot.navigationservice.beans.tk1.NavAcceleration;
import com.ainirobot.navigationservice.beans.tk1.NavVelocity;
import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.tk1.Statistic;
import com.ainirobot.navigationservice.beans.tk1.TargetPose;
import com.ainirobot.navigationservice.beans.tk1.Velocity;
import com.ainirobot.navigationservice.beans.waiter.CameraBean;
import com.ainirobot.navigationservice.beans.waiter.MultiRobotConfigBean;
import com.ainirobot.navigationservice.beans.waiter.NaviPathDetail;
import com.ainirobot.navigationservice.beans.waiter.NaviPathInfo;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.tk1.customListener.CreateMapStop;
import com.ainirobot.navigationservice.roversdkhelper.maptype.NaviMapType;

import java.util.ArrayList;
import java.util.List;

import ninjia.android.proto.ChassisPacketProtoWrapper;

public interface IChassisClient {

    void init(Context context);

    void setRoverConfig(RoverConfig roverConfig, ChassisResListener listener);

    void getRoverConfig(ChassisResListener listener);

    void setTime(long time, ChassisResListener listener);

    boolean isCommunicating();

    boolean takeSnapshot(String logID, ChassisResListener listener);

    boolean isServiceReady();

    boolean isSocketConnected();

    boolean isChassisReady();

    void setEventListener(ChassisEventListener listener);

    Pose getCurrentPose();

    /**
     * 获取相对于机器人开机时刻的实时位姿，不受定位模式影响
     */
    Pose getRealtimePose();

    ChassisPacketProtoWrapper.RealtimeObsMapProto getRealtimeObsMapProto();

    Pose getCurrentPoseWithoutEstimate();

    Velocity getVelocity();

    Velocity getRealtimeVelocity();

    List<Laser> getLasersData();

    void updateMotionAvoidState(boolean withAvoid);

    boolean isPoseEstimate();

    void checkCurNaviMap(ChassisResListener listener);

    boolean isMoving();

    /**
     * 切换地图，底盘新架构调整后使用loadMap替代  2020.8.14
     */
//    void switchMap(String mapName, ChassisResListener listener);

    void switchMap(String mapName, ChassisResListener listener);

    void loadCurrentMap(boolean useCustomKeepPose, boolean keepPose, ChassisResListener listener);

    void startCreatingMap(NaviMapType naviMapType, ChassisResListener listener);

    /**
     * 停止建图，底盘新架构停止建图需要上层确认是否保存地图，TK1老架构save指令无效 2020.8.14
     */
    void stopCreatingMap(String mapName, boolean save, String language, int type, int finishState, CreateMapStop listener);

    /**
     * 停止扩建地图，只结束建图即可，不需要处理文件和保存地图信息
     */
    void stopExtendMap(String mapName, ChassisResListener listener);

    void go(TargetPose targetPose, ChassisResListener listener);

    void go(TargetPose targetPose, NavVelocity navVelocity, NavAcceleration navAcc, ChassisResListener listener);

    void cancelNavigation(ChassisResListener listener);

    void setPoseEstimate(Pose pose, ChassisResListener listener);

    void setFixedEstimate(Pose pose, ChassisResListener listener);

    void setForceEstimate(Pose pose, ChassisResListener listener);

    void setVisionEstimate(ChassisResListener listener);

    void resetPoseEstimate(ChassisResListener listener);

    void setChassisRelocation(int type, Pose pose, ChassisResListener listener);

    void stopMove();

    void motion(double angularSpeed, double linearSpeed, double acceleration);

    void motion(double angularSpeed, double linearSpeed, double acceleration, boolean hasAcceleration);

    void motionWithObstacles(double angularSpeed, double linearSpeed, double acceleration);

    //默认基础运动使用动态避停策略
    void motionWithObstacles(double angularSpeed, double linearSpeed, double acceleration, double minDistance);

    //静态避停策略，人体跟随使用
    void motionWithStaticObstacles(double angularSpeed, double linearSpeed, double minDistance);

    void motionWithOnceObstacle(double angularSpeed, double linearSpeed, boolean hasAcceleration);

    void motionControlWithObstacle(double angularSpeed, double linearSpeed, double minDistance);

    void motionSoft(double angularSpeed, double linearSpeed, boolean hasAcceleration);

    void turnLeft(double angle, double speed, double acceleration, boolean noNeedAcceleration, ChassisResListener listener);

    void turnRight(double angle, double speed, double acceleration, boolean noNeedAcceleration, ChassisResListener listener);

    void forward(double distance, double speed, double acceleration, ChassisResListener listener);

    void forward(double distance, double speed, double acceleration, boolean avoid, ChassisResListener listener);

    void backward(double distance, double speed, double acceleration, ChassisResListener listener);

    void rotateInPlace(int direction, double angle, double speed, ChassisResListener listener);

    void getFullCheckStatus(ChassisResListener listener);

    void getSensorStatus(ChassisResListener listener);

    /**
     * 获取底盘日志信息
     * @param startTime 起始时间，TK1架构无需关注
     * @param endTime   截止时间,TK1架构无需关注
     * @param cmdType   上层下发获取日志的文件类型
     * @param fileType  TK1老架构保留参数，845项目无需关注
     * @param path      文件保存路径
     * @param listener
     * @return
     */
    boolean getLogFile(long startTime, long endTime, String cmdType, String fileType, String path, ChassisResListener listener);

    void removeTkMap(String mapName, ChassisResListener listener);

    boolean packLogFile(long startTime, long endTime, ChassisResListener listener);

    boolean startPlanRoute();

    void savePlanRoute(String routeName, List<Pose> poseList);

    void getSystemInformation(ChassisResListener listener);

    boolean hasObstacle(double startAngle, double endAngle, double distance);
    boolean hasObstacleInArea(double startAngle, double endAngle, double minDistance, double maxDistance);
    void recoveryNavigation(ChassisResListener listener);

    boolean getHasVision();

    void goCharge(boolean isFrontCamera, ChassisResListener listener);

    void stopCharge(ChassisResListener listener);

    String getMapStatus(String cmdType, String cmdParam);

    void addMappingPose(Pose pose, ChassisResListener listener);

    void deleteMappingPose(int poseId, ChassisResListener listener);

    void setMinObstaclesDistance(double distance);

    void resetMinObstaclesDistance();

    void setSimpleEventListener(SimpleEventListener mSimpleEventListener);

    Velocity getFollowVelocity(double distance, double angle,
                               double headAngleSpeed, Velocity velocity, double latency);

    Velocity getBodyFollowVelocity(double distance, double angle, double latency, double headAngleSpeed,
                                   Velocity velocity, double maxLinSpeed, double maxAngSpeed, double safeDistance);

    Velocity getBodyFollowVelocity(double distance, double angle,
                                   double headAngleSpeed, Velocity velocity, double latency);

    Velocity getFollowVelocity(double angle,double latency);

    /** ------------------以下为底盘新架构调整改动接口------------ */
//    void changeNaviMode(WorkMode workMode, ChassisResListener listener);
//
//    void setWorkMode(WorkMode mode, ChassisResListener listener);
//
//    void getWorkingMode(ChassisResListener listener);
//
//    void switchMotionMode(MotionMode mode);
//
//    void switchMotionMode(MotionMode mode, ChassisResListener listener);
//
//    void updateCurrentMotionMode(MotionMode mode);
//
//    void getMotionMode(ChassisResListener listener);

    /** ------------------以下为新架构新增接口------------ */
    void setCurrentWorkModeFree(ChassisResListener listener);

    void startExtendMap(ChassisResListener listener);

    void setRadarState(boolean state, ChassisResListener listener);

    void getRadarState(ChassisResListener listener);

    void switchChargeMode();

    void switchManualMode();

    /**
     * 此接口是为了兼容三个平台底盘添加，目前只有x86使用
     */
    void sendPrimitiveMovingSpeed(double angularSpeed, double linearSpeed);

    void motionPid(double angularSpeed, double linearSpeed);

    void setCameraEnable(int cameraType, boolean enable, ChassisResListener listener);

    CameraBean queryCameraEnableState(int cameraType);

    /**
     * 下发多机参数配置信息
     */
    void setMultiRobotSettingConfigData(MultiRobotConfigBean configBean, ChassisResListener listener);

    /**
     * 下发Lora多机数据,暂时无效
     * @param dataInfo
     * @param listener
     */
    void sendLoraMsgData(String dataInfo, ChassisResListener listener);

    /**
     * 下发多机任务优先级，值越大优先级越高
     * @param priority  优先级
     * @return true 下发成功 false 下发失败
     */
    boolean setNavigationPriority(int priority);

    /**
     * 发送lora测试数据
     */
    void sendLoraTestMsg(ChassisResListener listener);

    /**
     * 打开lora收发测试模式,仅招财豹工厂版本使用
     * @param enable    true为打开测试模式，false为关闭测试模式
     */
    void setLoraTestMode(boolean enable, ChassisResListener listener);

    /**
     * 重置Lora配置，仅招财豹工厂版本使用
     */
    void resetLoraDefaultConfig(ChassisResListener listener);

    void setWheelControlMode(boolean isRelease, ChassisResListener listener);

    void calcNaviPathInfo(List<NaviPathInfo> pathInfos, ChassisResListener listener);

    void calcNaviPathDetail(List<NaviPathDetail> pathInfos, ChassisResListener listener);

    /**
     * setMultiRobotWriteExtraData这个接口最开始用于下发多机补偿数据，现已不再下发多机补偿数据
     * 最新用来给多机数据中新增数据（是否在充电、充电优先级）
     */
    void setMultiRobotWriteExtraData(byte[] data, double time, ChassisResListener listener);

    //新增了一个写入外部多机数据的接口。用来给WIFI NAN使用
    void setMultiRobotWriteExternalData(byte[] data, double time, ChassisResListener listener);

    /**
     * 控制 底盘是否给上层上报雷达数据的接口
     */
    void enableReportLineData(boolean enable);

    /**
     * 控制 底盘是否给上层上报深度图的接口
     */
    void enableReportDepthImage(boolean enable, int deviceId);

    /**
     * 控制 底盘是否给上层上报IR图的接口
     */
    void enableReportIRImage(boolean enable, int deviceId);

    /**
     * 开启录制数据集
     */
    void startDataSetRecord(String sensorString);

    /**
     * 停止录制数据集
     */
    void stopDataSetRecord(boolean isLocalData);

    /**
     * 上传录制的导航数据集
     */
    void uploadNaviDataSet(String sensorString);

    /**
     * 自动生成巡线
     */
    void autoDrawRoadGraph(String mapName, ChassisResListener listener);

    void gotoAlign(Pose pose, ChassisResListener listener);

    void cancelAlign(ChassisResListener listener);

    void setOdomUpdateListener(OdomUpdate listener);

    interface OdomUpdate {
        void updateOdom(double move, double leftAcc, double rightAcc);
    }

    /**
     * 获得导航参数
     * @param type     参数type
     */
    void getNaviParams(String type,String params, ChassisResListener listener);

    /**
     * 导航暂停接口
     * @param isPause  参数isPause
     */
    void naviPause(boolean isPause, ChassisResListener listener);

    /**
     * 开始人体跟随
     */
    void startHumanFollowing(String followId, int lostFindTimeout, ChassisResListener listener);

    /**
     * 结束人体跟随
     */
    void stopHumanFollowing(ChassisResListener listener);

    /**
     * @param path 图片路径
     * 根据图片识别二维码数据
     */
    void detectQrCodeByPic(String path, ChassisResListener listener);

    interface ChassisResListener {
        void onResponse(boolean status, int resultCode, Object result);
    }

    /**
     * SimpleEventListener will simple the implements methods
     */
    interface SimpleEventListener {
        void onUpdateChassisConnectStatus(String channelName, String state);
    }

    interface ChassisEventListener {
        void onEvent(String key, Object status);

        void onMapUpdate(Map map);

        void onPushUpdate(String key);

        void onOutMap(int key);

        void onVelocityUpdate(Velocity velocity);

        void onPoseUpdate(Pose pose);

        void onException(String type, String data);

        void onPoseEstimate(boolean isPoseEstimate);

        void onStatisticUpdate(Statistic statistic);

        void onPackLogEnd(String from, Object status);

        void onTakeSnapshotEnd(String from, String fileID, String path);

        void onChassisDisconnect(String msg, String type);

        void onChassisConnectStatus(String channelName, String state);

        void OnUpdateCreateMapProcess(String additional);

        void OnRadarUpdate(boolean openRadar);

        void OnRadarUpdate(boolean openRadar, int status);

        void onChassisServiceState(boolean isReady);

        void onMonoInfoUpdate(String additional);

        void onLaserDataUpdate(ArrayList<Laser> data);

        void onAvoidStateChange(boolean isStopping);

        void onStaticAvoidStateChange(boolean isStopping);

        void onObstacleReport();

        void onOdometerUpdate(double move, double leftAcc, double rightAcc);

        void onMappingPoseUpdate(List<MappingPose> list);

        void onMultipleRobotStatusUpdate(String key ,String multiData);

        void onLoraDataUpdate(String loraData);

        void onMappingVisionInfoUpdate(String additional);

        void onLineDataUpdate(List<com.ainirobot.coreservice.client.actionbean.Pose> list);
    }
}
