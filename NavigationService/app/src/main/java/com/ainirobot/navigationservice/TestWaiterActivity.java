/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;
import static com.ainirobot.navigationservice.beans.tk1.TargetPose.STATUS_AVOID_END;
import static com.ainirobot.navigationservice.beans.tk1.TargetPose.STATUS_OUT_MAP_END;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.speech.tts.TextToSpeech;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.navigationservice.Defs.Def;
import com.ainirobot.navigationservice.beans.tk1.BaseEvent;
import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.tk1.TargetPose;
import com.ainirobot.navigationservice.chassisAbility.ChassisManager;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.commonModule.data.DataManager;
import com.ainirobot.navigationservice.db.entity.PlaceInfo;
import com.ainirobot.navigationservice.db.NavigationDataManager;
import com.ainirobot.navigationservice.roversdkhelper.maptype.NaviMapType;
import com.ainirobot.navigationservice.utils.MapUtils;
import com.ainirobot.navigationservice.utils.NavigationConfig;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Calendar;
import java.util.Enumeration;
import java.util.Locale;
import java.util.UUID;

import ninjia.android.roversdk.RoverClientFactory;


public class TestWaiterActivity extends Activity implements OnClickListener {
    private final static String TAG = TAGPRE + TestWaiterActivity.class.getSimpleName();
    private final long MINUTE = 60 * 1000;

    private final long DEFAULT_INTERVAL_TIME = 1;

    private ChassisManager chassisManager;
    private IChassisClient mNav;

    private TextToSpeech mSpeech;

    private EditText mMapName;
    private EditText mNavIpEdit;
    private EditText mLDistanceEdit;
    private EditText mObsDistanceEdit;
    private EditText mLSpeedEdit;
    private EditText mADistanceEdit;
    private EditText mASpeedEdit;
    private RadioGroup mDeviceGroup;
    private RadioGroup mGroundGroup;
    private RadioGroup mScenesGroup;
    private CheckBox mCamera;
    private CheckBox mFishEye;
    private CheckBox mRgbd;
    private CheckBox mIR;
    private CheckBox mSonar;
    private TextView mEstimate;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_test_waiter);

        Log.d(TAG, "onCreate:");

        Intent intent = new Intent(this, NavigationService.class);
        startService(intent);

        DelayTask.submit(new Runnable() {
            @Override
            public void run() {
                chassisManager = ChassisManager.getInstance();
                mNav = chassisManager.getChassisClient();
            }
        }, 1000);

        mSpeech = new TextToSpeech(this, new TextToSpeech.OnInitListener() {
            @Override
            public void onInit(int status) {
                if (status == TextToSpeech.SUCCESS) {
                    int result = mSpeech.setLanguage(Locale.CHINA);
                    if (result == TextToSpeech.LANG_MISSING_DATA
                            || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                        Toast.makeText(getBaseContext(),
                                getString(R.string.not_support_chinese), Toast.LENGTH_SHORT).show();
                    }
                }
            }
        });

        mMapName = (EditText) findViewById(R.id.mapName);
        mNavIpEdit = (EditText) findViewById(R.id.navIp);
        mLDistanceEdit = (EditText) findViewById(R.id.linear_distance);
        mObsDistanceEdit = (EditText) findViewById(R.id.obs_distance);
        mLSpeedEdit = (EditText) findViewById(R.id.linear_speed);
        mADistanceEdit = (EditText) findViewById(R.id.angle_distance);
        mASpeedEdit = (EditText) findViewById(R.id.angle_speed);
        mEstimate = (TextView) findViewById(R.id.showEstimate);

        mDeviceGroup = (RadioGroup) findViewById(R.id.deviceGroup);
        mGroundGroup = (RadioGroup) findViewById(R.id.groundGroup);
        mScenesGroup = (RadioGroup) findViewById(R.id.scenesGroup);
        mCamera = (CheckBox) findViewById(R.id.checkboxEnableCamera);
        mFishEye = (CheckBox) findViewById(R.id.enableFishEye);
        mIR = (CheckBox) findViewById(R.id.enableIR);
        mRgbd = (CheckBox) findViewById(R.id.enableRgbd);
        mSonar = (CheckBox) findViewById(R.id.enableSonar);

        mNavIpEdit.setText(NavigationConfig.getNavIp());

        findViewById(R.id.changeConfig).setOnClickListener(this);
        findViewById(R.id.turnLeft).setOnClickListener(this);
        findViewById(R.id.turnRight).setOnClickListener(this);
        findViewById(R.id.forward).setOnClickListener(this);
        findViewById(R.id.forward_avoid).setOnClickListener(this);
        findViewById(R.id.backward).setOnClickListener(this);
        findViewById(R.id.stop).setOnClickListener(this);
        findViewById(R.id.stop_direct).setOnClickListener(this);
        findViewById(R.id.switchMap).setOnClickListener(this);
        findViewById(R.id.createMap).setOnClickListener(this);
        findViewById(R.id.stopCreateMap).setOnClickListener(this);
        findViewById(R.id.cancelNavigation).setOnClickListener(this);
        findViewById(R.id.setRoverConfig).setOnClickListener(this);
        findViewById(R.id.getRoverConfig).setOnClickListener(this);
        findViewById(R.id.navigation_recharge_point).setOnClickListener(this);
        findViewById(R.id.automatic_recharge).setOnClickListener(this);
        findViewById(R.id.get_system_info_btn).setOnClickListener(this);
        findViewById(R.id.transfer_targets_data_btn).setOnClickListener(this);
        findViewById(R.id.transfer_road_data_btn).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.changeConfig:
                changeConfig();
                break;
            case R.id.turnLeft:
                turnLeftAction();
                break;
            case R.id.turnRight:
                turnRightAction();
                break;
            case R.id.forward:
                goForwardAction();
                break;
            case R.id.forward_avoid:
                goForwardAvoidAction();
                break;
            case R.id.backward:
                goBackAction();
                break;
            case R.id.stop:
                mNav.stopMove();
                break;
            case R.id.stop_direct:
                mNav.motion(0, 0, 0, false);
                break;
            case R.id.switchMap:
                switchMapAction();
                break;
            case R.id.createMap:
                startCreatMapAction();
                break;
            case R.id.stopCreateMap:
                stopCreatingMapAction();
                break;
            case R.id.cancelNavigation:
                mNav.cancelNavigation(null);
                break;
            case R.id.setRoverConfig:
                setRoverConfigAction();
                break;
            case R.id.getRoverConfig:
                getRoverConfigAction();
                break;
            case R.id.get_system_info_btn:
                getSystemInfoAction();
                break;
            case R.id.navigation_recharge_point:
                goRechargePointAction();
                break;
            case R.id.automatic_recharge:
                autoRechargeAction();
                break;
            case R.id.transfer_targets_data_btn:
                transferTargetDataToJson();
                break;
            case R.id.transfer_road_data_btn:
                transferRoadJsonToData();
                break;
            default:
                break;
        }
    }

    private void transferTargetDataToJson(){
        final String mapName = mMapName.getText().toString();
        Log.d(TAG, "transferTargetDataToJson mapName:" + mapName);
        if (TextUtils.isEmpty(mapName)){
            return;
        }
//        String mapName = "0917big-0918103311";

        MapUtils.transferTargetJson2Data(mapName);
    }

    private void transferRoadJsonToData(){
        final String mapName = mMapName.getText().toString();
        Log.d(TAG, "transferRoadJsonToData mapName:" + mapName);
        if (TextUtils.isEmpty(mapName)){
            return;
        }
//        String mapName = "0917big-0918103311";
//        String mapName = "工位送餐-1012145302";
        MapUtils.transferRoadJson2Data(mapName);

    }


    /**
     * 修改配置
     */
    private void changeConfig(){
        String navIp = mNavIpEdit.getText().toString();
        if (TextUtils.isEmpty(navIp)) {
            mNavIpEdit.setError(getString(R.string.nav_ip_empty));
            return;
        }
        NavigationDataManager.getInstance().updateIpNavigation(navIp.trim());
    }

    /**
     * 左转
     */
    private void turnLeftAction(){

        double angle = Double.MAX_VALUE;
        double speed = 0.4;
        if (!TextUtils.isEmpty(mADistanceEdit.getText().toString().trim())) {
            angle = Double.valueOf(mADistanceEdit.getText().toString().trim());
        }
        if (!TextUtils.isEmpty(mASpeedEdit.getText().toString().trim())) {
            speed = Double.valueOf(mASpeedEdit.getText().toString().trim());
        }

        mNav.turnLeft(Math.toRadians(angle), speed, 0, false, null);
    }

    private void turnRightAction(){
        double angle = Double.MAX_VALUE;
        double speed = 0.4;
        if (!TextUtils.isEmpty(mADistanceEdit.getText().toString().trim())) {
            angle = Double.valueOf(mADistanceEdit.getText().toString().trim());
        }
        if (!TextUtils.isEmpty(mASpeedEdit.getText().toString().trim())) {
            speed = Double.valueOf(mASpeedEdit.getText().toString().trim());
        }

        mNav.turnRight(Math.toRadians(angle), speed, 0, false, null);
    }

    private void goForwardAction(){
        double distance = Double.MAX_VALUE;
        double speed = 0.2;
        if (!TextUtils.isEmpty(mLDistanceEdit.getText().toString().trim())) {
            distance = Double.valueOf(mLDistanceEdit.getText().toString().trim());
        }
        if (!TextUtils.isEmpty(mLSpeedEdit.getText().toString().trim())) {
            speed = Double.valueOf(mLSpeedEdit.getText().toString().trim());
        }

        mNav.forward(distance, speed, 0, null);
    }

    private void goForwardAvoidAction(){
        double distance = Double.MAX_VALUE;
        double speed = 0.2;
        if (!TextUtils.isEmpty(mLDistanceEdit.getText().toString().trim())) {
            distance = Double.valueOf(mLDistanceEdit.getText().toString().trim());
        }
        if (!TextUtils.isEmpty(mLSpeedEdit.getText().toString().trim())) {
            speed = Double.valueOf(mLSpeedEdit.getText().toString().trim());
        }

        mNav.forward(distance, speed, 0, true, null);
    }

    private void goBackAction(){
        double distance = Double.MAX_VALUE;
        double speed = 0.2;
        if (!TextUtils.isEmpty(mLDistanceEdit.getText().toString().trim())) {
            distance = Double.valueOf(mLDistanceEdit.getText().toString().trim());
        }
        if (!TextUtils.isEmpty(mLSpeedEdit.getText().toString().trim())) {
            speed = Double.valueOf(mLSpeedEdit.getText().toString().trim());
        }

        mNav.backward(distance, speed, 0, null);
    }

    private void switchMapAction(){
        final String mapName = mMapName.getText().toString();
        Log.d(TAG, "Rover switch map name : " + mapName);
        mNav.switchMap(mapName, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "Rover switch map  : " + status + "  result=" + result);
            }
        });
    }

    private void startCreatMapAction(){
        mNav.startCreatingMap(new NaviMapType(), new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "startCreatingMap:" + status + " resultCode:" + resultCode
                        + " result:" + result);
            }
        });
    }

    private void stopCreatingMapAction(){

    }

    private void setRoverConfigAction(){
        String rosIp = NavigationConfig.getLocalIp();
        boolean isCamera = mCamera.isChecked();
        boolean isFishEye = mFishEye.isChecked();
        boolean isIR = mIR.isChecked();
        boolean isSonar = mSonar.isChecked();
        boolean isRgbd = mRgbd.isChecked();

        int device;
        int deviceId = mDeviceGroup.getCheckedRadioButtonId();
        if (deviceId == R.id.radioDeviceTrex) {
            device = 1;
        } else {
            device = 4;
        }

        int ground;
        int groundId = mGroundGroup.getCheckedRadioButtonId();
        if (groundId == R.id.radioGroundHard) {
            ground = 2;
        } else {
            ground = 1;
        }

        int scenes;
        int scenesId = mScenesGroup.getCheckedRadioButtonId();
        if (scenesId == R.id.radioScenesNarrow) {
            scenes = 1;
        } else {
            scenes = 2;
        }

        RoverConfig roverConfig = new RoverConfig(rosIp);
        roverConfig.setEnableCamera(isCamera);
        roverConfig.setEnableSonar(isSonar);
        roverConfig.setDeviceType(device);
        roverConfig.setEnableFishEye(isFishEye);
        roverConfig.setEnableIR(isIR);
        roverConfig.setScenesType(scenes);
        mNav.setRoverConfig(roverConfig, null);
    }

    private void getRoverConfigAction(){
        mNav.getRoverConfig(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "getRoverConfig : status= " + status + " || result= " + result);
            }
        });

    }

    private void goRechargePointAction(){
        Log.d(TAG, "goRechargePointAction: navigation to ChargePos");
        new Thread(new Runnable() {
            @Override
            public void run() {
                PlaceInfo placeBean = NavigationDataManager.getInstance().getPlaceByName(Definition.START_BACK_CHARGE_POSE);
                Pose pose = DataManager.placeBeanToPose(placeBean);
                TargetPose target = new TargetPose(pose);

                double obsDistance = Def.ROBOT_NAVIGATION_DEFAULT_OBS_DISTANCE;
                if (!TextUtils.isEmpty(mObsDistanceEdit.getText().toString().trim())) {
                    obsDistance = Double.valueOf(mObsDistanceEdit.getText().toString().trim());
                }
                Log.d(TAG, "goRechargePointAction: obsDistance:" + obsDistance);
                target.setBlockObsDistance(obsDistance);

                target.setResponseListener(new TargetPose.ResponseListener() {
                    @Override
                    public void onResult(final int result, BaseEvent event) {
                        Log.d(TAG, "goRechargePointAction: navigation onResult:" + result);

                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (result != TargetPose.RESULT_REPLACE) {
                                    String content = getString(R.string.navigation_content,
                                            result == TargetPose.RESULT_ARRIVED ? "到达" : "未到达");
                                    Toast.makeText(getBaseContext(),
                                            content, Toast.LENGTH_SHORT).show();
                                }
                            }
                        });
                    }

                    @Override
                    public void onStatusUpdate(final int status, BaseEvent event) {
                        Log.d(TAG, "goRechargePointAction: navigation onStatusUpdate:" + status);
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                String content = null;
                                switch (status) {
                                    case STATUS_AVOID_END:
                                        content = "目标点在临时障碍物内，尝试靠近";
                                        break;

                                    case STATUS_OUT_MAP_END:
                                        content = "目标点在地图外，停止导航";
                                        break;

                                    default:
                                        break;
                                }

                                if (content != null) {
                                    Toast.makeText(getBaseContext(),
                                            content, Toast.LENGTH_SHORT).show();
                                }
                            }
                        });
                    }
                });
                mNav.go(target, new IChassisClient.ChassisResListener() {
                    @Override
                    public void onResponse(final boolean status, int resultCode, Object result) {
                        Log.d(TAG, "goRechargePointAction: navigation status = " + status + "  resultCode = " + resultCode + "  result = " + result);

                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                String content = getString(R.string.goaction_content, status ? "开始" : "失败");
                                Toast.makeText(getBaseContext(),
                                        content, Toast.LENGTH_SHORT).show();
                            }
                        });
                    }
                });
            }
        }).start();
    }

    private void autoRechargeAction(){
        Log.d(TAG, "goCharge");
        new Thread(new Runnable() {
            @Override
            public void run() {
                mNav.goCharge(false, new IChassisClient.ChassisResListener() {

                    @Override
                    public void onResponse(final boolean status, final int resultCode, final Object result) {
                        Log.d(TAG, "goCharge status:" + status
                                + ", resultCode:" + resultCode + ", result:" + result);

                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                final String content;
                                if (status) {
                                    if (resultCode == 0) {
                                        content = getString(R.string.charge_content, "开始");
                                    } else {
                                        content = getString(R.string.charge_content, "成功");
                                    }
                                } else {
                                    content = getString(R.string.charge_content, "失败");
                                }

                                Toast.makeText(getBaseContext(),
                                        content, Toast.LENGTH_SHORT).show();
                            }
                        });
                    }
                });
            }
        }).start();
    }

    private void getSystemInfoAction(){
        String chassisVersion = RoverClientFactory.version();
        Log.d(TAG, "RoverClientFactory getSystemInformation:" + chassisVersion);
        mNav.getSystemInformation(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "ICommandHandle getSystemInformation status:" + status + " resultCode:" + resultCode
                        + " result:" + result);
            }
        });
    }

    private void speak() {
        String speakId = UUID.randomUUID().toString();
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int min = calendar.get(Calendar.MINUTE);
        String content = getString(R.string.speak_content
                , String.valueOf(hour), String.valueOf(min));
        if (mSpeech != null) {
            mSpeech.speak(content, TextToSpeech.QUEUE_FLUSH, null, speakId);
        }
    }

    public static String getIp() {
        try {
            Enumeration<NetworkInterface> nets = NetworkInterface.getNetworkInterfaces();
            while (nets.hasMoreElements()) {
                NetworkInterface net = nets.nextElement();
                Enumeration<InetAddress> addresses = net.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress inetA = addresses.nextElement();
                    if (inetA instanceof Inet4Address && !inetA.isLoopbackAddress()) {
                        return inetA.getHostAddress();
                    }
                }
            }
            return null;
        } catch (SocketException e) {
            return null;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    public void queryEstimate(View view) {
        if (mNav.isPoseEstimate()) {
            mEstimate.setText("定位成功");
        } else {
            mEstimate.setText("定位失败");
        }
    }
}
