package com.ainirobot.navigationservice.chassisAbility.ota.connector;

public interface IOtaConnect {
    void init();

    boolean request(String message);

    void registerEventListener(CnnEventListener listener);

    void registerConnectListener(CnnOnConnectListener listener);

    void registerResponseListener(CnnResListener listener);

    interface CnnResListener {
        void onResponse(String message);
    }

    interface CnnEventListener {
        void onEvent(String message);
    }

    interface CnnOnConnectListener {
        void onConnected();

        void onDisconnected();
    }
}
