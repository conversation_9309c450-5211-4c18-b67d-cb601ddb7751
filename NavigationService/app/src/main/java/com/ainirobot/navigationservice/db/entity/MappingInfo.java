package com.ainirobot.navigationservice.db.entity;

import io.objectbox.annotation.Entity;
import io.objectbox.annotation.Id;

/**
 * Created by Orion on 2022/1/16.
 */
@Entity
public class MappingInfo {
    @Id
    public long id;
    private String mapName;
    private int placeType = 0;
    private String placeCnName;
    private String placeId="";
    private String mappingPoseId="";
    private int posePriority = 0;

    public String getMapName() {
        return mapName;
    }

    public void setMapName(String mapName) {
        this.mapName = mapName;
    }

    public int getPlaceType() {
        return placeType;
    }

    public void setPlaceType(int placeType) {
        this.placeType = placeType;
    }

    public String getPlaceCnName() {
        return placeCnName;
    }

    public void setPlaceCnName(String placeCnName) {
        this.placeCnName = placeCnName;
    }

    public String getPlaceId() {
        return placeId;
    }

    public void setPlaceId(String placeId) {
        this.placeId = placeId;
    }

    public String getMappingPoseId() {
        return mappingPoseId;
    }

    public void setMappingPoseId(String mappingPoseId) {
        this.mappingPoseId = mappingPoseId;
    }

    public int getPosePriority() {
        return posePriority;
    }

    public void setPosePriority(int pose_priority) {
        this.posePriority = pose_priority;
    }

    @Override
    public String toString() {
        return "MappingInfo{" +
                "mapName='" + mapName + '\'' +
                ", placeType=" + placeType +
                ", placeCnName='" + placeCnName + '\'' +
                ", placeId='" + placeId + '\'' +
                ", mappingPoseId='" + mappingPoseId + '\'' +
                ", posePriority=" + posePriority +
                '}';
    }
}
