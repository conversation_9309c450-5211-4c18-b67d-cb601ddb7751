package com.ainirobot.navigationservice.db.helper.objectbox;

import android.util.Log;

import androidx.annotation.NonNull;

import com.ainirobot.navigationservice.db.DataChangeListener;
import com.ainirobot.navigationservice.db.entity.MapInfo;
import com.ainirobot.navigationservice.db.entity.MapInfo_;
import com.ainirobot.navigationservice.db.helper.iml.MapInfoHelperIml;

import java.util.List;

import javax.annotation.Nullable;

import io.objectbox.Box;
import io.objectbox.BoxStore;
import io.objectbox.query.PropertyQueryCondition;
import io.objectbox.query.Query;

public class MapInfoObjectHelper extends BaseObjectHelper<MapInfo> implements MapInfoHelperIml {

    /**
     * 用于同步地图使用状态的锁
     */
    private final Object USE_MAP_LOCK = new Object();

    public MapInfoObjectHelper(BoxStore boxStore) {
        super(boxStore);
    }

    /**
     * 通过地图名字获取地图信息
     *
     * @param mapName 地图名字
     * @return 地图信息，可能为空
     */
    @Override
    public MapInfo getMapByName(String mapName) {
        Query<MapInfo> mapInfoQuery = getMapInfoQuery(mapName);
        MapInfo mapInfo = mapInfoQuery.findFirst();
        mapInfoQuery.close();

        if (null != mapInfo) {
            mapInfo.updateVisionTargetType();
        }
        Log.d(TAG, "getMapByName: " + mapInfo);
        return mapInfo;
    }

    @Override
    public void deleteMapByName(String mapName) {
        Query<MapInfo> infoQuery = getMapInfoQuery(mapName);
        boolean remove = infoQuery.remove() > 0;
        infoQuery.close();
        Log.d(TAG, "deleteMapByName : " + remove);
    }

    @Override
    public void updateUseState(MapInfo mapInfo) {
        synchronized (USE_MAP_LOCK) {
            Box<MapInfo> mapInfoBox = getBox();
            Query<MapInfo> mapInfoQuery = mapInfoBox
                    .query(MapInfo_.useState.equal(MapInfo.UseState.IN_USE))
                    .build();
            List<MapInfo> lastInUseMapInfos = mapInfoQuery.find();

            for (MapInfo info : lastInUseMapInfos) {
                info.setUseState(MapInfo.UseState.NO_USE);
            }
            mapInfo.setUseState(MapInfo.UseState.IN_USE);
            lastInUseMapInfos.add(mapInfo);
            mapInfoBox.put(lastInUseMapInfos);
            mapInfoQuery.close();
        }
    }

    /**
     * 获取当前导航的地图信息
     *
     * @return 地图新，可能为空
     */
    @Nullable
    @Override
    public MapInfo getNavMapInfo() {
        synchronized (USE_MAP_LOCK) {
            Query<MapInfo> mapInfoQuery = getMapInfoQuery(MapInfo_.useState.equal(MapInfo.UseState.IN_USE));
            MapInfo mapInfo = mapInfoQuery.findFirst();
            mapInfoQuery.close();

            if (null != mapInfo) {
                mapInfo.updateVisionTargetType();
            }
            Log.d(TAG, "getNavMapInfo: " + mapInfo);
            return mapInfo;
        }
    }

    @Override
    public void clearUseState() {
        synchronized (USE_MAP_LOCK) {
            Box<MapInfo> mapInfoBox = getBox();
            Query<MapInfo> mapInfoQuery = mapInfoBox
                    .query(MapInfo_.useState.equal(MapInfo.UseState.IN_USE))
                    .build();
            List<MapInfo> lastInUseMapInfos = mapInfoQuery.find();
            for (MapInfo info : lastInUseMapInfos) {
                info.setUseState(MapInfo.UseState.NO_USE);
            }
            mapInfoBox.put(lastInUseMapInfos);
            mapInfoQuery.close();
        }
    }

    @NonNull
    @Override
    public List<MapInfo> getMapInfos(String[] mapName) {
        Box<MapInfo> mapInfoBox = getBox();
        Query<MapInfo> mapInfoQuery;
        if (null != mapName) {
            mapInfoQuery = mapInfoBox.query(MapInfo_.mapName.oneOf(mapName)).build();
        } else {
            mapInfoQuery = mapInfoBox.query().build();
        }
        List<MapInfo> mapInfos = mapInfoQuery.find();
        if(mapInfos != null && !mapInfos.isEmpty()) {
            for (MapInfo mapInfo : mapInfos) {
                mapInfo.updateVisionTargetType();
                mapInfo.updateNaviType();
            }
        }
        mapInfoQuery.close();
        return mapInfos;
    }

    @Override
    public void initMapInfoData(List<MapInfo> mapInfoList) {
        if (null == mapInfoList || mapInfoList.isEmpty()) {
            Log.d(TAG, "initMapInfoData: list is null");
            return;
        }
        Log.d(TAG, "initMapInfoData: start");
        Box<MapInfo> mapInfoBox = getBox();
        mapInfoBox.removeAll();
        mapInfoBox.put(mapInfoList);
        Log.d(TAG, "initMapInfoData: " + mapInfoBox.count());
    }

    @Override
    public boolean updateMapInfo(MapInfo mapInfo) {
        Box<MapInfo> mapInfoBox = getBox();
        return mapInfoBox.put(mapInfo) > 0;
    }

    @Override
    public void updateMapInfo(List<MapInfo> mapInfos) {
        Box<MapInfo> mapInfoBox = getBox();
        mapInfoBox.put(mapInfos);
    }

    @Override
    public void setOnDataChangeListener(DataChangeListener listener) {
        // do nothing
    }

    private Query<MapInfo> getMapInfoQuery(PropertyQueryCondition<MapInfo> queryCondition) {
        return getBox()
                .query(queryCondition)
                .build();
    }

    private Query<MapInfo> getMapInfoQuery(String mapName) {
        return getMapInfoQuery(MapInfo_.mapName.equal(mapName));
    }
}
