package com.ainirobot.navigationservice.db.entity;

import com.ainirobot.coreservice.client.Definition;

import java.util.HashMap;

import io.objectbox.annotation.Entity;
import io.objectbox.annotation.Id;
import io.objectbox.annotation.Index;
import io.objectbox.annotation.Transient;

/**
 * @version V1.0.0
 * @date 2020/7/21 11:30
 */
@Entity
public class PlaceInfo {
    @Id
    public long id;
    @Index
    private String placeId="";
    private String iconUrl="";
    private int placeType;
    private int placeStatus;
    private float pointTheta;
    private float pointX;
    private float pointY;
    private long updateTime;
    private String mapName="";
    private String alias="";
    /**
     * 特殊点位类型
     */
    private int typeId = 0;
    /**
     * 特殊点位优先级
     */
    private int priority = 0;

    @Transient
    private HashMap<String, String> placeNameList=new HashMap<>();

    private String createTime="";
    private int syncState=0;
    private boolean ignoreDistance = false;
    /**
     * 是否无方向停靠
     */
    private boolean noDirectionalParking = false;

    private int safeDistance = Definition.POSE_SAFE_DISTANCE_DEFAULT;

    public PlaceInfo() {
    }

    public String getPlaceId() {
        return placeId;
    }

    public PlaceInfo setPlaceId(String placeId) {
        this.placeId = placeId;
        return this;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public PlaceInfo setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
        return this;
    }

    public int getPlaceType() {
        return placeType;
    }

    public PlaceInfo setPlaceType(int placeType) {
        this.placeType = placeType;
        return this;
    }

    public int getPlaceStatus() {
        return placeStatus;
    }

    public PlaceInfo setPlaceStatus(int placeStatus) {
        this.placeStatus = placeStatus;
        return this;
    }

    public float getPointTheta() {
        return pointTheta;
    }

    public PlaceInfo setPointTheta(float pointTheta) {
        this.pointTheta = pointTheta;
        return this;
    }

    public float getPointX() {
        return pointX;
    }

    public PlaceInfo setPointX(float pointX) {
        this.pointX = pointX;
        return this;
    }

    public float getPointY() {
        return pointY;
    }

    public PlaceInfo setPointY(float pointY) {
        this.pointY = pointY;
        return this;
    }

    public int getSyncState() {
        return syncState;
    }

    public PlaceInfo setSyncState(int syncState) {
        this.syncState = syncState;
        return this;
    }

    public boolean getIgnoreDistance() {
        return ignoreDistance;
    }

    public PlaceInfo setIgnoreDistance(boolean ignoreDistance) {
        this.ignoreDistance = ignoreDistance;
        return this;
    }
    public int getSafeDistance() {
        return safeDistance;
    }

    public PlaceInfo setSafeDistance(int safeDistance) {
        this.safeDistance = safeDistance;
        return this;
    }

    public String getCreateTime() {
        return createTime;
    }

    public PlaceInfo setCreateTime(String createTime) {
        this.createTime = createTime;
        return this;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public String getMapName() {
        return mapName;
    }

    public PlaceInfo setMapName(String mapName) {
        this.mapName = mapName;
        return this;
    }

    public String getAlias() {
        return alias;
    }

    public PlaceInfo setAlias(String alias) {
        this.alias = alias;
        return this;
    }

    public HashMap<String, String> getPlaceNameList() {
        return this.placeNameList;
    }

    public String getPlaceName(String languageType) {
        return null == this.placeNameList ? "" : this.placeNameList.get(languageType);
    }

    public PlaceInfo setPlaceNames(HashMap<String, String> placeNames) {
        this.placeNameList = placeNames;
        return this;
    }

    public PlaceInfo addPlaceName(String type, String placeName) {
        if (placeNameList == null) {
            placeNameList = new HashMap<>();
        }
        placeNameList.put(type, placeName);
        return this;
    }

    public void removePlaceName(String languageType) {
        if (this.placeNameList != null) {
            this.placeNameList.remove(languageType);
        }
    }

    public PlaceInfo setTypeId(int typeId) {
        this.typeId = typeId;
        return this;
    }

    public int getTypeId() {
        return typeId;
    }

    public PlaceInfo setPriority(int priority) {
        this.priority = priority;
        return this;
    }

    public int getPriority() {
        return priority;
    }

    public boolean getNoDirectionalParking() {
        return noDirectionalParking;
    }

    public PlaceInfo setNoDirectionalParking(boolean noDirectionalParking) {
        this.noDirectionalParking = noDirectionalParking;
        return this;
    }


    @Override
    public String toString() {
        return "PlaceInfo{" +
                "placeId='" + placeId + '\'' +
                ", iconUrl='" + iconUrl + '\'' +
                ", placeType=" + placeType +
                ", placeStatus=" + placeStatus +
                ", pointTheta=" + pointTheta +
                ", pointX=" + pointX +
                ", pointY=" + pointY +
                ", noDirectionalParking=" + noDirectionalParking +
                ", syncState=" + syncState +
                ", createTime='" + createTime + '\'' +
                ", updateTime='" + updateTime + '\'' +
                ", mapName='" + mapName + '\'' +
                ", alias='" + alias + '\'' +
                ", typeId='" + typeId + '\'' +
                ", priority='" + priority + '\'' +
                ", placeNameList=" + placeNameList.toString() +
                '}';
    }
}
