package com.ainirobot.navigationservice.chassisAbility.ota.client;

import android.content.Context;

public interface IOtaClient {

    void init(Context mContext);
    void getVersion(OtaResListener listener);
    void startUpdate(String otaInfo, String[] packagePath, OtaResListener listener);
    void getUpdateParams(OtaResListener listener);
    boolean isOtaConnected();

    interface OtaResListener {
        void onResult(String result);
    }
}
