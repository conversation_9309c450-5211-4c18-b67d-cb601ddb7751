package com.ainirobot.navigationservice.chassisAbility.chassis.client.waiter;

import static com.ainirobot.navigationservice.Defs.Def.ResultCode.CHASSIS_ALREADY_INITED;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.FAIL_NO_REASON;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.MOTION_AVOID_STOP;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.START_EXPANSION_MAP_FAILED;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.STOP_EXPANSION_MAP_FAILED;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.SUCCESS;
import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.SystemClock;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.ashmem.DepthImageBean;
import com.ainirobot.coreservice.client.ashmem.ShareMemoryApi;
import com.ainirobot.coreservice.client.ashmem.TopIRImageBean;
import com.ainirobot.coreservice.client.hardware.RobotCore;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.techreport.AbnormalReport;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.navigationservice.ApplicationWrapper;
import com.ainirobot.navigationservice.Defs.Def;
import com.ainirobot.navigationservice.Defs.IDeviceConfig;
import com.ainirobot.navigationservice.Defs.NavigationError;
import com.ainirobot.navigationservice.Defs.NavigationResult;
import com.ainirobot.navigationservice.Defs.NavigationStatus;
import com.ainirobot.navigationservice.Defs.mini.MiniConfig;
import com.ainirobot.navigationservice.Defs.saiph.SaiphConfig;
import com.ainirobot.navigationservice.beans.MappingPose;
import com.ainirobot.navigationservice.beans.tk1.Event;
import com.ainirobot.navigationservice.beans.tk1.Laser;
import com.ainirobot.navigationservice.beans.tk1.Map;
import com.ainirobot.navigationservice.beans.tk1.MapData;
import com.ainirobot.navigationservice.beans.tk1.Message;
import com.ainirobot.navigationservice.beans.tk1.NavAcceleration;
import com.ainirobot.navigationservice.beans.tk1.NavMode;
import com.ainirobot.navigationservice.beans.tk1.NavVelocity;
import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.tk1.SensorStatus;
import com.ainirobot.navigationservice.beans.tk1.Statistic;
import com.ainirobot.navigationservice.beans.tk1.SystemData;
import com.ainirobot.navigationservice.beans.tk1.TargetPose;
import com.ainirobot.navigationservice.beans.tk1.Velocity;
import com.ainirobot.navigationservice.beans.waiter.BasePoseBean;
import com.ainirobot.navigationservice.beans.waiter.CameraBean;
import com.ainirobot.navigationservice.beans.waiter.HumanFollowBean;
import com.ainirobot.navigationservice.beans.waiter.MultiRobotConfigBean;
import com.ainirobot.navigationservice.beans.waiter.MultiRobotStatus;
import com.ainirobot.navigationservice.beans.waiter.NaviPathDetail;
import com.ainirobot.navigationservice.beans.waiter.NaviPathInfo;
import com.ainirobot.navigationservice.beans.waiter.RoadGraph;
import com.ainirobot.navigationservice.beans.waiter.RoadGraphEdge;
import com.ainirobot.navigationservice.beans.waiter.RoadGraphNode;
import com.ainirobot.navigationservice.beans.waiter.SubDeviceBean;
import com.ainirobot.navigationservice.beans.waiter.Vector2d;
import com.ainirobot.navigationservice.business.rpc.MotionControl;
import com.ainirobot.navigationservice.business.rpc.SpeedBean;
import com.ainirobot.navigationservice.chassisAbility.IrLedManager;
import com.ainirobot.navigationservice.chassisAbility.MapDriftWarningManager;
import com.ainirobot.navigationservice.chassisAbility.MapOutsideManager;
import com.ainirobot.navigationservice.chassisAbility.SensorExceptionManager;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.AbsChassisClient;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.ClientConfigManager;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.tk1.customListener.CreateMapStop;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.waiter.monitor.LoraLostMonitor;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.x86.ChargeResult;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.x86.WheelControlX86;
import com.ainirobot.navigationservice.chassisAbility.controller.BaseAvoidPolicy;
import com.ainirobot.navigationservice.chassisAbility.controller.BasicMotionProcess;
import com.ainirobot.navigationservice.chassisAbility.controller.BasicMotionProcessNew;
import com.ainirobot.navigationservice.chassisAbility.controller.DynamicStoppingPolicy;
import com.ainirobot.navigationservice.chassisAbility.controller.MotionPIDPolicy;
import com.ainirobot.navigationservice.chassisAbility.controller.MotionPolicyMini;
import com.ainirobot.navigationservice.chassisAbility.controller.NarrowPassagePolicy;
import com.ainirobot.navigationservice.chassisAbility.controller.StaticStoppingPolicy;
import com.ainirobot.navigationservice.commonModule.bi.BiManager;
import com.ainirobot.navigationservice.commonModule.bi.bean.BiCorrectLocationBean;
import com.ainirobot.navigationservice.commonModule.bi.report.BiRunningErrorReport;
import com.ainirobot.navigationservice.commonModule.bi.report.MotionCallChainReporter;
import com.ainirobot.navigationservice.commonModule.configuration.ConfigManager;
import com.ainirobot.navigationservice.commonModule.data.utils.UuidUtils;
import com.ainirobot.navigationservice.commonModule.logs.LogManager;
import com.ainirobot.navigationservice.db.NavigationDataManager;
import com.ainirobot.navigationservice.db.entity.MapInfo;
import com.ainirobot.navigationservice.db.entity.MultiFloorInfo;
import com.ainirobot.navigationservice.roversdkhelper.MapFileHelper;
import com.ainirobot.navigationservice.roversdkhelper.maptype.MapTypeHelper;
import com.ainirobot.navigationservice.roversdkhelper.maptype.NaviMapType;
import com.ainirobot.navigationservice.utils.DistanceUtils;
import com.ainirobot.navigationservice.utils.FileUtils;
import com.ainirobot.navigationservice.utils.FaceAngleRangeConverter;
import com.ainirobot.navigationservice.utils.GsonUtil;
import com.ainirobot.navigationservice.utils.LogUtils;
import com.ainirobot.navigationservice.utils.MD5;
import com.ainirobot.navigationservice.utils.MapUtils;
import com.ainirobot.navigationservice.utils.NavigationConfig;
import com.ainirobot.navigationservice.utils.ThreadUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.google.protobuf.ByteString;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import ninjia.android.proto.ChassisFileProtoWrapper;
import ninjia.android.proto.ChassisPacketProtoWrapper;
import ninjia.android.proto.CommonProtoWrapper;
import ninjia.android.proto.CostmapProtoWrapper;
import ninjia.android.proto.GetLocationStateProtoWrapper;
import ninjia.android.proto.GetSystemInformationProtoWrapper;
import ninjia.android.proto.Pose2dProtoWrapper;
import ninjia.android.proto.RelocateDataProtoWrapper;
import ninjia.android.proto.ReportStatisticProtoWrapper;
import ninjia.android.proto.RoadGraphProtoWrapper;
import ninjia.android.proto.RoverConfigProtoWrapper.RoverConfigProto;
import ninjia.android.proto.SelfCheckResultProtoWrapper;
import ninjia.android.proto.SetGoalProtoWrapper;
import ninjia.android.proto.UpdateEventProtoWrapper;
import ninjia.android.proto.UpdateLaserProtoWrapper;
import ninjia.android.proto.UpdateLineProtoWrapper;
import ninjia.android.proto.UpdateRawImageProtoWrapper;
import ninjia.android.proto.Vector2dProtoWrapper;
import ninjia.android.proto.VelocityProtoWrapper;
import ninjia.android.roversdk.Result;
import ninjia.android.roversdk.RoverClientFactory;
import ninjia.android.roversdk.iface.ICommandHandle;
import ninjia.android.roversdk.iface.IRoverClient;
import ninjia.android.roversdk.listener.SdkListener;
import ninjia.android.roversdk.listener.UpdateListener;
import ninjia.android.roversdk.sdk.SdkError;


//TODO 1. 视觉地图接口 2. 雷达数据上报
public class ChassisClientWaiterImpl extends AbsChassisClient {

    public static final String TAG = TAGPRE + ChassisClientWaiterImpl.class.getSimpleName();
    public static final String TAG_AVOID = TAG + "motion_avoid";

    private static final String API_IN = "api__in";
    private static final String API_BACK = "api__back";
    private static final boolean RELEASE = true;
    private static final boolean UNRELEASE = false;

    private static final boolean OPEN = true;
    private static final boolean CLOSE = false;

    private static final Object TAG_INIT_LOAD_MAP = new Object();

    //X86 Client
    private IRoverClient mRoverClient;
    private volatile boolean mRoverSdkReady = false;
    private volatile boolean mChassisOK = false;
    private ICommandHandle commandApi;

    private volatile boolean isPoseEstimate;
    private String mNavIp;

    private volatile TargetPose mTargetPose;
    private ChassisEventListener mChassisEventListener;

    private CreateMapStop mCreateMapStop;

    private ArrayList<Laser> mLaserDatas;
    private static final Object laserUpdateLock = new Object();
    private ChassisResListener mGoChargelistener;

    private boolean hasObstacle = false;
    private boolean avoidTag = false;
    private boolean isServiceInitComplete = false;
    private volatile boolean isRelocate = false;
    private volatile boolean wheelReleaseState = RELEASE;
    /**
     * 旧版本开关状态：底盘新架构版本
     * true-已开启 false-已关闭
     */
    private volatile boolean radarOpenState = OPEN;
    /**
     * 新增具体状态：静止休眠需求
     * 0-已开启 1-已关闭 2-开启中 3-关闭中
     */
    private static volatile int mRadarStatus = Definition.RADAR_STATUS_OPENED;

    private final int EVENT_WC_CMD = 1;
    private final int MSG_COLLECT_LORA_DATA = 2;
    private static final long DEFAULT_LORA_TEST_DATA_INTERVAL = 10100L;
    private Handler mWheelControlHandler;
    private volatile VelocityProtoWrapper.VelocityProto mVelocityProto = null;
    private static final int MULTI_ROBOT_PRIORITY_DELAY = 50;

    private volatile boolean mMotionWithAvoid = false;
    private AtomicReference<Velocity> mRealtimeVelocity = new AtomicReference<>();
    private AtomicReference<ArrayList<Laser>> mLasers = new AtomicReference<>();
    private BaseAvoidPolicy mDynamicAvoidPolicy;
    private BaseAvoidPolicy mStaticAvoidPolicy;
    private BaseAvoidPolicy mNarrowPassagePolicy;
    private SimpleEventListener mSimpleEventListener;

    private ChassisResListener operationListener;
    private ExecutorService mFileExecutor;
    private Future<?> mCurrentTask;
    private Future<?> mSnapshotTask;
    private Future<?> mPreSnapshotTask;
    //导航任务下，机器人原地30秒无位移是否已上报过异常事件
    private AtomicBoolean hasReportedUnmove = new AtomicBoolean(false);
    private AtomicReference<Pose> mRecordPose;
    private HashMap<CommonProtoWrapper.CameraTypeProto, CameraBean> mCameraMap;
    /**
     * 是不是正在收集lora测试数据
     */
    private volatile boolean mCollectLoraTestData = false;
    private ChassisResListener mLoraTestListener;
    private ArrayList mLoradTestData;
    /**
     * 旧的Lora数据，用于过滤数据的有效性，如果lora的数据间隔小于50ms即视为无数据更新
     */
    private ArrayList<MultiRobotStatus> mOldLoraDataList;
    private volatile boolean mLoraTestMode = false;
    private final CopyOnWriteArrayList<com.ainirobot.coreservice.client.actionbean.Pose> lineDatas
            = new CopyOnWriteArrayList<>();

    /**
     * 不同机器人之间的差异性配置信息
     */
    private IDeviceConfig mDeviceConfig;
    private LoraLostMonitor mLoraLostMonitor = null;
    private int FRAME_RATE = 1;
//    private ArrayList<Short> distanceArray = new ArrayList<>();

//    byte[] testByte = new byte[1024 * 1024 * 10];
    private long mUpdatePoseTime = 0;

    /**
     * 以机器人开机位置为原点的坐标系实时位姿
     */
    private final AtomicReference<Pose> mCurrentRealPose;

    /**
     * 多楼层地图列表，只存地图名称，用于判断当前地图是否在多楼层列表中
     */
    private AtomicReference<ArrayList<String>> mMultiFloorMapList = new AtomicReference<>();
    private final AtomicReference<ChassisPacketProtoWrapper.RealtimeObsMapProto> mRealtimeObsMapProto;

    public ChassisClientWaiterImpl() {
        Log.d(TAG, "ChassisClientWaiterImpl: Construct method");
        mNavIp = NavigationConfig.getNavIp();
        Log.d(TAG, "ChassisClientWaiterImpl: mNavIp=" + mNavIp);

        HandlerThread commandThread = new HandlerThread("WheelControlThread");
        commandThread.start();
        mWheelControlHandler = new WheelControlHandler(commandThread.getLooper());
        if (mFileExecutor == null) {
            mFileExecutor = Executors.newFixedThreadPool(4);
        }
        mCurrentRealPose = new AtomicReference<>();
        mRealtimeObsMapProto = new AtomicReference<>();

//        if ((ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus())
//                || (ProductInfo.isMiniProduct()
//                && MapFileHelper.getDeviceProperties() != null
//                && "1".equals(MapFileHelper.getDeviceProperties()
//                .getProperty("TOPIR_mini2", "0")))) {
//            initLoraMonitor(this);
//        } else {
//            Log.d(TAG, "no need to start MultiRobotMonitor");
//        }

        if (ProductInfo.isSaiph() || ProductInfo.isCarryProduct()) {
            Log.d(TAG, "start MultiRobotMonitor for Saiph product");
            initLoraMonitor(this);
        } else {
            Log.d(TAG, "no need to start MultiRobotMonitor");
        }

//        Arrays.fill(testByte, Integer.valueOf(25).byteValue());
    }

    private void initLoraMonitor(IChassisClient chassisClient) {
        mLoraLostMonitor = new LoraLostMonitor();
        mLoraLostMonitor.setChassisClient(chassisClient);
        mLoraLostMonitor.setOnLoraStatusError(new LoraLostMonitor.LoraStatusErrorListener() {
            @Override
            public void onStatueError(String distance) {
                if (mChassisEventListener != null) {
                    mChassisEventListener.onMultipleRobotStatusUpdate(Definition.STATUS_MULTIPLE_ROBOT_NO_DATA, distance);
                }
            }
        });
    }

    @Override
    public void init(Context context) {
        super.init(context);
        loadClientDeviceConfig();
        initDefaultCameraState();
        //优先获取RoveConfig信息，并且更新deviceType
        ClientConfigManager.getInstance().storeDifferentClientRoverConfig();
        ClientConfigManager.getInstance().storeMultiRobotConfig();
        regitSnapshotTaskListener();
        startInitRoverClient();

        BasicMotionProcess.getInstance().init(this);
        BasicMotionProcessNew.getInstance().init(this);

        updateMultiFloorMapList();//初始化多楼层地图列表

        mDynamicAvoidPolicy = new DynamicStoppingPolicy(this);
        mStaticAvoidPolicy = new StaticStoppingPolicy(this);
        mNarrowPassagePolicy = new NarrowPassagePolicy(this);
        mDynamicAvoidPolicy.addObserver(mDynamicAvoidObserver);
        mStaticAvoidPolicy.addObserver(mStaticAvoidObserver);
        mNarrowPassagePolicy.addObserver(mNarrowPassageObserver);

        NavigationDataManager.getInstance().registerDataChangeListener(multiFloorDataChangeListener);
    }

    /**
     * 不同机器人参数配置差异
     */
    private void loadClientDeviceConfig() {
        int deviceTN = ConfigManager.getInstance().getDeviceTypeNumber();
        switch (deviceTN) {
            case RoverConfig.DEVICE_X86_MINI2:
                mDeviceConfig = new MiniConfig();
                break;
            case RoverConfig.DEVICE_WAITER:
            case RoverConfig.DEVICE_WAITER_PRO:
            case RoverConfig.DEVICE_MESSIA_PLUS:
            case RoverConfig.DEVICE_MEISSA2: // default速度参数，Pro底盘
            default:
                mDeviceConfig = new SaiphConfig();
                break;
        }
    }

    private void initDefaultCameraState() {
        //加载机器人默认相机开关状态
        mCameraMap = ClientConfigManager.getInstance().generateDiffClientCameraBean();
    }

    private void startInitRoverClient() {
        String sn = RobotSettings.getSystemSn();
        Log.d(TAG, "startInitRoverClient naviIP:" + mNavIp + " sn:" + sn);
//        mRoverClient = RoverClientFactory.create(mContext, mNavIp, RoverClientFactory.TYPE_PRO, sn);
        mRoverClient = RoverClientFactory.create(mContext, mNavIp, RoverClientFactory.TYPE_PRO, sn
                , ProductInfo.isOverSea() ? this.getOverSeaServiceType() : RoverClientFactory.CloudServiceType.kCos);
        mRoverClient.setSdkListener(sdkListener);
        mRoverClient.setUpdateListener(mUpdateListener);
        new Thread(new Runnable() {
            @Override
            public void run() {
                mRoverClient.init();
            }
        }).start();

        commandApi = mRoverClient.getCommandHandle();
    }


    /**
     * 海外版根据配置获取对应分区
     *
     * @return
     */
    private RoverClientFactory.CloudServiceType getOverSeaServiceType() {

        //默认欧区
        RoverClientFactory.CloudServiceType serviceType = RoverClientFactory.CloudServiceType.kAwsEuCentral1;
        String zone = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_SETTINGS_CLOUD_SERVER_ZONE);

        Definition.CloudServerZone serverZone = Definition.CloudServerZone.fromValue(zone);
        if (null != serverZone) {
            switch (serverZone) {
                case US:
                    serviceType = RoverClientFactory.CloudServiceType.kAwsUsWest2;
                    break;
            }
        }
        Log.d(TAG, "getOverSeaServiceType: " + serviceType);
        return serviceType;
    }


    private SdkListener sdkListener = new SdkListener() {
        @Override
        public void onRemoteStateChange(int i) {
            Log.d(TAG, "onRemoteStateChange:" + i);
        }

        @Override
        public void onSdkReady(Boolean aBoolean) {
            Log.d(TAG, "onSdkReady:" + aBoolean + " mRoerSdkReady:" + mRoverSdkReady);
            if (aBoolean == mRoverSdkReady) {
                return;
            }
            mRoverSdkReady = aBoolean;
            if (mRoverSdkReady) {
                settingManager.syncTimeToTk(System.currentTimeMillis());
                initRoverService();
                if (mChassisEventListener != null) {
                    mChassisEventListener.onChassisConnectStatus(Def.CHANNEL_COMMAND, Definition.STATUS_HW_CONNECTED);
                }
                //重启底盘进程时，同步雷达和定位状态，防止CoreService未更新状态
                reportDefaultStatusToCoreService();
                //通知自检开始执行
                notifyInspect(null, Definition.STATUS_HW_CONNECTED);
                //首次初始化成功执行上报
                if (!isServiceInitComplete && mChassisEventListener != null) {
                    isServiceInitComplete = true;
                    mChassisEventListener.onChassisServiceState(true);
                }
                BiManager.getInstance().reportDeviceType();
            } else {
                if (mChassisEventListener != null) {
                    mChassisEventListener.onChassisDisconnect("chassis socket disconnect", Def.CHANNEL_COMMAND);
                    mChassisEventListener.onChassisConnectStatus(Def.CHANNEL_COMMAND, Definition.STATUS_HW_DISCONNECTED);
                }
                Log.d(TAG, "start report");
                biManager.disconnectNavigationReport("rover SDK");
                logManager.startCollectLogFlow(RobotSettings.getSystemSn() + "-" + System.currentTimeMillis(),
                        String.valueOf(System.currentTimeMillis()), LogManager.TYPE_SOCKET_DISCONNECT);
            }
        }

        @Override
        public void onError(SdkError sdkError) {
            Log.d(TAG, "onError:" + sdkError.toString());
        }
    };

    public boolean isChassisOK() {
        return mChassisOK;
    }

    public void setChassisOK(boolean mChassisOK) {
        Log.d(TAG, "setChassisOK: " + mChassisOK);
        this.mChassisOK = mChassisOK;
    }

    private void initRoverService() {
        initChassisSystem();
        RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, Definition.STATUS_NAVI_LOAD_MAP, NavigationDataManager.getInstance().getMapName());
    }

    private void initChassisSystem() {
        Log.d(TAG, "initChassisSystem: " + API_IN);
        RoverConfigProto configProto = ClientConfigManager.getInstance().generateRoverConfigProtoFromConfigFile();
        Result result = commandApi.init(configProto);
        Log.d(TAG, "initChassisSystem: " + API_BACK + ", result = " + result.toString());
        //如果配置的有lora，则下发lora配置
        MultiRobotConfigBean loraConfig = ClientConfigManager.getInstance().loadMultiRobotConfig();
        setMultiRobotSettingConfigData(loraConfig, null);
        if (result.getCode() == SUCCESS || result.getCode() == CHASSIS_ALREADY_INITED) {
            setChassisOK(true);
            BiManager.getInstance().doFixedRateTask();
        }
        Log.d(TAG, "initChassisSystem: end!");
    }

    private void destroyChassisSystem() {
        Log.d(TAG, "destroyChassisSystem: " + API_IN);
        Result result = commandApi.destroy();
        if (result.getCode() == SUCCESS) {
            setChassisOK(false);
        }
        Log.d(TAG, "destroyChassisSystem: " + API_BACK + ", result = " + result);
    }

    private void notifyInspect(String name, String status) {
        if (mSimpleEventListener != null) {
            mSimpleEventListener.onUpdateChassisConnectStatus(name, status);
        }
    }

    @Override
    public void setRoverConfig(RoverConfig roverConfig, ChassisResListener listener) {
        //todo
        NavigationDataManager.getInstance().updateMultiRobotConfig(roverConfig);
    }

    @Override
    public void getRoverConfig(ChassisResListener listener) {
        //todo
        String roverConfig = NavigationDataManager.getInstance().getRoverConfig();
        if (TextUtils.isEmpty(roverConfig)) {
            //            throw new RuntimeException("no roverConfig in navigation.properties");
            Log.e(TAG, "no roverConfig in navigation.properties");
            listener.onResponse(false, FAIL_NO_REASON, "no roverConfig in navigation.properties");
            return;
        }

        RoverConfig config = GsonUtil.fromJson(roverConfig, RoverConfig.class);
        listener.onResponse(true, SUCCESS, config);
    }

    @Override
    public void setTime(final long time, final ChassisResListener listener) {
        Log.d(TAG, API_IN + "setTime = " + time);
        Result result = commandApi.setTime(time);
        Log.d(TAG, API_BACK + "setTime result = " + result.toString());

        if (listener != null) {
            if (result.getCode() == 0) {
                listener.onResponse(true, result.getCode(), "Success");
            } else {
                listener.onResponse(false, result.getCode(), "Failed");
            }
        }

    }

    @Override
    public boolean isCommunicating() {
        return isSocketConnected() && isRemoteRunning();
    }

    @Override
    public boolean takeSnapshot(final String logID, ChassisResListener listener) {
        Log.d(TAG, "takeSnapshot logID:" + logID);
        if (listener == null) {
            return false;
        }
        if (mSnapshotTask != null && !mSnapshotTask.isDone()) {
            listener.onResponse(false, -1, "this thread is running");
            return false;
        }
        listener.onResponse(true, 0, "");
        mSnapshotTask = mFileExecutor.submit(new Runnable() {
            @Override
            public void run() {
                File logFile = MapFileHelper.createSnapshotLogFile(logID);
                long sTime = System.currentTimeMillis() / 1000;
                Log.d(TAG, "takeSnapshot:getLog: logFile:" + logFile.getAbsolutePath() + " sTime:" + sTime);
                Result result = commandApi.getLog(logFile,
                        CommonProtoWrapper.LogTypeProto.LOG_TYPE_SNAPSHOT,
                        sTime,
                        sTime);
                Log.d(TAG, "takeSnapshot:getLog: result:" + result.getCode() + " data:" + result.getPayload());
                if (mChassisEventListener == null) {
                    return;
                }
                if (result.getCode() == Result.CODE_SUCCESS) {
                    updateTakeSnapshotEnd(Definition.HW_NAVI_TAKE_SNAPSHOT_END, logID,
                            logFile.getAbsolutePath());
                } else {
                    //路径传空，代表文件不存在
                    updateTakeSnapshotEnd(Definition.HW_NAVI_TAKE_SNAPSHOT_END, logID,
                            "");
                }

            }
        });
        return false;
    }

    @Override
    public boolean isServiceReady() {
        Log.e(TAG, "isServiceReady:" + mRoverSdkReady);
        return mRoverSdkReady;
    }

    @Override
    public boolean isSocketConnected() {
        Log.e(TAG, "mRoverSdkReady:" + mRoverSdkReady);
        return mRoverSdkReady;
    }

    @Override
    public boolean isChassisReady() {
        Log.e(TAG, "isChassisReady:" + isChassisOK());
        return isChassisOK();
    }

    @Override
    public void setEventListener(ChassisEventListener listener) {
        mChassisEventListener = listener;
    }

    @Override
    public Pose getCurrentPose() {
        if (!isPoseEstimate) {
            Log.d(TAG, "not pose estimate");
            return null;
        }
        return mCurPose.get();
    }

    @Override
    public Pose getCurrentPoseWithoutEstimate() {
        return mCurPose.get();
    }

    @Override
    public Pose getRealtimePose() {
        return mCurrentRealPose.get();
    }

    @Override
    public ChassisPacketProtoWrapper.RealtimeObsMapProto getRealtimeObsMapProto() {
        return mRealtimeObsMapProto.get();
    }

    @Override
    public Velocity getVelocity() {
        Velocity velocity = mVelocity.get();
        if (velocity == null) {
            velocity = new Velocity(0, 0);
        }
        return velocity;
    }

    @Override
    public boolean isPoseEstimate() {
        return isPoseEstimate;
    }

    private void updatePoseEstimate(boolean isPoseEstimate) {
        Log.d(TAG, "updatePoseEstimate: " + isPoseEstimate + " oldState:" + this.isPoseEstimate);
        if (this.isPoseEstimate == isPoseEstimate) {
            return;
        }
        this.isPoseEstimate = isPoseEstimate;

        if (mChassisEventListener != null) {
            mChassisEventListener.onPoseEstimate(isPoseEstimate);
        }
        if(!isPoseEstimate){
            biManager.resetLastPose();
        }
    }

    @Override
    public void checkCurNaviMap(ChassisResListener listener) {
        final String mapName = NavigationDataManager.getInstance().getMapName();
        Log.d(TAG, "checkCurNaviMap mapName=" + mapName);
        if (TextUtils.isEmpty(mapName)) {
            listener.onResponse(false, FAIL_NO_REASON, "821 has no naviMap");
            return;
        }

        if (!checkNaviMap(mapName)) {
            listener.onResponse(false, FAIL_NO_REASON, "Chassis has no map");
            return;
        }
        listener.onResponse(true, SUCCESS, "Chassis has map");
    }

    @Override
    public void goCharge(boolean isFrontCamera, ChassisResListener listener) {
        Log.d(TAG, "goCharge: " + API_IN);
        Result result = commandApi.gotoCharge(isFrontCamera);
        Log.d(TAG, "goCharge: " + API_BACK + ", result = " + result.toString());
        this.mGoChargelistener = listener;
        ChargeResult chargeResult = new ChargeResult();
        int code = result.getCode();
        chargeResult.setCode(code);
        if (null != listener) {
            if (code == 0) {
                chargeResult.setMessage("Go charge command success");
                listener.onResponse(true, code, GsonUtil.toJson(chargeResult));
            } else {
                chargeResult.setMessage("Go charge command failed");
                listener.onResponse(false, code, GsonUtil.toJson(chargeResult));
            }
        }
    }

    @Override
    public void stopCharge(ChassisResListener listener) {
        Result result = commandApi.cancelCharge();
        Log.d(TAG, "stopCharge result = " + result.toString());
        ChargeResult chargeResult = new ChargeResult();
        int code = result.getCode();
        chargeResult.setCode(code);
        if (null != listener) {
            if (code == 0) {
                chargeResult.setMessage("Stop charge command success");
                listener.onResponse(true, code, GsonUtil.toJson(chargeResult));
            } else {
                chargeResult.setMessage("Stop charge command failed");
                listener.onResponse(false, code, GsonUtil.toJson(chargeResult));
            }
        }
    }

    @Override
    public String getMapStatus(String cmdType, String cmdParam) {
        if (isCreatingMap) {
            return Definition.NAVIGATION_WORK_MODE_CREATING_MAP;
        }
        return "";
    }

    /**
     * 指定名称地图是否存在于底盘
     *
     * @param name 用户在 MapTool 设定的地图名
     * @return true 存在
     */
    private boolean checkNaviMap(String name) {
        String curMap = NavigationDataManager.getInstance().getMapName();
        return name.equals(curMap);
    }

    @Override
    public boolean isMoving() {
        return isNavigationing();
    }


    private boolean isNavigationing() {
        return mTargetPose != null
                && mTargetPose.isAvailable();
    }


    /**
     * 加載指定地图，load 成功后需要设置当前使用地图
     */
    @Override
    public void switchMap(String mapName, ChassisResListener listener) {
        Log.d(TAG, "switchMap: mapName=" + mapName);
        if (TextUtils.isEmpty(mapName)) {
            listener.onResponse(false, FAIL_NO_REASON, "Map name is null!");
            return;
        }
        String curMapName = NavigationDataManager.getInstance().getMapName();
        Log.d(TAG, "switchMap: curMapName=" + curMapName);
        boolean keepEstimate = false;
        //如果当前是定位状态，且当前地图和需要切换的是同一张地图，则可以不必重定位
        if (TextUtils.equals(curMapName, mapName) && isPoseEstimate) {
            keepEstimate = true;
        }
        loadMap(NavigationDataManager.getInstance().getMapByName(mapName), keepEstimate, listener);
    }

    @Override
    public void loadCurrentMap(boolean isUseCustomKeepPose, boolean keepPose, ChassisResListener listener) {
        Log.d(TAG, "loadCurrentMap: isUseCustomKeepPose:" + isUseCustomKeepPose + " keepPose:" + keepPose);
        loadMap(NavigationDataManager.getInstance().getNavMapInfo(), isUseCustomKeepPose ? keepPose : isPoseEstimate, listener);
    }

    private void loadMap(MapInfo mapInfo, boolean keepPose, ChassisResListener listener) {
        Log.d(TAG, "loadMap:");
        if (mapInfo == null || TextUtils.isEmpty(mapInfo.getMapName())) {
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON,
                        "loadMap mapName is null");
            }
            return;
        }
        String mapName = mapInfo.getMapName();
        Log.d(TAG, "loadMap: mapName=" + mapName);
        CommonProtoWrapper.MapConfigProto mapModePro = MapTypeHelper.
                generateLoadMapModeProto(mapInfo.getMapType());//loadMap
        Log.d(TAG, "loadMap: " + API_IN + " mapMode:" + mapModePro
                + " keepPose:" + keepPose);
        Result result = commandApi.loadMap(MapFileHelper.getNaviDataFile(mapName),//loadMap
                keepPose, mapModePro, mapInfo.getMapUuid());
        Log.d(TAG, "loadMap: " + API_BACK + " code=" + result.getCode() +
                " result=" + result.getPayload());
        if (result.getCode() == Result.CODE_SUCCESS) {
            NavigationDataManager.getInstance().setMapName(mapName);//loadMap
            listener.onResponse(true, SUCCESS, "loadMap success");
        } else {
            listener.onResponse(false, FAIL_NO_REASON, "loadMap fail");
        }
        updateCurEstimateState();
        IrLedManager.getInstance().setTagMode();
    }

    /**
     * LOCATION_STATE_NONE(0),      //没有加载地图，此状态下需要先加载地图；
     * LOCATION_STATE_UNLOCATED(1), //加载地图后，从来没有基于当前地图定位过；
     * LOCATION_STATE_NORMAL(2),    //定位状态正常；
     * LOCATION_STATE_LOST(3),      //定位已经丢失，但是还是有位置的，只是不准；
     */
    private void updateCurEstimateState() {
        Result<GetLocationStateProtoWrapper.LocationStateProto> locationStateResult = commandApi.getLocationState();
        Log.d(TAG, "updateCurEstimateState resultCode: " + locationStateResult.getCode());
        if (locationStateResult.getCode() == Result.CODE_SUCCESS) {
            GetLocationStateProtoWrapper.LocationStateProto locationState = locationStateResult.
                    getPayload();
            Log.d(TAG, "updateCurEstimateState: " + locationState.getTypeValue());
            //更新点位状态
            updatePoseEstimate(locationState.getTypeValue() ==
                    GetLocationStateProtoWrapper.LocationStateProto.Type.LOCATION_STATE_NORMAL_VALUE);
        }
    }

    @Override
    public void setCurrentWorkModeFree(final ChassisResListener listener) {
        if (listener != null) {
            listener.onResponse(true, SUCCESS, "");
        }
    }

    private volatile boolean isStoppingCreateMap = false;
    private volatile boolean isCreatingMap = false;

    private boolean isCreatingMap() {
        return isCreatingMap;
    }

    private void setCreatingMap(boolean creatingMap) {
        Log.d(TAG, "setCreatingMap: " + creatingMap);
        isCreatingMap = creatingMap;
        MapOutsideManager.getInstance().onMapCreate(isCreatingMap);
        IrLedManager.getInstance().setCreatingMap(isCreatingMap);
    }

    @Override
    public void startCreatingMap(NaviMapType naviMapType, ChassisResListener listener) {
        Log.d(TAG, "startCreatingMap: isCreating=" + isCreatingMap() +
                " naviMapType=" + naviMapType);
        if (isCreatingMap()) {
            listener.onResponse(true, SUCCESS, "is building");
            return;
        }
        CommonProtoWrapper.MapConfigProto mapModePro = MapTypeHelper.
                generateMapModeProtoByNaviMapType(naviMapType);//startCreateMap
        if (mapModePro == null) {
            Log.d(TAG, "startCreatingMap:Error: mapModePro null!");
            listener.onResponse(false, FAIL_NO_REASON, "Map type error!");
            return;
        }
        if (isStoppingCreateMap) {
            isStoppingCreateMap = false;
        }
        if (mCreateMapStop != null) {
            mCreateMapStop = null;
        }
        setCreatingMap(true);
        IrLedManager.getInstance().setCreatingMapType(naviMapType);

        Log.d(TAG, "startCreatingMap: Navi api call: startCreateMap: mapModePro=" +
                mapModePro.getVisionMode() + ", " + mapModePro.getTargetMode());
        Result resultStart = commandApi.startCreateMap(mapModePro);
        Log.d(TAG, "startCreatingMap:  Navi api callback: resultStart=" +
                resultStart.toString());
        if (resultStart.getCode() != Result.CODE_SUCCESS) {
            setCreatingMap(false);
            listener.onResponse(false, resultStart.getCode(), resultStart.getPayload());
        } else {
            listener.onResponse(true, SUCCESS, "start create map success");
        }
    }

    @Override
    public void stopCreatingMap(String mapName, boolean save, String language, int mapType, int finishState, CreateMapStop listener) {
        Log.d(TAG, "stopCreatingMap: mapName=" + mapName + ", save=" + save +
                ", language=" + language + ", mapType=" + mapType + ", finishState=" + finishState +
                ", isCreatingMap:" + isCreatingMap() +
                " isStoppingCreateMap:" + isStoppingCreateMap);
        if (!isCreatingMap()) {
            listener.onResult(false, CreateMapStop.RESULT_NOT_CREATING_MAP_MODE);
            return;
        }
        if (isStoppingCreateMap) {
            listener.onResult(false, CreateMapStop.RESULT_ALREADY_STOPPING_CREATE_MAP);
            return;
        }
        isStoppingCreateMap = true;
        mCreateMapStop = listener;

        String naviDataPath = save ? MapFileHelper.getNaviDataPath(mapName) : null;//stopCreateMap
        Log.d(TAG, "stopCreatingMap: naviDataPath=" + naviDataPath);
        Log.d(TAG, "stopCreatingMap: Navi api call:stopCreateMap:");
        Result<CommonProtoWrapper.MapInfoProto> saveMapResult = commandApi.stopCreateMap(naviDataPath);
        Log.d(TAG, "stopCreatingMap: Navi api callback:stopCreateMap: " + saveMapResult.toString());

        if (save) {
            if (saveMapResult.getCode() == SUCCESS) {
                updatePoseEstimate(true);//stopCreatingMap Success

                if (saveMapResult.getPayload()
                        instanceof CommonProtoWrapper.MapInfoProto) {
                    CommonProtoWrapper.MapInfoProto proto = saveMapResult.getPayload();
                    List<CommonProtoWrapper.TargetPointProto> list = proto.getPoints().getPointsList();
                    Log.d(TAG, "stopCreatingMap: - commandApi.saveMap(): " + "getPointsList: " + list.size());

                    List<MappingPose> mappingPoseList = new ArrayList<>();
                    for (CommonProtoWrapper.TargetPointProto point : list) {
                        MappingPose mappingPose = new MappingPose();

                        boolean _valid = point.getValid();
                        int id = point.getId();
                        Pose2dProtoWrapper.Pose2dProto poseProto = point.getPose();

                        mappingPose.setValid(_valid);
                        mappingPose.setId(id);
                        Pose pose = new Pose((float) poseProto.getX(), (float) poseProto.getY(),
                                (float) poseProto.getT());
                        mappingPose.setPose(pose);
                        mappingPoseList.add(mappingPose);
                    }
                    mCreateMapStop.onSaveMap(true, mappingPoseList);
                }

                if (!TextUtils.isEmpty(mapName) && save) {
                    Log.d(TAG, "stopCreatingMap:Save map info to DB!");
                    File pgmFile = MapFileHelper.getMapPgm(mapName);//stopCreateMap
                    int targetData = transferTargetsData2Json(mapName);//stopCreateMap
                    int trackData = transferTrackData2Json(mapName);//stopCreateMap
                    String md5 = createFileMd5(pgmFile.getAbsolutePath(), mapName);
                    String mapId = UuidUtils.createMapId(mapName);
                    //地图文件存储完成，插入一条地图信息数据
                    boolean saveDbResult = NavigationDataManager.getInstance().
                            saveNewCreatedMapInfo(mapId
                                    , mapName
                                    , md5
                                    , MapFileHelper.getMapFilePath(mapName)
                                    , language
                                    , mapType
                                    , finishState
                                    , targetData);
                    Log.d(TAG, "stopCreatingMap: saveDbResult=" + saveDbResult);

                    //save 参数为 true 时，结束建图后底盘就已经切换到当前地图
                    NavigationDataManager.getInstance().setMapName(mapName);//stopCreatingMap
                    //将地图信息存入到地图文件夹内一份，只在开始建图时存储
                    MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
                    MapUtils.saveMapInfoJson(mapName, mapInfo);//stopCreatingMap
                    MapUtils.saveMapConfig(mapName);//stopCreatingMap
                    Log.d(TAG, "stopCreatingMap: setMapName done");
                }
                isStoppingCreateMap = false;
                setCreatingMap(false);
                Log.d(TAG, "stopCreatingMap: Success!");
                listener.onResult(true, "Save success!");
            } else {
                isStoppingCreateMap = false;
                setCreatingMap(false);
                mCreateMapStop.onSaveMap(false, null);
                listener.onResult(false, "Save failed!");
                updateCurEstimateState();
                BiManager.getInstance().reportCreateMapResult(mapName, saveMapResult.getCode(), 0, 0, 0, "", "");
                return;
            }
        } else {
            if (saveMapResult.getCode() == SUCCESS) {
                listener.onResult(true, "Cancel success!");
            } else {
                listener.onResult(false, "Cancel failed!");
            }
            isStoppingCreateMap = false;
            setCreatingMap(false);
        }
        updateCurEstimateState();
    }

    @Override
    public void startExtendMap(ChassisResListener listener) {
        MapInfo mapInfo = NavigationDataManager.getInstance().getNavMapInfo();
        Log.d(TAG, "startExtendMap: " + mapInfo.getMapName());

        if (isCreatingMap()) {
            listener.onResponse(true, SUCCESS, "is building");
            return;
        }

        if (isStoppingCreateMap) {
            isStoppingCreateMap = false;
        }
        if (mCreateMapStop != null) {
            mCreateMapStop = null;
        }
        setCreatingMap(true);
        CommonProtoWrapper.MapConfigProto mapModePro = MapTypeHelper.
                generateMapModeProtoByNaviMapType(mapInfo.getMapType());//startExtendMap

        Result result = commandApi.startExtendMap(mapModePro);
        Log.d(TAG, "startExtendMap: " + result.toString());
        if (result.getCode() == Result.CODE_SUCCESS) {
            listener.onResponse(true, SUCCESS, "startExtendMap success");
        } else if (result.getCode() == START_EXPANSION_MAP_FAILED) {
            setCreatingMap(false);
            if (listener != null) {
                listener.onResponse(false, START_EXPANSION_MAP_FAILED, "");
                return;
            }
        } else {
            setCreatingMap(false);
            listener.onResponse(false, FAIL_NO_REASON, "startExtendMap fail");
        }
    }

    @Override
    public void stopExtendMap(String mapName, ChassisResListener listener) {
        Log.d(TAG, "stopExtendMap: mapName=" + mapName);
        String naviDataPath = MapFileHelper.getNaviDataPath(mapName);//stopExtendMap
        Result<CommonProtoWrapper.MapInfoProto> saveMapResult = commandApi.stopCreateMap(naviDataPath);
        Log.d(TAG, "stopExtendMap: saveMapResult=" + saveMapResult.toString());
        if (saveMapResult.getCode() == SUCCESS) {
            setCreatingMap(false);
            Log.d(TAG, "stopExtendMap: Save map success!");
            //判断如果有road.json数据，需要将数据中转成road_graph.data存储到data.zip内
            MapUtils.RoadDataState roadState = MapUtils.transferRoadJson2Data(mapName);//stopExtendMap
            Log.d(TAG, "stopExtendMap: Transfer Result: roadState:" + roadState);
            //判断如果有zip内mapAreaConfig.json数据，需要将数据中转成mapAreaConfig.data存储到data.zip内
            MapUtils.MapAreaDataState mapAreaDataState =  MapUtils.transferMapAreaJson2Data(mapName);
            Log.d(TAG, " stopExtendMap: mapAreaDataState = " +mapAreaDataState);

            //targets.data 转 target.json
            int targetData = transferTargetsData2Json(mapName);//stopExtendMap
            //mapping_track.data 转 mapping_track.json
            int trackData = transferTrackData2Json(mapName);//stopExtendMap
            Log.d(TAG, "stopExtendMap: Transfer Result: targetData:" + targetData
                    + " trackData=" + trackData);
            File pgmFile = MapFileHelper.getMapPgm(mapName);//stopExtendMap
            String md5 = createFileMd5(pgmFile.getAbsolutePath(), mapName);
            //更新md5、targetData
            MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
            if (mapInfo != null) {
                Log.d(TAG, "stopExtendMap: Save mapInfo, old=" + mapInfo);
                mapInfo.setMd5(md5);
                mapInfo.setTargetData(targetData);
                Log.d(TAG, "stopExtendMap: Save mapInfo, new=" + mapInfo);
                NavigationDataManager.getInstance().updateMapInfo(mapInfo);
            }
            //更新mapinfo.json
            MapUtils.saveMapInfoJson(mapName, mapInfo);//stopExtendMap

            Log.d(TAG, "stopExtendMap: Save mapConfig.json with new md5");
            //更新mapConfig.json，主要是更新md5，并使用最新的config结构
            MapUtils.saveMapConfig(mapName);//stopExtendMap

            if (listener != null) {
                listener.onResponse(true, SUCCESS, "");
                return;
            }

        } else if (saveMapResult.getCode() == STOP_EXPANSION_MAP_FAILED) {
            //停止扩建地图失败，还在建图中模式
            if (listener != null) {
                listener.onResponse(false, STOP_EXPANSION_MAP_FAILED, "");
                return;
            }
        }
        setCreatingMap(false);
        listener.onResponse(false, FAIL_NO_REASON, "");
    }

    /**
     * 如果是招财豹机器人，地图信息有二维码信息，需要转存地图内的Target信息到地图磁盘内
     * 生成地图时targets信息存储在/sdcard/robot/map/***map/pgm.zip/targets.data
     * 转成Json后存在/sdcard/robot/map/***map/target.json中
     */
    private int transferTargetsData2Json(String mapName) {
        int resultCode = MapUtils.transferTargetsData2Json(mapName);
        Log.d(TAG, "transferTargetsData2Json: mapName=" + mapName + " result=" + resultCode);
        return resultCode;
    }

    /**
     * 地图有建图路径信息，需要转存地图内的mapping_track信息到地图磁盘内
     * 生成地图时mapping_track信息存储在/sdcard/robot/map/***map/data.zip/mapping_track.data
     * 转成Json后存在/sdcard/robot/map/***map/mapping_track.json中
     */
    private int transferTrackData2Json(String mapName) {
        int resultCode = MapUtils.transferTrackData2Json(mapName);
        Log.d(TAG, "transferTrackData2Json: mapName=" + mapName + " result=" + resultCode);
        return resultCode;
    }

    private String createFileMd5(String mapPath, String mapName) {
        File pgmFile = new File(mapPath);
        if (pgmFile.exists()) {
            String md5 = MD5.getFileMd5(mapPath);
            if (!TextUtils.isEmpty(md5)) {
                return md5;
            }
        }
        return "";
    }

    private synchronized void updateTargetPose(int status, int navigationCode) {
        if (mTargetPose != null) {
            mTargetPose.onResult(status, navigationCode);
            DistanceUtils.resetCalculationCount();
        }
    }

    @Override @Deprecated
    public void go(TargetPose targetPose, ChassisResListener listener) {
        go(targetPose, null, null, listener);
    }

    @Override
    public void go(TargetPose targetPose, NavVelocity navVelocity, NavAcceleration navAcc,
                   ChassisResListener listener) {
        Log.d(TAG, "go:: targetPose=" + targetPose.toString()
                + " navVelocity=" + (navVelocity == null ? "null" : navVelocity.toString()));
        updateTargetPose(TargetPose.RESULT_REPLACE, NavigationResult.RESULT_NAVI_EVENT_RESULT_REPLACE);
        mTargetPose = targetPose;
        hasReportedUnmove.set(false);
        if (mRecordPose == null) {
            mRecordPose = new AtomicReference<>();
        }
        mRecordPose.set(null);
        operationListener = listener;
        WheelControlX86.getInstance().cancelDecTimer(true);
        WheelControlX86.getInstance().resetSpeed();
        updateMotionAvoidState(false);

        Pose pose = targetPose.getPose();

        double navLinear;
        double navAngular;
        double maxJerk = 0;
        CountDownLatch latch = new CountDownLatch(1);

        if (null == navVelocity) {
            navLinear = Def.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
            navAngular = Def.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
        } else {
            navLinear = navVelocity.getLinear();
            navAngular = navVelocity.getAngular();
        }
        int moveType = targetPose.getMoveType();
        int rotateType = targetPose.getRotateType();
        boolean keepMove = targetPose.getIsKeepMove();
        float destinationRangeParam = (float) targetPose.getDestinationRange();
        float areaRange = RobotSettingApi.getInstance().getRobotFloat(Definition.ROBOT_SETTING_PARKING_AREA); //areaRange现在单位是cm
        areaRange = areaRange / 100;
        float destinationRange;
        if (ProductInfo.isCarryProduct() && pose.getTypeId() == Definition.NORMAL_POINT_TYPE) {
            destinationRange = calculateRobotAdaptRange(destinationRangeParam);
        } else {
            destinationRange = Math.max(destinationRangeParam, areaRange);
        }

        double obsDistance = targetPose.getBlockObsDistance();
        NavAcceleration correctAcc = correctNavAcceleration(navAcc, navLinear);
        String poseName = TextUtils.isEmpty(pose.getName()) ? "" : pose.getName();

        int startModeLevel = 0;
        int brakeModeLevel = 0;
        NavMode navMode = targetPose.getNavMode();
        if (navMode != null) {
            startModeLevel = navMode.getStartModeLevel();
            brakeModeLevel = navMode.getBrakeModeLevel();
        }
        int roadMode = targetPose.getRoadMode();
        Log.d(TAG, "go:: action pose:" + pose + " velocity:" + navVelocity
                + ", moveType:" + moveType + ", rotateType = " + rotateType
                + ", keepMove = " + keepMove
                + ", destinationRangeParam = " + destinationRangeParam
                + ", areaRange = " + areaRange
                + ", destinationRange = " + destinationRange
                + ", obsDistance = " + obsDistance
                + ", navAcc:" + navAcc + " correctAcc:" + correctAcc
                + ", roadMode" + roadMode);


        ///> FIXME: 只招财豹生效, 根据启动档位,重新覆盖 角速度, 线加速度, 角加速度, 临时代码
        ///> FIXME: 导航建议非旋转时走档位
        if ((ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) && moveType != SetGoalProtoWrapper.MoveTypeProto.MOVE_PURE_ROTATION_VALUE) {
            StartModeResetParam p = toResetNavigationParam(navLinear, startModeLevel);
            navAngular = p.mAng;
            correctAcc.setLinearAcc(p.mLineAcc);
            correctAcc.setAngularAcc(p.mAngAcc);
            maxJerk = p.mJ;
        }

        VelocityProtoWrapper.VelocityProto velocity = VelocityProtoWrapper.VelocityProto.newBuilder()
                .setAngular(navAngular)
                .setLiner(navLinear)
                .build();
        VelocityProtoWrapper.VelocityProto accelerationProto = VelocityProtoWrapper.VelocityProto.newBuilder()
                .setLiner(correctAcc.getLinearAcc())
                .setAngular(correctAcc.getAngularAcc())
                .build();
        Pose2dProtoWrapper.Pose2dProto pose2dProto = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                .setX(pose.getX())
                .setY(pose.getY())
                .setT(pose.getTheta())
                .build();

        SetGoalProtoWrapper.MoveTypeProto moveTypeParam = SetGoalProtoWrapper.MoveTypeProto.forNumber(moveType);
        SetGoalProtoWrapper.RotationTypeProto rotateTypeParam = SetGoalProtoWrapper
                .RotationTypeProto.forNumber(rotateType);

        SetGoalProtoWrapper.BrakeModeProto breakModeParam = getBreakMode(brakeModeLevel);

        SetGoalProtoWrapper.RoadModeProto roadModeProto = SetGoalProtoWrapper.RoadModeProto.forNumber(roadMode);

        SetGoalProtoWrapper.GoalProto goal = SetGoalProtoWrapper.GoalProto.newBuilder()
                .setVelocity(velocity)
                .setMoveType(moveTypeParam)
                .setRotationType(rotateTypeParam)
                .setPose(pose2dProto)
                .setKeepMove(keepMove)
                .setRange(destinationRange)
                .setMaxAcc(accelerationProto)
                .setName(poseName)
                .setBrakeMode(breakModeParam)
                .setMaxJerk(maxJerk)
                .setSafeDistance((float) pose.getSafeDistance() / 100)
                .setIgnoreDepthObsDistance(pose.getIgnoreDistance() ? Definition.POSE_IGNORE_DEPTH_OBS_DISTANCE_DEFAULT : 0)
                .setBlockObsDistance(obsDistance)//最大避障距离
                .setPosTolerance(targetPose.getPosTolerance())
                .setAngleTolerance(targetPose.getAngleTolerance())
                .setRoadMode(roadModeProto)
                .build();

        if (mTargetPose != null) {
            mTargetPose.onStatusUpdate(TargetPose.STATUS_STARTED, NavigationStatus.STATUS_NAVI_EVENT_START, "");
        }

        //去掉产品线判断，根据多机模型、巡线行走两个开关来处理业务逻辑。
        //跑多机必须开巡线，所以多机打开，巡线必须打开，设置多机参数
        //多机未打开，则不需要设置多机参数，也不需要判断巡线是否打开
        boolean isEnableLineTracking = false;
        boolean isEnableMultiRobot = false;
        String loraConfig = NavigationDataManager.getInstance().getMultiRobotConfig();
        String roverConfig = NavigationDataManager.getInstance().getRoverConfig();
        Log.d(TAG, "go:: loraConfig = " + loraConfig + ", roverConfig = " + roverConfig);
        if (!TextUtils.isEmpty(loraConfig)) {
            MultiRobotConfigBean multiRobotConfigBean = GsonUtil.fromJson(loraConfig, MultiRobotConfigBean.class);
            if (multiRobotConfigBean != null) {
                Log.d(TAG, "go:: multiRobotConfigBean = " + multiRobotConfigBean);
                isEnableMultiRobot = multiRobotConfigBean.isEnable();
            }
        }
        if (!TextUtils.isEmpty(roverConfig)) {
            RoverConfig roverConfigBean = GsonUtil.fromJson(roverConfig, RoverConfig.class);
            if (roverConfigBean != null) {
                Log.d(TAG, "go:: roverConfigBean = " + roverConfigBean);
                isEnableLineTracking = roverConfigBean.isEnableLineTracking();
            }
        }

        Log.d(TAG, "go:: isEnableMultiRobot = " + isEnableMultiRobot +
                ", isEnableLineTracking = " + isEnableLineTracking);
        if (isEnableMultiRobot) {
            if (!isEnableLineTracking) {
                Log.d(TAG, "go:: line tracking is closed in multi robot mode!");
                mTargetPose.onStatusUpdate(TargetPose.STATUS_GLOBAL_PATH_FAILED,
                            NavigationStatus.STATUS_NAVI_LINE_TRACKING_CLOSED_IN_MULTI_MODE, "");
                return;
            }
            int priority = targetPose.getNavPriority();
            Log.d(TAG, API_IN + " go:: naviToGoal pre priority:" + priority);
            //priority 取值范围0-29，大于29的值会导致底盘崩溃，这里做一下保护：大于29的值设为29，小于0的值设为0
            if (priority > 29) {
                priority = 29;
            } else if (priority < 0) {
                priority = 0;
            }
            Log.d(TAG, API_IN + " go:: naviToGoal final priority:" + priority);
            Result priorityResult = commandApi.multiRobotPriority(priority);
            Log.d(TAG, API_BACK + " go:: " + "naviToGoal priority result = " + priorityResult.toString());
            if (priorityResult.getCode() != Result.CODE_SUCCESS) {
                mTargetPose.onPassThroughStatusUpdate(TargetPose.STATUS_PRIORITY_SET_FAILED,
                        NavigationResult.RESULT_NAVI_SET_PRIORITY_FAILED, "");
            }
            try {
                boolean res = latch.await(MULTI_ROBOT_PRIORITY_DELAY, TimeUnit.MILLISECONDS);
                Log.d(TAG, "go:: await:" + res);
            } catch (InterruptedException e) {
                Log.e(TAG, "go:: await err:" + e.getMessage());
            }
        }

        Log.d(TAG, API_IN + " go:: naviToGoal");
        Result result1 = commandApi.naviToGoal(goal);
        Log.d(TAG, API_BACK + "go:: " + "naviToGoal result1 = " + result1.toString());

        if (result1.getCode() != Result.CODE_SUCCESS) {
            updateTargetPose(TargetPose.RESULT_FAILED, NavigationResult.RESULT_NAVI_EVENT_RESULT_FAILED);
            if (listener != null) {
                listener.onResponse(false, result1.getCode(), result1.getPayload());
            }
        } else {
            if (operationListener != null) {
                operationListener.onResponse(true, result1.getCode(), result1.getPayload());
            }
        }
    }

    class StartModeResetParam {
        public double mJ;           ///> J
        public double mAng;         ///> 角速度
        public double mLineAcc;     ///> 线加速度
        public double mAngAcc;      ///> 角加速度

        public String toString() {
            return "j: " + mJ
                    + ", lineracc: " + mLineAcc
                    + ", angular: " + mAng
                    + ", angularacc: " + mAngAcc;
        }
    }

    private StartModeResetParam toResetNavigationParam(double navLinear, int startModeLevel) {
        SubDeviceBean subDeviceBean = MapFileHelper.getSubDeviceInfo();
        // TODO: Merge回主线要特别注意,与导航PID算法联调完一起上线
        if (!(ProductInfo.isMeissa2() || ProductInfo.isSaiphPro() || ProductInfo.isAlnilamPro()) && (subDeviceBean == null || subDeviceBean.getDampener() == 0)) {
            return toNavigationParamNoDampener(navLinear, startModeLevel);
        } else {
            return toNavigationParamDampener(navLinear, startModeLevel);
        }
    }

    private StartModeResetParam toNavigationParamNoDampener(double navLinear, int startModeLevel) {
        StartModeResetParam p = new StartModeResetParam();
        //平缓、顺滑、常规、敏捷、迅速
        final int START_MODE_GENTLE = 1;
        final int START_MODE_SMOOTH = 2;
        final int START_MODE_REGULAR = 3;
        final int START_MODE_AGILE = 4;
        final int START_MODE_SWIFT = 5;
        final int START_MODE_OLD_GENTLE = 6;
        final int START_MODE_OLD_SMOOTH = 7;
        final int START_MODE_OLD_REGULAR = 8;
        final int START_MODE_OLD_AGILE = 9;
        final int START_MODE_OLD_SWIFT = 10;
        startModeLevel = startModeLevel > 0 ? startModeLevel : RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_NAVIGATION_START_MODE_LEVEL);
        float angularSpeedScale = RobotSettingApi.getInstance().getRobotFloat(Definition.ROBOT_SETTING_CUSTOM_ANGULAR_SPEED);
        if (Math.abs(angularSpeedScale) < 0.01f) {
            angularSpeedScale = 1.0f;
        }
        if (ProductInfo.isCarryProduct()) {
            switch (startModeLevel) {
                case START_MODE_GENTLE:
                    p.mJ = 0.6f;
                    p.mLineAcc = navLinear / 4f;
                    p.mAng = 0.5 * angularSpeedScale;
                    p.mAngAcc = navLinear / 1.6f;
                    break;
                case START_MODE_SMOOTH:
                    p.mJ = 20f;
                    p.mLineAcc = navLinear / 2.5f;
                    p.mAng = calculateAngularVelocityFormulaCarryLv2(navLinear) * angularSpeedScale;
                    p.mAngAcc = navLinear / 0.8f;
                    break;
                case START_MODE_REGULAR:
                    p.mJ = 1.0f;
                    p.mLineAcc = navLinear / 1.2f;
                    p.mAng = calculateAngularVelocityFormulaCarryLv3(navLinear) * angularSpeedScale;
                    p.mAngAcc = navLinear / 0.7f;
                    break;
                default:
                    p.mJ = 1.0f;
                    p.mLineAcc = navLinear / 1.2f;
                    p.mAng = navLinear / 0.8f * angularSpeedScale;
                    p.mAngAcc = navLinear / 0.7f;
                    break;
            }
        } else {
            switch (startModeLevel) {
                case START_MODE_GENTLE:
                    p.mJ = 0.6f;
                    p.mLineAcc = navLinear / 4f;
                    p.mAng = 0.5 * angularSpeedScale;
                    p.mAngAcc = navLinear / 1.6f;
                    break;
                case START_MODE_SMOOTH:
                    p.mJ = 20f;
                    p.mLineAcc = navLinear / 2.5f;
                    p.mAng = calAngSpdWithSlopeIncreasingCurve(navLinear) * angularSpeedScale;
                    p.mAngAcc = navLinear / 0.8f;
                    break;
                case START_MODE_REGULAR:
                    p.mJ = 1.0f;
                    p.mLineAcc = navLinear / 1.2f;
                    p.mAng = calAngSpdWithSlopeIncreasingCurve(navLinear) * angularSpeedScale;
                    p.mAngAcc = navLinear / 0.7f;
                    break;
                case START_MODE_AGILE:
                    p.mJ = 1.5f;
                    p.mLineAcc = navLinear / 1.1f;
                    p.mAng = calAngSpdWithSlopeDecreasingCurve(navLinear) * angularSpeedScale;
                    p.mAngAcc = navLinear / 1.2f;
                    break;
                case START_MODE_SWIFT:
                    p.mJ = 20f;
                    p.mLineAcc = navLinear / 1.0f;
                    p.mAng = calAngSpdWithSlopeDecreasingCurve(navLinear) * angularSpeedScale;
                    p.mAngAcc = navLinear / 0.7f;
                    break;
                case START_MODE_OLD_GENTLE:
                    p.mJ = 0.06f;
                    p.mLineAcc = navLinear / 4f;
                    p.mAng = navLinear / 1.3f * angularSpeedScale;
                    p.mAngAcc = navLinear / 1.6f;
                    break;
                case START_MODE_OLD_SMOOTH:
                    p.mJ = 20f;
                    p.mLineAcc = navLinear / 2.5f;
                    p.mAng = navLinear / 0.9 * angularSpeedScale;
                    p.mAngAcc = navLinear / 0.8f;
                    break;
                case START_MODE_OLD_REGULAR:
                    p.mJ = 1.0f;
                    p.mLineAcc = navLinear / 1.2f;
                    p.mAng = navLinear / 0.8f * angularSpeedScale;
                    p.mAngAcc = navLinear / 0.7f;
                    break;
                case START_MODE_OLD_AGILE:
                    p.mJ = 1.2f;
                    p.mLineAcc = navLinear / 1.2f;
                    p.mAng = navLinear / 1.0f * angularSpeedScale;
                    p.mAngAcc = navLinear / 1.2f;
                    break;
                case START_MODE_OLD_SWIFT:
                    p.mJ = 20f;
                    p.mLineAcc = navLinear / 1.0f;
                    p.mAng = navLinear / 0.7f * angularSpeedScale;
                    p.mAngAcc = navLinear / 0.7f;
                    break;
                default:
                    p.mJ = 1.0f;
                    p.mLineAcc = navLinear / 1.2f;
                    p.mAng = navLinear / 0.8f * angularSpeedScale;
                    p.mAngAcc = navLinear / 0.7f;
                    break;
            }
        }

        Log.i(TAG, "startModeLevel: " + startModeLevel + ", navLinear: " + navLinear + ", " + p.toString());
        return p;
    }

    private StartModeResetParam toNavigationParamDampener(double navLinear, int startModeLevel) {
        StartModeResetParam p = new StartModeResetParam();
        final int START_MODE_GENTLE = 1;
        final int START_MODE_SMOOTH = 2;
        final int START_MODE_REGULAR = 3;
        final int START_MODE_AGILE = 4;
        final int START_MODE_SWIFT = 5;
        final int START_MODE_OLD_GENTLE = 6;
        final int START_MODE_OLD_SMOOTH = 7;
        final int START_MODE_OLD_REGULAR = 8;
        final int START_MODE_OLD_AGILE = 9;
        final int START_MODE_OLD_SWIFT = 10;
        startModeLevel = startModeLevel > 0 ? startModeLevel : RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_NAVIGATION_START_MODE_LEVEL);
        float angularSpeedScale = 1.0f;
        if (RobotSettingApi.getInstance().hasRobotSetting(Definition.ROBOT_SETTING_CUSTOM_ANGULAR_SPEED)) {
            angularSpeedScale = RobotSettingApi.getInstance().getRobotFloat(Definition.ROBOT_SETTING_CUSTOM_ANGULAR_SPEED);
        }
        if (Math.abs(angularSpeedScale) < 0.01f) {
            angularSpeedScale = 1.0f;
        }
        if (ProductInfo.isCarryProduct()) {
            switch (startModeLevel) {
                case START_MODE_GENTLE:
                    p.mJ = 1.2f;
                    p.mLineAcc = navLinear / 4.0f;
                    p.mAng = calAngSpdWithSlopeIncreasingCurve(navLinear) * angularSpeedScale;
                    p.mAngAcc = p.mAng * 1.25f;
                    break;
                case START_MODE_SMOOTH:
                    p.mJ = 1.2f;
                    p.mLineAcc = navLinear / 2.0f;
                    p.mAng = calculateAngularVelocityFormulaCarryLv2(navLinear) * angularSpeedScale;
                    p.mAngAcc = p.mAng * 1.25f;
                    break;
                case START_MODE_REGULAR:
                    p.mJ = 1.5f;
                    p.mLineAcc = navLinear / 1.5f;
                    p.mAng = calculateAngularVelocityFormulaCarryLv3(navLinear) * angularSpeedScale;
                    p.mAngAcc = p.mAng * 1.5f;
                    break;
                default:
                    p.mJ = 0.8f;
                    p.mLineAcc = navLinear / 1.5f;
                    p.mAng = navLinear / 0.8f * angularSpeedScale;
                    p.mAngAcc = navLinear / 0.7f;
                    break;
            }
        } else {
            switch (startModeLevel) {
                case START_MODE_GENTLE:
                    p.mJ = 1.2f;
                    p.mLineAcc = navLinear / 4.0f;
                    p.mAng = calAngSpdWithSlopeIncreasingCurve(navLinear) * angularSpeedScale;
                    p.mAngAcc = p.mAng * 1.25f;
                    break;
                case START_MODE_SMOOTH:
                    p.mJ = 1.2f;
                    p.mLineAcc = navLinear / 2.0f;
                    p.mAng = calAngSpdWithSlopeIncreasingCurve(navLinear) * angularSpeedScale;
                    p.mAngAcc = p.mAng * 1.25f;
                    break;
                case START_MODE_REGULAR:
                    p.mJ = 1.5f;
                    p.mLineAcc = navLinear / 1.5f;
                    p.mAng = calAngSpdWithSlopeIncreasingCurve(navLinear) * angularSpeedScale;
                    p.mAngAcc = p.mAng * 1.5f;
                    break;
                case START_MODE_AGILE:
                    p.mJ = 2.0f;
                    p.mLineAcc = navLinear / 1.2f;
                    p.mAng = calAngSpdWithSlopeDecreasingCurve(navLinear) * angularSpeedScale;
                    p.mAngAcc = p.mAng * 1.75f;
                    break;
                case START_MODE_SWIFT:
                    p.mJ = 10.0f;
                    p.mLineAcc = navLinear / 1.0f;
                    p.mAng = calAngSpdWithSlopeDecreasingCurve(navLinear) * angularSpeedScale;
                    p.mAngAcc = p.mAng * 2.0f;
                    break;
                case START_MODE_OLD_GENTLE:
                    p.mJ = 0.06f;
                    p.mLineAcc = navLinear / 4f;
                    p.mAng = navLinear / 1.3f * angularSpeedScale;
                    p.mAngAcc = navLinear / 1.6f;
                    break;
                case START_MODE_OLD_SMOOTH:
                    p.mJ = 20f;
                    p.mLineAcc = navLinear / 2.5f;
                    p.mAng = navLinear / 0.9 * angularSpeedScale;
                    p.mAngAcc = navLinear / 0.8f;
                    break;
                case START_MODE_OLD_REGULAR:
                    p.mJ = 0.8f;
                    p.mLineAcc = navLinear / 1.5f;
                    p.mAng = navLinear / 0.8 * angularSpeedScale;
                    p.mAngAcc = navLinear / 0.7f;
                    break;
                case START_MODE_OLD_AGILE:
                    p.mJ = 1.2f;
                    p.mLineAcc = navLinear / 1.2f;
                    p.mAng = navLinear / 1.0f * angularSpeedScale;
                    p.mAngAcc = navLinear / 1.2f;
                    break;
                case START_MODE_OLD_SWIFT:
                    p.mJ = 20f;
                    p.mLineAcc = navLinear / 1.0f;
                    p.mAng = navLinear / 0.7f * angularSpeedScale;
                    p.mAngAcc = navLinear / 0.7f;
                    break;
                default:
                    p.mJ = 0.8f;
                    p.mLineAcc = navLinear / 1.5f;
                    p.mAng = navLinear / 0.8f * angularSpeedScale;
                    p.mAngAcc = navLinear / 0.7f;
                    break;
            }
        }

        Log.i(TAG, "startModeLevel: " + startModeLevel + ", navLinear: " + navLinear + ", " + p.toString());
        return p;
    }


    private SetGoalProtoWrapper.BrakeModeProto getBreakMode(int brakeModeLevel) {
        SetGoalProtoWrapper.BrakeModeProto breakMode = SetGoalProtoWrapper.BrakeModeProto.LEVEL_2;
        int level = brakeModeLevel > 0 ? brakeModeLevel : RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_NAVIGATION_BREAK_MODE_LEVEL);
        Log.i(TAG, "get navigation break mode: " + level);
        switch (level) {
            case 1:
                breakMode = SetGoalProtoWrapper.BrakeModeProto.LEVEL_2;
                break;
            case 2:
                breakMode = SetGoalProtoWrapper.BrakeModeProto.LEVEL_3;
                break;
            case 3:
                breakMode = SetGoalProtoWrapper.BrakeModeProto.LEVEL_4;
                break;
            case 4:
                breakMode = SetGoalProtoWrapper.BrakeModeProto.LEVEL_1;
                break;
            case 5:
                breakMode = SetGoalProtoWrapper.BrakeModeProto.LEVEL_5;
                break;
            default:
                break;
        }
        return breakMode;
    }

    private NavAcceleration correctNavAcceleration(NavAcceleration acceleration, double navLinear) {
        double linearAcc = mDeviceConfig.getDefaultLinearAcceleration();
        double angularAcc = mDeviceConfig.getDefaultAngularAcceleration(navLinear);
        double deviationValue = 0.000001;
        if (acceleration != null) {
            if (acceleration.getLinearAcc() > mDeviceConfig.getMinLinearAcceleration() - deviationValue
                    && acceleration.getLinearAcc() < mDeviceConfig.getMaxLinearAcceleration() + deviationValue) {
                linearAcc = acceleration.getLinearAcc();
            }

            if (acceleration.getAngularAcc() > mDeviceConfig.getMinAngularAcceleration() - deviationValue
                    && acceleration.getAngularAcc() < mDeviceConfig.getMaxAngularAcceleration() + deviationValue) {
                angularAcc = acceleration.getAngularAcc();
            }
        }

        return new NavAcceleration(linearAcc, angularAcc);
    }

    @Override
    public void cancelNavigation(ChassisResListener listener) {
        cancelNavigation(listener, false);
    }

    private Object curModeLock = new Object();


    private ChassisResListener mResetEstimate;

    private synchronized void addResetEstimateListener(ChassisResListener listener) {
        mResetEstimate = listener;
    }

    private synchronized void updateResetEstimate(boolean result, int resultCode, String message) {
        if (mResetEstimate != null) {
            mResetEstimate.onResponse(result, resultCode, message);
            mResetEstimate = null;
        }
    }

    @Override
    public void setPoseEstimate(Pose pose, ChassisResListener listener) {
        Log.d(TAG, "Set pose estimate : " + pose + ", isCreatingMap = " + isCreatingMap());
        //一般用在手滑定位场景，不判断定位状态，不拦截
        setChassisRelocation(RelocateDataProtoWrapper.RelocateModeProto.MANUAL_VALUE, pose, listener);
    }

    @Override
    public void setFixedEstimate(Pose pose, ChassisResListener listener) {
        Log.d(TAG, "Set fixed estimate : " + pose + ", isCreatingMap = " + isCreatingMap());
        setChassisRelocation(RelocateDataProtoWrapper.RelocateModeProto.FIXED_VALUE, pose, listener);
    }

    @Override
    public void setForceEstimate(Pose pose, ChassisResListener listener) {
        Log.d(TAG, "Set force estimate : " + pose + ", isCreatingMap = " + isCreatingMap());
        setChassisRelocation(RelocateDataProtoWrapper.RelocateModeProto.FORCE_VALUE, pose, listener);
    }

    @Override
    public void setVisionEstimate(ChassisResListener listener) {
        Log.d(TAG, "setVisionEstimate");
        if (isPoseEstimate) {
            if (listener != null) {
                listener.onResponse(true, SUCCESS, "already relocated");
            }
            return;
        }

        boolean hasVision = getHasVision();
        Log.d(TAG, "hasVision = " + hasVision);
        if (hasVision) {
            addResetEstimateListener(listener);
            Pose pose = mCurPose.get();
            Pose2dProtoWrapper.Pose2dProto pose2dProto;
            if (pose != null) {
                pose2dProto = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                        .setX(pose.getX())
                        .setY(pose.getY())
                        .setT(pose.getTheta())
                        .build();

            } else {
                pose2dProto = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                        .setX(Float.NaN)
                        .setY(Float.NaN)
                        .setT(Float.NaN)
                        .build();
            }

            RelocateDataProtoWrapper.RelocateDataProto relocateDataProto = RelocateDataProtoWrapper.RelocateDataProto.newBuilder()
                    .setMode(RelocateDataProtoWrapper.RelocateModeProto.VISION)
                    .setPose(pose2dProto)
                    .build();
            Log.d(TAG, API_IN + "setVisionEstimate" + " relocate");
            Result relocateResult = commandApi.relocate(relocateDataProto);
            Log.d(TAG, API_BACK + "setVisionEstimate" + " relocate" + ", relocateResult = " + relocateResult.toString());
            //如果底盘直接返回错误码，则不会再返回定位结果信息
            if (relocateResult.getCode() != Result.CODE_SUCCESS) {
                updateResetEstimate(false, relocateResult.getCode(), "");
            }
        } else {
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "not support vision locate");
            }
        }
    }

    @Override
    public void resetPoseEstimate(ChassisResListener listener) {
        Log.d(TAG, "resetPoseEstimate");
        if (isPoseEstimate) {
            if (listener != null) {
                listener.onResponse(true, SUCCESS, "Already pose estimate");
            }
            return;
        }
        addResetEstimateListener(listener);
        isRelocate = true;
    }

    @Override
    public void setChassisRelocation(int type, Pose targetPose, ChassisResListener listener) {
        Log.d(TAG, "setChassisRelocation: type=" + type + " pose=" + targetPose +
                " isCreatingMap=" + isCreatingMap());
        //手动定位，不做拦截
        if (isPoseEstimate && type != RelocateDataProtoWrapper.RelocateModeProto.MANUAL_VALUE) {
            if (listener != null) {
                listener.onResponse(true, SUCCESS, "Already relocated!");
            }
            return;
        }

        if (isCreatingMap()) {
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "curMode is createMap");
            }
            return;
        }

        Pose2dProtoWrapper.Pose2dProto pose2dProto;
        if (targetPose != null) {
            pose2dProto = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                    .setX(targetPose.getX())
                    .setY(targetPose.getY())
                    .setT(targetPose.getTheta())
                    .build();
        } else {
            //视觉重定位和Target重定位允许pose为空,其他情况不允许为空
            if (!(type == RelocateDataProtoWrapper.RelocateModeProto.VISION_VALUE
                    || type == RelocateDataProtoWrapper.RelocateModeProto.TARGET_VALUE)) {
                if (listener != null) {
                    listener.onResponse(false, FAIL_NO_REASON, "Relocate pose is null!");
                }
                return;
            }
            pose2dProto = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                    .setX(Float.NaN)
                    .setY(Float.NaN)
                    .setT(Float.NaN)
                    .build();
        }
        RelocateDataProtoWrapper.RelocateDataProto relocateDataProto = RelocateDataProtoWrapper.RelocateDataProto.newBuilder()
                .setMode(translocationRelocationMode(type))
                .setPose(pose2dProto)
                .build();
        addResetEstimateListener(listener);

        Log.d(TAG, "setChassisRelocation:" + API_IN + " relocate" +
                " mode=" + relocateDataProto.getMode());
        Result relocateResult = commandApi.relocate(relocateDataProto);
        Log.d(TAG, "setChassisRelocation:" + API_BACK + " relocate" +
                " relocateResult=" + relocateResult.toString());
        //如果底盘直接返回错误码，则不会再返回定位结果信息
        if (relocateResult.getCode() != Result.CODE_SUCCESS) {
            updateResetEstimate(false, relocateResult.getCode(), "");
        }
    }

    private RelocateDataProtoWrapper.RelocateModeProto translocationRelocationMode(int type) {
        switch (type) {
            case 0:
                return RelocateDataProtoWrapper.RelocateModeProto.FORCE;
            case 1:
                return RelocateDataProtoWrapper.RelocateModeProto.MANUAL;
            case 2:
                return RelocateDataProtoWrapper.RelocateModeProto.LASER;
            case 3:
                return RelocateDataProtoWrapper.RelocateModeProto.VISION;
            case 4:
                return RelocateDataProtoWrapper.RelocateModeProto.FIXED;
            case 5:
                return RelocateDataProtoWrapper.RelocateModeProto.TARGET;
            default:
                return RelocateDataProtoWrapper.RelocateModeProto.FIXED;
        }

    }

    private void onVelocityUpdate(Velocity velocity) {
//        LogUtils.printLog(LogUtils.TYPE_VEL, TAG, "onVelocityUpdate x=" + velocity.getX()
//                + ",z=" + velocity.getZ(), 1000);
        //        Log.d(TAG, "onVelocityUpdate x=" + velocity.getX() + ",z=" + velocity.getZ());
        mRealtimeVelocity.set(velocity);
        Velocity preVelocity = mVelocity.get();
        if (WheelControlX86.getInstance().isVelocityDiff(preVelocity, velocity)) {
            mVelocity.set(velocity);
            if (mChassisEventListener != null) {
                mChassisEventListener.onVelocityUpdate(velocity);
            }
        }

        if (isRelocate && WheelControlX86.getInstance().isStill(velocity)) {
            Log.d(TAG, "start relocate");
            isRelocate = false;

            Pose pose = mCurPose.get();
            if (pose != null) {
                Pose2dProtoWrapper.Pose2dProto pose2dProto = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                        .setX(pose.getX())
                        .setY(pose.getY())
                        .setT(pose.getTheta())
                        .build();
                RelocateDataProtoWrapper.RelocateDataProto relocateDataProto = RelocateDataProtoWrapper.RelocateDataProto.newBuilder()
                        .setMode(RelocateDataProtoWrapper.RelocateModeProto.MANUAL)
                        .setPose(pose2dProto)
                        .build();
                Log.d(TAG, API_IN + "laser" + " relocate");
                Result result1 = commandApi.relocate(relocateDataProto);
                Log.d(TAG, API_BACK + "laser" + " relocate" + ", result1 = " + result1.toString());
            }
        }
    }

    private MotionTimer mMotionTimer;

    private void startMotionTimer(long time, ChassisResListener listener) {
        if (mMotionTimer != null) {
            mMotionTimer.cancel();
        }

        mMotionTimer = new MotionTimer(listener);
        mMotionTimer.schedule(time);
    }

    private class MotionTimer extends Timer {
        ChassisResListener mListener;

        public MotionTimer(ChassisResListener listener) {
            super();
            this.mListener = listener;
        }

        public void schedule(long delay) {
            if (delay <= 0) {
                Log.e(TAG, "motionLinear-avoid, delay less than 0");
                if (mListener != null) {
                    mListener.onResponse(false, FAIL_NO_REASON, null);
                }
                updateMotionAvoidState(false);//MotionTimer
                return;
            }
            this.schedule(new TimerTask() {
                @Override
                public void run() {
                    Log.e(TAG, "motionLinear-avoid, Motion time out, Stop move");
                    motion(0, 0, 0, true);
                    if (mListener != null) {
                        mListener.onResponse(true, SUCCESS, null);
                        mListener = null;
                    }
                    updateMotionAvoidState(false);//MotionTimer
                }
            }, delay);
        }

        @Override
        public void cancel() {
            Log.e(TAG, "Motion canceled");
            super.cancel();
        }

        public ChassisResListener getListener() {
            return mListener;
        }
    }

    @Override
    public void stopMove() {
        Log.d(TAG, "stopMove");
        MotionCallChainReporter.stopMoveReport(TAG + "_stopMove");
        updateMotionAvoidState(false);//stopMove
        BasicMotionProcess.getInstance().stopMotionTask(false, FAIL_NO_REASON, "Cancel");
        BasicMotionProcessNew.getInstance().stopMotionTask(false, FAIL_NO_REASON, "Cancel");
        motion(0, 0, 0, true);
    }

    @Override
    public void motion(double angularSpeed, double linearSpeed, double acceleration) {
        motion(angularSpeed, linearSpeed, acceleration, true);
    }

    public boolean sendPrimitiveMovingCommand(double angularSpeed, double linearSpeed) {
        mVelocityProto = VelocityProtoWrapper.VelocityProto.newBuilder()
                .setLiner(linearSpeed)
                .setAngular(angularSpeed)
                .build();
        Log.d(TAG, "sendPrimitiveMovingCommand mVelocityProto=" + mVelocityProto.toString());
        if (mWheelControlHandler.hasMessages(EVENT_WC_CMD)) {
            mWheelControlHandler.removeMessages(EVENT_WC_CMD);
        }
        mWheelControlHandler.sendEmptyMessage(EVENT_WC_CMD);
        return true;
    }

    @Override
    public void motion(double angularSpeed, double linearSpeed, double acceleration, boolean hasAcceleration) {
        if (isNavigationing()) {
            Log.e(TAG, "motion: Is navigationing");
            return;
        }
        updateMotionAvoidState(false);
        final double cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
        final double cAngularSpeed = WheelControlX86.getInstance().correctAngularSpeed(angularSpeed);
        WheelControlX86.getInstance().startDecTimer(cAngularSpeed, cLinearSpeed, acceleration, hasAcceleration);
    }

    @Override
    public void motionWithObstacles(double angularSpeed, double linearSpeed, double acceleration) {
        if (isNavigationing()) {
            Log.e(TAG, "motionWithObstacles, Is navigationing");
            return;
        }

        double cLinearSpeed = 0;
        final double cAngularSpeed = WheelControlX86.getInstance().correctAngularSpeed(angularSpeed);
        if (mDynamicAvoidPolicy.getState() && linearSpeed > 0) {
            Log.d(TAG, "Avoid to set linear speed 0");
            onObstacleReport();
            if (mNarrowPassagePolicy.getState()) { // 采用区域中障碍物数判断，解决机器人无法通过窄通道问题
                cLinearSpeed = 0;
                updateMotionAvoidState(false);
            }
        } else {
            cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
            updateMotionAvoidState(true);
        }
        WheelControlX86.getInstance().startDecTimer(cAngularSpeed, cLinearSpeed, acceleration, true);
    }

    @Override
    public void motionWithObstacles(double angularSpeed, double linearSpeed, double acceleration, double minDistance) {
        if (isNavigationing()) {
            Log.e(TAG, "motionWithObstacles, Is navigationing");
            return;
        }
        Log.d(TAG, "motionWithObstacles, speeds : " + linearSpeed + " " + angularSpeed);
        final double cLinearSpeed;
        final double cAngularSpeed = WheelControlX86.getInstance().correctAngularSpeed(angularSpeed);
        if (mDynamicAvoidPolicy.getState(minDistance) && linearSpeed > 0) {
            onObstacleReport();
            cLinearSpeed = 0;
            updateMotionAvoidState(false);
        } else {
            cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
            updateMotionAvoidState(true);
        }
        Log.d(TAG, "motionWithObstacles, speeds final : " + cLinearSpeed + " " + cAngularSpeed);
        WheelControlX86.getInstance().startDecTimer(cAngularSpeed, cLinearSpeed, acceleration, true);
    }

    @Override
    public void motionWithStaticObstacles(double angularSpeed, double linearSpeed, double minDistance) {
        MotionCallChainReporter.motionWithObstaclesReport(TAG + "_motionWithStaticObstacles",
                angularSpeed, linearSpeed, minDistance);
        Log.d(TAG, "motionWithStaticObstacles, speeds : " + linearSpeed + " " + angularSpeed);
        final double cLinearSpeed;
        final double cAngularSpeed = WheelControlX86.getInstance().correctAngularSpeed(angularSpeed);
        if (mStaticAvoidPolicy.getState() && linearSpeed > 0) {
            onObstacleReport();
            cLinearSpeed = 0;
        } else {
            cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
        }
        Log.d(TAG, "motionWithObstacles, speeds final : " + cLinearSpeed + " " + cAngularSpeed);
        WheelControlX86.getInstance().startDecTimer(cAngularSpeed, cLinearSpeed, 0, true);
    }

    @Override
    public void motionWithOnceObstacle(double angularSpeed, double linearSpeed, boolean hasAcceleration) {
        if (isNavigationing()) {
            Log.e(TAG, "motionWithObstacles, Is navigationing");
            return;
        }

        updateMotionAvoidState(false);
        final double cLinearSpeed;
        final double cAngularSpeed = WheelControlX86.getInstance().correctAngularSpeed(angularSpeed);

        boolean avoidState = mDynamicAvoidPolicy.getState(1.0);
        Log.i(TAG, "avoid state: " + avoidState + " , last state: " + hasObstacle);

        if (avoidState && linearSpeed > 0) {
            onObstacleReport();
            if (!hasObstacle) {
                //appear obstacle
                Log.i(TAG, "appear obstacle");
                startAvoidTimer();
                cLinearSpeed = 0;
            } else if (avoidTag) {
                Log.i(TAG, "appear obstacle, continuing。。。。");
                cLinearSpeed = 0;
            } else {
                cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
            }
        } else {
            cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
        }
        hasObstacle = avoidState;
        WheelControlX86.getInstance().startDecTimer(cAngularSpeed, cLinearSpeed, 0, hasAcceleration);
    }

    @Override
    public void motionControlWithObstacle(double angularSpeed, double linearSpeed, double minDistance) {
        MotionCallChainReporter.motionWithObstaclesReport(TAG + "_motionWithObstacles",
                angularSpeed, linearSpeed, minDistance);
        if (isNavigationing()) {
            Log.e(TAG, "motionWithObstacles, Is navigationing");
            return;
        }
        Log.d(TAG, "motionWithObstacles, speeds : " + linearSpeed + " " + angularSpeed);
        final double cLinearSpeed;
        final double cAngularSpeed = WheelControlX86.getInstance().correctAngularSpeed(angularSpeed);
        if (mDynamicAvoidPolicy.getState(minDistance) && linearSpeed > 0) {
            onObstacleReport();
            cLinearSpeed = 0;
            updateMotionAvoidState(false);
        } else {
            cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
            updateMotionAvoidState(true);
        }
        Log.d(TAG, "motionWithObstacles, speeds final : " + cLinearSpeed + " " + cAngularSpeed);
        //        startDecTimer(cAngularSpeed, cLinearSpeed, hasAcceleration);
        Log.e(TAG, "motion: x86 ------- ");
        BigDecimal linearSpeeds = new BigDecimal(Float.toString((float) cLinearSpeed));
        BigDecimal angularSpeeds = new BigDecimal(Float.toString((float) cAngularSpeed));
        MotionControl.getInstance().motion(new SpeedBean(linearSpeeds, angularSpeeds));
    }

    @Override
    public void motionSoft(double angularSpeed, double linearSpeed, boolean hasAcceleration) {
        if (isNavigationing()) {
            Log.e(TAG, "motionSoft, Is navigationing");
            return;
        }
        final double cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
        final double cAngularSpeed = WheelControlX86.getInstance().correctAngularSpeed(angularSpeed);

        updateMotionAvoidState(false);
        WheelControlX86.getInstance().startDecTimer(cAngularSpeed, cLinearSpeed, 0, hasAcceleration);
    }

    @Override
    public void turnLeft(double angle, double speed, double acceleration, boolean noNeedAcceleration, ChassisResListener listener) {
        motionRotate(angle, speed, acceleration, noNeedAcceleration, listener);
    }

    @Override
    public void turnRight(double angle, double speed, double acceleration, boolean noNeedAcceleration, ChassisResListener listener) {
        motionRotate(angle, -speed, acceleration, noNeedAcceleration, listener);
    }

    @Override
    public void forward(double distance, double speed, double acceleration, ChassisResListener listener) {
        motionLinear(distance, speed, acceleration, false, listener);
    }

    @Override
    public void forward(double distance, double speed, double acceleration, boolean avoid, ChassisResListener listener) {
        motionLinear(distance, speed, acceleration, avoid, listener);
    }

    @Override
    public void backward(double distance, double speed, double acceleration, ChassisResListener listener) {
        motionLinear(distance, -speed, acceleration, false, listener);
    }

    @Override
    public void rotateInPlace(int direction, double angle, double speed, ChassisResListener listener) {
        Log.e(TAG, "rotateInPlace : direction=" + direction + " angle=" + angle + " speed=" + speed);

        //direction 转动方向，-1表示左转，1表示右转
        double directionSpeed = direction == -1 ? speed : -speed;

        //开始控制轮子转动，设置轮子速度
        motion(directionSpeed, 0, 0, true);

        if (angle != Double.MAX_VALUE) {
            //启动角度控制任务
            BasicMotionProcessNew.getInstance().startAngularTask(angle, directionSpeed, listener);
        } else {
            if (listener != null) {
                listener.onResponse(true, SUCCESS, null);
            }
        }
    }

    private synchronized void startAvoidTimer() {
        avoidTag = true;
        DelayTask.cancel(this);
        DelayTask.submit(this, new Runnable() {
            @Override
            public void run() {
                Log.i(TAG, "avoid timer come, remote can continue control");
                avoidTag = false;
            }
        }, 3000);
    }

    private void motionRotate(double angle, double speed, double acceleration, boolean noNeedAcceleration, ChassisResListener listener) {
        Log.e(TAG, "motionRotate : angle=" + angle + "  speed=" + speed +
                " acceleration=" + acceleration + " noNeedAcceleration=" + noNeedAcceleration);
        updateMotionAvoidState(false);//motionRotate
        motion(speed, 0, acceleration, !noNeedAcceleration);

        if (angle != Double.MAX_VALUE) {
            BasicMotionProcess.getInstance().startAngularTask(angle, speed, listener);
        } else {
            if (listener != null) {
                listener.onResponse(true, SUCCESS, null);
            }
        }
    }

    public void motionLinear(double distance, double speed, double acceleration, boolean avoid, ChassisResListener listener) {
        Log.d(TAG, "motionLinear ：distance=" + distance
                + " speed=" + speed + " avoid=" + avoid);
        if (avoid && mDynamicAvoidPolicy.getState()) {
            if (listener != null) {
                listener.onResponse(false, MOTION_AVOID_STOP, "Avoid stop");
            }
            motionLinearAvoidStop();
            return;
        }

        motion(0, speed, acceleration);
        updateMotionAvoidState(avoid); //motionLinear

        if (distance != Double.MAX_VALUE) {
            BasicMotionProcess.getInstance().startLinearTask(distance, speed, listener);
        } else {
            Log.d(TAG, "motionLinear-avoid : MAX_VALUE distance");
            if (listener != null) {
                listener.onResponse(true, SUCCESS, null);
            }
        }
    }

    public boolean isRadarOpenState() {
        return radarOpenState;
    }

    //新架构版本：开关状态
    public void setRadarOpenState(boolean radarOpenState) {
        this.radarOpenState = radarOpenState;
        if (mChassisEventListener != null) {
            mChassisEventListener.OnRadarUpdate(radarOpenState);
        }
    }

    //新增具体状态：静止休眠需求
    private void setRadarStatus(int status) {
        mRadarStatus = status;
        if (mChassisEventListener != null) {
            mChassisEventListener.OnRadarUpdate(radarOpenState, mRadarStatus);
        }
    }

    @Override
    public void setRadarState(boolean state, final ChassisResListener listener) {
        Log.d(TAG, "setRadarState: state=" + state + ", mRadarStatus=" + mRadarStatus);
        if (state == CLOSE && isCreatingMap()) {
            Log.e(TAG, "setRadarState: Error: Can not close radar when creating map");
            listener.onResponse(false, FAIL_NO_REASON, Definition.FAILED_CREATING_MAP);
            return;
        }

        if (mRadarStatus == Definition.RADAR_STATUS_OPENING) {
            Log.e(TAG, "setRadarState: Error: Is opening");
            listener.onResponse(false, FAIL_NO_REASON, Definition.FAILED_RADAR_OPENING);
            return;
        }
        if (mRadarStatus == Definition.RADAR_STATUS_CLOSING) {
            Log.e(TAG, "setRadarState: Error: Is closing");
            listener.onResponse(false, FAIL_NO_REASON, Definition.FAILED_RADAR_CLOSING);
            return;
        }
        if (state == OPEN && mRadarStatus == Definition.RADAR_STATUS_OPENED) {
            Log.e(TAG, "setRadarState: Result: Already opened");
            listener.onResponse(true, SUCCESS, Definition.SUCCEED);
            return;
        }
        if (state == CLOSE && mRadarStatus == Definition.RADAR_STATUS_CLOSED) {
            Log.e(TAG, "setRadarState: Result: Already closed");
            listener.onResponse(true, SUCCESS, Definition.SUCCEED);
            return;
        }

        setRadarStateAsync(state, listener);
    }

    /**
     * 异步调用雷达开关接口，避免阻塞command线程
     *
     * @param state
     * @param listener
     */
    private void setRadarStateAsync(final boolean state, final ChassisResListener listener) {
        Log.d(TAG, "setRadarStateAsync: state=" + state + ", mRadarStatus=" + mRadarStatus);
        final int currentStatus = mRadarStatus;
        setRadarStatus(state
                ? Definition.RADAR_STATUS_OPENING
                : Definition.RADAR_STATUS_CLOSING);

        new Thread(new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "enableLaser: start: state=" + state);
                Result result = commandApi.enableLaser(state);
                Log.d(TAG, "enableLaser: result: " + result.toString());

                // V7.18导航版本是orion_3.0.6.3, 导航无法带雷达关闭时调用导航api直接停止的功能
                // 由端上做兼容, 雷达关闭失败时认为关闭成功, 可能影响是雷达真的没关闭成功, 会调用使能两次
                // 冠毅确认该使用场景不会有问题
                if (result.getCode() == Result.CODE_SUCCESS || !state) {
                    setRadarOpenState(state);
                    setRadarStatus(state
                            ? Definition.RADAR_STATUS_OPENED
                            : Definition.RADAR_STATUS_CLOSED);
                    listener.onResponse(true, SUCCESS, Definition.SUCCEED);
                } else {
                    Definition.RUNNING_ERROR_TYPE radarType = Definition.RUNNING_ERROR_TYPE.TYPE_RADAR;
                    new BiRunningErrorReport().addError(radarType.getErrorType(), state ? Definition.RADAR_OPEN_FAILED : Definition.RADAR_CLOSE_FAILED).report();
                    setRadarStatus(currentStatus);
                    listener.onResponse(false, result.getCode(), result.getPayload());
                }
            }
        }, "EnableLaserThread").start();
    }

    @Override
    public void getRadarState(ChassisResListener listener) {
        // 用本地缓存的motion mode 直接返回给上层。
        if (listener == null) {
            Log.e(TAG, "getRadarState: Error: listener null");
            return;
        }
        JSONObject response = new JSONObject();
        try {
            response.put(Definition.JSON_NAVI_OPEN_RADAR, radarOpenState);
            response.put(Definition.JSON_NAVI_RADAR_STATUS, mRadarStatus);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        listener.onResponse(true, SUCCESS, response.toString());
    }

    public boolean isWheelReleaseState() {
        return wheelReleaseState;
    }

    public void setWheelReleaseState(boolean wheelReleaseState) {
        this.wheelReleaseState = wheelReleaseState;
    }

    public void setWheelState(boolean state, ChassisResListener listener) {
        Log.d(TAG, API_IN + "setWheelState state = " + state);
        Result result = commandApi.enableWheel(state);
        Log.d(TAG, API_BACK + "setWheelState result = " + result.toString());
        if (result.getCode() == Result.CODE_SUCCESS) {
            setWheelReleaseState(state);
            if (listener != null) {
                listener.onResponse(true, result.getCode(), "");
            }
        } else {
            if (listener != null) {
                listener.onResponse(false, result.getCode(), "");
            }
        }
    }

    @Override
    public void switchChargeMode() {
        this.setWheelState(false, new ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "switchChargeMode status=" + status
                        + " resultCode=" + resultCode + " result=" + result);
            }
        });
    }

    @Override
    public void switchManualMode() {
        this.setWheelState(true, new ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "switchManualMode status=" + status
                        + " resultCode=" + resultCode + " result=" + result);
            }
        });
    }

    @Override
    public void sendPrimitiveMovingSpeed(double angularSpeed, double linearSpeed) {
        this.sendPrimitiveMovingCommand(angularSpeed, linearSpeed);
    }

    @Override
    public void getFullCheckStatus(ChassisResListener listener) {
        Log.d(TAG, API_IN + " getFullCheckStatus");
        Result result = commandApi.selfCheck();
        Log.d(TAG, API_BACK + " self check, result = " + result.toString());

        if (result.getCode() != Result.CODE_SUCCESS) {
            listener.onResponse(false, result.getCode(), result.getPayload());
        } else {
            listener.onResponse(true, result.getCode(),
                    generateSensorStatus((SelfCheckResultProtoWrapper.SelfCheckResultProto) result.getPayload()));
        }
    }

    private SensorStatus generateSensorStatus(SelfCheckResultProtoWrapper.SelfCheckResultProto resultProto) {
        SensorStatus status = new SensorStatus();
        status.setFishEyeReady(resultProto.getIsMonoImageReady());
        status.setIrReady(true);
        status.setLaserReady(resultProto.getIsLaserReady());
        status.setOdomReady(resultProto.getIsOdomReady());
        status.setRgbdReady(resultProto.getIsDepthImageReady());
        status.setLaserAvailable(resultProto.getIsLaserDataValid());
        status.setIscalibrationready(resultProto.getIsCalibrationReady());
        status.setIsharddiskspaceok(resultProto.getIsHardDiskSpaceOk());
        status.setCanControlReady(resultProto.getIsCanControlReady());
        status.setMonoImage(parseImagePath(resultProto.getMonoImage()));
        status.setDepthImage(parseImagePath(resultProto.getDepthImage()));
        status.setIrImage(parseImagePath(resultProto.getIrImage()));
        status.setTopIrImage(parseImagePath(resultProto.getTopIrImage()));
        status.setGyroReady(resultProto.getIsGyroReady());
        status.setIrImageReady(resultProto.getIsIrImageReady());
        status.setTopIrImageReady(resultProto.getIsTopIrImageReady());
        return status;
    }

    private String parseImagePath(ChassisFileProtoWrapper.ChassisFileProto proto) {
        Log.d(TAG, "parseImagePath: " + proto.toString());
        return proto.getPath();
    }

    @Override
    public void getSensorStatus(ChassisResListener listener) {
        //todo

        Log.d(TAG, API_IN + "self check");
        Result result = commandApi.selfCheck();
        Log.d(TAG, API_BACK + "self check, result = " + result.toString());

        if (result.getCode() != Result.CODE_SUCCESS) {
            listener.onResponse(false, result.getCode(), result.getPayload());
        } else {
            listener.onResponse(true, result.getCode(),
                    generateSensorStatus((SelfCheckResultProtoWrapper.SelfCheckResultProto) result.getPayload()));
        }

    }

    @Override
    public boolean getLogFile(long startTime, long endTime, String cmdType, String fileType, String path, ChassisResListener listener) {
        Log.d(TAG, "getLogFile startTime:" + startTime + " endTime:" + endTime
                + " cmdType:" + cmdType + " fileType:" + fileType + " path:" + path);
        if (startTime <= 0 || endTime <= 0 || TextUtils.isEmpty(path)) {
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "");
            }
            return false;
        }
        if (startTime >= endTime) {
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "");
            }
            return false;
        }
        long sTime = startTime / 1000;
        long eTime = endTime / 1000;
        File saveFile = new File(path);
        startGetFileDataTask(cmdType, saveFile, sTime, eTime, listener);
        return true;
    }

    private CommonProtoWrapper.LogTypeProto generateDiffLogType(String cmdType) {
        if (!TextUtils.isEmpty(cmdType)) {
            switch (cmdType) {
                case Def.CHASSIS_LOG_TYPE_NORMAL:
                    return CommonProtoWrapper.LogTypeProto.LOG_TYPE_NORMAL;
                case Def.CHASSIS_LOG_TYPE_SPECIAL:
                    return CommonProtoWrapper.LogTypeProto.LOG_TYPE_SPECIAL;
                case Def.CHASSIS_LOG_TYPE_SNAPSHOT:
                    return CommonProtoWrapper.LogTypeProto.LOG_TYPE_SNAPSHOT;
                case Def.CHASSIS_LOG_TYPE_FOLLOWING:
                    return CommonProtoWrapper.LogTypeProto.LOG_TYPE_FOLLOWING;
                default:
                    return CommonProtoWrapper.LogTypeProto.LOG_TYPE_NONE;
            }
        } else {
            return CommonProtoWrapper.LogTypeProto.LOG_TYPE_NONE;
        }
    }

    private void startGetFileDataTask(String cmdType, final File saveFile, final long sTime,
                                      final long eTime, final ChassisResListener listener) {
        Log.d(TAG, "startGetFileDataTask type:" + cmdType + " path:" + saveFile + " sTime:" + sTime + " eTime:" + eTime);
        if (mCurrentTask != null && !mCurrentTask.isDone() && listener != null) {
            listener.onResponse(false, FAIL_NO_REASON, "");
        }
        final CommonProtoWrapper.LogTypeProto logType = generateDiffLogType(cmdType);
        mCurrentTask = mFileExecutor.submit(new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "getLog start:" + saveFile + " sTime:" + sTime + " eTime:" + eTime);
                Result result = commandApi.getLog(saveFile, logType, sTime, eTime);
                Log.d(TAG, "getLog result:" + result.getCode() + " data:" + result.getPayload());
                if (listener == null) {
                    return;
                }
                if (result.getCode() != Result.CODE_SUCCESS) {
                    listener.onResponse(false, result.getCode(), result.getPayload());
                } else {
                    listener.onResponse(true, result.getCode(), "succeed");
                }
            }
        });

    }

    @Override
    public void removeTkMap(String mapName, ChassisResListener listener) {
        Log.i(TAG, "Remove map : " + mapName);
        listener.onResponse(true, SUCCESS, "no need del");
    }

    @Override
    public boolean packLogFile(long startTime, long endTime, ChassisResListener listener) {
        //todo
        return false;
    }

    @Override
    public boolean startPlanRoute() {
        //todo
        return false;
    }

    @Override
    public void savePlanRoute(String routeName, List<Pose> poseList) {
        //todo
    }

    @Override
    public void getSystemInformation(ChassisResListener listener) {
        Log.d(TAG, API_IN + " getSystemInfo");
        Result result = commandApi.getSystemInformation();
        Log.d(TAG, API_BACK + " getSystemInfo, result = " + result.toString());
        if (result.getCode() != Result.CODE_SUCCESS) {
            listener.onResponse(false, result.getCode(), result.getPayload());
        } else {
            if (result.getPayload() instanceof GetSystemInformationProtoWrapper.SystemInformationProto) {
                listener.onResponse(true, result.getCode(),
                        new SystemData((GetSystemInformationProtoWrapper.SystemInformationProto) result.getPayload()));
            } else {
                listener.onResponse(false, result.getCode(), "data error");
            }
        }
    }

    @Override
    public boolean hasObstacle(double startAngle, double endAngle, double distance) {
        synchronized (laserUpdateLock) {
//            double arcStartAngle = startAngle * Math.PI / 180;
//            double arcEndAngle = endAngle * Math.PI / 180;
//            Log.d(TAG, "arcStartAngle = " + arcStartAngle + ", arcEndAngle = " + arcEndAngle);
//            if (mLaserDatas == null) {
//                return true;
//            }
//            for (Laser info : mLaserDatas) {
//                Log.d(TAG, "laser data = " + info.toString());
//                if (isInAngleRange(arcStartAngle, arcEndAngle, info.getAngle()) && info.getDistance() < distance) {
//                    return true;
//                }
//            }

            try {
                float robotRadius = Settings.Global.getFloat(
                        ApplicationWrapper.getContext().getContentResolver(),
                        Definition.ROBOT_CHASSIS_RADIUS, 0);
                Log.d(TAG, "hasObstacle: robotRadius=" + robotRadius);
                boolean hasObstacle = StaticStoppingPolicy.hasObstacle(mLaserDatas,
                        robotRadius, startAngle, endAngle, distance);
                Log.d(TAG, "hasObstacle: hasObstacle=" + hasObstacle);
                return hasObstacle;
            } catch (Exception e) {
                Log.d(TAG, "hasObstacle:Exception: " + e.getMessage());
                e.printStackTrace();
            }
            return false;
        }
    }

    @Override
    public boolean hasObstacleInArea(double startAngle, double endAngle, double minDistance, double maxDistance) {
        synchronized (laserUpdateLock) {
            if (mLaserDatas == null || mLaserDatas.size() <= 0) {
                Log.d(TAG, "hasObstacleInArea: lasers null!");
                return false;
            }

            double[] arcRange = FaceAngleRangeConverter.convertAngleRange(startAngle, endAngle);

            double arcStartAngle = arcRange[0];
            double arcEndAngle = arcRange[1];
            Log.d(TAG, "arcStartAngle = " + arcStartAngle + ", arcEndAngle = " + arcEndAngle
                    + " minDistance = " + minDistance + " maxDistance = " + maxDistance);

            for (Laser info : mLaserDatas) {
                if (isInAngleRange(arcStartAngle, arcEndAngle, info.getAngle()) &&
                        info.getDistance() < maxDistance && info.getDistance() > minDistance) {
                    return true;
                }
            }

            return false;
        }
    }

    @Override
    public boolean getHasVision() {
        return NavigationDataManager.getInstance().getHasVision();
    }


    @Override
    public void addMappingPose(final Pose pose, ChassisResListener listener) {


        if (!isCreatingMap()) {
            listener.onResponse(false, -1, "not in creatimg map mode");
            return;
        }

        Pose2dProtoWrapper.Pose2dProto pose2dProto = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                .setX(pose.getX())
                .setY(pose.getY())
                .setT(pose.getTheta())
                .build();
        Result<CommonProtoWrapper.TargetResultProto> result = commandApi.addTarget(pose2dProto);
        Log.d(TAG, "addMappingPose: Payload=" + result.getPayload());
        if (result.getCode() != Result.CODE_SUCCESS) {
            listener.onResponse(false, result.getCode(), result.getPayload());
        } else {
            if (result.getPayload() instanceof CommonProtoWrapper.TargetResultProto) {
                MappingPose mappingPose = new MappingPose();
                mappingPose.setId(result.getPayload().getId());
                mappingPose.setValid(isPoseValid(result.getPayload(), pose));
                mappingPose.setPose(pose);
                Log.d(TAG, "addMappingPose: mappingPose=" + mappingPose);
                listener.onResponse(true, result.getCode(), mappingPose);
            } else {
                Log.d(TAG, "addMappingPose: data error!");
                listener.onResponse(false, result.getCode(), "data error");
            }
        }
    }

    private boolean isPoseValid(CommonProtoWrapper.TargetResultProto payload, Pose pose) {
        if (payload.getObsDistance() != 0) { // 非设置值，不用取精度
            double obsDistance = payload.getObsDistance();
            int safeDistance = pose.getSafeDistance();//单位是cm
            BigDecimal decimalDis = new BigDecimal(Double.toString(safeDistance)).multiply(new BigDecimal("0.01"));
            Log.d(TAG, "isPoseValid  obsDistance : " + obsDistance + ", safeDistance : " + decimalDis.doubleValue());
            boolean isSafe = new BigDecimal(String.valueOf(obsDistance)).compareTo(decimalDis) >= 0;
            boolean payloadValid = payload.getValid();
            Log.d(TAG, "isPoseValid getValid " + payloadValid + " , isSafe : " + isSafe);

            return payloadValid && isSafe;
        } else {
            return payload.getValid();
        }
    }

    @Override
    public void deleteMappingPose(int poseId, ChassisResListener listener) {

        if (!isCreatingMap()) {
            listener.onResponse(false, -1, "not in creatimg map mode");
            return;
        }

        Result result = commandApi.removeTarget(poseId);
        if (result.getCode() != Result.CODE_SUCCESS) {
            listener.onResponse(false, result.getCode(), result.getPayload());
        } else {
            listener.onResponse(true, result.getCode(), result.getPayload());
        }
    }

    @Override
    public Velocity getFollowVelocity(double distance, double angle, double headAngleSpeed, Velocity velocity, double latency) {
        return MotionPolicyMini.follow(distance, angle, latency, velocity);
    }

    @Override
    public Velocity getBodyFollowVelocity(double distance, double angle, double latency, double headAngleSpeed,
                                          Velocity velocity, double maxLinSpeed, double maxAngSpeed, double safeDistance) {
        return MotionPolicyMini.bodyFollow(distance, angle, latency, velocity,
                maxLinSpeed, maxAngSpeed, safeDistance);
    }

    @Override
    public Velocity getBodyFollowVelocity(double distance, double angle, double headAngleSpeed, Velocity velocity, double latency) {
        return MotionPolicyMini.bodyFollow(distance, angle, latency, velocity);
    }

    @Override
    public Velocity getFollowVelocity(double angle, double latency) {
        return MotionPIDPolicy.follow(angle, latency);
    }

    @Override
    public void recoveryNavigation(ChassisResListener listener) {
        //TODO
        if (listener != null) {
            listener.onResponse(true, 0, null);
        }
    }

    private boolean isInAngleRange(double startAngle, double endAngle, double aimAngle) {
        return (aimAngle >= startAngle && aimAngle <= endAngle);
    }

    private UpdateListener mUpdateListener = new UpdateListener() {

        @Override
        public void onUpdateLaser(UpdateLaserProtoWrapper.LaserDataProto laserDataProto) {
            List<UpdateLaserProtoWrapper.LaserProto> laserList = laserDataProto.getLasersList();
            ArrayList<Laser> lasers = new ArrayList<>();

            for (UpdateLaserProtoWrapper.LaserProto laser : laserList) {
                if (laser.getDistance() == 0) {
                    lasers.add(new Laser(laser.getAngle(), 100));
                } else {
                    lasers.add(new Laser(laser.getAngle(), laser.getDistance()));
                }
            }

            long laserSize = lasers.toString().getBytes().length;
            if (laserSize > 20 * 1024) {
                long orionLaserSize = laserList.toString().getBytes().length;
                Log.e(TAG, "onUpdateLaser_Statistical: Laser exception size=" + laserSize / 1024 + "(kb) " + lasers.size());
                Log.e(TAG, "onUpdateLaser_Statistical: Orion Laser exception size=" + orionLaserSize / 1024 + "(kb) " + laserList.size());
                AbnormalReport report = new AbnormalReport(this.getClass().getName(), "onUpdateLaser");
                report.setAbnormalInfo("lasers_size_abnormal", ">20k", "larsers size:" + laserSize / 1024 + "(kb)");
                report.addData("app_name", "NavigationService");
                report.report();
                return;
            }

            mLasers.set(lasers);
            onLaserDataIn(lasers);
            synchronized (laserUpdateLock) {
                mLaserDatas = lasers;
            }
        }

        @Override
        public void onUpdateVelocity(VelocityProtoWrapper.VelocityProto velocityProto) {
            onVelocityUpdate(new Velocity(velocityProto.getLiner(), velocityProto.getAngular()));
        }

        //定位状态下地图坐标系的位姿
        @Override
        public void onUpdateNaviPose(ChassisPacketProtoWrapper.NaviPoseProto poseDataProto) {
            if (mUpdatePoseTime == 0) {
                mUpdatePoseTime = SystemClock.uptimeMillis();
            }
            long currentTime = SystemClock.uptimeMillis();
            if (currentTime - mUpdatePoseTime >= 100) {
                onPoseUpdate(poseDataProto.getPose().getX(), poseDataProto.getPose().getY(), poseDataProto.getPose().getT(),
                        poseDataProto.getTypeValue(), poseDataProto.getObsDistance());
                mUpdatePoseTime = currentTime;
            }
        }

        @Override
        public void onUpdateLineData(UpdateLineProtoWrapper.LineDataProto lineDataProto) {
            onUpdateRgbdLineData(lineDataProto.getDataList());
        }

        @Override
        public void onUpdateDepthImage(UpdateRawImageProtoWrapper.ImageDataProto imageDataProto) {
            UpdateRawImageProtoWrapper.RawImageProto frameData = imageDataProto.getFrameData();
            if ((++ FRAME_RATE ) % 3 != 0){
                return;
            }
            FRAME_RATE = 0;// 底盘上报帧率是1秒5帧，大数据传输业务，需要适当降低帧率

            double timestamp = frameData.getTimestamp();
            int typeSize = frameData.getTypeSize();
            int height = frameData.getRows();
            int width = frameData.getCols();
            int stride = frameData.getStride();
            ByteString imageBase = frameData.getImageBase();
            byte[] bytes = imageBase.toByteArray();
            Log.d(TAG, "rawImageProto typeSize : " + typeSize);

            if (width * height * typeSize != bytes.length) {
                Log.d(TAG, "getDepthImage data invalid: ");
                return;
            }
            DepthImageBean depthImageBean = new DepthImageBean((long) timestamp, typeSize, width, height, stride, bytes.length, bytes);
            ShareMemoryApi.getInstance().writeToMemoryFile(depthImageBean.toBytes());
//            ShareMemoryApi.getInstance().writeToMemoryFile(testByte);
        }

        @Override
        public void onUpdateTopIrImage(UpdateRawImageProtoWrapper.ImageDataProto imageDataProto) {
            UpdateRawImageProtoWrapper.RawImageProto frameData = imageDataProto.getFrameData();
            if ((++ FRAME_RATE ) % 5 != 0){
                return;
            }
            FRAME_RATE = 0;// 底盘上报帧率是1秒5帧，大数据传输业务，需要适当降低帧率
            double timestamp = frameData.getTimestamp();
            int typeSize = frameData.getTypeSize();
            int height = frameData.getRows();
            int width = frameData.getCols();
            int stride = frameData.getStride();
            ByteString imageBase = frameData.getImageBase();
            byte[] bytes = imageBase.toByteArray();
            Log.d(TAG, "rawImageProto : " + typeSize);

            if (width * height * typeSize != bytes.length) {
                Log.d(TAG, "getTopIRImage data invalid: ");
                return;
            }

            TopIRImageBean topIRImageBean = new TopIRImageBean((long) timestamp, typeSize, width, height, stride, bytes.length, bytes);
            ShareMemoryApi.getInstance().writeToMemoryFile(topIRImageBean.toBytes());
        }

        @Override
        public void onUpdateGlobalMap(CostmapProtoWrapper.CostmapProto costmapProto) {
            Log.d(TAG, "onUpdateGlobalMap in");
            onMapDataUpdate(new MapData(MapData.GLOBAL,
                    costmapProto.getWidth(),
                    costmapProto.getHeight(),
                    costmapProto.getResolution(),
                    costmapProto.getOriginX(),
                    costmapProto.getOriginY(),
                    costmapProto.getData().toByteArray()));
        }

        @Override
        public void onUpdateEvent(UpdateEventProtoWrapper.RoverEventProto roverEvent) {
            Log.d(TAG, "onUpdateEvent in");
            onNavigationEvent(new Event(roverEvent.getCodeValue(), roverEvent.getMessage(), roverEvent.getAdditional(), roverEvent.getData().toByteArray()));
        }

        @Override
        public void onReportStatistic(ReportStatisticProtoWrapper.StatisticProto statistic) {
            Log.d(TAG, "onReportStatistic in");
            onStatisticUpdate(new Statistic(statistic.getType(),
                    statistic.getInt1(),
                    statistic.getInt2(),
                    statistic.getInt3(),
                    statistic.getInt4(),
                    statistic.getDouble1(),
                    statistic.getDouble2(),
                    statistic.getDouble3(),
                    statistic.getDouble4(),
                    statistic.getStringValue()));
        }

        @Override
        public void onUpdateTargets(CommonProtoWrapper.TargetPointsProto targetPointsProto) {

            List<CommonProtoWrapper.TargetPointProto> list = targetPointsProto.getPointsList();

            if (list == null || list.size() == 0) {
                return;
            }

            List<MappingPose> mappingPoseList = new ArrayList<>();
            for (CommonProtoWrapper.TargetPointProto point : list) {
                MappingPose mappingPose = new MappingPose();
                mappingPose.setValid(point.getValid());
                mappingPose.setId(point.getId());
                Pose pose = new Pose((float) point.getPose().getX(), (float) point.getPose().getY(),
                        (float) point.getPose().getT());
                mappingPose.setPose(pose);
                mappingPoseList.add(mappingPose);
            }

            if (mChassisEventListener != null) {
                mChassisEventListener.onMappingPoseUpdate(mappingPoseList);
            }
        }

        @Override
        public void onUpdateOdom(CommonProtoWrapper.OdomDataProto odomDataProto) {
            BasicMotionProcess.getInstance().handleOdomData(odomDataProto.getMove(),
                    odomDataProto.getLeftAcc(), odomDataProto.getRightAcc());
            MapDriftWarningManager.getInstance().onUpdateOdom(odomDataProto, mRadarStatus);
            if (mChassisEventListener != null) {
                mChassisEventListener.onOdometerUpdate(odomDataProto.getMove(),
                        odomDataProto.getLeftAcc(), odomDataProto.getRightAcc());
            }
            updateOdom(odomDataProto.getMove(),
                    odomDataProto.getLeftAcc(), odomDataProto.getRightAcc());
        }

        @Override
        public void onUpdateHumanData(List<ChassisPacketProtoWrapper.HumanDataProto> list, long l) {

        }

        @Override
        public void onUpdateRealtimeObsMap(ChassisPacketProtoWrapper.RealtimeObsMapProto realtimeObsMapProto) {
            mRealtimeObsMapProto.set(realtimeObsMapProto);
        }

        //以机器开机位置为原点的坐标系位姿
        @Override
        public void onUpdatePoseData(ChassisPacketProtoWrapper.PoseDataProto poseDataProto) {
            Pose2dProtoWrapper.Pose2dProto realtimePose = poseDataProto.getRealtimePose();
            float x = (float) realtimePose.getX();
            float y = (float) realtimePose.getY();
            float t = (float) realtimePose.getT();
//            Log.d(TAG, "onUpdatePoseData: x=" + x + ", y=" + y + ", t=" + t);
            Pose realtimePoseT = new Pose(x, y, t);
            mCurrentRealPose.set(realtimePoseT);
            BasicMotionProcessNew.getInstance().handleRealtimePose(realtimePoseT);
        }
    };

    private void onStatisticUpdate(Statistic statistic) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onStatisticUpdate(statistic);
        }
    }

    private void onMapDataUpdate(MapData data) {
        int type;
        if (data.getType() == MapData.GLOBAL) {
            type = Message.MSG_TYPE_GLOBAL_MAP;
        } else {
            type = Message.MSG_TYPE_LOCAL_MAP;
        }
        Log.d(TAG, "onMapDataUpdate: type = " + type);
        Map map = new Map(type);
        map.setWidth(data.getWidth());
        map.setHeight(data.getHeight());
        map.setResolution(data.getResolution());
        map.setOriginalX(data.getOriginalX());
        map.setOriginalY(data.getOriginalY());
        map.setData(data.getData());

        if (mChassisEventListener != null) {
            Log.i(TAG, "onMapDataUpdate: send map data");
            mChassisEventListener.onMapUpdate(map);
        }
    }

    private void onPoseUpdate(double x, double y, double theta, final int status, double obsDistance) {
        final Pose pose = new Pose((float) x, (float) y, (float) theta, status);
        pose.setObsDistance(obsDistance);
        Log.i(TAG, "onPoseUpdate: nav pose="+pose);
        LogUtils.printLog(LogUtils.TYPE_POSE,
                TAG,
                "onPoseUpdate : " + pose.toString() + ",target pose:"
                        + (mTargetPose == null ? "null" : mTargetPose.isAvailable())
                        + ", isNavigationing = " + isNavigationing()
                        + ", hasReportNotMoving=" + hasReportedUnmove.get()
                , 2000);
        mCurPose.set(pose);
        MapOutsideManager.getInstance().onPoseUpdate(pose);
        // Lora 监控类中更新当前实时位置
        if (mLoraLostMonitor != null) {
            mLoraLostMonitor.updateBasePose(new BasePoseBean(pose.getX(), pose.getY(), pose.getTheta()));
        }
        ///> caculateIsRobotMoving(pose);
        biManager.reportPoseData(pose);
        if (mChassisEventListener != null) {
            mChassisEventListener.onPoseUpdate(pose);
        }

        if (!isNavigationing() && WheelControlX86.getInstance().getLinearSpeed() == 0
                && !isCreatingMap() && mChassisEventListener != null) {
            DistanceUtils.pushRobotTooFarAway(x, y, new DistanceUtils.MoveListener() {
                @Override
                public void pushExceedDistance(String pushDistance) {
                    mChassisEventListener.onPushUpdate(pushDistance);
                }
            });
        }
    }

    /**
     * 雷达底层通过接口上报的点位数据
     *
     * @param dataList
     */
    private void onUpdateRgbdLineData(List<UpdateLineProtoWrapper.LineProto> dataList) {
        synchronized (lineDatas) {
            lineDatas.clear();
            for (UpdateLineProtoWrapper.LineProto proto : dataList) {
                com.ainirobot.coreservice.client.actionbean.Pose pose = new com.ainirobot.coreservice.client.actionbean.Pose();
                pose.setX((float) proto.getX());
                pose.setY((float) proto.getY());
                lineDatas.add(pose);
            }
        }
        if (mChassisEventListener != null) {
            mChassisEventListener.onLineDataUpdate(lineDatas);
        }
    }

    /**
     * 控制 底盘是否给上层上报雷达数据
     *
     * @param enable true 表示开启上报 ; false 表示关闭上报
     */
    @Override
    public void enableReportLineData(boolean enable) {
        Log.d(TAG, "enableReportLineData enable : " + enable);
        commandApi.setReportSwitch(ChassisPacketProtoWrapper.ChassisSetReportSwitchProto.newBuilder()
                .setEnableLaser(enable)
                .build());
    }

    /**
     * 控制 底盘是否给上层上报深度图
     *
     * @param enable true 表示开启上报 ; false 表示关闭上报
     * @param deviceId Depth类型
     */
    @Override
    public void enableReportDepthImage(boolean enable, int deviceId) {
        Log.d(TAG, "enableReportDepthImage enable:" + enable + ", deviceId:" + deviceId);

        setReportSwitch(enable, deviceId);
    }

    /**
     * 控制 底盘是否给上层上报IR图
     *
     * @param enable true 表示开启上报 ; false 表示关闭上报
     * @param deviceId IR类型
     */
    @Override
    public void enableReportIRImage(boolean enable, int deviceId) {
        Log.d(TAG, "enableReportIRImage enable:" + enable + ", deviceId:" + deviceId);

        setReportSwitch(enable, deviceId);
    }

    private void setReportSwitch(boolean enable, int deviceId) {
        ChassisPacketProtoWrapper.ChassisSetReportSwitchProto.Builder builder =
                ChassisPacketProtoWrapper.ChassisSetReportSwitchProto.newBuilder();

        switch (UpdateRawImageProtoWrapper.ImageDataProto.FrameType.forNumber(deviceId)) {
            case Depth1:
                builder.setEnableDepth1(enable);
                break;
            case Depth2:
                builder.setEnableDepth2(enable);
                break;
            case Depth3:
                builder.setEnableDepth3(enable);
                break;
            case Mono:
                builder.setEnableMono(enable);
                break;
            case TopIr:
                builder.setEnableTopIr(enable);
                break;
            case BackIr:
                builder.setEnableBackIr(enable);
                break;
            case Stereo:
                builder.setEnableStereo(enable);
                break;
        }
        commandApi.setReportSwitch(builder.build());
    }

    /**
     * 在导航任务过程中，如果机器人30秒无位移时，获取快照日志
     */
    private void caculateIsRobotMoving(Pose pose) {
        try {
            if (!isNavigationing() || hasReportedUnmove.get()) {
                return;
            }
            if (mRecordPose == null) {
                mRecordPose = new AtomicReference<>();
                mRecordPose.set(pose);
                return;
            }
            if (mRecordPose.get() == null) {
                mRecordPose.set(pose);
                return;
            }
            double distance = Math.sqrt(Math.pow((pose.getX() - mRecordPose.get().getX()), 2)
                    + Math.pow((pose.getY() - mRecordPose.get().getY()), 2));
            if (distance > Def.DEFAULT_ROBOT_NOT_MOVING_DISTANCE) {
                mRecordPose.set(pose);
                return;
            }

            double angleDiff = pose.getTheta() - mRecordPose.get().getTheta();
            if (angleDiff < -Math.PI) {
                angleDiff = angleDiff + 2 * Math.PI;
            } else if (angleDiff > Math.PI) {
                angleDiff = angleDiff - 2 * Math.PI;
            } else {

            }
            if (Math.abs(angleDiff) > Def.DEFAULT_ROBOT_NOT_MOVING_ANGLE) {
                mRecordPose.set(pose);
                return;
            }

            if (pose.getTime() - mRecordPose.get().getTime() > Def.DEFAULT_ROBOT_NOT_MOVING_TIME_INTERVAL) {
                hasReportedUnmove.set(true);
                biManager.reportRobotStopMoving(mRecordPose.get(), pose);
                String cacheId = RobotSettings.getSystemSn() + "-" + System.currentTimeMillis();
                //如果出现长时间无位移，上报导航状态事件，由NavigationAction缓存视觉照片，后期扩展用StatusReport
                if (isNavigationing()) {
                    JSONObject cacheInfo = new JSONObject();
                    cacheInfo.put(Def.JSON_KEY_CACHE_ID, cacheId);
                    mTargetPose.onPassThroughStatusUpdate(TargetPose.STATUS_NOT_MOVING_LONG_TIME,
                            NavigationStatus.STATUS_NAVI_NOT_MOVING_LONG_TIME, cacheInfo.toString());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private volatile boolean mRemoteRunning = false;

    public boolean isRemoteRunning() {
        return mRemoteRunning;
    }

    private void updateRemoteStatus(boolean remoteRunning) {
        this.mRemoteRunning = remoteRunning;
        if (mChassisEventListener != null) {
            mChassisEventListener.onChassisConnectStatus(Def.CHASSIS_REMOTE,
                    remoteRunning ? Definition.STATUS_HW_CONNECTED : Definition.STATUS_HW_DISCONNECTED);
        }
    }

    private void onNavigationEvent(Event event) {
        Log.d(TAG, "onEvent:" + event.getCode() + " + " + event.getMessage() + " + " + event
                .getAdditional() + ", isNavigationing:" + isNavigationing());

        switch (event.getCode()) {
            case Event.GOAL_REACHED:
                if (mTargetPose != null) {
                    Log.d(TAG, "Navigation arrived : " + mTargetPose.toString());
                    updateTargetPose(TargetPose.RESULT_ARRIVED, NavigationResult.RESULT_NAVI_EVENT_RESULT_ARRIVED);
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;

            case Event.GOAL_INVALID:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_OUT_MAP,
                            NavigationStatus.STATUS_NAVI_EVENT_GOAL_INVALID, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }

                if (!isRemoteRunning() || !isServiceReady()) {
                    return;
                }
                break;
            case Event.GOAL_IS_DANGEROUS:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_OUT_MAP,
                            NavigationStatus.STATUS_NAVI_EVENT_GOAL_IS_DANGEROUS, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }

                if (!isRemoteRunning() || !isServiceReady()) {
                    return;
                }
                break;
            case Event.ROBOT_IS_OUT:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_OUT_MAP,
                            NavigationStatus.STATUS_NAVI_EVENT_ROBOT_IS_OUT, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }

                if (!isRemoteRunning() || !isServiceReady()) {
                    return;
                }
                break;
            case Event.ROBOT_IS_OUT_OF_ROAD_ERROR:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_OUT_MAP,
                            NavigationStatus.STATUS_NAVI_EVENT_ROBOT_IS_OUT_OF_ROAD_ERROR, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }

                if (!isRemoteRunning() || !isServiceReady()) {
                    return;
                }
                break;
            case Event.GOAL_IS_OUT_OF_ROAD_ERROR:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_OUT_MAP,
                            NavigationStatus.STATUS_NAVI_EVENT_GOAL_IS_OUT_OF_ROAD_ERROR, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }

                if (!isRemoteRunning() || !isServiceReady()) {
                    return;
                }
                break;

            case Event.PATH_FAILED:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_GLOBAL_PATH_FAILED,
                            NavigationStatus.STATUS_NAVI_EVENT_GLOBAL_PATH_FAILED, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }

                //TODO 判断连接状态
                if (!isRemoteRunning() || !isServiceReady()) {
                    return;
                }

                Pose pose = null;
                if (mCurPose != null) {
                    pose = mCurPose.get();
                }
                if (pose != null && pose.getStatus() == Pose.MAP_STATUS_OUTSIDE_AREA) {
                    Log.d(TAG, "current pose status is " + pose.getStatus());
                }

                Log.d(TAG, "global path failed");
                break;
            case Event.GRAPH_SEARCHER_FAILED_ERROR:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_GLOBAL_PATH_FAILED,
                            NavigationStatus.STATUS_NAVI_EVENT_GRAPH_SEARCHER_FAILED_ERROR, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }

                //TODO 判断连接状态
                if (!isRemoteRunning() || !isServiceReady()) {
                    return;
                }

                Pose pose1 = null;
                if (mCurPose != null) {
                    pose1 = mCurPose.get();
                }
                if (pose1 != null && pose1.getStatus() == Pose.MAP_STATUS_OUTSIDE_AREA) {
                    Log.d(TAG, "current pose status is " + pose1.getStatus());
                }

                Log.d(TAG, "GRAPH_SEARCHER_FAILED_ERROR");
                break;
            case Event.GRAPH_ROAD_INVALID_ERROR:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_GLOBAL_PATH_FAILED,
                            NavigationStatus.STATUS_NAVI_EVENT_GRAPH_ROAD_INVALID_ERROR, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }

                //TODO 判断连接状态
                if (!isRemoteRunning() || !isServiceReady()) {
                    return;
                }

                Pose pose2 = null;
                if (mCurPose != null) {
                    pose2 = mCurPose.get();
                }
                if (pose2 != null && pose2.getStatus() == Pose.MAP_STATUS_OUTSIDE_AREA) {
                    Log.d(TAG, "current pose status is " + pose2.getStatus());
                }

                Log.d(TAG, "GRAPH_ROAD_INVALID_ERROR");
                break;
            case Event.NAVI_GRAPH_ROAD_PARAM_ERROR:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_GLOBAL_PATH_FAILED,
                            NavigationStatus.STATUS_NAVI_GRAPH_ROAD_PARAM_ERROR, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;

            case Event.PATH_SUCCESS:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_PATH_SUCCESS,
                            NavigationStatus.STATUS_NAVI_EVENT_PATH_SUCCESS, event.getMessage());
                }
                break;

            case Event.LOCAL_GOAL_INVAILD:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_AVOID,
                            NavigationStatus.STATUS_NAVI_EVENT_LOCAL_GOAL_INVAILD, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;
            case Event.LOCAL_PATH_FAILED:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_AVOID,
                            NavigationStatus.STATUS_NAVI_EVENT_LOCAL_PATH_FAILED, event.getMessage());
                }
                break;

            case Event.OBSTACLES_AVOID:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_OBSTACLES_AVOID,
                            NavigationStatus.STATUS_NAVI_EVENT_OBSTACLES_AVOID, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;

            case Event.ESTIMATE_SUCCESS:
                updatePoseEstimate(true);
                Log.d(TAG, "Rover estimate success");
                updateResetEstimate(true, SUCCESS, event.getMessage());
                biManager.pushNavigationReport(event.getCode(), event.getMessage());
                break;

            case Event.ESTIMATE_FAILED:
//                updatePoseEstimate(false);
                Log.d(TAG, "Rover estimate failed");
                //only estimate failed event use getAdditional，because fail reason defined by it
                updateResetEstimate(false, FAIL_NO_REASON, event.getAdditional());
                biManager.pushNavigationReport(event.getCode(), event.getMessage());
                break;

            case Event.REMOTE_ERROR:
                updateRemoteStatus(false);
                updatePoseEstimate(false);

                Log.d(TAG, "Rover remote service is died");
                biManager.pushNavigationReport(event.getCode(), event.getMessage());

                logManager.startCollectLogFlow(RobotSettings.getSystemSn() + "-" + System.currentTimeMillis(),
                        String.valueOf(System.currentTimeMillis()), LogManager.TYPE_REMOTE_DISCONNECT);
                Definition.RUNNING_ERROR_TYPE chassisCrash = Definition.RUNNING_ERROR_TYPE.TYPE_NAVIGATION;
                new BiRunningErrorReport().addError(chassisCrash.getErrorType(), Definition.CHASSIS_CRASH).report();
                startAutoRecovery();
                break;

            case Event.REMOTE_RECOVER:
                updateRemoteStatus(true);

                // 在此开启原因：整机重启之前恰好5分钟还未来及消费就重启，需要在重启后及时通知tk1打包
                if (logManager.collectLogNotOpened()) {
                    Log.d(Definition.TAG_NAVI_LOG_REPORT, "algorithm process recover, try start collect task");
                    logManager.scheduleCollectTask();
                }

                Log.d(TAG, "Rover remote service is recovered");
                biManager.pushNavigationReport(event.getCode(), event.getMessage());

                //                syncWorkStatusWithChassis();
                break;

            case Event.ESTIMATE_LOST:
                Log.d(TAG, "Rover estimate lost");
                updatePoseEstimate(false);
                onLostEvent(event.getAdditional());
                updateEvent(Definition.HW_NAVI_ESTIMATE_LOST, event.getAdditional());
                reportException(Definition.HW_NAVI_ESTIMATE_LOST, event.getMessage());
                Pose currentPose;
                boolean isOutOfMap = false;
                if (mCurPose != null && (currentPose = mCurPose.get()) != null) {
                    isOutOfMap = currentPose.getStatus() == Pose.MAP_STATUS_OUTSIDE_AREA;
                }
                biManager.lostEventNavigationReport(isOutOfMap, event.getMessage());

                if (!isRemoteRunning() || !isServiceReady()) {
                    return;
                }
                logManager.startCollectLogFlow(
                        RobotSettings.getSystemSn() + "-" + System.currentTimeMillis(),
                        String.valueOf(System.currentTimeMillis()), LogManager.TYPE_ESTIMATE_LOST);

                break;

            case Event.ESTIMATE_RECOVERY:
                Log.d(TAG, "Rover estimate recovery");
                updatePoseEstimate(true);
                updateResetEstimate(true, SUCCESS, event.getMessage());
                biManager.pushNavigationReport(event.getCode(), event.getMessage());
                break;

            case Event.EXPECTED_TIME_FOR_VISION:
                Log.d(TAG, "expected time");
                if (mCreateMapStop != null) {
                    mCreateMapStop.onStatusUpdate(CreateMapStop
                            .STATUS_STOP_CREATE_MAP_EXPECTED_TIME, event.getAdditional());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;

            case Event.ERROR_LOG:
                Log.d(TAG, "error log");
                //TK1 has remove tk1 error log event report on V4.8 2019.4.20
                //                updateErrorLog(Definition.HW_NAVI_ERROR_LOG, event.getAdditional());
                //                NavigationCmdReportUtils.pushNavigationReport(event.getCode(), event.getMessage());
                break;

            case Event.PACK_LOG_END:
                Log.d(TAG, "pack log end");
                updatePackLogEnd(Definition.HW_NAVI_PACK_LOG_END, event.getMessage());
                break;

            case Event.TAKE_SNAPSHOT_END:
                Log.d(TAG, "take snapshot end");
                updateTakeSnapshotEnd(Definition.HW_NAVI_TAKE_SNAPSHOT_END, event.getMessage(),
                        event.getAdditional());
                break;
            case Event.PROCESS_CREATE_MAP:
                updateCreateMapProcess(event.getAdditional());
                break;

            case Event.DETECT_SHAKE:
                biManager.reportDetectShakeFromTk1();
                break;

            case Event.DETECT_PEOPLE:
                Log.d(TAG, "detect people");
                onMonoInfoUpdate(event.getAdditional());
                break;
            case Event.GOTO_CHARGE_SUCCESS: {
                Log.d(TAG, "goto charge success");
                if (null != mGoChargelistener) {
                    ChargeResult chargeResult = new ChargeResult();
                    chargeResult.setCode(event.getCode());
                    chargeResult.setMessage("Go charge success");
                    mGoChargelistener.onResponse(true, event.getCode(), GsonUtil.toJson(chargeResult));
                }
            }

            break;
            case Event.GOTO_CHARGE_FAILED: {
                Log.d(TAG, "goto charge fail");
                if (null != mGoChargelistener) {
                    ChargeResult chargeResult = new ChargeResult();
                    chargeResult.setCode(event.getCode());
                    chargeResult.setMessage("Go charge failed");
                    mGoChargelistener.onResponse(false, event.getCode(), GsonUtil.toJson(chargeResult));
                }
            }
            break;
            case Event.REPORT_JSON:
                String resultMsg = event.getMessage();
                Log.d(TAG, "report json data:" + resultMsg);
                parseChassisReportJsonData(resultMsg);
                break;

            case Event.MULTI_ROBOT_AVOID_WAITING:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_MULTI_AVOID_WAITING, event.getCode(), event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;
            case Event.MULTI_ROBOT_AVOID_WAITING_END:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_MULTI_AVOID_WAITING_END, event.getCode(), event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;

            case Event.TURN_SIGNAL:
                parseNavigationTurnSignal(event.getMessage(), event.getAdditional());
                break;
            case Event.ROBOT_BEING_PUSHED:
                MapOutsideManager.getInstance().onPushEventReport(isPoseEstimate);
                RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, Definition.STATUS_ROBOT_HAS_BEING_PUSHED, "RobotHasBeingPushed");
                break;
            case Event.LORA_TEST:
                parseLoraTestResult(event.getMessage());
                break;
            case Event.LORA_MULTI_ROBOT_STATUS:
                parseMultipleRobotStatus(event.getAdditional());
                break;
            case Event.MULTI_ROBOT_DATA:
                parseMultipleRobotByteData(event.getAdditional(), event.getBytesData());
                break;

            case Event.NAVI_CALC_VEL_RECIEVE_POSE_TIMEOUT:
                if (isNavigationing()) {
                    receivePoseTimeout(); //该Event底盘是持续上报的
                } else {
                    naviSensorNormal();
                }
                break;
            case Event.NAVI_CALC_VEL_RECIEVE_MAP_TIMEOUT:
                if (isNavigationing()) {
                    sensorReceiveMapTimeout();  //该Event底盘是持续上报的
                } else {
                    naviSensorNormal();
                }
                break;
            case Event.NAVI_CALC_VEL_SUCCESS_VALUE:
                naviSensorNormal(); // 该Event底盘是持续上报的
                break;

            case Event.PATH_WAITING:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_PATH_WAITING,
                            NavigationStatus.STATUS_NAVI_EVENT_PATH_WAITING, event.getMessage());
                }
                break;
            case Event.LORA_RECEIVE_DATA:
                if (mChassisEventListener != null
                        && !TextUtils.isEmpty(event.getAdditional())
                        && mLoraTestMode) {
                    Log.d(TAG, "onLoraDataUpdate:" + event.getAdditional());
                    mChassisEventListener.onLoraDataUpdate(event.getAdditional());
                }
                break;

            //底盘不再上报该事件，由机器端根据mapMatch和多楼层配置来判断是否需要上报
            case Event.MULTI_ROBOTS_MAP_NOT_MATCH:
//                if (mTargetPose != null) {
//                    Log.d(TAG, "MULTI_ROBOTS_MAP_NOT_MATCH:" + event.getAdditional());
//                    mTargetPose.onStatusUpdate(TargetPose.STATUS_MULTIPLE_MAP_NOT_MATCH,
//                            NavigationError.NAVI_ERROR_MULTIPLE_MAP_NOT_MATCH, event.getMessage());
//                }
//                Definition.RUNNING_ERROR_TYPE mapNotMatch = Definition.RUNNING_ERROR_TYPE.TYPE_MULTIPLE_ROBOT;
//                new BiRunningErrorReport().addError(mapNotMatch.getErrorType(), Definition.MAP_NOT_MATCH).report();
                break;

            case Event.LORA_DISCONNECTED:
                if (mTargetPose != null) {
                    Log.d(TAG, "LORA_DISCONNECTED:" + event.getAdditional());
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_MULTIPLE_LORA_DISCONNECT,
                            NavigationError.NAVI_ERROR_MULTIPLE_LORA_DISCONNECT, event.getMessage());
                }
                Definition.RUNNING_ERROR_TYPE loraDisconnect = Definition.RUNNING_ERROR_TYPE.TYPE_MULTIPLE_ROBOT;
                new BiRunningErrorReport().addError(loraDisconnect.getErrorType(), Definition.LORA_DISCONNECT).report();
                break;

            case Event.LORA_CONFIG_ERROR:
                if (mTargetPose != null) {
                    Log.d(TAG, "LORA_CONFIG_ERROR:" + event.getAdditional());
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_MULTIPLE_LORA_CONFIG_FAIL,
                            NavigationError.NAVI_ERROR_MULTIPLE_LORA_CONFIG_FAIL, event.getMessage());
                }
                Definition.RUNNING_ERROR_TYPE loraConfigError = Definition.RUNNING_ERROR_TYPE.TYPE_MULTIPLE_ROBOT;
                new BiRunningErrorReport().addError(loraConfigError.getErrorType(), Definition.LORA_CONFIGURATION_FAILURE).report();
                break;

            case Event.MULTI_ROBOTS_VERSION_NOT_MATCH:
                if (mTargetPose != null) {
                    Log.d(TAG, "MULTI_ROBOTS_VERSION_NOT_MATCH:" + event.getAdditional());
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_MULTIPLE_VERSION_NOT_MATCH,
                            NavigationError.NAVI_ERROR_MULTIPLE_VERSION_NOT_MATCH, event.getMessage());
                }
                Definition.RUNNING_ERROR_TYPE versionNotMatch = Definition.RUNNING_ERROR_TYPE.TYPE_MULTIPLE_ROBOT;
                new BiRunningErrorReport().addError(versionNotMatch.getErrorType(), Definition.VERSION_NOT_MATCH).report();
                break;
            case Event.CAMERA_ERROR_CODE:
                biManager.pushNavigationReport(event.getCode(), event.getAdditional());
                break;
            case Event.NINJIA_SLAM_MAPPING_VISION_INFO:
                //建图时视觉信息上报处理
                updateCreateMapVisonInfo(event.getAdditional());
                break;
            case Event.SENSORHUB_ERROR_CODE:
                biManager.pushNavigationReport(event.getCode(), event.getAdditional());
                break;
            case Event.SLAM_SLIP_INFO:
                //轮子打滑处理
                this.parseSlamSlipInfo(event.getCode(), event.getMessage(), event.getAdditional());
                this.reportWheelSlipEvent(event.getCode(), event.getAdditional());
                break;
            case Event.NAVI_GOTO_ALIGN_SUCCESS:
                //结构相似，临时借用ChargeResult和mGoChargelistener，后续有区分要修改
                Log.d(TAG, "goto align success");
                if (mGoChargelistener != null) {
                    ChargeResult chargeResult = new ChargeResult();
                    chargeResult.setCode(event.getCode());
                    chargeResult.setMessage("goto align success");
                    mGoChargelistener.onResponse(true, event.getCode(), GsonUtil.toJson(chargeResult));
                }
                break;
            case Event.NAVI_GOTO_ALIGN_FAILED:
                //结构相似，临时借用ChargeResult和mGoChargelistener，后续有区分要修改
                Log.d(TAG, "goto align failed");
                if (mGoChargelistener != null) {
                    ChargeResult chargeResult = new ChargeResult();
                    chargeResult.setCode(event.getCode());
                    chargeResult.setMessage("goto align failed");
                    mGoChargelistener.onResponse(false, event.getCode(), GsonUtil.toJson(chargeResult));
                }
                break;
            case Event.HUMAN_FOLLOWING_INIT_SUCCESS:
                //结构相似，临时借用mGoChargelistener，后续有区分要修改
                if (mGoChargelistener != null) {
                    mGoChargelistener.onResponse(true, event.getCode(), Definition.NAVIGATION_HUMAN_FOLLOWING);
                }
                break;
            case Event.HUMAN_FOLLOWING_TRACKING_LOST:
                //结构相似，临时借用mGoChargelistener，后续有区分要修改
                if (mGoChargelistener != null) {
                    mGoChargelistener.onResponse(true, event.getCode(), Definition.NAVIGATION_HUMAN_LOST);
                }
                break;
            case Event.HUMAN_FOLLOWING_ID_CHANGE:
                //结构相似，临时借用mGoChargelistener，后续有区分要修改
                if (mGoChargelistener != null) {
                    mGoChargelistener.onResponse(true, event.getCode(), new HumanFollowBean(Definition.NAVIGATION_HUMAN_ID_CHANGE, event.getAdditional()));
                }
                break;
            case Event.HUMAN_FOLLOWING_LONG_TIME_NO_TAG:
                //结构相似，临时借用mGoChargelistener，后续有区分要修改
                if (mGoChargelistener != null) {
                    mGoChargelistener.onResponse(true, event.getCode(), Definition.NAVIGATION_HUMAN_FOLLOWING_LONG_TIME_NO_TAG);
                }
                break;
            default:
                break;
        }
    }


    private void reportWheelSlipEvent(int statusCode, String eventAdditional) {

        if (TextUtils.isEmpty(eventAdditional)) {
            return;
        }

        int level = 0;
        //{level:1}
        try {
            JSONObject jsonData = new JSONObject(eventAdditional);
            if (jsonData != null) {
                level = jsonData.optInt("level");
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }

        String actionType = String.valueOf(level);
        if (this.isNavigationing()) {
            actionType = actionType + "Nav";
        }

        Log.d(TAG, "reportWheelSlipEvent actionType: " + actionType);
        Log.d(TAG, "reportWheelSlipEvent curPosion: " + GsonUtil.toJson(this.getCurrentPose()));

        biManager.pushNavigationReport(statusCode, actionType, GsonUtil.toJson(this.getCurrentPose()));

    }


    //轮子打滑信息处理
    private void parseSlamSlipInfo(int code, String message, String eventAdditional) {
        if (TextUtils.isEmpty(eventAdditional) || mTargetPose == null) {
            return;
        }

        int mode = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_NAVIGATION_WHEEL_SLIP_MODE_LEVEL);

        int level = 0;
        //{level:1}
        try {
            JSONObject jsonData = new JSONObject(eventAdditional);
            if (jsonData != null) {
                level = jsonData.optInt("level");
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }

        Log.i(TAG, "get navigation slam slip mode: " + mode + " level: " + level);

        switch (mode) {
            case 0:
                //用户对报警设置为“禁用”时，则底盘上报任何打滑级别都不执行
                Log.d(TAG, "disable wheel slip report");
                break;
            case 1:
                if (level >= 1) {
                    this.wheelSlipEventReport(message);
                }
                break;
            case 2:
                if (level >= 2) {
                    this.wheelSlipEventReport(message);
                }
                break;
            case 3:
                if (level >= 3) {
                    this.wheelSlipEventReport(message);
                }
                break;
        }
    }

    /**
     * 上报轮子打滑异常事件
     */
    private void wheelSlipEventReport(String message) {
        Log.d(TAG, "reportWheelSlipEvent");
        if (mTargetPose != null) {
            mTargetPose.onStatusUpdate(TargetPose.STATUS_WHEEL_SLIP,
                    NavigationStatus.STATUS_NAVI_EVENT_WHEEL_SLIP, message);
        }
    }

    private void parseLoraTestResult(String eventData) {
        if (TextUtils.isEmpty(eventData)) {
            return;
        }
        try {
            Log.d(TAG, "parseLoraTestResult :" + eventData);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void parseMultipleRobotStatus(String eventData) {
        if (TextUtils.isEmpty(eventData)) {
            return;
        }
        try {
//            Log.v(TAG, "parseMultipleRobotStatus start:" + eventData);
            //TODO 为了测试解析后的数据是否正常，后续可以直接透传数据结果
            ArrayList<MultiRobotStatus> dataList = new ArrayList<>();
            ArrayList<MultiRobotStatus> availableList = new ArrayList<>();
            ArrayList<MultiRobotStatus> availableOtherRobotsList = new ArrayList<>();

            //当前机器的数据
            JSONObject jsonData = new JSONObject(eventData);
            JSONObject curRobotJb = jsonData.optJSONObject("curr_robot");
            MultiRobotStatus curStatus = parasMultiJsonToBean(curRobotJb, true);
            boolean isUpdate = isMultiRobotDataUpdate(curStatus);
            if (isUpdate) {
                availableList.add(curStatus);
            }
            dataList.add(curStatus);

            //其他机器的状态数据
            JSONArray jsonArray = jsonData.optJSONArray("other_robots");
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    MultiRobotStatus robotStatus = parasMultiJsonToBean(jsonObject, false);
                    boolean dataAvailable = isMultiRobotDataUpdate(robotStatus);
                    if (dataAvailable) {
                        availableList.add(robotStatus);
                        availableOtherRobotsList.add(robotStatus);
                    }
                    dataList.add(robotStatus);
                }
            }

            //处理多机状态数据，更新多机状态，用于多机状态匹配
            handleMapMatchState(curStatus, availableOtherRobotsList);

            // 用于对比 dataList 元素的时间与当前的系统流逝时间ellapsed, 找出来超过5秒的， 即为长时间未收到lora.
            if (mLoraLostMonitor != null) {
                mLoraLostMonitor.updateLoraStatus(dataList);
            }
            //更新旧数据，用户下一次判断数据是否更新
            mOldLoraDataList = dataList;
            //将有效的数据通知上层
            if (availableList != null && availableList.size() > 0) {
                String statusData = mGson.toJson(availableList);
                if (mChassisEventListener != null) {
                    mChassisEventListener.onMultipleRobotStatusUpdate(Definition.STATUS_MULTIPLE_ROBOT_WORKING, statusData);
                }
                if (mCollectLoraTestData) {
                    if (mLoradTestData == null) {
                        mLoradTestData = new ArrayList<String>();
                    }
                    mLoradTestData.add(statusData);
                }
//                Log.v(TAG, "parseMultipleRobotStatus end:" + statusData);
            }

            //发送多机状态实时广播
            String availableOtherRobotsData = "";
            if (availableOtherRobotsList != null && availableOtherRobotsList.size() > 0) {
                availableOtherRobotsData = mGson.toJson(availableOtherRobotsList);
            }

            Intent intent = new Intent();
            intent.setAction(Definition.INTENT_MULTI_ROBOT_STATUS_UPDATE);
            Bundle bundle = new Bundle();
            bundle.putString("data", availableOtherRobotsData);
            bundle.putString("rawData", eventData);
            intent.putExtras(bundle);
            ApplicationWrapper.getContext().sendBroadcast(intent);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 解析多机字节流数据
     *
     * @param eventData
     */
    private void parseMultipleRobotByteData(String eventData, byte[] bytes) {
        if (TextUtils.isEmpty(eventData) || bytes == null || bytes.length <= 0) {
            return;
        }
        // Log.d(TAG, "parseMultipleRobotByteData eventData:" + eventData
        //        + " bytes length=" + bytes.length + " bytes=" + Arrays.toString(bytes));
        try {
            JSONObject jsonData = new JSONObject(eventData);
            double time = jsonData.optDouble("time");//（单位：秒，精度：微秒）
            if (mLoraLostMonitor != null) {
                mLoraLostMonitor.updateMultiByteData(time, bytes);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 接收位姿异常，属于里程计的异常,该Event底盘是持续上报的
     */
    private void receivePoseTimeout() {
        Log.d(TAG, "receivePoseTimeout eventData");
        SensorExceptionManager.getInstance().receivePoseTimeout();
    }

    /**
     * 处理激光雷达传感器异常，该Event底盘是持续上报的
     */
    private void sensorReceiveMapTimeout() {
        Log.d(TAG, "parseNaviSensorException eventData");
        SensorExceptionManager.getInstance().sensorReceiveMapTimeout();
    }

    /**
     * 处理传感器正常，该Event底盘是持续上报的
     * 上面的两种异常，一次上报仅有一种，两种不会同时上报．
     * 上面两种异常对应的正常状态
     */
    private void naviSensorNormal() {
        SensorExceptionManager.getInstance().sensorNormal();
    }

    /**
     * 当前lora消息是否无效，当底盘上来的Lora消息2秒内没更新，则消息无效
     */
    private boolean isMultiRobotDataUpdate(MultiRobotStatus curStatus) {
        boolean dataAvailable = false;
        long timeDiff;
        if (mOldLoraDataList != null && mOldLoraDataList.size() > 0) {
            for (int j = 0; j < mOldLoraDataList.size(); j++) {
                MultiRobotStatus oldStatus = mOldLoraDataList.get(j);
                if (oldStatus.getId() == curStatus.getId()) {
                    //如果2秒未更新lora数据，则认为该机器离线
                    timeDiff = Math.abs(oldStatus.getTime() - curStatus.getTime());
//                    if ((timeDiff > 0 ) && (timeDiff < 2000)){
                    if (timeDiff < 2000) {
                        dataAvailable = true;
                    }
                    return dataAvailable;
                }
            }
        }
        return dataAvailable;
    }

    /**
     * 解析多机数据，由于底盘协议经常调整，本地做一次字段中转
     *
     * @param jsonObject 需要解析的json数据
     * @param curRobot   是否是当前机器的数据
     * @return
     */
    private MultiRobotStatus parasMultiJsonToBean(JSONObject jsonObject, boolean curRobot) {
        int id = jsonObject.optInt("id");
        String map_id = jsonObject.optString("map_id");
        String extra_data_base64 = jsonObject.optString("extra_data_base64");
        int timeStamp = (int) (jsonObject.optDouble("time_stamp") * 1000);
        int priority = jsonObject.optInt("priority");
        int status = jsonObject.optInt("status");
        boolean mapMatch = jsonObject.optBoolean("map_match", false);
        int errorStatus = jsonObject.optInt("error_states", 0);
        double distance = jsonObject.optDouble("goal_dis", 0.0);
        JSONObject pose = jsonObject.optJSONObject("pose");
        JSONObject goal = jsonObject.optJSONObject("goal");
        BasePoseBean poseBean = new BasePoseBean(pose.optDouble("x", 0.0),
                pose.optDouble("y", 0.0),
                pose.optDouble("theta", 0.0));
        BasePoseBean goalBean = new BasePoseBean(goal.optDouble("x", 0.0),
                goal.optDouble("y", 0.0),
                goal.optDouble("theta", 0.0));
        return new MultiRobotStatus(poseBean, goalBean,
                id, priority, mapMatch, timeStamp, status, curRobot, errorStatus, distance, map_id, extra_data_base64);
    }

    private void parseNavigationTurnSignal(String eventMsg, String eventAdditional) {
        if (TextUtils.isEmpty(eventAdditional) || mTargetPose == null) {
            return;
        }
        switch (eventAdditional) {
            case Def.NAVIGATION_STATUS_GO_STRAIGHT:
                mTargetPose.onPassThroughStatusUpdate(TargetPose.STATUS_GO_STRAIGHT,
                        NavigationStatus.STATUS_NAVI_EVENT_GO_STRAIGHT, eventMsg);
                break;
            case Def.NAVIGATION_STATUS_TURN_LEFT:
                mTargetPose.onPassThroughStatusUpdate(TargetPose.STATUS_TURN_LEFT,
                        NavigationStatus.STATUS_NAVI_EVENT_TURN_LEFT, eventMsg);
                break;
            case Def.NAVIGATION_STATUS_TURN_RIGHT:
                mTargetPose.onPassThroughStatusUpdate(TargetPose.STATUS_TURN_RIGHT,
                        NavigationStatus.STATUS_NAVI_EVENT_TURN_RIGHT, eventMsg);
                break;
            default:
                Log.d(TAG, "parseNavigationTurnSignal unknown msg:" + eventMsg);
                break;
        }

    }

    private void parseChassisReportJsonData(String dataInfo) {
        if (TextUtils.isEmpty(dataInfo)) {
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject(dataInfo);
            int type = jsonObject.optInt("type", -1);
            int subType = jsonObject.optInt("sub_type", -1);
            JSONObject data = jsonObject.optJSONObject("data");
            switch (type) {
                case 0:
                    if (subType == 0) {
                        reportCorrectLocationMode(data);
                    }
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void reportCorrectLocationMode(JSONObject data) {
        if (data == null) {
            return;
        }
        Gson gson = new Gson();
        BiCorrectLocationBean bean = gson.fromJson(data.toString(), BiCorrectLocationBean.class);
        biManager.reportChassisCorrectLocation(bean);
    }


    private synchronized void updateEvent(String key, Object status) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onEvent(key, status);
        }
    }

    private synchronized void reportException(String type, String data) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onException(type, data);
        }
    }

    private synchronized void updatePackLogEnd(String from, Object status) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onPackLogEnd(from, status);
        }
    }

    private void updateCreateMapProcess(String additional) {
        if (mChassisEventListener != null) {
            mChassisEventListener.OnUpdateCreateMapProcess(additional);
        }
    }

    private void updateCreateMapVisonInfo(String additional) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onMappingVisionInfoUpdate(additional);
        }
    }

    private synchronized void updateTakeSnapshotEnd(String from, String fileID, String path) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onTakeSnapshotEnd(from, fileID, path);
        }
    }

    private void onMonoInfoUpdate(String additional) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onMonoInfoUpdate(additional);
        }
    }

    private void onLaserDataIn(ArrayList<Laser> data) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onLaserDataUpdate(data);
        }
    }

    private void onObstacleReport() {
        if (mChassisEventListener != null) {
            mChassisEventListener.onObstacleReport();
        }
    }

    private void onAvoidStateReport(boolean isStopping) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onAvoidStateChange(isStopping);
        }
    }

    private void onStaticAvoidStateReport(boolean isStopping) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onStaticAvoidStateChange(isStopping);
        }
    }

    BaseAvoidPolicy.AvoidObserver mNarrowPassageObserver = new BaseAvoidPolicy.AvoidObserver() {
        @Override
        public void onStateUpdate(boolean avoid, int score) {
            Log.i(TAG_AVOID, "mNarrowPassageObserver onStateUpdate : avoid=" + avoid
                    + ", score=" + score + ", is Navigation ing=" + isNavigationing()
                    + ", mMotionWithAvoid=" + mMotionWithAvoid);
        }
    };

    BaseAvoidPolicy.AvoidObserver mDynamicAvoidObserver = new BaseAvoidPolicy.AvoidObserver() {
        @Override
        public void onStateUpdate(boolean avoid, int score) {
            Log.i(TAG_AVOID, "mDynamicAvoidObserver onStateUpdate : avoid=" + avoid
                    + ", score=" + score + ", isNavigationing=" + isNavigationing()
                    + ", mMotionWithAvoid=" + mMotionWithAvoid);
            onAvoidStateReport(avoid);
            if (isNavigationing() || !mMotionWithAvoid) {
                return;
            }
            if (score == StaticStoppingPolicy.OBSTACLES_SCORE_PERILOUS) {
                Log.e(TAG_AVOID, "mDynamicAvoidObserver onStateUpdate : Perilous stop");
                motionPerilousAvoidStop();
            } else if (avoid) {
                Log.e(TAG_AVOID, "mDynamicAvoidObserver onStateUpdate : Dangerous stop");
                motionDangerousAvoidStop();
            }
        }
    };

    BaseAvoidPolicy.AvoidObserver mStaticAvoidObserver = new BaseAvoidPolicy.AvoidObserver() {
        @Override
        public void onStateUpdate(boolean avoid, int score) {
            Log.i(TAG_AVOID, "mStaticAvoidObserver onStateUpdate : avoid=" + avoid
                    + ", score=" + score);
            onStaticAvoidStateReport(avoid);
        }
    };

    /**
     * 避停急刹
     */
    private void motionPerilousAvoidStop() {
        motion(0, 0, 0, false);
    }

    /**
     * 减速避停
     */
    private void motionDangerousAvoidStop() {
        Velocity realVelocity = mRealtimeVelocity.get();
        if (realVelocity == null || WheelControlX86.getInstance().isLinearStill(realVelocity)) {
            Log.d(TAG_AVOID, "motionDangerousAvoidStop , Robot is linear still");
            return;
        }

        double tAngularSpeed = WheelControlX86.getInstance().getTargetAngularSpeed();
        Log.d(TAG_AVOID, "motionDangerousAvoidStop , Stop motion : realVelocity="
                + realVelocity.toString() + ", tAngularSpeed=" + tAngularSpeed);
        BasicMotionProcess.getInstance().stopMotionTask(false, MOTION_AVOID_STOP, "Avoid stop");
        motion(tAngularSpeed, 0, 0, true);
        updateMotionAvoidState(false);
    }

    private void motionLinearAvoidStop() {
        Log.d(TAG_AVOID, "motionLinearAvoidStop , mMotionWithAvoid=" + mMotionWithAvoid);
        BasicMotionProcess.getInstance().stopMotionTask(false, MOTION_AVOID_STOP, "Avoid stop");
        motion(0, 0, 0, true);
        updateMotionAvoidState(false);
    }

    @Override
    public Velocity getRealtimeVelocity() {
        return mRealtimeVelocity.get();
    }

    @Override
    public List<Laser> getLasersData() {
        return mLasers.get();
    }

    @Override
    public void updateMotionAvoidState(boolean withAvoid) {
        Log.d(TAG_AVOID, "updateMotionAvoidState , withAvoid=" + withAvoid);
        mMotionWithAvoid = withAvoid;
    }

    private class WheelControlHandler extends Handler {

        public WheelControlHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(android.os.Message msg) {
            switch (msg.what) {
                case EVENT_WC_CMD:
                    if (mVelocityProto == null) {
                        Log.w(TAG, "current velocity proto is null, return!!!");
                        return;
                    }
                    double angularSpeed = mVelocityProto.getAngular();
                    double linearSpeed = mVelocityProto.getLiner();
                    Log.d(TAG, API_IN + ", controlWheel angularSpeed=" + angularSpeed
                            + ", linearSpeed=" + linearSpeed);
                    MotionCallChainReporter.controlWheelCallReport(TAG + "_controlWheel", angularSpeed, linearSpeed);
                    Result result = commandApi.controlWheel(mVelocityProto);
                    MotionCallChainReporter.controlWheelResultReport(TAG + "_controlWheel", result.toString());
                    Log.d(TAG, API_BACK + ", controlWheel result = " + result.toString());
                    break;
                case MSG_COLLECT_LORA_DATA:
                    caculateLoraTestDataInfo();
                    break;
                default:
                    break;
            }
        }
    }

    private void caculateLoraTestDataInfo() {
        if (mCollectLoraTestData) {
            mCollectLoraTestData = false;
        }
        if (mLoraTestListener == null) {
            return;
        }
        String resultInfo = "";
        try {
            if (mLoradTestData != null) {
                Type dataType = new TypeToken<List<MultiRobotStatus>>() {
                }.getType();
                HashMap<Integer, Integer> hashMap = new HashMap<>();
                for (int i = 0; i < mLoradTestData.size(); i++) {
                    String statusStr = (String) mLoradTestData.get(i);
                    List<MultiRobotStatus> statusList = mGson.fromJson(statusStr, dataType);
                    for (int j = 0; j < statusList.size(); j++) {
                        MultiRobotStatus robotStatus = statusList.get(j);
                        int msgCnt = 1;
                        if (hashMap != null && !hashMap.isEmpty() && robotStatus.getId() > 0) {
                            Integer curValue = hashMap.get(Integer.valueOf(robotStatus.getId()));
                            if (curValue != null) {
                                msgCnt = curValue.intValue() + 1;
                            }
                        }
                        hashMap.put(Integer.valueOf(robotStatus.getId()), Integer.valueOf(msgCnt));
                    }
                }
                resultInfo = mGson.toJson(hashMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        Log.d(TAG, "caculateLoraTestDataInfo:" + resultInfo);
        if (mLoraTestListener != null) {
            mLoraTestListener.onResponse(true, Result.CODE_SUCCESS, resultInfo);
            mLoraTestListener = null;
        }
        if (mLoradTestData != null) {
            mLoradTestData.clear();
        }
    }

    @Override
    public void setMinObstaclesDistance(double distance) {
        mStaticAvoidPolicy.setSafeDistance(distance);
    }

    @Override
    public void resetMinObstaclesDistance() {
        mStaticAvoidPolicy.resetSafeDistance();
    }

    @Override
    public void setSimpleEventListener(SimpleEventListener mSimpleEventListener) {
        this.mSimpleEventListener = mSimpleEventListener;
    }

    @Override
    public void motionPid(double angularSpeed, double linearSpeed) {
        if (isNavigationing()) {
            Log.e(TAG, "motionWithObstacles, Is navigationing");
            return;
        }
        angularSpeed = WheelControlX86.getInstance().correctAngularSpeed(angularSpeed);
        linearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);

        WheelControlX86.getInstance().startDecTimer(angularSpeed, linearSpeed, 0, false);
    }

    @Override
    public void setCameraEnable(int cameraType, boolean enable, ChassisResListener listener) {
        Log.d(TAG, "setCameraEnable, cameraType = " + cameraType + " enable:" + enable
                + " cameraBean:" + mCameraMap.toString());
        CommonProtoWrapper.CameraTypeProto typeProto = CommonProtoWrapper.CameraTypeProto.forNumber(cameraType);
        CameraBean cameraBean = mCameraMap.get(typeProto);
        if (!cameraBean.isAvailable()) {
            if (listener != null) {
                listener.onResponse(false, Result.CODE_ERROR, "Current type " + cameraType + " is not available");
            }
            return;
        }
        if (cameraBean.isEnableState() == enable) {
            if (listener != null) {
                listener.onResponse(true, Result.CODE_SUCCESS, "No need update config!");
            }
            return;
        }

        Result result = commandApi.enableCamera(typeProto, enable);
        Log.d(TAG, API_BACK + " setCameraEnable, result = " + result.toString());

        if (result.getCode() != Result.CODE_SUCCESS) {
            if (listener != null) {
                listener.onResponse(false, result.getCode(), result.getPayload());
            }
        } else {
            cameraBean.setEnableState(enable);
            if (listener != null) {
                listener.onResponse(true, result.getCode(), result.getPayload());
            }
        }
    }

    @Override
    public CameraBean queryCameraEnableState(int cameraType) {
        try {
            Log.d(TAG, "queryCameraEnableState type:" + cameraType + " data:" + mCameraMap.toString());
            CommonProtoWrapper.CameraTypeProto typeProto = CommonProtoWrapper.CameraTypeProto.forNumber(cameraType);
            CameraBean cameraBean = mCameraMap.get(typeProto);
            return cameraBean;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void setMultiRobotSettingConfigData(MultiRobotConfigBean configBean, ChassisResListener listener) {
        try {
            if (configBean == null) {
                if (listener != null) {
                    listener.onResponse(false, FAIL_NO_REASON, "");
                }
                Log.d(TAG, " setLoraSettingConfigData, bean is null");
                return;
            }
            Log.d(TAG, API_IN + " setLoraSettingConfigData, bean = " + configBean.toString());
            //WARNING 注意关闭多机时不能下发lora配置，只需要关闭多机功能，底盘会自动关闭lora。否则会报错
            MultiRobotConfigBean loraConfig;
            if (null == (loraConfig = GsonUtil.fromJson(NavigationDataManager.getInstance().getMultiRobotConfig(), MultiRobotConfigBean.class))) {
                Log.d(TAG, API_IN + " setLoraSettingConfigData, loraConfig is null ");
                return;
            }
            //如果要关闭多机，不能修改原有多机配置参数，否则会关闭成功，配置失败
            if (!configBean.isEnable() && !loraConfig.equalsPartialConfig(configBean)) {
                Log.d(TAG, " setLoraSettingConfigData fail, local config:" + loraConfig.toString());
                if (listener != null) {
                    listener.onResponse(false, FAIL_NO_REASON, loraConfig);
                }
                return;
            }

            Log.d(TAG, " setLoraSettingConfigData, loraConfig:" + loraConfig.toString());
            Result enableResult = commandApi.multiRobotEnable(configBean.isEnable());
            Log.d(TAG, " multiRobotEnable, result = " + enableResult.toString());
            if (!configBean.isEnable()) {
                if (enableResult.getCode() != Result.CODE_SUCCESS) {
                    if (listener != null) {
                        listener.onResponse(false, enableResult.getCode(), loraConfig);
                    }
                } else {
                    //如果开关关闭，则多机配置不会同步生效
                    loraConfig.setEnable(configBean.isEnable());
                    loraConfig.setErrorStatus(0);
                    NavigationDataManager.getInstance().updateMultiRobotConfig(loraConfig);
                    if (mLoraLostMonitor != null) {
                        mLoraLostMonitor.updateLaraEnableStatus(loraConfig.isEnable());
                    }
                    biManager.reportMultiRobotSetting(configBean);
                    if (listener != null) {
                        listener.onResponse(true, enableResult.getCode(), loraConfig);
                    }
                }
            } else {
                if (enableResult.getCode() != Result.CODE_SUCCESS) {
                    loraConfig.setEnable(configBean.isEnable());
                    loraConfig.setErrorStatus(1);
                    NavigationDataManager.getInstance().updateMultiRobotConfig(loraConfig);
                    if (mLoraLostMonitor != null) {
                        mLoraLostMonitor.updateLaraEnableStatus(loraConfig.isEnable());
                    }
                    if (listener != null) {
                        listener.onResponse(false, enableResult.getCode(), loraConfig);
                    }
                    return;
                }
                Result result = commandApi.multiRobotConfig(transferLoraConfigToProto(configBean));
                Log.d(TAG, API_BACK + " setLoraSettingConfigData, result = " + result.toString());
                if (result.getCode() != Result.CODE_SUCCESS) {
                    //lora配置如果失败，机器人多机不可用，多机导航时会抛出异常错误
                    loraConfig.setErrorStatus(1);
                    NavigationDataManager.getInstance().updateMultiRobotConfig(loraConfig);
                    if (mLoraLostMonitor != null) {
                        mLoraLostMonitor.updateLaraEnableStatus(loraConfig.isEnable());
                    }
                    biManager.reportMultiRobotSetting(loraConfig);
                    if (listener != null) {
                        listener.onResponse(false, result.getCode(), loraConfig);
                    }
                } else {
                    configBean.setErrorStatus(0);
                    Log.d(TAG, " setLoraSettingConfigData, configBean:" + configBean.toString());
                    NavigationDataManager.getInstance().updateMultiRobotConfig(configBean);
                    if (mLoraLostMonitor != null) {
                        mLoraLostMonitor.updateLaraEnableStatus(configBean.isEnable());
                    }
                    biManager.reportMultiRobotSetting(configBean);
                    if (listener != null) {
                        listener.onResponse(true, result.getCode(), configBean);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private CommonProtoWrapper.MultiRobotConfigProto transferLoraConfigToProto(MultiRobotConfigBean configBean) {
        if (configBean == null) {
            return null;
        }

        CommonProtoWrapper.RfTypeProto rfType = CommonProtoWrapper.RfTypeProto.kUnknown;
        //1代表lora，2代表esp32
        if (configBean.getRf_type() == 1) {
            rfType = CommonProtoWrapper.RfTypeProto.kLora;
        } else if (configBean.getRf_type() == 2) {
            rfType = CommonProtoWrapper.RfTypeProto.kEsp32;
        }
        CommonProtoWrapper.MultiRobotConfigProto configProto = CommonProtoWrapper.MultiRobotConfigProto.newBuilder()
                .setMultiSetRfPower(configBean.getRfPower())
                .setMultiSetSeq(configBean.getLoraId())
                .setMultiSetTotal(configBean.getTotalDeviceCnt())
                .setMultiSetChannel(configBean.getChannel())
                .setMultiSetSlot(configBean.getMsgInterval())
                .setMultiRfType(rfType)
                .build();
        return configProto;
    }

    @Override
    public void sendLoraMsgData(String dataInfo, ChassisResListener listener) {
        if (TextUtils.isEmpty(dataInfo)) {
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "params error!");
            }
            return;
        }
        Log.d(TAG, API_IN + " sendLoraMsgData, data = " + dataInfo);
        Result result = commandApi.multiRobotSend(dataInfo.getBytes());
        Log.d(TAG, API_BACK + " sendLoraMsgData, result = " + result.toString());
        if (listener != null) {
            if (result.getCode() != Result.CODE_SUCCESS) {
                listener.onResponse(false, result.getCode(), result.getPayload());
            } else {
                listener.onResponse(true, result.getCode(), result.getPayload());
            }
        }
    }

    @Override
    public void setMultiRobotWriteExtraData(byte[] data, double time, ChassisResListener listener) {
        if (data == null || data.length <= 0) {
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "params error!");
            }
            return;
        }
        Log.d(TAG, API_IN + " setMultiRobotWriteExtraData:");
        Result result = commandApi.multiRobotWriteExtraData(data, time);
        Log.d(TAG, API_BACK + " setMultiRobotWriteExtraData: result=" + result.toString());
        if (listener != null) {
            if (result.getCode() != Result.CODE_SUCCESS) {
                listener.onResponse(false, result.getCode(), result.getPayload());
            } else {
                listener.onResponse(true, result.getCode(), result.getPayload());
            }
        }
    }

    @Override
    public void setMultiRobotWriteExternalData(byte[] data, double time, ChassisResListener listener) {
        if (data == null || data.length <= 0) {
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "params error!");
            }
            return;
        }

        ChassisPacketProtoWrapper.ChassisMultiRobotWriteExternalDataReqProto.Builder builder =
                ChassisPacketProtoWrapper.ChassisMultiRobotWriteExternalDataReqProto.newBuilder();
        builder.setData(ByteString.copyFrom(data));
        builder.setTimestamp(time);
        ChassisPacketProtoWrapper.ChassisMultiRobotWriteExternalDataReqProto reqProto = builder.build();

        Log.d(TAG, API_IN + " setMultiRobotWriteExternalData:");
        Result result = commandApi.multiRobotWriteExternalData(reqProto);
        Log.d(TAG, API_BACK + " setMultiRobotWriteExternalData: result=" + result.toString());
        if (listener != null) {
            if (result.getCode() != Result.CODE_SUCCESS) {
                listener.onResponse(false, result.getCode(), result.getPayload());
            } else {
                listener.onResponse(true, result.getCode(), result.getPayload());
            }
        }
    }

    @Override
    public boolean setNavigationPriority(int priority) {
        Log.d(TAG, API_IN + " setNavigationPriority, data = " + priority);
        Result result = commandApi.multiRobotPriority(priority);
        Log.d(TAG, API_BACK + " setNavigationPriority, result = " + result.toString());
        if (result.getCode() == Result.CODE_SUCCESS) {
            return true;
        }
        return false;
    }

    @Override
    public void sendLoraTestMsg(ChassisResListener listener) {
        Log.d(TAG, API_IN + " sendLoraTestMsg:" + mCollectLoraTestData);
        if (mCollectLoraTestData) {
            if (listener != null) {
                listener.onResponse(false, -1, "Lora test task is running!");
            }
            return;
        }
        mLoraTestListener = listener;
        mCollectLoraTestData = true;
        if (mWheelControlHandler != null) {
            if (mWheelControlHandler.hasMessages(MSG_COLLECT_LORA_DATA)) {
                mWheelControlHandler.removeMessages(MSG_COLLECT_LORA_DATA);
            }
            mWheelControlHandler.sendEmptyMessageDelayed(MSG_COLLECT_LORA_DATA,
                    DEFAULT_LORA_TEST_DATA_INTERVAL);
        }
    }

    @Override
    public void setLoraTestMode(boolean enable, ChassisResListener listener) {
        Log.d(TAG, API_IN + " setLoraTestMode enable:" + enable + " current:" + mLoraTestMode);
        Result result = commandApi.multiRobotTestMode(enable);
        Log.d(TAG, API_BACK + " setLoraTestMode, result = " + result.toString());
        if (result.getCode() != Result.CODE_SUCCESS) {
            if (listener != null) {
                listener.onResponse(false, result.getCode(), result.getPayload());
            }
        } else {
            mLoraTestMode = enable;
            if (listener != null) {
                listener.onResponse(true, result.getCode(), result.getPayload());
            }
        }
    }

    @Override
    public void resetLoraDefaultConfig(ChassisResListener listener) {
        Log.d(TAG, API_IN + " resetLoraDefaultConfig");
        Result result = commandApi.multiRobotDefaultConfig();
        Log.d(TAG, API_BACK + " resetLoraDefaultConfig, result = " + result.toString());
        if (result.getCode() != Result.CODE_SUCCESS) {
            //重置本地Lora配置参数。
            ClientConfigManager.getInstance().storeMultiRobotConfig();
            if (listener != null) {
                listener.onResponse(false, result.getCode(), result.getPayload());
            }
        } else {
            if (listener != null) {
                listener.onResponse(true, result.getCode(), result.getPayload());
            }
        }
    }

    @Override
    public void setWheelControlMode(boolean isRelease, ChassisResListener listener) {
        setWheelState(isRelease, listener);
    }

    @Override
    public void calcNaviPathInfo(List<NaviPathInfo> pathList, ChassisResListener listener) {
        if (pathList == null || pathList.size() <= 0) {
            listener.onResponse(false, -2, "params error");
            return;
        }

        ChassisPacketProtoWrapper.CalcNaviPoseProto.Builder builder = ChassisPacketProtoWrapper.CalcNaviPoseProto.
                newBuilder();

        for (NaviPathInfo pathInfo : pathList) {
            Pose startPose = pathInfo.getStartPose();
            Pose endPose = pathInfo.getEndPose();
            Pose2dProtoWrapper.Pose2dProto start = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                    .setX(startPose.getX())
                    .setY(startPose.getY())
                    .setT(startPose.getTheta())
                    .build();
            Pose2dProtoWrapper.Pose2dProto end = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                    .setX(endPose.getX())
                    .setY(endPose.getY())
                    .setT(endPose.getTheta())
                    .build();
            ChassisPacketProtoWrapper.CalcNaviPoseUnitProto unitProto = ChassisPacketProtoWrapper.CalcNaviPoseUnitProto
                    .newBuilder()
                    .setPose1(start)
                    .setPose2(end)
                    .build();

            builder.addPose(unitProto);
        }

        int roadMode = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTINGS_NAVIGATION_LINE_TRACKING);
        builder.setRoadMode(ChassisPacketProtoWrapper.CalcNaviPoseProto.RoadMode.forNumber(roadMode));
        Result<CommonProtoWrapper.PathInfoProto> result = commandApi.calcNaviPathInfo(builder.build());

        if (result.getCode() == Result.CODE_SUCCESS) {
            CommonProtoWrapper.PathInfoProto pathInfo = result.getPayload();
            List<CommonProtoWrapper.PathInfoUnitProto> unitPathInfos = pathInfo.getPathInfoList();
            if (unitPathInfos.isEmpty()) {
                listener.onResponse(false, -3, result.getPayload().toString());
            }
            if (unitPathInfos.size() != pathList.size()) {
                listener.onResponse(false, -4, "length error");
            }

            for (int i = 0; i < unitPathInfos.size(); i++) {
                if (pathList.get(i).getState() == NaviPathInfo.STATE_POSE_NOT_EXIT) {
                    continue;
                }
                pathList.get(i).setPathLength(unitPathInfos.get(i).getPathLength());
                pathList.get(i).setState(unitPathInfos.get(i).getCode());
            }
            listener.onResponse(true, Result.CODE_SUCCESS, pathList);
        } else {
            listener.onResponse(false, result.getCode(), result.toString());
        }

    }

    @Override
    public void calcNaviPathDetail(List<NaviPathDetail> pathDetail, ChassisResListener listener) {
        if (pathDetail == null || pathDetail.size() <= 0) {
            listener.onResponse(false, -2, "params error");
            return;
        }

        ChassisPacketProtoWrapper.CalcNaviPoseProto.Builder builder = ChassisPacketProtoWrapper.CalcNaviPoseProto.
                newBuilder();

        for (NaviPathDetail item : pathDetail) {
            NaviPathInfo pathInfo = item.getPathInfo();
            Pose startPose = pathInfo.getStartPose();
            Pose endPose = pathInfo.getEndPose();
            Pose2dProtoWrapper.Pose2dProto start = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                    .setX(startPose.getX())
                    .setY(startPose.getY())
                    .setT(startPose.getTheta())
                    .build();
            Pose2dProtoWrapper.Pose2dProto end = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                    .setX(endPose.getX())
                    .setY(endPose.getY())
                    .setT(endPose.getTheta())
                    .build();
            int roadMode = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTINGS_NAVIGATION_LINE_TRACKING);
            ChassisPacketProtoWrapper.CalcNaviPoseUnitProto unitProto = ChassisPacketProtoWrapper.CalcNaviPoseUnitProto
                    .newBuilder()
                    .setPose1(start)
                    .setPose2(end)
                    .build();

            builder.addPose(unitProto);
        }

        int roadMode = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTINGS_NAVIGATION_LINE_TRACKING);
        builder.setRoadMode(ChassisPacketProtoWrapper.CalcNaviPoseProto.RoadMode.forNumber(roadMode));
        Result<CommonProtoWrapper.PathInfoProto> result = commandApi.calcNaviPathInfo(builder.build());

        if (result.getCode() == Result.CODE_SUCCESS) {
            CommonProtoWrapper.PathInfoProto pathInfo = result.getPayload();
            List<CommonProtoWrapper.PathInfoUnitProto> unitPathInfos = pathInfo.getPathInfoList();
            if (unitPathInfos.isEmpty()) {
                listener.onResponse(false, -3, result.getPayload().toString());
            }
            if (unitPathInfos.size() != pathDetail.size()) {
                listener.onResponse(false, -4, "length error");
            }

            for (int i = 0; i < unitPathInfos.size(); i++) {
                if (pathDetail.get(i).getPathInfo().getState() == NaviPathInfo.STATE_POSE_NOT_EXIT) {
                    continue;
                }
                pathDetail.get(i).getPathInfo().setPathLength(unitPathInfos.get(i).getPathLength());
                pathDetail.get(i).getPathInfo().setState(unitPathInfos.get(i).getCode());
                List<Vector2d> poseList = new ArrayList<>();
                for (Vector2dProtoWrapper.Vector2dProto item : unitPathInfos.get(i).getPathList()) {
                    poseList.add(new Vector2d(item.getX(), item.getY()));
                }
                pathDetail.get(i).setPathList(poseList);
            }
            listener.onResponse(true, Result.CODE_SUCCESS, pathDetail);
        } else {
            listener.onResponse(false, result.getCode(), result.toString());
        }
    }

    /**
     * 底盘算法crash后开始自动恢复
     */
    private void startAutoRecovery() {
        Log.d(TAG, "startAutoRecovery destroy: " + API_IN);
        resetAllCustomState();
        Result result = commandApi.destroy();
        if (result.getCode() == Result.CODE_SUCCESS) {
            setChassisOK(false);
        }
        Log.d(TAG, "startAutoRecovery destroy: " + API_BACK + ", result = " + result);
        initRoverService();
        Log.d(TAG, "startAutoRecovery end");
        BiManager.getInstance().reportDeviceType();
    }

    /**
     * 重置所有自定义属性字段
     */
    private void resetAllCustomState() {
        Log.d(TAG, "resetAllCustomState start isRelocate:" + isRelocate
                + "isCreatingMap:" + isCreatingMap + " isStoppingCreateMap:" + isStoppingCreateMap + " \n"
                + " mCurPose:" + mCurPose.get() + " mVelocity:" + mVelocity
                + " hasObstacle:" + hasObstacle + " avoidTag:" + avoidTag + "\n"
                + " mMotionWithAvoid:" + mMotionWithAvoid + " mRadarStatus:" + mRadarStatus
                + " radarOpenState:" + radarOpenState + " wheelReleaseState:" + wheelReleaseState);
        // 重置定位
        updatePoseEstimate(false);
        isRelocate = false;
        //重置导航任务
        updateTargetPose(TargetPose.RESULT_FAILED, NavigationResult.RESULT_NAVI_EVENT_RESULT_ABORTED);
        //重置建图状态
        setCreatingMap(false);
        isStoppingCreateMap = false;
        //重置位姿和速度
        mCurPose.set(null);
        mVelocity.set(null);
        mVelocityProto = null;

        //避障状态，待验证
        hasObstacle = false;
        avoidTag = false;
        mMotionWithAvoid = false;

        //断连恢复后默认雷达开启
        setRadarOpenState(true);
        setRadarStatus(Definition.RADAR_STATUS_OPENED);
        //轮子默认解锁
        setWheelReleaseState(RELEASE);
        Log.d(TAG, "resetAllCustomState end");
        //重置摄像头状态
        initDefaultCameraState();
        //重置Lora测试状态。
        mLoraTestMode = false;
    }

    /**
     * 当机器人初始化成功后上报部分默认状态，防止由于进程重启导致CoreService部分状态无法更新
     * 如雷达资源未释放bug：https://jira.orionstar.com/browse/SESQE-4395
     */
    private void reportDefaultStatusToCoreService() {
        //定位状态
        updatePoseEstimate(false);
        //雷达状态
        setRadarOpenState(true);
        setRadarStatus(Definition.RADAR_STATUS_OPENED);
    }

    private void regitSnapshotTaskListener() {
        logManager.registSnapshotTaskListener(new LogManager.LogTaskListener() {
            @Override
            public void onNewSnapshotCollectTask() {
                startPreSnapshot();
            }
        });
    }

    /**
     * 收到底盘紧急事件时立即调用此接口，底盘会存储三张RGBD，三张bino_left
     * 下次打包快照时会放入快照包内。
     */
    private void startPreSnapshot() {
        try {
            if (mPreSnapshotTask != null && !mPreSnapshotTask.isDone()) {
                Log.d(TAG, "startPreSnapshot task is running!");
                return;
            }
            mPreSnapshotTask = mFileExecutor.submit(new Runnable() {
                @Override
                public void run() {
                    Log.d(TAG, API_IN + " startPreSnapshot");
                    if (isSocketConnected() && isRemoteRunning()) {
                        Result result = commandApi.preSnapshot();
                        Log.d(TAG, API_BACK + " startPreSnapshot, result = " + result.toString());
                    }
                    ;
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private double calAngSpdWithSlopeIncreasingCurve(double navLinear) {
        return 2.23 / (1.237 + Math.pow(Math.E, -4 * (navLinear - 0.67)));
    }

    private double calAngSpdWithSlopeDecreasingCurve(double navLinear) {
        return 1.4 * Math.sin((navLinear - 0.8) * 0.5 * Math.PI) + 1.3;
    }

    public static double calculateAngularVelocityFormulaCarryLv2(double navLinear) {
        if (navLinear >= 0.6 && navLinear <= 1.5) {
            // Formula: y = -0.41625x^2 + 1.06475x + 0.1817
            return -0.41625 * navLinear * navLinear + 1.06475 * navLinear + 0.1817;
        } else if (navLinear >= 0.3 && navLinear < 0.6) {
            // Constant: y = 0.6707
            return 0.6707;
        } else {
            throw new IllegalArgumentException("Speed out of range for Formula Carry Lv2.");
        }
    }

    public static double calculateAngularVelocityFormulaCarryLv3(double navLinear) {
        if (navLinear >= 0.6 && navLinear <= 1.5) {
            // Formula: y = -0.8775x^2 + 1.287x + 0.2132
            return -0.8775 * navLinear * navLinear + 1.287 * navLinear + 0.2132;
        } else if (navLinear >= 0.3 && navLinear < 0.6) {
            // Constant: y = 0.6707
            return 0.6707;
        } else {
            throw new IllegalArgumentException("Speed out of range for Formula Carry Lv3.");
        }
    }

    @Override
    public void startDataSetRecord(String sensorString) {
        List<Integer> sensorList = sensorString2List(sensorString);
        if (sensorList.size() <= 0) {
            return;
        }
        Log.d(TAG, API_IN + " startDataSetRecord sensor:" + sensorString);
        Result result = commandApi.startDataSetRecord(sensorList);
        Log.d(TAG, API_BACK + " startDataSetRecord, result = " + result.toString());
    }

    @Override
    public void stopDataSetRecord(boolean isLocalData) {
        Log.d(TAG, API_IN + " stopDataSetRecord isLocalData:" + isLocalData);
        Result result = commandApi.stopDataSetRecord(isLocalData);
        Log.d(TAG, API_BACK + " stopDataSetRecord, result = " + result.toString());
    }

    @Override
    public void uploadNaviDataSet(String sensorString) {
        List<Integer> sensorList = sensorString2List(sensorString);
        if (sensorList.size() <= 0) {
            return;
        }
        Log.d(TAG, API_IN + " uploadNaviDataSet sensor:" + sensorString);
        Result result = commandApi.uploadNaviDataSet(sensorList);
        Log.d(TAG, API_BACK + " uploadNaviDataSet, result = " + result.toString());
    }

    /**
     * 数据类型，多个用英文逗号分隔
     * 0: 传感器数据
     * 1: Mono数据
     * 2: Ir数据
     * 3: 深度摄像头数据
     * 4: 左目摄像头数据
     * 5: TopIr数据
     */
    private List<Integer> sensorString2List(String sensorString) {
        String[] sensor = sensorString.split(",");
        List<Integer> list = new ArrayList<>();
        for (int i = 0; i < sensor.length; i++) {
            list.add(Integer.parseInt(sensor[i]));
        }
        return list;
    }

    @Override
    public void autoDrawRoadGraph(String mapName, ChassisResListener listener) {
        Log.d(TAG, "autoDrawRoadGraph: --Start: mapName=" + mapName);
        long startTime = System.currentTimeMillis();
        File mapPgm = MapFileHelper.getMapPgm(mapName);
        try {
            Result result = commandApi.autoDrawRoadGraph(mapPgm);
            Log.d(TAG, "autoDrawRoadGraph：result=" + result.toString());
            RoadGraphProtoWrapper.RoadGraphProto roadGraphProto = (RoadGraphProtoWrapper.RoadGraphProto) result.getPayload();
            List<RoadGraphProtoWrapper.RoadGraphNodeProto> nodesListProto = roadGraphProto.getNodesList();
            List<RoadGraphProtoWrapper.RoadGraphEdgeProto> edgesListProto = roadGraphProto.getEdgesList();
            RoadGraph roadGraph = new RoadGraph();
            ArrayList<RoadGraphNode> nodeList = new ArrayList<>();
            ArrayList<RoadGraphEdge> edgeList = new ArrayList<>();
            for (RoadGraphProtoWrapper.RoadGraphNodeProto roadGraphNodeProto : nodesListProto) {
                RoadGraphNode roadGraphNode = new RoadGraphNode();
                roadGraphNode.setId(roadGraphNodeProto.getId());
                roadGraphNode.setPosition(new Vector2d(roadGraphNodeProto.getPosition().getX(), roadGraphNodeProto.getPosition().getY()));
                nodeList.add(roadGraphNode);
            }
            for (RoadGraphProtoWrapper.RoadGraphEdgeProto roadGraphEdgeProto : edgesListProto) {
                RoadGraphEdge roadGraphEdge = new RoadGraphEdge();
                roadGraphEdge.setId(roadGraphEdgeProto.getId());
                roadGraphEdge.setNode_start_id(roadGraphEdgeProto.getNodeStartId());
                roadGraphEdge.setNode_end_id(roadGraphEdgeProto.getNodeEndId());
                edgeList.add(roadGraphEdge);
            }
            roadGraph.setNodes(nodeList);
            roadGraph.setEdges(edgeList);
            listener.onResponse(true, result.getCode(), GsonUtil.toJson(roadGraph));
        } catch (Exception e) {
            Log.d(TAG, "autoDrawRoadGraph: Exception=" + e.getMessage());
            listener.onResponse(false, FAIL_NO_REASON, "Exception");
        }
        Log.d(TAG, "autoDrawRoadGraph：--End. Cost time: " + (System.currentTimeMillis() - startTime));
    }

    /**
     * 是否开启了电梯功能
     */
    private boolean isEnableMultiNavigation() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED)
                == Definition.ROBOT_SETTING_ENABLE;
    }

    @Override
    public void getNaviParams(String type, String params, ChassisResListener listener) {
        if (TextUtils.equals(type, Definition.CMD_NAVI_GET_NAVI_ANGLE_SPEED)) {
            getNaviAngSpeed(params, listener);
            return;
        }
    }

    /**
     * 根据速度获得角速度
     */
    private void getNaviAngSpeed(String param, ChassisResListener listener) {
        try {
            StartModeResetParam startModeResetParam = toResetNavigationParam(Double.parseDouble(param),
                    RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_NAVIGATION_START_MODE_LEVEL));
            if (null != listener) {
                listener.onResponse(true, SUCCESS, String.valueOf(startModeResetParam.mAng));
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (null != listener) {
                listener.onResponse(false, FAIL_NO_REASON, e.getMessage());
            }
        }
    }

    @Override
    public void naviPause(boolean isPause, ChassisResListener listener) {
        Log.d(TAG, API_IN + " naviPause:" + isPause);
        Result result = commandApi.naviPause(isPause);
        Log.d(TAG, API_BACK + " naviPause, result = " + result.toString());
        if (result.getCode() != Result.CODE_SUCCESS) {
            if (listener != null) {
                listener.onResponse(false, result.getCode(), result.getPayload());
            }
        } else {
            if (listener != null) {
                listener.onResponse(true, result.getCode(), result.getPayload());
            }
        }
    }

    @Override
    public void gotoAlign(Pose pose, ChassisResListener listener) {
        Log.d(TAG, "gotoAlign: " + API_IN);
        Pose2dProtoWrapper.Pose2dProto pose2dProto = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                .setX(pose.getX())
                .setY(pose.getY())
                .setT(pose.getTheta())
                .build();
        Result result = commandApi.gotoAlign(pose2dProto);
        Log.d(TAG, "gotoAlign: " + API_BACK + ", result = " + result.toString());
        //结构相似，临时借用ChargeResult和mGoChargelistener，后续有区分要修改
        this.mGoChargelistener = listener;
        ChargeResult chargeResult = new ChargeResult();
        int code = result.getCode();
        chargeResult.setCode(code);
        if (null != listener) {
            if (code == 0) {
                chargeResult.setMessage("goto align command success");
                listener.onResponse(true, code, GsonUtil.toJson(chargeResult));
            } else {
                chargeResult.setMessage("goto align command failed");
                listener.onResponse(false, code, GsonUtil.toJson(chargeResult));
            }
        }
    }

    @Override
    public void cancelAlign(ChassisResListener listener) {
        Log.d(TAG, "cancelAlign: " + API_IN);
        Result result = commandApi.cancelAlign();
        Log.d(TAG, "cancelAlign: " + API_BACK + ", result = " + result.toString());
        //结构相似，临时借用ChargeResult和mGoChargelistener，后续有区分要修改
        ChargeResult chargeResult = new ChargeResult();
        int code = result.getCode();
        chargeResult.setCode(code);
        if (null != listener) {
            if (code == 0) {
                chargeResult.setMessage("cancel align command success");
                listener.onResponse(true, code, GsonUtil.toJson(chargeResult));
            } else {
                chargeResult.setMessage("cancel align command failed");
                listener.onResponse(false, code, GsonUtil.toJson(chargeResult));
            }
        }
    }

    private OdomUpdate mOdomUpdate = null;

    public void setOdomUpdateListener(OdomUpdate odomUpdate) {
        mOdomUpdate = odomUpdate;
    }

    private void updateOdom(double move, double leftAcc, double rightAcc) {
        if (mOdomUpdate != null) {
            mOdomUpdate.updateOdom(move, leftAcc, rightAcc);
        }
    }

    /**
     * 处理当前机器人多机地图匹配状态
     */
    public void handleMapMatchState(MultiRobotStatus curStatus, ArrayList<MultiRobotStatus> availableOtherRobotsList) {
        ArrayList<String> multiFloorMapNames = mMultiFloorMapList.get();
        boolean isMapMatch = true;

        //没有多楼层配置，通过多机上报的map_id判断，检查当前机器地图是否和其他机器地图一致，
        if (multiFloorMapNames == null || multiFloorMapNames.size() == 0) {
            if (curStatus == null || availableOtherRobotsList == null || availableOtherRobotsList.size() == 0) {
                //多机状态为空时，不上报
            } else {
                //用 map_id 判断 curStatus 和 availableOtherRobotsList 机器是否一致
                //TODO 理论上是可以用map_id判断，但是目前多机上报的map_id不准确，所以暂时不用。
                //TODO 底盘没时间看这个问题，暂时先用map_match字段判断。本机的map_match一定为true，判断其他机器有一个map_match为false则认为地图不匹配.
                //TODO 注意：底盘本来计划以后去掉map_match字段，所以这个逻辑以后可能会有变化。
                for (MultiRobotStatus otherRobotStatus : availableOtherRobotsList) {
                    if (!otherRobotStatus.isMapMatch()) {
                        Log.d(TAG, "handleMapMatchState:: otherRobotStatus isMapMatch is false");
                        isMapMatch = false;
                        break;
                    }
//                    if (!TextUtils.equals(curStatus.getMap_id(), otherRobotStatus.getMap_id())) {
//                        isMapMatch = false;
//                        break;
//                    }
                }
            }
        } else if (!multiFloorMapNames.contains(NavigationDataManager.getInstance().getMapName())) {
            //存在多楼层配置，不再依赖多机状态判断，只根据当前地图是否在多楼层列表中
            //如果当前地图不在多楼层列表中，则认为地图不匹配
            isMapMatch = false;
        }

        if (!isMapMatch) {
            //当前地图errorStatus状态值正常（0）时，把errorStatus置为1，即修改为地图不匹配状态
            if (curStatus != null && curStatus.getErrorStatus() == 0) {
                Log.d(TAG, "handleMapMatchState:: curStatus errorStatus is 0, set errorStatus to 1");
                curStatus.setErrorStatus(1);
            }
            //上报多机地图不匹配事件
            reportMapNotMatchEvent();
        }
    }

    private void reportMapNotMatchEvent() {
        if (mTargetPose != null) {
            Log.d(TAG, "reportMapNotMatchEvent: ");
            mTargetPose.onStatusUpdate(TargetPose.STATUS_MULTIPLE_MAP_NOT_MATCH,
                    NavigationError.NAVI_ERROR_MULTIPLE_MAP_NOT_MATCH, "Map not match");
        }
        Definition.RUNNING_ERROR_TYPE mapNotMatch = Definition.RUNNING_ERROR_TYPE.TYPE_MULTIPLE_ROBOT;
        new BiRunningErrorReport().addError(mapNotMatch.getErrorType(), Definition.MAP_NOT_MATCH).report();
    }

    /**
     * 多楼层数据变化监听
     */
    NavigationDataManager.MultiFloorChangeListener multiFloorDataChangeListener = new NavigationDataManager.MultiFloorChangeListener() {
        @Override
        public void onDataChanged() {
            Log.d(TAG, "multiFloorDataChangeListener:onDataChanged:");
            updateMultiFloorMapList();
        }
    };

    /**
     * 更新多楼层地图列表
     */
    public void updateMultiFloorMapList() {
        List<MultiFloorInfo> multiFloorInfos = NavigationDataManager.getInstance().getMultiFloorConfig();
        if (multiFloorInfos != null && multiFloorInfos.size() > 0) {
            ArrayList<String> multiFloorMapNames = new ArrayList<>();
            for (MultiFloorInfo multiFloorInfo : multiFloorInfos) {
                multiFloorMapNames.add(multiFloorInfo.getMapName());
            }
            Log.d(TAG, "updateMultiFloorMapList: " + multiFloorMapNames);
            mMultiFloorMapList.set(multiFloorMapNames);
        }
    }

    private void onLostEvent(String lostReason) {
        ThreadUtils.getCpuService().submit(() -> {
            try {
                int enableEmergencyStop = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_EMERGENCY_BRAKE_ON_SECURITY_CODE);
                Log.d(TAG, "onLostEvent:" + lostReason + " enableEmergencyStop:" + enableEmergencyStop);
                JSONObject lostMsgJson = new JSONObject(lostReason);
                String lostCode = lostMsgJson.optString(Definition.HW_NAVI_ESTIMATE_LOSE_CODE);
                if (TextUtils.equals(lostCode, Definition.ESTIMATE_LOST_BY_DANGER_CODE)
                        && enableEmergencyStop == Definition.ROBOT_SETTING_ENABLE) {
                    Log.e(TAG, "estimate lost by danger code");
                    cancelNavigation(null, true);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    private void cancelNavigation(ChassisResListener listener, boolean forceStop) {
        if (!isNavigationing()) {
            Log.d(TAG, "not in navigation mode, no need to cancel");
            if (listener != null) {
                listener.onResponse(true, SUCCESS, "not in navi mode");
            }
            return;
        }

        Result result = commandApi.cancelNavi(forceStop);
        mCurrentRealPose.set(null);
        Log.d(TAG, "cancelNavigation result = " + result.toString());
        if (result.getCode() != Result.CODE_SUCCESS) {
            if (listener != null) {
                listener.onResponse(false, result.getCode(), result.getPayload());
            }
        } else {
            updateTargetPose(TargetPose.RESULT_CANCELED, NavigationResult.RESULT_NAVI_EVENT_RESULT_CANCELED);
            if (listener != null) {
                listener.onResponse(true, result.getCode(), result.getPayload());
            }
        }
    }

    @Override
    public void startHumanFollowing(String followId, int lostFindTimeout, ChassisResListener listener) {
        Log.d(TAG, "startHumanFollowing: followId = " + followId + ", lostFindTimeout = " + lostFindTimeout);

        //启动模式
        int startModeLevel = 0;
        int brakeModeLevel = 0;
        NavMode navMode = TargetPose.DEFAULT_NAV_MODE;
        if (navMode != null) {
            startModeLevel = navMode.getStartModeLevel();
            brakeModeLevel = navMode.getBrakeModeLevel();
        }
        Log.d(TAG, "startHumanFollowing:  brakeModeLevel = " + brakeModeLevel + ", startModeLevel = " + startModeLevel);
        SetGoalProtoWrapper.BrakeModeProto breakModeParam = getBreakMode(brakeModeLevel);

        //速度，范围0.5-1.5m/s，默认0.8m/s
        double navLinear = 0.8;
        double navAngular;
        double maxJerk = 0;
        float followSpeed =  RobotSettingApi.getInstance().getRobotFloat("robot_setting_follow_navSpeed");
        Log.d(TAG, "startHumanFollowing: followSpeed = " + followSpeed);
        if (followSpeed >= 0.5 && followSpeed <= 1.5) {
            navLinear = followSpeed;
        }
        StartModeResetParam p = toResetNavigationParam(navLinear, startModeLevel);
        Log.d(TAG, "startHumanFollowing: " + p.toString());
        navAngular = p.mAng;
        maxJerk = p.mJ;
        VelocityProtoWrapper.VelocityProto velocity = VelocityProtoWrapper.VelocityProto.newBuilder()
                .setAngular(navAngular)
                .setLiner(navLinear)
                .build();
        VelocityProtoWrapper.VelocityProto accelerationProto = VelocityProtoWrapper.VelocityProto.newBuilder()
                .setLiner(p.mLineAcc)
                .setAngular(p.mAngAcc)
                .build();

        Log.d(TAG, "startHumanFollowing: " + API_IN);
        Result result = commandApi.startHumanFollowing(followId, lostFindTimeout, breakModeParam, velocity, accelerationProto, maxJerk);
        Log.d(TAG, "startHumanFollowing: " + API_BACK + ", result = " + result.toString());
        //结构相似，临时借用ChargeResult和mGoChargelistener，后续有区分要修改
        this.mGoChargelistener = listener;
        if (result.getCode() == SUCCESS) {
            listener.onResponse(true, result.getCode(), Definition.NAVIGATION_HUMAN_START_SUCCESS);
        } else {
            listener.onResponse(false, result.getCode(), Definition.NAVIGATION_HUMAN_START_FAILED);
        }
    }

    @Override
    public void stopHumanFollowing(ChassisResListener listener) {
        Log.d(TAG, "stopHumanFollowing: " + API_IN);
        Result result = commandApi.stopHumanFollowing();
        Log.d(TAG, "stopHumanFollowing: " + API_BACK + ", result = " + result.toString());
        //结构相似，临时借用ChargeResult和mGoChargelistener，后续有区分要修改
        this.mGoChargelistener = listener;
        if (result.getCode() == SUCCESS) {
            listener.onResponse(true, result.getCode(), "Success");
        } else {
            listener.onResponse(false, result.getCode(), "Failed");
        }
    }

    @Override
    public void detectQrCodeByPic(String path, ChassisResListener listener) {
        Log.d(TAG, "detectQrCodeByPic path = " + path);
        //目前检测，是以拷贝图片到底盘可以访问的目录实现的，后续要优化为传入protobuf数据
        File targetFile = FileUtils.moveOrCopyFile(path, "/data/lxc/rootfs/tmp/");
        if (targetFile == null) {
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "Failed");
            }
            return;
        }
        String targetTempPath = "/tmp/" + targetFile.getName();
        Log.d(TAG, "detectQrCodeByPic: " + API_IN + ", targetTempPath = " + targetTempPath);//tmp下，文件名为file
        Result<CommonProtoWrapper.FollowingTagIdProto> result = commandApi.getFollowingTagId(targetTempPath);
        Log.d(TAG, "detectQrCodeByPic: " + API_BACK + ", result = " + result.toString());
        CommonProtoWrapper.FollowingTagIdProto followingTagIdProto = result.getPayload();
        if (result.getCode() == SUCCESS && followingTagIdProto.getResult()) {
            listener.onResponse(true, result.getCode(), followingTagIdProto.getId());
        } else {
            listener.onResponse(false, result.getCode(), "Failed");
        }
        FileUtils.deleteFile(targetFile);
    }

    /**
     * 如果是【点位停靠自动适配机器人模式】，需计算真正的自适应半径值。carry机型
     * 无货架 : 业务值
     * 标准货架 : 业务值 + (有货架半径 - 无货架半径)
     * 自定义 ：业务值 + (有货架半径 - 无货架半径)， 需要根据 (有货架半径 - 无货架半径) 的正负情况进行区分判断
     */
    private float calculateRobotAdaptRange(float destinationRange) {
        int dockingAutoAdaptMode = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_DOCKING_AUTO_ADAPT_MODE);
        if (dockingAutoAdaptMode == 0) {
            return destinationRange;
        }
        int structureMode = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_NAVI_ROBOT_STRUCTURE_MODE);
        float standardRadius = 0.4F; // 标准半径
        float factoryRobotRadius = RobotSettingApi.getInstance().getRobotFloat(Definition.ROBOT_SETTING_NAVI_FACTORY_ROBOT_RADIUS);
        float naviLethalRadiusOffset = factoryRobotRadius - standardRadius;
        Log.d(TAG, "structureMode = " + structureMode + ", factoryRobotRadius = " + factoryRobotRadius + ", naviLethalRadiusOffset = " + naviLethalRadiusOffset);
        float finalDestinationRange = destinationRange; // 最终结果
        switch (structureMode) {
            case 0: // 无货架
                finalDestinationRange = destinationRange;
                break;
            case 1: // 标准货架
                finalDestinationRange = destinationRange + naviLethalRadiusOffset;
                break;
            case 2: // 自定义货架
                if (naviLethalRadiusOffset > 0) {
                    finalDestinationRange = destinationRange + naviLethalRadiusOffset;
                } else {
                    finalDestinationRange = destinationRange;
                }
                break;
            default:
                break;
        }
        Log.d(TAG, "calculateRobotAdaptRange, finalDestinationRange: " + finalDestinationRange);
        return finalDestinationRange;
    }
}