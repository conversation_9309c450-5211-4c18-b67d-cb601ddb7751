package com.ainirobot.navigationservice.utils;

/**
 * 将人脸输入的角度，转化为雷达数据对应的角度
 * 人脸从左到右为 -90° 到 90°
 * 雷达从左到右为 90° 到 -90°
 * 雷达数据保存的是弧度，最后把对应好的角度再转化为弧度
 */
public class FaceAngleRangeConverter {
    private static final double PI = Math.PI;
    
    // 角度转弧度
    private static double toRadians(double degree) {
        return degree * PI / 180.0;
    }
    
    // 将角度范围数组转换为弧度范围数组
    private static double[] convertToRadians(double[] degreeRanges) {
        double[] radianRanges = new double[degreeRanges.length];
        for (int i = 0; i < degreeRanges.length; i++) {
            radianRanges[i] = toRadians(degreeRanges[i]);
        }
        return radianRanges;
    }

    public static double[] convertAngleRange(double startAngle, double endAngle) {
        // 确保输入角度在 -90 到 90 度之间
        if (startAngle < -90 || startAngle > 90 || endAngle < -90 || endAngle > 90) {
            throw new IllegalArgumentException("输入角度必须在 -90 到 90 度之间");
        }
        if (startAngle > endAngle) {
            throw new IllegalArgumentException("起始角度必须小于等于结束角度");
        }
        
        // 将角度范围转换为弧度范围
        return convertToRadians(new double[]{-endAngle, -startAngle});
    }
}
