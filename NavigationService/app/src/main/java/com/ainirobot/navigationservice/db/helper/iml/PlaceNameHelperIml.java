package com.ainirobot.navigationservice.db.helper.iml;

import com.ainirobot.navigationservice.db.entity.PlaceName;

import java.util.List;


public interface PlaceNameHelperIml extends BaseHelper<PlaceName> {

    String[] getPlaceIdsByName(String placeName);

    String[] getPlaceIdsByNameList(String[] nameArr);

    List<PlaceName> getPlaceNameByPlaceId(String[] placeIds);

    void updatePlace(String[] idArr, List<String> newNameList, String language);

    void initPlaceNameData(List<PlaceName> nameList);

    boolean updatePlaceNames(List<PlaceName> placeNames);

    void deletePlaceNameByPlaceId(String[] placeIds);

    boolean hasPlaceNameData();
}
