package com.ainirobot.navigationservice.utils;

import android.content.Context;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.hardware.RobotCore;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.navigationservice.ApplicationWrapper;
import com.ainirobot.navigationservice.commonModule.data.DataManager;
import com.ainirobot.navigationservice.db.NavigationDataManager;

/**
 * 点图分离模式工具类
 */
public class PosSeparateUtils {
    private static final String TAG = "Navi-PosSeparateUtils";

    //点图分离模式，1：分离模式，0：同步模式，默认为1
    private static volatile int sNapPosSeparate = Definition.SETTING_DEFAULT_MAP_POS_SEPARATE;

    private static volatile Context sContext = null;

    /**
     * 是否点图分离模式
     *
     * @return true:分离模式 false:同步模式
     */
    public static boolean isNapPosSeparate(Context context) {
        updateContext(context);
        int value = Definition.SETTING_DEFAULT_MAP_POS_SEPARATE;
        if (RobotSettingApi.getInstance().hasRobotSetting(Definition.ROBOT_SETTING_MAP_POS_SEPARATE)) {
            value = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_MAP_POS_SEPARATE);
            Log.d(TAG, "isNapPosSeparate: hasRobotSetting: value=" + value);
        } else {
            //读Global
            int napPosSeparate = Settings.Global.getInt(
                    sContext.getContentResolver(),
                    Definition.ROBOT_SETTING_MAP_POS_SEPARATE,
                    Definition.SETTING_DEFAULT_MAP_POS_SEPARATE);
            Log.d(TAG, "isNapPosSeparate:ReadGlobal: napPosSeparate=" + napPosSeparate);
            value = napPosSeparate;
        }
        sNapPosSeparate = value;
        Log.d(TAG, "isNapPosSeparate:Final: " + value);
        return value == 1;
    }

    /**
     * 监听点图分离模式setting变化
     * 判断点图同步状态由1-0时执行本地列表初始化逻辑
     */
    public static void listenNapPosSeparate(Context context) {
        updateContext(context);
        Log.d(TAG, "listenNapPosSeparate: ");
        registerGlobalContentObserver(Definition.ROBOT_SETTING_MAP_POS_SEPARATE,
                new ContentObserver(new Handler(Looper.getMainLooper())) {

                    @Override
                    public void onChange(boolean selfChange, Uri uri) {
                        super.onChange(selfChange, uri);
                        String lastPath = uri.getLastPathSegment();
                        Log.d(TAG, "onChange: selfChange : " + selfChange + ", String uri : " + lastPath);
                        if (!selfChange && Definition.ROBOT_SETTING_MAP_POS_SEPARATE.equals(lastPath)) {
                            int oldNapPosSeparate = sNapPosSeparate;
                            int newNapPosSeparate = Settings.Global.getInt(
                                    sContext.getContentResolver(),
                                    Definition.ROBOT_SETTING_MAP_POS_SEPARATE,
                                    Definition.SETTING_DEFAULT_MAP_POS_SEPARATE);
                            Log.d(TAG, "onChange: oldNapPosSeparate=" + oldNapPosSeparate + ", newNapPosSeparate=" + newNapPosSeparate);
                            if(oldNapPosSeparate == 1 && newNapPosSeparate == 0) {
                                //点图同步状态由1-0时执行本地列表初始化逻辑
                                Log.d(TAG, "onChange: napPosSeparate change from 1 to 0, init local place info data");
                                NavigationDataManager.getInstance().initLocalPlaceInfoData();
                                //主动触发core中点位列表更新
                                DataManager.getInstance().sendPlaceDataUpdateReport();//点图同步状态由1-0后初始化本地列表后，发送更新通知
                                //触发一个新的status上报，通知业务层更新点位
                                RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE,
                                        Definition.REPORT_NAVI_CONFIG, "status_place_list_change");
                            }
                            sNapPosSeparate = newNapPosSeparate;
                        }
                    }
                });
    }

    //如果sContext为空，则更新
    public static void updateContext(Context context) {
        if (sContext == null) {
            sContext = context;
        }
    }

    private static void registerGlobalContentObserver(String key, ContentObserver observer) {
        sContext.getContentResolver().registerContentObserver(
                getGlobalUriFor(key), false, observer);
    }

    private static Uri getGlobalUriFor(String key) {
        return Settings.Global.getUriFor(key);
    }

}
