package com.ainirobot.navigationservice.commonModule.bi.report;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class BiNaviSettingReport extends BaseBiReport {

    private static final String TABLE_NAME = "base_robot_navi_setting";
    private static final String CTIME = "ctime";
    private static final String WAKEUP_ID = "wakeup_id";
    private static final String VALUE = "value";
    private static final String TYPE = "type";

    private static final String GB_RGBD_SWITCH = "gb_rgbd_switch";
    private static final String NAVI_RGBD_AVOID_OBS = "navi_rgbd_avoid_obs";
    private static final String NAVI_MULTI_ROBOT_SETTING = "navi_multi_robot_setting";
    private static final String NAVI_DEV_TYPE = "navi_dev_type";

    public static final String MODE = "mode";
    public static final int SWITCH_MODE_OPEN = 1;
    public static final int SWITCH_MODE_CLOSE = 2;

    public static final String SERVER_SWITCH = "server_switch";
    public static final int SWITCH_SERVER_MODE_DEFAULT = 0;
    public static final int SWITCH_SERVER_MODE_OPEN = 1;
    public static final int SWITCH_SERVER_MODE_CLOSE = 2;
    public static final int SWITCH_SERVER_MODE_ACTIVE = 3;

    public BiNaviSettingReport() {
        super(TABLE_NAME);
        initData();
    }

    private void initData() {

    }

    public void reportRgbdStatus(String value) {
        addData(TYPE, GB_RGBD_SWITCH);
        addData(VALUE, value);
        report();
    }

    public void reportRgbdAvoidObsStatus(String value) {
        addData(TYPE, NAVI_RGBD_AVOID_OBS);
        addData(VALUE, value);
        report();
    }

    public void reportMultiRobotSetting(String value) {
        addData(TYPE, NAVI_MULTI_ROBOT_SETTING);
        addData(VALUE, value);
        report();
    }

    public void reportNaviDevType(String value) {
        addData(TYPE, NAVI_DEV_TYPE);
        addData(VALUE, value);
        report();
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }
}
