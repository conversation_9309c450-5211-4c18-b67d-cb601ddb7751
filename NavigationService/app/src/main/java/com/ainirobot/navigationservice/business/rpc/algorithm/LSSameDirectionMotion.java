package com.ainirobot.navigationservice.business.rpc.algorithm;

import android.util.Log;

import com.ainirobot.navigationservice.business.rpc.SpeedBean;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * single linear speed change
 *
 * mode 1:
 *
 * max acc:0.1
 * unit time: 125
 *
 * mode 2:
 *
 * max acc:0.15
 *
 * unit time: 180
 *
 *
 * @version V1.0.0
 * @date 2019/11/15 15:00
 */
public class LSSameDirectionMotion extends MotionAlgorithm {


    private static final String LINEAR_SPEED_MAX_ACC = "0.15";
    private static final String MAX_INCREASE_TIMES = "5";
    private volatile boolean isCancel = false;

    private static final BigDecimal ACC_TIMES = new BigDecimal(MAX_INCREASE_TIMES);
    private static final BigDecimal MAX_ACC = new BigDecimal(LINEAR_SPEED_MAX_ACC);

    private static final int INCREASE_SPEED_TIME_PERIOD = 180;

    public LSSameDirectionMotion(SpeedBean target) {
        super(target);
    }

    @Override
    public void motion() {
        BigDecimal targetLS = target.getLinearSpeed();
        BigDecimal currentLS = current.getLinearSpeed();
        BigDecimal speedDiff = targetLS.subtract(currentLS);

        if (speedDiff.abs().compareTo(MAX_ACC) <= 0) {
            motion(targetLS.floatValue(), 0);
            Log.i(TAG, "motion: speed diff= " + speedDiff);
            return;
        }

        BigDecimal acc = calculateAcc(speedDiff);
        Log.i(TAG, "motion: acc=" + acc);
        BigDecimal accTimes = calculateAccTimes(acc, speedDiff);
        Log.i(TAG, "motion: accTimes~" + accTimes.intValue()+" accTimes="+accTimes);

        for (int i = 0; i < accTimes.intValue() && !isCancel; i++) {
            BigDecimal except = currentLS.add(acc);
            if (i == accTimes.intValue()-1) {
                Log.i(TAG, "motion: last acc");
                currentLS = targetLS;
            } else {
                currentLS = except;
            }
            Log.i(TAG, "motion: currentLS=" + currentLS);
            motion(currentLS.floatValue(), 0);
            try {
                Thread.sleep(INCREASE_SPEED_TIME_PERIOD);
            } catch (InterruptedException e) {
                isCancel = true;
                Log.i(TAG, "motion: LSSameDirectionMotion interrupted!");
            }
            current.setLinearSpeed(currentLS);
        }
    }

    private BigDecimal calculateAccTimes(BigDecimal acc, BigDecimal speedDiff) {
        if (acc.abs().compareTo(MAX_ACC) < 0) {
            return ACC_TIMES;
        }
        return speedDiff.abs().divide(MAX_ACC,0, RoundingMode.UP);
    }

    private BigDecimal calculateAcc(BigDecimal speedDiff) {
        BigDecimal acc = speedDiff.divide(ACC_TIMES, 2, RoundingMode.UP);
        int compare = acc.abs().compareTo(MAX_ACC);
        if (compare > 0) {
            Log.i(TAG, "calculateAcc: acc more than max :" + acc);
            acc = acc.compareTo(BigDecimal.ZERO) >= 0 ? MAX_ACC : MAX_ACC.negate();
        }
        Log.i(TAG, "calculateAcc: acc=" + acc);
        return acc;
    }
}
