/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.tk1;

import java.io.DataOutput;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;

public class Message implements Serializable, Cloneable {

    private final int messageType;
    private final long time;

    private static final int HEAD_FIRST = 0XAA;
    private static int HEAD_SECOND = 0X55;

    public static final int MSG_TYPE_STRING = 1;
    public static final int MSG_TYPE_ROBOT_POSE = 2;
    public static final int MSG_TYPE_POSE = 3;
    public static final int MSG_TYPE_GLOBAL_MAP = 4;
    public static final int MSG_TYPE_LOCAL_MAP = 14;
    public static final int MSG_TYPE_GLOBAL_PATH = 5;
    public static final int MSG_TYPE_VELOCITY_PATH = 6;
    public static final int MSG_TYPE_POINT_CLOUD = 7;
    public static final int MSG_TYPE_LINES = 8;
    public static final int MSG_TYPE_LOCAL_PATH = 9;
    public static final int MSG_TYPE_GOAL = 11;
    public static final int MSG_TYPE_ESTIMATE = 12;
    public static final int MSG_TYPE_POSE_COVARIANCE = 13;
    public static final int MSG_TYPE_NAV_VELOCITY = 14;
    public static final int MSG_TYPE_NAV_MODE = 15;

    public static final int MSG_TYPE_POINT = 101;
    public static final int MSG_TYPE_QUATERNION = 102;

    public Message(int messageType) {
        this.messageType = messageType;
        time = System.currentTimeMillis();
    }

    public long getTime() {
        return time;
    }

    public int getMessageType() {
        return messageType;
    }

    public void readData(ReverseInputStream in) throws IOException {

    }

    public void writeTo(DataOutput out) throws IOException {

    }

    public final void writeTo(DataOutput out, boolean isWriteHead) throws IOException {
        if (isWriteHead) {
            writeHead(out);
            out.write(getMessageType());
        }
        writeTo(out);
    }

    public long getLength() {
        return -1;
    }

    public static Message newInstance(int type) {
        switch (type) {
            case MSG_TYPE_GLOBAL_MAP:
                return new Map(MSG_TYPE_GLOBAL_MAP);

            case MSG_TYPE_LOCAL_MAP:
                return new Map(MSG_TYPE_LOCAL_MAP);

            case MSG_TYPE_POSE:
                return new Pose3D();

            case MSG_TYPE_STRING:
                return new StringMessage();

            case MSG_TYPE_ROBOT_POSE:
                return new Pose(MSG_TYPE_ROBOT_POSE);

            case MSG_TYPE_GOAL:
                return new Pose();

            case MSG_TYPE_ESTIMATE:
                return new Estimate();

            case MSG_TYPE_POSE_COVARIANCE:
                return new PoseWithCovariance();

            case MSG_TYPE_GLOBAL_PATH:
                return new Points(MSG_TYPE_GLOBAL_PATH);

            case MSG_TYPE_LOCAL_PATH:
                return new Points(MSG_TYPE_LOCAL_PATH);

            case MSG_TYPE_VELOCITY_PATH:
                return new Points(MSG_TYPE_VELOCITY_PATH);

            case MSG_TYPE_POINT_CLOUD:
                return new Points(MSG_TYPE_POINT_CLOUD);

            case MSG_TYPE_LINES:
                return new Points(MSG_TYPE_LINES);
        }
        return null;
    }


    public static boolean verifyMessage(InputStream in) throws IOException {
        int sign = in.read();
//        Log.d(TAG, "receive message head : " + sign);
        if (sign != Message.HEAD_FIRST) {
            return false;
        }

        sign = in.read();
//        Log.d(TAG, "receive message head : " + sign);
        return sign == Message.HEAD_SECOND;
    }

    private void writeHead(DataOutput out) throws IOException {
        out.write(Message.HEAD_FIRST);
        out.write(Message.HEAD_SECOND);
    }

    @Override
    public Message clone() {
        try {
            return (Message) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
            return null;
        }
    }
}
