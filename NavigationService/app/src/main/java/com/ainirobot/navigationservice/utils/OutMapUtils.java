package com.ainirobot.navigationservice.utils;

import android.util.Log;
import java.util.Timer;
import java.util.TimerTask;

/**
 * Created on 2020-03-24.
 */
public class OutMapUtils {
    private static String TAG = "OutMapUtils ChassisClientTk1Impl";

    private static OutMapUtils mInstance;

    public static synchronized OutMapUtils getInstance() {
        if (mInstance == null) {
            Log.e(TAG, " getInstance " + mInstance);
            mInstance = new OutMapUtils();
        }
        return mInstance;
    }

    /**
     * 正常区域，可以到
     */
    private static final int MAP_STATUS_NORMAL_AREA = 0;
    /**
     * 危险区，不可以到
     */
    private static final int MAP_STATUS_DANGEROUS_AREA = 1;
    /**
     * 禁行区，不可以到
     */
    public static final int MAP_STATUS_OBSTACLE_AREA = 2;
    /**
     * 地图外，不可以到
     */
    public static final int MAP_STATUS_OUTSIDE_AREA = 3;
    /**
     * 无法逃脱
     * 和禁行区区分，处于地图禁行线上
     */
    public static final int MAP_STATUS_NO_ESCAPE = 4;

    private static int mLastStatus = -1;

    private static Timer mTimer;

    private static OutMapLiniser mLiniser;

    private OutMapUtils() {
    }

    public void checkOutMap(int status, OutMapLiniser liniser) {
//        Log.i(TAG, "checkOutMap: status " + status);
        mLiniser = liniser;
        switch (status) {
            case MAP_STATUS_OBSTACLE_AREA:
                if (status != mLastStatus) {
                    mLastStatus = status;
                    resetTimer();
                }
                break;
            case MAP_STATUS_OUTSIDE_AREA:
                if (status != mLastStatus) {
                    mLastStatus = status;
                    resetTimer();
                }
                break;
            default:
                mLastStatus = status;
                if (mTimer != null) {
                    //上报关闭弹窗的消息
                    mLiniser.outMap(MAP_STATUS_NORMAL_AREA);
                }
                cancleTimer();
                break;
        }
    }

    private static void resetTimer() {
        cancleTimer();
        if (mTimer == null) {
            mTimer = new Timer();
        }
        TimerTask task = new TimerTask() {
            @Override
            public void run() {
                Log.i(TAG, "run: out map " + mLastStatus);
                mLiniser.outMap(mLastStatus);
            }
        };
        mTimer.schedule(task, 10 * 1000);
    }

    private static void cancleTimer() {
//        Log.i(TAG, "cancleTimer: " + mTimer);
        if (mTimer != null) {
            mTimer.cancel();
            mTimer = null;
        }
    }

    public interface OutMapLiniser {
        void outMap(int params);
    }
}
