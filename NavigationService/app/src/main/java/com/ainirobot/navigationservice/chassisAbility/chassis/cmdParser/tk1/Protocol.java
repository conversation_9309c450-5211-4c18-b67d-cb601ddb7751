/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1;

import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_REPORT_STATISTIC;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_RES_COMMAND;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_UPDATE_EVENT;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_UPDATE_GLOBAL_MAP;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_UPDATE_LASER;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_UPDATE_MAPPING_POSE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_UPDATE_POSE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_UPDATE_REALTIME_MAP;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_UPDATE_VELOCITY;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_WORKING_MODE_STATE_CHANGE;

import android.util.Log;

import com.ainirobot.navigationservice.beans.MappingPose;
import com.ainirobot.navigationservice.beans.tk1.Event;
import com.ainirobot.navigationservice.beans.tk1.Laser;
import com.ainirobot.navigationservice.beans.tk1.MapData;
import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.beans.tk1.PoseState;
import com.ainirobot.navigationservice.beans.tk1.RelocateMode;
import com.ainirobot.navigationservice.beans.tk1.RobotPose;
import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.tk1.SensorStatus;
import com.ainirobot.navigationservice.beans.tk1.Statistic;
import com.ainirobot.navigationservice.beans.tk1.SystemData;
import com.ainirobot.navigationservice.beans.tk1.Velocity;
import com.ainirobot.navigationservice.beans.tk1.WorkStateMode;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import ninjia.android.proto.CancelAutoMovingProtoWrapper.CancelAutoMovingProto;
import ninjia.android.proto.ChassisPacketProtoWrapper;
import ninjia.android.proto.CommonProtoWrapper;
import ninjia.android.proto.CostmapProtoWrapper.CostmapProto;
import ninjia.android.proto.DeleteMapProtoWrapper.DeleteMapProto;
import ninjia.android.proto.GetGlobalMapFileProtoWrapper.GetGlobalMapFileProto;
import ninjia.android.proto.GetLocationStateProtoWrapper;
import ninjia.android.proto.GetMapListProtoWrapper.GetMapListProto;
import ninjia.android.proto.GetMapListProtoWrapper.MapListProto;
import ninjia.android.proto.GetRoverConfigProtoWrapper.GetRoverConfigProto;
import ninjia.android.proto.GetSystemInformationProtoWrapper.GetSystemInformationProto;
import ninjia.android.proto.GetSystemInformationProtoWrapper.SystemInformationProto;
import ninjia.android.proto.GetWorkingModeStateProtoWrapper;
import ninjia.android.proto.MotionModeProtoWrapper;
import ninjia.android.proto.MotionModeProtoWrapper.MotionModeProto;
import ninjia.android.proto.PackLogProtoWrapper.LogProto;
import ninjia.android.proto.PackLogProtoWrapper.PackLogProto;
import ninjia.android.proto.Pose2dProtoWrapper.Pose2dProto;
import ninjia.android.proto.RelocateDataProtoWrapper.RelocateDataProto;
import ninjia.android.proto.RelocateDataProtoWrapper.RelocateModeProto;
import ninjia.android.proto.ReportStatisticProtoWrapper.StatisticProto;
import ninjia.android.proto.RoverConfigProtoWrapper.RoverConfigProto;
import ninjia.android.proto.RoverPacketProtoWrapper;
import ninjia.android.proto.RoverPacketProtoWrapper.RoverPacketCodeProto;
import ninjia.android.proto.RoverPacketProtoWrapper.RoverPacketHeaderProto;
import ninjia.android.proto.RoverPacketProtoWrapper.RoverPacketProto;
import ninjia.android.proto.SelfCheckAllProtoWrapper.SelfCheckAllProto;
import ninjia.android.proto.SelfCheckResultProtoWrapper.SelfCheckResultProto;
import ninjia.android.proto.SendPrimitiveMovingCommandProtoWrapper.SendPrimitiveMovingCommandProto;
import ninjia.android.proto.SetGoalProtoWrapper;
import ninjia.android.proto.SetGoalProtoWrapper.SetGoalProto;
import ninjia.android.proto.SetMotionModeProtoWrapper.SetMotionModeProto;
import ninjia.android.proto.SetNavigationMapProtoWrapper.MapTypeProto;
import ninjia.android.proto.SetNavigationMapProtoWrapper.SetNavigationMapProto;
import ninjia.android.proto.SetPoseEstimateProtoWrapper.SetPoseEstimateProto;
import ninjia.android.proto.SetRelocateProtoWrapper.SetRelocateProto;
import ninjia.android.proto.SetRoverConfigProtoWrapper.SetRoverConfigProto;
import ninjia.android.proto.SetTimeProtoWrapper.SetTimeProto;
import ninjia.android.proto.SetTimeProtoWrapper.TimeProto;
import ninjia.android.proto.SetWorkingModeProtoWrapper.SetWorkingModeProto;
import ninjia.android.proto.StartCreateMapProtoWrapper.StartCreateMapProto;
import ninjia.android.proto.StartMappingProtoWrapper.StartMappingProto;
import ninjia.android.proto.StopCreateMapProtoWrapper.MapNameProto;
import ninjia.android.proto.StopCreateMapProtoWrapper.StopCreateMapProto;
import ninjia.android.proto.StopMappingProtoWrapper.StopMappingProto;
import ninjia.android.proto.TakeSnapshotProtoWrapper.SnapshotProto;
import ninjia.android.proto.TakeSnapshotProtoWrapper.TakeSnapshotProto;
import ninjia.android.proto.UpdateEventProtoWrapper.RoverEventProto;
import ninjia.android.proto.UpdateLaserProtoWrapper.LaserDataProto;
import ninjia.android.proto.UpdateLaserProtoWrapper.LaserProto;
import ninjia.android.proto.VelocityProtoWrapper.VelocityProto;
import ninjia.android.proto.WorkingModeProtoWrapper;
import ninjia.android.proto.WorkingModeProtoWrapper.WorkingModeProto;

public enum Protocol {
    GET_WORKING_MODE_STATE {
        @Override
        public Message create(Object... args) {
            return GetWorkingModeStateProtoWrapper.GetWorkingModeStateProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.GET_WORKING_MODE_STATE, null))
                    .build();
        }

        @Override
        public Object parse(String params, ByteString content) {
            try {
                WorkingModeProtoWrapper.WorkingModeStateProto workingModeProto =
                        WorkingModeProtoWrapper.WorkingModeStateProto.parseFrom(content);
                return new WorkStateMode(workingModeProto.getModeValue(), workingModeProto.getStateValue());
            } catch (IOException e) {
                e.printStackTrace();
                return null;
            }
        }
    },

    SET_WORKING_MODE {
        @Override
        public Message create(Object... args) {
            int workMode = (int) args[0];

            WorkingModeProto workingModeProto = WorkingModeProto.forNumber(workMode);

            return SetWorkingModeProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.SET_WORKING_MODE, null))
                    .setWorkingMode(workingModeProto)
                    .build();
        }
    },

    SET_ROVER_CONFIG {
        @Override
        public Message create(Object... args) {
            RoverConfig config = (RoverConfig) args[0];

            RoverConfigProto.Builder builder = RoverConfigProto.newBuilder()
                    //底盘不再支持这些接口配置
//                    .setEnableCamera(config.isEnableCamera())
//                    .setEnableSonar(config.isEnableSonar())
//                    .setEnableRgbd(config.isEnableRgbd())
//                    .setEnableFishEye(config.isEnableFishEye())
//                    .setAvoidStopDetectionDistance(config.getAvoidStopDetectionDistance())
//                    .setEnableDriveOnRight(config.isEnableKeepRight())
//                    .setMultiRobotServerPort(config.getMultiRobotServerPort())
//                    .setMultiRobotClientId(config.getMultiRobotClientId())
//                    .setEnableCollisionAvoid(config.isEnableCollisionAvoid())
//                    .setEnableStuckAvoid(config.isEnableStuckAvoid())
//                    .setEnableCoveringMapObstacles(config.isEnableConveringMapObstacles())
//                    .setEnablePathWidthLimit(config.isEnablePathWidthLimit())
//                    .setMultiRobotServerHost(config.getServerIp())

                    .setRosServerIp(config.getRosIp())
                    .setDeviceTypeValue(config.getDeviceType())
                    .setScenesTypeValue(config.getScenesType())
                    .setEnableIr(config.isEnableIR())
                    .setEnableVision(config.isEnableVision())
                    .setEnableMultiRobot(config.isEnableMultiRobot())
                    .setEnableAvoidObs(true)
                    .setEnableTargetLocate(config.isEnableTargetLocate())
                    .setEnableLineTracking(config.isEnableLineTracking())
                    .setEnableCheckCliff(config.isEnableCheckCliff());

              // rover_sdk  2.3.5.X版本中　setMaxLostDistance接口修改,tk1业务逻辑中上层无需设置,sdk使用默认值10
//            RoverConfigProtoWrapper.MaxLostDistanceProto proto = config.isOpenMaxLostDistance()
//                    ? RoverConfigProtoWrapper.MaxLostDistanceProto.LOST_DISTANCE_LEVEL3 :
//                RoverConfigProtoWrapper.MaxLostDistanceProto.LOST_DISTANCE_LEVEL1;
//            if (proto != null) {
//                builder.setMaxLostDistance(proto);
//            }
            RoverConfigProto roverConfigProto = builder.build();

            return SetRoverConfigProto
                    .newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.SET_ROVER_CONFIG, null))
                    .setRoverConfig(roverConfigProto)
                    .build();
        }
    },

    GET_ROVER_CONFIG {
        @Override
        public Message create(Object... args) {
            return GetRoverConfigProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.GET_ROVER_CONFIG, null))
                    .build();
        }

        @Override
        public Object parse(String params, ByteString content) {
            try {
                RoverConfigProto config = RoverConfigProto.parseFrom(content);
                String rosIp = config.getRosServerIp();
                int deviceType = config.getDeviceTypeValue();
                int scenesType = config.getScenesTypeValue();
                boolean isEnableIR = config.getEnableIr();
                boolean isEnableVision = config.getEnableVision();
                RoverConfig roverConfig = new RoverConfig(rosIp);

                //底盘不再支持这些接口配置
//                boolean isEnableCamera = config.getEnableCamera();
//                boolean isEnableFishEye = config.getEnableFishEye();
//                boolean isEnableRgbd = config.getEnableRgbd();
//                boolean isEnableSonar = config.getEnableSonar();
//                double avoidStopDetectionDistance = config.getAvoidStopDetectionDistance();

//                roverConfig.setEnableCamera(isEnableCamera);
//                roverConfig.setEnableFishEye(isEnableFishEye);
//                roverConfig.setEnableRgbd(isEnableRgbd);
//                roverConfig.setEnableSonar(isEnableSonar);
//                roverConfig.setAvoidStopDetectionDistance(avoidStopDetectionDistance);
//                roverConfig.setEnableCollisionAvoid(config.getEnableCollisionAvoid());
//                roverConfig.setEnableStuckAvoid(config.getEnableStuckAvoid());
//                roverConfig.setServerIp(config.getMultiRobotServerHost());
//                roverConfig.setEnableKeepRight(config.getEnableDriveOnRight());
//                roverConfig.setMultiRobotServerPort(config.getMultiRobotServerPort());
//                roverConfig.setMultiRobotClientId(config.getMultiRobotClientId());
//                roverConfig.setEnableCollisionAvoid(config.getEnableCollisionAvoid());
//                roverConfig.setEnableStuckAvoid(config.getEnableStuckAvoid());
//                roverConfig.setEnableRgbdAvoidObs(config.getEnableRgbd());
//                roverConfig.setEnableConveringMapObstacles(config.getEnableCoveringMapObstacles());
//                roverConfig.setEnablePathWidthLimit(config.getEnablePathWidthLimit());

                roverConfig.setDeviceType(deviceType);
                roverConfig.setScenesType(scenesType);
                roverConfig.setEnableIR(isEnableIR);
                roverConfig.setEnableVision(isEnableVision);
                roverConfig.setEnableTargetLocate(config.getEnableTargetLocate());
                roverConfig.setEnableLineTracking(config.getEnableLineTracking());
                roverConfig.setEnableCheckCliff(config.getEnableCheckCliff());
//                RoverConfig.MaxLostDistance maxLostDistance = roverConfig.generateLocalBean(config.getMaxLostDistance());
//                if (maxLostDistance != null) {
//                    roverConfig.setMaxLostDistance(maxLostDistance);
//                }
                //rover_sdk  2.3.5.X版本或后续　setMaxLostDistance接口修改,tk1业务逻辑中上层无需设置,sdk底层使用默认值10
//                roverConfig.setOpenMaxLostDistance(false);

                roverConfig.setEnableMultiRobot(config.getEnableMultiRobot());
                roverConfig.setEnableTargetLocate(config.getEnableTargetLocate());
                roverConfig.setEnableLineTracking(config.getEnableLineTracking());
                roverConfig.setEnableCheckCliff(config.getEnableCheckCliff());
                return roverConfig;
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
                return null;
            }
        }
    },

    GET_SYSTEM_INFORMATION {
        @Override
        public Message create(Object... args) {
            return GetSystemInformationProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.GET_SYSTEM_INFORMATION, null))
                    .build();
        }

        @Override
        public Object parse(String params, ByteString content) {
            try {
                SystemInformationProto systemInformationProto =
                        SystemInformationProto.parseFrom(content);
                return new SystemData(systemInformationProto);
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
                return null;
            }
        }
    },

    GET_MOTION_MODE {
        @Override
        public Message create(Object... args) {
            return RoverPacketProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.GET_MOTION_MODE, null))
                    .build();
        }

        @Override
        public Object parse(String params, ByteString content) {
            try {
                MotionModeProtoWrapper.MotionModeMsgProto motionModeProto =
                        MotionModeProtoWrapper.MotionModeMsgProto.parseFrom(content);
                return motionModeProto.getMode().getNumber();
            } catch (IOException e) {
                e.printStackTrace();
                return null;
            }
        }
    },

    SET_MOTION_MODE {
        @Override
        public Message create(Object... args) {
            int motionMode = (int) args[0];
            MotionModeProto motionModeProto = MotionModeProto.forNumber(motionMode);

            return SetMotionModeProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.SET_MOTION_MODE, null))
                    .setMotionMode(motionModeProto)
                    .build();
        }
    },

    SEND_PRIMITIVE_MOVING_COMMAND {
        @Override
        public Message create(Object... args) {
            double angularSpeed = (double) args[0];
            double linearSpeed = (double) args[1];

            VelocityProto velocityProto = VelocityProto.newBuilder()
                    .setAngular(angularSpeed)
                    .setLiner(linearSpeed)
                    .build();

            return SendPrimitiveMovingCommandProto.newBuilder()
                    .setHeader(
                            buildHeader(RoverPacketCodeProto.SEND_PRIMITIVE_MOVING_COMMAND, null))
                    .setVelocity(velocityProto)
                    .build();

        }
    },

    CANCEL_AUTO_MOVING {
        @Override
        public Message create(Object... args) {
            return CancelAutoMovingProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.CANCEL_AUTO_MOVING, null))
                    .build();
        }
    },

    SET_POSE_ESTIMATE {
        @Override
        public Message create(Object... args) {
            double x = (double) args[0];
            double y = (double) args[1];
            double t = (double) args[2];

            Pose2dProto pose2dProto = Pose2dProto.newBuilder().setX(x).setY(y).setT(t).build();

            return SetPoseEstimateProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.SET_POSE_ESTIMATE, null))
                    .setPose(pose2dProto)
                    .build();
        }

        @Override

        public Object parse(String params, ByteString content) {
            try {
                Pose2dProto pose2d = Pose2dProto.parseFrom(content);
                return new RobotPose(pose2d.getX(), pose2d.getY(), pose2d.getT());
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
                return null;
            }
        }
    },

    GET_GLOBAL_MAP_FILE {
        @Override
        public Message create(Object... args) {
            String mapName = (String) args[0];

            return GetGlobalMapFileProto.newBuilder()
                    .setHeader(
                            buildHeader(RoverPacketCodeProto.GET_GLOBAL_MAP_FILE, mapName))
                    .build();
        }

        @Override
        public Object parse(String mapName, ByteString content) {
            return null;
        }
    },

    GET_ERROR_LOG,

    SET_GLOBAL_MAP_FILE {
        @Override
        public Message create(Object... args) {
            return null;
        }
    },

    START_MAPPING {
        @Override
        public Message create(Object... args) {
            return StartMappingProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.START_MAPPING, null))
                    .build();
        }
    },

    STOP_MAPPING {
        @Override
        public Message create(Object... args) {
            return StopMappingProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.STOP_MAPPING, null))
                    .build();
        }
    },

    SELF_CHECK_ALL {
        @Override
        public Message create(Object... args) {
            return SelfCheckAllProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.SELF_CHECK_ALL, null))
                    .build();
        }

        @Override
        public Object parse(String params, ByteString content) {
            try {
                SelfCheckResultProto resultProto = SelfCheckResultProto.parseFrom(content);
                SensorStatus status = new SensorStatus();
                status.setFishEyeReady(resultProto.getIsMonoImageReady());
                status.setIrReady(true);
                status.setLaserReady(resultProto.getIsLaserReady());
                status.setOdomReady(resultProto.getIsOdomReady());
                status.setRgbdReady(resultProto.getIsDepthImageReady());
                status.setLaserAvailable(resultProto.getIsLaserDataValid());
                status.setIscalibrationready(resultProto.getIsCalibrationReady());
                status.setIsharddiskspaceok(resultProto.getIsHardDiskSpaceOk());
                status.setCanControlReady(resultProto.getIsCanControlReady());
                status.setMonoImage(resultProto.getMonoImage().getPath());
                status.setDepthImage(resultProto.getDepthImage().getPath());
                status.setIrImage(resultProto.getIrImage().getPath());
                status.setTopIrImage(resultProto.getTopIrImage().getPath());
                status.setGyroReady(resultProto.getIsGyroReady());
                status.setIrImageReady(resultProto.getIsIrImageReady());
                status.setTopIrImageReady(resultProto.getIsTopIrImageReady());
                return status;
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
                return null;
            }
        }

        @Override
        public String type() {
            return super.type();
        }
    },

    SET_GOAL {
        @Override
        public Message create(Object... args) {
            double x = (double) args[0];
            double y = (double) args[1];
            double t = (double) args[2];

            double liner = (double) args[3];
            double angular = (double)args[4];
            int moveType = (int) args[5];
            int rotateType = (int) args[6];
            float desRange = (float) args[7];
            Pose2dProto pose2dProto = Pose2dProto.newBuilder()
                    .setX(x)
                    .setY(y)
                    .setT(t)
                    .build();
            VelocityProto velocityProto = VelocityProto.newBuilder()
                    .setLiner(liner)
                    .setAngular(angular)
                    .build();
            SetGoalProtoWrapper.MoveTypeProto moveTypeParam = SetGoalProtoWrapper.MoveTypeProto.forNumber(moveType);
            SetGoalProtoWrapper.RotationTypeProto rotateTypeParam = SetGoalProtoWrapper
                    .RotationTypeProto.forNumber(rotateType);
            SetGoalProtoWrapper.GoalProto goalProto = SetGoalProtoWrapper.GoalProto.newBuilder()
                    .setPose(pose2dProto)
                    .setVelocity(velocityProto)
                    .setMoveType(moveTypeParam)
                    .setRotationType(rotateTypeParam)
                    .setRange(desRange)
                    .build();
            return SetGoalProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.SET_GOAL, null))
                    .setGoal(goalProto)
                    .build();
        }

    },

    UPDATE_LASER {
        @Override
        public Object parse(String params, ByteString content) {
            try {
                LaserDataProto laserData = LaserDataProto.parseFrom(content);
                ArrayList<Laser> lasers = new ArrayList<>();
                for (LaserProto laserInfo : laserData.getLasersList()) {
                    lasers.add(new Laser(laserInfo.getAngle(), laserInfo.getDistance()));
                }
                return lasers;
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
                return null;
            }
        }

        @Override
        public String type() {
            return EVENT_UPDATE_LASER;
        }
    },

    UPDATE_POSE {
        @Override
        public Object parse(String params, ByteString content) {
            try {
                ChassisPacketProtoWrapper.NaviPoseProto naviPoseProto =
                        ChassisPacketProtoWrapper.NaviPoseProto.parseFrom(content);
                Pose2dProto pose2d = naviPoseProto.getPose();
                int status = naviPoseProto.getTypeValue();
                return new RobotPose(pose2d.getX(), pose2d.getY(), pose2d.getT(),status);
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
                return null;
            }
        }

        @Override
        public String type() {
            return EVENT_UPDATE_POSE;
        }
    },

    UPDATE_VELOCITY {
        @Override
        public Object parse(String params, ByteString content) {
            try {
                VelocityProto velocityProto = VelocityProto.parseFrom(content);
                return new Velocity(velocityProto.getLiner(), velocityProto.getAngular());
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
                return null;
            }
        }

        @Override
        public String type() {
            return EVENT_UPDATE_VELOCITY;
        }
    },

    UPDATE_GLOBAL_MAP {
        @Override
        public Object parse(String params, ByteString content) {
            try {
                CostmapProto globalMap = CostmapProto.parseFrom(content);
                return new MapData(MapData.GLOBAL,
                        globalMap.getWidth(),
                        globalMap.getHeight(),
                        globalMap.getResolution(),
                        globalMap.getOriginX(),
                        globalMap.getOriginY(),
                        globalMap.getData().toByteArray());
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
                return null;
            }
        }

        @Override
        public String type() {
            return EVENT_UPDATE_GLOBAL_MAP;
        }
    },

    UPDATE_REALTIME_MAP {
        @Override
        public Object parse(String params, ByteString content) {
            try {
                CostmapProto realTimeMap = CostmapProto.parseFrom(content);
                return new MapData(MapData.REAL_TIME,
                        realTimeMap.getWidth(),
                        realTimeMap.getHeight(),
                        realTimeMap.getResolution(),
                        realTimeMap.getOriginX(),
                        realTimeMap.getOriginY(),
                        realTimeMap.getData().toByteArray());
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
                return null;
            }
        }

        @Override
        public String type() {
            return EVENT_UPDATE_REALTIME_MAP;
        }
    },

    UPDATE_EVENT {
        @Override
        public Object parse(String params, ByteString content) {
            try {
                RoverEventProto roverEvent = RoverEventProto.parseFrom(content);
                return new Event(roverEvent.getCodeValue(), roverEvent.getMessage(), roverEvent.getAdditional());
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
                return null;
            }
        }

        @Override
        public String type() {
            return EVENT_UPDATE_EVENT;
        }
    },

    START_CREATE_MAP {
        @Override
        public Message create(Object... args) {
            Object args0 = args[0];
            if (args0 instanceof String) {
                String mapName = (String) args0;

                return StartCreateMapProto.newBuilder()
                        .setHeader(buildHeader(RoverPacketCodeProto.START_CREATE_MAP, null))
                        .setMapName(mapName)
                        .build();
            } else {
                return null;
            }
        }
    },

    STOP_CREATE_MAP {
        @Override
        public Message create(Object... args) {
            return StopCreateMapProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.STOP_CREATE_MAP, null))
                    .build();
        }

        @Override
        public Object parse(String params, ByteString content) {
            try {
                MapNameProto proto = MapNameProto.parseFrom(content);
                return proto.getName();
            } catch (IOException e) {
                e.printStackTrace();
                return null;
            }
        }
    },

    SET_NAVIGATION_MAP {
        @Override
        public Message create(Object... args) {
            Object args0 = args[0];
            if (args0 instanceof String) {
                String mapName = (String) args0;

                return SetNavigationMapProto.newBuilder()
                        .setHeader(buildHeader(RoverPacketCodeProto.SET_NAVIGATION_MAP, null))
                        .setMapName(mapName)
                        .build();
            } else {
                return null;
            }
        }

        @Override
        public Object parse(String params, ByteString content) {
            try {
                MapTypeProto proto = MapTypeProto.parseFrom(content);
                return proto.getHasVision();
            } catch (IOException e) {
                e.printStackTrace();
                return null;
            }
        }
    },

    DELETE_MAP {
        @Override
        public Message create(Object... args) {
            Object args0 = args[0];
            if (args0 instanceof String) {
                String mapName = (String) args0;

                return DeleteMapProto.newBuilder()
                        .setHeader(buildHeader(RoverPacketCodeProto.DELETE_MAP, null))
                        .setMapName(mapName)
                        .build();
            } else {
                return null;
            }
        }
    },

    GET_MAP_LIST {
        @Override
        public Message create(Object... args) {
            return GetMapListProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.GET_MAP_LIST, null))
                    .build();
        }

        @Override
        public Object parse(String params, ByteString content) {
            try {
                MapListProto proto = MapListProto.parseFrom(content);
                return proto.getNamesList();
            } catch (IOException e) {
                e.printStackTrace();
                return null;
            }
        }
    },

    SET_RELOCATE {
        @Override
        public Message create(Object... args) {
            Object args0 = args[0];
            Object args1 = args[1];
            Object args2 = args[2];
            Object args3 = args[3];

            if (args0 instanceof RelocateMode && args1 instanceof Double && args2 instanceof Double && args3 instanceof Double ) {
                RelocateMode mode = (RelocateMode) args0;
                double x = (Double) args1;
                double y = (Double) args2;
                double t = (Double) args3;

                Pose2dProto pose2dProto = Pose2dProto.newBuilder().setX(x).setY(y).setT(t).build();
                RelocateDataProto relocateDataProto = RelocateDataProto.newBuilder()
                        .setPose(pose2dProto)
                        .setMode(RelocateModeProto.forNumber(mode.ordinal()))
                        .build();
                return SetRelocateProto.newBuilder()
                        .setHeader(buildHeader(RoverPacketCodeProto.SET_RELOCATE, null))
                        .setData(relocateDataProto)
                        .build();
            } else {
                return null;
            }
        }
    },

    REPORT_STATISTIC {
        @Override
        public Object parse(String params, ByteString content) {
            try {
                StatisticProto statistic = StatisticProto.parseFrom(content);
                return new Statistic(statistic.getType(),
                        statistic.getInt1(),
                        statistic.getInt2(),
                        statistic.getInt3(),
                        statistic.getInt4(),
                        statistic.getDouble1(),
                        statistic.getDouble2(),
                        statistic.getDouble3(),
                        statistic.getDouble4(),
                        statistic.getStringValue());
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
                return null;
            }
        }

        @Override
        public String type() {
            return EVENT_REPORT_STATISTIC;
        }
    },

    PACK_LOG {
        @Override
        public Message create(Object... args) {
            Object args0 = args[0];
            Object args1 = args[1];
            if (args0 instanceof Long && args1 instanceof Long) {
                Long startTime = (Long) args0;
                Long endTime = (Long) args1;

                LogProto logProto = LogProto.newBuilder()
                        .setStartTime(startTime)
                        .setEndTime(endTime)
                        .build();
                return PackLogProto.newBuilder()
                        .setHeader(buildHeader(RoverPacketCodeProto.PACK_LOG, null))
                        .setLog(logProto)
                        .build();
            } else {
                return null;
            }
        }
    },

    SET_TIME {
        @Override
        public Message create(Object... args) {
            Object args0 = args[0];
            if (args0 instanceof Long) {
                long currTime = (Long) args0;

                TimeProto timeProto = TimeProto.newBuilder()
                        .setTimeMills(currTime)
                        .build();
                return SetTimeProto.newBuilder()
                        .setHeader(buildHeader(RoverPacketCodeProto.SET_TIME, null))
                        .setTime(timeProto)
                        .build();
            } else {
                return null;
            }
        }
    },

    ADD_MAP {
        @Override
        public Message create(Object... args) {
            return super.create(args);
        }
    },

    TAKE_SNAP_SHOT {
        @Override
        public Message create(Object... args) {
            Object args0 = args[0];
            if (args0 instanceof String) {
                String logID = (String) args0;

                SnapshotProto snapshotProto = SnapshotProto.newBuilder()
                        .setId(logID)
                        .build();
                return TakeSnapshotProto.newBuilder()
                        .setHeader(buildHeader(RoverPacketCodeProto.TAKE_SNAP_SHOT, null))
                        .setSnapshot(snapshotProto)
                        .build();
            } else {
                return null;
            }
        }
    },

    WORKING_MODE_STATE_CHANGE {
        @Override
        public Object parse(String params, ByteString content) {
            try {
                WorkingModeProtoWrapper.WorkingModeStateProto workingModeProto =
                        WorkingModeProtoWrapper.WorkingModeStateProto.parseFrom(content);
                Log.i("Navigation", "working mode state change parse");
                return new WorkStateMode(workingModeProto.getModeValue(), workingModeProto.getStateValue());
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
                return null;
            }
        }

        @Override
        public String type() {
            return EVENT_WORKING_MODE_STATE_CHANGE;
        }
    },

    GET_POSE_STATE {
        @Override
        public Message create(Object... args) {
            return GetLocationStateProtoWrapper.GetLocationStateProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.GET_LOCATION_STATE, null))
                    .build();
        }

        @Override
        public Object parse(String params, ByteString content) {
            try {
                GetLocationStateProtoWrapper.LocationStateProto poseStateProto = GetLocationStateProtoWrapper.LocationStateProto.parseFrom(content);
                return new PoseState(poseStateProto.getTypeValue());
            } catch (IOException e) {
                e.printStackTrace();
                return null;
            }
        }
    },

    STORAGE_RECOVERY {
        @Override
        public Message create(Object... args) {
            return RoverPacketProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.STORAGE_RECOVERY, null))
                    .build();
        }

        @Override
        public Object parse(String params, ByteString content) {
                return null;
        }

    },

    ADD_TARGET {
        @Override
        public Message create(Object... args) {

            Log.i("NLOG_protocol", "create add mappingpose, t:");
            Object args0 = args[0];
            Object args1 = args[1];
            Object args2 = args[2];
            double x = 0;
            double y = 0;
            double t = 0;

            if (args0 instanceof Double && args1 instanceof Double && args2 instanceof Double) {
                x = (Double) args0;
                y = (Double) args1;
                t = (Double) args2;
            }

            Log.i("NLOG_protocol", "create add mappingpose, x:"+x+" ,y:"+y+" ,theta:"+t);

            Pose2dProto pose2dProto = Pose2dProto.newBuilder().setX(x).setY(y).setT(t).build();

            RoverPacketProtoWrapper.AddTargetReqProto addTargetReqProto = RoverPacketProtoWrapper
                    .AddTargetReqProto.newBuilder().setPose(pose2dProto).build();

            RoverPacketHeaderProto headerProto = RoverPacketHeaderProto.newBuilder()
                    .setCode(RoverPacketCodeProto.ADD_TARGET).build();

            return RoverPacketProto.newBuilder()
                    .setHeader(headerProto)
                    .setBody(addTargetReqProto.toByteString())
                    .build();
        }

        @Override
        public Object parse(String params, ByteString content) {
            Log.i("NLOG_protocol", "add mapping pose, param:"+params+" content: "+content.toString());
            try {
                RoverPacketProtoWrapper.AddTargetRepProto proto = RoverPacketProtoWrapper.AddTargetRepProto.parseFrom(content);
                MappingPose pose = new MappingPose();
                pose.setValid(proto.getResult().getValid());
                pose.setId(proto.getResult().getId());
                Log.i("", "ADD_TARGET , poseid:"+pose.getId()+" _vaild: "+pose.isValid());
                return pose;
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }

    },
    REMOVE_TARGET {
        @Override
        public Message create(Object... args) {

            Log.i("NLOG_protocol", "delete mappingpose, id:"+args[0]);
            int id = Integer.parseInt(args[0].toString());

            RoverPacketProtoWrapper.RemoveTargetReqProto removeTargetReqProto
                    = RoverPacketProtoWrapper.RemoveTargetReqProto.newBuilder().setId(id).build();

            RoverPacketHeaderProto header = RoverPacketHeaderProto.newBuilder()
                    .setCode(RoverPacketCodeProto.REMOVE_TARGET).build();
            return RoverPacketProto.newBuilder().setHeader(header)
                    .setBody(removeTargetReqProto.toByteString())
                    .build();
        }

        @Override
        public Object parse(String params, ByteString content) {
            return null;
        }

    },

    SAVE_MAP {
        @Override
        public Message create(Object... args) {
            Log.i("NLOG_protocol", "saveMap");
            return RoverPacketProto.newBuilder()
                    .setHeader(buildHeader(RoverPacketCodeProto.SAVE_MAP, null))
                    .build();
        }

        @Override
        public Object parse(String params, ByteString content) {
            Log.i("NLOG_protocol", "save map, param:"+params+" content: "+content.toString());
            //TODO dev_master 不需要兼容 TK1
//            try {
//                RoverPacketProtoWrapper.SaveMapRepProto proto = RoverPacketProtoWrapper.SaveMapRepProto.parseFrom(content);
//                CommonProtoWrapper.TargetGroupPointsProto targetGroupProto= proto.getTargets();
//                List<CommonProtoWrapper.TargetGroupPointProto> list = targetGroupProto.getPointsList();
//                List<PlaceDataManager.MappingPose> mappingPoseList = new ArrayList<>();
//                for (CommonProtoWrapper.TargetGroupPointProto point : list){
//                    int id = point.getPoint().getId();
//                    boolean _valid = point.getPoint().getValid();
//                    Pose2dProto poseProto = point.getPoint().getPose();
//                    PlaceDataManager.MappingPose mappingPose = new PlaceDataManager.MappingPose();
//                    mappingPose.setId(id);
//                    mappingPose.setValid(_valid);
//                    Pose pose = new Pose((float) poseProto.getX(), (float) poseProto.getY()
//                            , (float) poseProto.getT());
//                    mappingPose.setPose(pose);
//                    mappingPoseList.add(mappingPose);
//                    Log.i("", "SAVE_MAP , poseid:"+id+" _vaild:"+_valid+", posex :"+poseProto.getX());
//                }
//                return mappingPoseList;
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
            return null;
        }

    },

    UPDATE_TARGETS {
        @Override
        public Object parse(String params, ByteString content) {
            Log.i("NLOG_protocol", "mapping pose update, param:"+params+" content: "+content.toString());
            try {
                RoverPacketProtoWrapper.UpdateTargetsProto targetsProto = RoverPacketProtoWrapper
                        .UpdateTargetsProto.parseFrom(content);
                List<CommonProtoWrapper.TargetPointProto> pointsList = targetsProto.getTargets().getPointsList();
                if (pointsList != null && pointsList.size() > 0){
                    List<MappingPose> mappingPoseList = new ArrayList<>();
                    for (CommonProtoWrapper.TargetPointProto point : pointsList){
                        boolean _valid = point.getValid();
                        int id = point.getId();
                        Pose2dProto poseProto = point.getPose();
                        Pose pose = new Pose((float) poseProto.getX(), (float) poseProto.getY()
                                , (float) poseProto.getT());
                        MappingPose mappingPose = new MappingPose();
                        mappingPose.setPose(pose);
                        mappingPose.setValid(_valid);
                        mappingPose.setId(id);
                        mappingPoseList.add(mappingPose);
                        Log.i("NLOG_protocol", "update target:    " + mappingPose.toJson());
                    }
                    return mappingPoseList;
                }
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
            }
            return null;
        }

        @Override
        public String type() {
            return EVENT_UPDATE_MAPPING_POSE;
        }
    };

    protected RoverPacketHeaderProto buildHeader(RoverPacketCodeProto code, String text) {
        RoverPacketHeaderProto.Builder builder = RoverPacketHeaderProto
                .newBuilder()
                .setCode(code);

        if (text != null) {
            builder.setText(text);
        }
        return builder.build();
    }

    public Message create(Object... args) {
        return null;
    }

    public String type() {
        return EVENT_RES_COMMAND;
    }

    public Object parse(String params, ByteString content) {
        return null;
    }

    //------------ Static ----------//
    private static HashMap<String, Protocol> protocols;

    private static synchronized HashMap<String, Protocol> getProtocols() {
        if (protocols == null) {
            protocols = new HashMap<>();
            for (Protocol protocol : Protocol.values()) {
                protocols.put(protocol.name(), protocol);
            }
        }
        return protocols;
    }

    public static Protocol getProtocol(String event) {
        return getProtocols().get(event);
    }

    public static boolean isHeartbeat(RoverPacketProto packet) {
        RoverPacketCodeProto code = packet.getHeader().getCode();
        return code == RoverPacketCodeProto.SEND_HEART_BEATING;
    }
}
