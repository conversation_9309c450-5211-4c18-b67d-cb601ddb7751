/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.tk1;

import androidx.annotation.NonNull;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.utils.DelayTimer;
import com.ainirobot.navigationservice.Defs.Def;
import com.ainirobot.navigationservice.Defs.NavigationStatus;

import java.io.DataOutput;
import java.io.IOException;

/**
 * Target point of navigation, composed of pose and status
 */
public class TargetPose extends Message {
    private static final String TAG = "TargetPose";

    public static final int STATUS_NORMAL = 1000;
    public static final int STATUS_STARTED = 0;
    public static final int STATUS_OCCLUDED = 1;
    public static final int STATUS_OCCLUDED_END = 2;
    public static final int STATUS_AVOID = 3;
    public static final int STATUS_AVOID_END = 4;
    public static final int STATUS_OUT_MAP = 5;
    public static final int STATUS_OUT_MAP_END = 6;
    public static final int STATUS_OBSTACLES_AVOID = 7;
    public static final int STATUS_GLOBAL_PATH_FAILED = 8;
    public static final int STATUS_PATH_SUCCESS = 9;
    public static final int STATUS_MULTI_AVOID_WAITING = 10;
    public static final int STATUS_MULTI_AVOID_WAITING_END = 11;
    public static final int STATUS_GO_STRAIGHT = 12;
    public static final int STATUS_TURN_LEFT = 13;
    public static final int STATUS_TURN_RIGHT = 14;
    public static final int STATUS_PATH_WAITING = 15;
    public static final int STATUS_PATH_WAITING_END = 16;
    public static final int STATUS_NOT_MOVING_LONG_TIME = 17;

    /**
     * 导航任务优先级设置失败，招财豹专属
     */
    public static final int STATUS_PRIORITY_SET_FAILED = 18;

    /**
     * Lora配置异常
     */
    public static final int STATUS_MULTIPLE_LORA_CONFIG_FAIL = 19;

    /**
     *多机地图不匹配
     */
    public static final int STATUS_MULTIPLE_MAP_NOT_MATCH = 20;

    /**
     *多机Lora断连
     */
    public static final int STATUS_MULTIPLE_LORA_DISCONNECT = 21;

    /**
     * 多机版本不匹配
     */
    public static final int STATUS_MULTIPLE_VERSION_NOT_MATCH = 22;

    /**
     * 轮子打滑
     */
    public static final int STATUS_WHEEL_SLIP = 23;

    public static final int RESULT_ARRIVED = 0;
    public static final int RESULT_ABORTED = -1;
    public static final int RESULT_CANCELED = -2;
    public static final int RESULT_REPLACE = -4;
    public static final int RESULT_FAILED = -3;

    public static final int TURN_LEFT = 1;
    public static final int TURN_RIGHT = 2;
    public static final int TURN_DEFAULT = 0;

    public static final int MOVE_NORMAL = 0;
    public static final int MOVE_PURE_ROTATION = 1;

    private static final int ROTATION_NORMAL = 0;
    private static final int ROTATION_ONLY_RIGHT = 1;
    private static final int ROTATION_ONLY_LEFT = 2;
    private static final int ROTATION_NONE = 3;

    public static final double POS_TOLERANCE_DEFAULT = 0.15;
    public static final double POS_TOLERANCE_MIN = 0.05;
    public static final double ANGLE_TOLERANCE_DEFAULT = 0.05;
    public static final double ANGLE_TOLERANCE_MIN = 0.02;
    // 导航时，是否依赖地图巡线
    public static final int ROAD_MODE_MAP_PATH = 0; // 不依靠地图巡线
    public static final int ROAD_MODE_GRAPH_PATH = 1; // 依靠地图巡线

    private DelayTimer mStatusTimer;
    private Pose pose;
    private double range = 0.6;
    private ResponseListener responseListener;
    private volatile boolean isAvailable = true;
    private int mStatus = STATUS_NORMAL;
    /**
     * 默认为false，代表到达位置点需要转动。20190702底盘更改协议，去掉isAdjustAngle，更换为moveType和rotateType定义，
     * 为了兼容coreService接口，在此保留isAdjustAngle，并且做兼容转换，
     * false对应MOVE_NORMAL+ROTATION_NORMAL，true对应MOVE_NORMAL+ROTATION_NONE
     */
    private boolean isAdjustAngle = false;
    /**
     * turnParam代表转圈往左转，往右转。一旦此参数不为TURN_DEFAULT，就表示调用的是转动接口。
     * TURN_LEFT 对应MOVE_PURE_ROTATION+ROTATION_ONLY_LEFT, TURN_RIGHT 对应MOVE_PURE_ROTATION +
     *     ROTATION_ONLY_RIGHT
     */
    private int turnParam;
    /** true:连续切换目标点不减速 */
    private boolean keepMove = false;
    /**
     * 目的地点坐标范文半径，导航任务当未能到达指定位姿点时，需要到达位姿点的默认半径
     */
    private double mDestinationRange = Def.ROBOT_NAVIGATION_DEFAULT_DESTINATION_RANGE;
    /**
     * 最大避障距离默认值，距离目标的障碍物小于该值时，机器人停止
     */
    private double blockObsDistance = Def.ROBOT_NAVIGATION_DEFAULT_OBS_DISTANCE;;
    /**
     * KTV服务器多机功能，新版本已废弃。和STATUS_MULTI_AVOID_WAITING  STATUS_MULTI_AVOID_WAITING_END两个状态有关。
     */
    private volatile boolean isMultipleWaiting = false;
    /**
     * Lora版本多机功能标识位,和服务器多机功能互斥
     */
    private volatile boolean isLoraMultiRobotWaiting = false;

    private int mMoveType = MOVE_NORMAL;

    public static final int DEFAULT_NAVIGAITON_PRIORITY = 0;

    public static final NavMode DEFAULT_NAV_MODE = new NavMode(0, 0);

    /**
     * 多机导航任务优先级，值越大优先级越高
     * 取值范围：0~29
     */
    private int mNavPriority = DEFAULT_NAVIGAITON_PRIORITY;

    // 设定定位偏差值，pos设定最小值为0.05，默认为0.15
    private double mPosTolerance = POS_TOLERANCE_DEFAULT;

    //设定定位偏差值，angle设定最小值为0.02，默认为0.05
    private double mAngleTolerance = ANGLE_TOLERANCE_DEFAULT;

    private NavMode mNavMode;

    // 移动路径模式（是否依靠巡线），默认为系统设置的。目前远程建图-探索地图功能使用mode为0
    private int mRoadMode = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTINGS_NAVIGATION_LINE_TRACKING);

    public TargetPose(@NonNull Pose pose) {
        this(pose, false, TURN_DEFAULT);
    }

    public TargetPose(@NonNull Pose pose, boolean isAdjustAngle, int turnParam) {
        this(pose, isAdjustAngle, false, turnParam,
                Def.ROBOT_NAVIGATION_DEFAULT_DESTINATION_RANGE, Def.ROBOT_NAVIGATION_DEFAULT_OBS_DISTANCE,
                DEFAULT_NAVIGAITON_PRIORITY, DEFAULT_NAV_MODE);
    }

    public TargetPose(@NonNull Pose pose, boolean isAdjustAngle, boolean keepMove,
                      int turnParam, double destinationRange, double blockObsDistance,
                      int navPriority, NavMode navMode) {
        super(Message.MSG_TYPE_GOAL);
        this.pose = pose;
        this.isAdjustAngle = isAdjustAngle;
        this.keepMove = keepMove;
        this.turnParam = turnParam;
        this.mDestinationRange = destinationRange;
        this.blockObsDistance = blockObsDistance;
        this.mNavPriority = navPriority;
        this.mNavMode = navMode;
        // 这个打印要注意，有两个obsDistance
        // 接口的参数会下发导航，影响到达判断，默认0.75
        // Pose对象内部的用于poseUpdate时设置默认值，不会下发给导航影响业务，默认0.1
        // 已经把Pose对象的打印key修改为poseObsDistance，避免歧义
        Log.d(TAG, "TargetPose : " + pose.toString());
        Log.d(TAG, "NavMode : " + (navMode == null ? "null" : navMode.toString()));
        mStatusTimer = new DelayTimer(1500, new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "Status timeout : " + mStatus);
                onStatusUpdate(STATUS_NORMAL, NavigationStatus.STATUS_NAVI_EVENT_NORMAL, "");
            }
        });
        mStatusTimer.start();
    }

    public int getTurnParam() {
        return turnParam;
    }

    public void setTurnParam(int turnParam) {
        this.turnParam = turnParam;
    }

    public double getDestinationRange() {
        return mDestinationRange;
    }

    public double getBlockObsDistance() {
        return blockObsDistance;
    }

    public void setBlockObsDistance(double blockObsDistance) {
        this.blockObsDistance = blockObsDistance;
    }

    public void setMoveType(int moveType){
        this.mMoveType = moveType;
    }

    /**
     *
     */
    public int getMoveType() {
        if (this.mMoveType == MOVE_PURE_ROTATION) {
            return MOVE_PURE_ROTATION;
        } else {
            //adjustAngle生效，moveType为正常行驶,否则为纯转动
            return turnParam == TURN_DEFAULT ? MOVE_NORMAL : MOVE_PURE_ROTATION;
        }
    }

    public int getRotateType() {
        if (turnParam == TURN_DEFAULT) {
            return isAdjustAngle ? ROTATION_NONE : ROTATION_NORMAL;
        } else {
            return (turnParam == TURN_LEFT) ? ROTATION_ONLY_LEFT : ROTATION_ONLY_RIGHT;
        }
    }

    @Override
    public void writeTo(DataOutput out) throws IOException {
        this.pose.writeTo(out);
        this.mNavMode.writeTo(out);
    }

    public void setResponseListener(ResponseListener listener) {
        this.responseListener = listener;
    }

    public void setRange(double range) {
        this.range = range;
    }

    public void onResult(int result, int navigationCode) {
        Log.d(TAG, "TargetPose result : " + result + "   isAvailable : " + isAvailable);
        if (!isAvailable) {
            return;
        }

        isAvailable = false;
        isMultipleWaiting = false;
        isLoraMultiRobotWaiting = false;
        mStatusTimer.destroy();
        if (responseListener != null && result != RESULT_CANCELED) {
            BaseEvent event = new BaseEvent(navigationCode, "");
            responseListener.onResult(result, event);
        }
        Log.d(TAG, "onResult : " + (responseListener != null) + ", isAvailable=" + isAvailable);
    }

    public void onPassThroughStatusUpdate(int status, int eventCode, String msg){
        Log.d(TAG, "passThroughStatusUpdate status: " + status + " eventCode:" + eventCode
                + "  isAvailable : " + isAvailable);
        if (!isAvailable) {
            return;
        }
        updateStatus(status, eventCode, msg);
    }

    public void onStatusUpdate(int status, int eventCode, String msg) {
        Log.d(TAG, "TargetPose status : " + status + "  isAvailable : " + isAvailable
                + " mstatus:" + mStatus + " isLoraMultiRobotWaiting:"+isLoraMultiRobotWaiting);
        if (!isAvailable) {
            return;
        }
        //如果处在多机等待状态中，屏蔽其他导航消息，除非有新的导航任务，或者等待结束
        if (isMultipleWaiting){
            if (status != mStatus && status == STATUS_MULTI_AVOID_WAITING_END){
                isMultipleWaiting = false;
                updateStatus(STATUS_MULTI_AVOID_WAITING_END, eventCode, msg);
                mStatus = status;
            }
            if (isContinueStatus(status)) {
                mStatusTimer.reset();
            } else {
                mStatusTimer.cancel();
            }
            return;
        }
        //如果处在Lora多机等待状态中，屏蔽其他导航消息，除非有新的导航任务，或者等待结束
        if (isLoraMultiRobotWaiting){
            int tmpStatus = status;
            if (status != mStatus){
                isLoraMultiRobotWaiting = false;
                switch (status){
                    //收到以下状态可以释放多机等待状态
                    case STATUS_OCCLUDED:
                    case STATUS_AVOID:
                    case STATUS_OBSTACLES_AVOID:
                    case STATUS_PATH_SUCCESS:
                        tmpStatus = STATUS_PATH_WAITING_END;
                        updateStatus(tmpStatus, eventCode, msg);
                        break;
                        //以下状态属于全局路径规划失败，可以直接退出
                    case STATUS_OUT_MAP:
                    case STATUS_GLOBAL_PATH_FAILED:
                        updateStatus(status, eventCode, msg);
                        break;
                    default:
                        break;
                }
                mStatus = tmpStatus;
            }
            if (isContinueStatus(status)) {
                mStatusTimer.reset();
            } else {
                mStatusTimer.cancel();
            }
            return;
        }
        if (status != mStatus) {
            if (status == STATUS_PATH_WAITING) {
                isLoraMultiRobotWaiting = true;
            }
            switch (mStatus) {
                case STATUS_AVOID:
                    updateStatus(STATUS_AVOID_END, eventCode, msg);
                    break;

                case STATUS_OCCLUDED:
                    updateStatus(STATUS_OCCLUDED_END, eventCode, msg);
                    break;

                case STATUS_OUT_MAP:
                    updateStatus(STATUS_OUT_MAP_END, eventCode, msg);
                    break;

                case STATUS_GLOBAL_PATH_FAILED:
                    updateStatus(STATUS_PATH_SUCCESS, eventCode, msg);
                    break;

                case STATUS_MULTI_AVOID_WAITING:
                    isMultipleWaiting = true;
                    break;
                default:
                    break;
            }

            if (status != STATUS_NORMAL) {
                updateStatus(status, eventCode, msg);
            }
            mStatus = status;
        }

        if (isContinueStatus(status)) {
            mStatusTimer.reset();
        } else {
            mStatusTimer.cancel();
        }
        Log.d(TAG, "onStatusUpdate");
    }

    private void updateStatus(int status, int eventCode, String msg) {
        if (responseListener != null) {
            String message = msg;
            if (message == null){
                message = "";
            }
            BaseEvent event = new BaseEvent(eventCode, message);
            responseListener.onStatusUpdate(status, event);
        }
    }

    private boolean isContinueStatus(int status) {
        return status == STATUS_OCCLUDED
                || status == STATUS_AVOID
                || status == STATUS_OBSTACLES_AVOID
                || status == STATUS_GLOBAL_PATH_FAILED
                || status == STATUS_OUT_MAP
                || status == STATUS_MULTI_AVOID_WAITING
                || status == STATUS_MULTI_AVOID_WAITING_END
                || status == STATUS_PATH_WAITING;
    }

    public boolean isAvailable() {
        return isAvailable;
    }

    public Pose getPose() {
        return pose;
    }

    public NavMode getNavMode() {
        return mNavMode;
    }

    public boolean getIsAdjustAngle() {
        return isAdjustAngle;
    }

    public boolean getIsKeepMove() {
        return keepMove;
    }

    public boolean isReachRange(Pose pose) {
        double distance = this.pose.getDistance(pose);
        return Double.compare(distance, range) < 0;
    }

    public int getNavPriority() {
        return mNavPriority;
    }

    public void setNavPriority(int navPriority) {
        mNavPriority = navPriority;
    }

    public double getPosTolerance() {
        return mPosTolerance;
    }

    public void setPosTolerance(double posTolerance) {
        mPosTolerance = posTolerance;
    }

    public double getAngleTolerance() {
        return mAngleTolerance;
    }

    public void setAngleTolerance(double angleTolerance) {
        mAngleTolerance = angleTolerance;
    }

    public int getRoadMode() {
        return mRoadMode;
    }

    public void setRoadMode(int mRoadMode) {
        this.mRoadMode = mRoadMode;
    }

    public interface ResponseListener {
        void onResult(int result, BaseEvent event);

        void onStatusUpdate(int status, BaseEvent event);
    }

    @Override
    public String toString() {
        if (this.pose != null) {
            return this.pose.toString();
        }
        return super.toString();
    }
}
