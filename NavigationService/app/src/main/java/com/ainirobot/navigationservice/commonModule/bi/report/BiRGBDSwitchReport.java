package com.ainirobot.navigationservice.commonModule.bi.report;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

/**
 * 该类在旧版本使用，新版本埋点迁移到base_robot_navi_setting表  2020.1.13
 * 新埋点表为BiNaviSettingReport类。
 */
@Deprecated
public class BiRGBDSwitchReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_rgbd_switch";
    private static final String TABLE_COLUMN_CTIME = "ctime";
    private static final String TABLE_COLUMN_WAKEUP_ID = "wakeup_id";
    private static final String TABLE_COLUMN_MODE = "mode";
    private static final String TABLE_COLUMN_SERVER_SWITCH = "server_switch";

    public static final int SWITCH_MODE_OPEN = 1;
    public static final int SWITCH_MODE_CLOSE = 2;

    public static final int SWITCH_SERVER_MODE_DEFAULT = 0;
    public static final int SWITCH_SERVER_MODE_OPEN = 1;
    public static final int SWITCH_SERVER_MODE_CLOSE = 2;
    public static final int SWITCH_SERVER_MODE_ACTIVE = 3;

    public BiRGBDSwitchReport() {
        super(TABLE_NAME);
        initData();
    }

    private void initData() {
        clearReportDatas();
        addData(TABLE_COLUMN_CTIME, 0);
        addData(TABLE_COLUMN_MODE, SWITCH_MODE_OPEN);
        addData(TABLE_COLUMN_SERVER_SWITCH, SWITCH_SERVER_MODE_DEFAULT);
    }

    public void reportStatus(int mode,int serverMode) {
        clearReportDatas();
        addData(TABLE_COLUMN_CTIME, System.currentTimeMillis());
        addData(TABLE_COLUMN_MODE, mode);
        addData(TABLE_COLUMN_SERVER_SWITCH, serverMode);
        report();
    }

    @Override
    public void report() {
        super.report();
    }
}
