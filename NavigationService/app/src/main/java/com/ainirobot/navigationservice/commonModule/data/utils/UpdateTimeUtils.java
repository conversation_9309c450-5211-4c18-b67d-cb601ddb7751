package com.ainirobot.navigationservice.commonModule.data.utils;

import android.text.TextUtils;
import android.util.Log;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 地图信息，服务端上传下载数据精度都是秒(10位时间戳，毫秒时间戳是13位)
 * 点位信息的时间戳来自底盘pose上报，默认精度毫秒
 * 本地数据库存储格式化存储默认精度只能是毫秒，使用时：写入前*1000，读取后/1000
 * Created by Orion on 2020/12/18.
 */
public class UpdateTimeUtils {
    private static final String TAG = "remote:" + UpdateTimeUtils.class.getSimpleName();

    /**
     * 时间戳格式化位日期
     *
     * @param time 秒
     * @return
     */
    public static String timestampToDateSeconds(long time) {
        Log.d(TAG, "timestampToDateSeconds: time=" + time);
        String pattern = "yyyy-MM-dd HH:mm:ss";
        Date date = new Date(time * 1000);
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        String dateStr = format.format(date);
        Log.d(TAG, "timestampToDateSeconds: dateStr=" + dateStr);
        return dateStr;
    }

    /**
     * 日期转时间戳
     *
     * @param datetime
     * @return 秒
     */
    public static long datetimeToTimestampSeconds(String datetime) {
        Log.d(TAG, "datetimeToTimestampSeconds: datetime=" + datetime);
        long timeStamp = datetimeToTimestampMillis(datetime);
        Log.d(TAG, "datetimeToTimestampSeconds: timeStamp=" + timeStamp);
        return timeStamp / 1000;
    }

    /**
     * @param time 毫秒
     * @return
     */
    public static String timestampToDateMillis(long time) {
        Log.d(TAG, "timestampToDateMillis: time=" + time);
        String pattern = "yyyy-MM-dd HH:mm:ss";
        Date date = new Date(time);
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        String dateStr = format.format(date);
        Log.d(TAG, "timestampToDateMillis: dateStr=" + dateStr);
        return dateStr;
    }

    /**
     * @param datetime
     * @return 毫秒
     */
    public static long datetimeToTimestampMillis(String datetime) {
        Log.d(TAG, "datetimeToTimestampSeconds: datetime=" + datetime);
        if (TextUtils.isEmpty(datetime)) {
            return 0;
        }
        String pattern = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        Date date = new Date();
        long timeStamp = date.getTime();
        try {
            date = dateFormat.parse(datetime);
            timeStamp = date.getTime();
        } catch (Exception e) {
            e.printStackTrace();
            try {
                timeStamp = Long.parseLong(datetime);
            } catch (Exception ex) {
                e.printStackTrace();
            }
        }
        Log.d(TAG, "datetimeToTimestampSeconds: timeStamp=" + timeStamp);
        return timeStamp;
    }

}
