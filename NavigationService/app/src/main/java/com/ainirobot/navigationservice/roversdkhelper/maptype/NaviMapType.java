package com.ainirobot.navigationservice.roversdkhelper.maptype;

import com.ainirobot.coreservice.client.Definition;

import ninjia.android.proto.CommonProtoWrapper;

/**
 * 底盘地图类型，对应的数据 Bean
 * <p>
 * 对应底盘的protocol类：{@link CommonProtoWrapper.MapConfigProto}
 */
public class NaviMapType {
    private int visionType;
    private int targetType;

    public NaviMapType() {
        this.visionType = Definition.VisionType.TYPE_NONE.getValue();
        this.targetType = Definition.TargetType.TYPE_TARGET_NONE.getValue();
    }

    public NaviMapType(int visionType, int targetType) {
        this.visionType = visionType;
        this.targetType = targetType;
    }

    public int getVisionType() {
        return visionType;
    }

    public NaviMapType setVisionType(int visionType) {
        this.visionType = visionType;
        return this;
    }

    public int getTargetType() {
        return targetType;
    }

    public NaviMapType setTargetType(int targetType) {
        this.targetType = targetType;
        return this;
    }

    public boolean hasVision() {
        return this.visionType > Definition.VisionType.TYPE_NONE.getValue();
    }

    public boolean hasTarget() {
        return targetType > Definition.TargetType.TYPE_TARGET_NONE.getValue();
    }

    @Override
    public String toString() {
        return "NaviMapType{" +
                "visionType=" + visionType +
                ", targetType=" + targetType +
                '}';
    }

    @Override
    protected NaviMapType clone() throws CloneNotSupportedException {
        return (NaviMapType) super.clone();
    }
}
