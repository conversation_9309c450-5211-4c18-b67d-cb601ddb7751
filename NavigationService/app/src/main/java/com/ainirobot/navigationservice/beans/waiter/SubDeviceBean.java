package com.ainirobot.navigationservice.beans.waiter;

public class SubDeviceBean {

    /**
     * 是否有lora模块
     */
    private int lora;

    /**
     * 是否有esp32模块
     */
    private int esp32;

    /**
     * 是否有滤光片
     */
    private int lightFilter;

    /**
     * 是否有减震器
     */
    private int dampener;

    /**
     * 激光雷达的类型
     * 0为蓝海 1为EAI
     */
    private int lidarEai;

    /**
     * 线性偏振片
     * 0为无 1为有
     */
    private int cplFilter;

    /**
     * 字段"TOPIR_mini2"标识mini2机器人同时具有TopIR 和 ESP32外设
     */
    private int topIr;

    /**
     * 机器中是否有D430摄像头，旧机器默认都有，default值为1
     * Mini新版本可能没有，没有工厂会写0，代表无RGBD
     * 招财新版本更换其他品牌，工厂不会主动写，兼容旧版本保持为1，不代表真的有设备
     * 0 表示不包含rgbd , 1 表示有该RGBD 硬件
     */
    private int rgbdD430 = 1;

    /**
     * 默认机器为正常高度，后续有机身加高版本
     * 0 表示正常版本 , 1 机身加高版本
     */
    private int heightBody = 0;

    /**
     * 招财新机型，雷达210开口
     */
    private int lidarFov = 0;

    /**
     * 招财新机型，带回充IR，暂时使用消毒豹类型
     */
    private int chargeIr = 0;

    /**
     * 招财新机型，肇观RGBD
     */
    private int rgbdFm1 = 0;

    /**
     * plus & pro新机型，瞰瞰RGBD Wolf
     */
    private int rgbdWf = 0;

    /**
     * pro新机型，X100
     */
    private String depthType;

    /**
     * pro新机型，电动门
     */
    private int autoDoor = 0;

    /**
     * mini新sensor，ssMono
     */
    private int ssMono = 0;

    /**
     * pro新sensor，topMono
     */
    private int topMono = 0;

    public SubDeviceBean() {

    }

    public int getLora() {
        return lora;
    }

    public void setLora(int lora) {
        this.lora = lora;
    }

    public int getEsp32() {
        return esp32;
    }

    public void setEsp32(int esp32) {
        this.esp32 = esp32;
    }

    public int getLightFilter() {
        return lightFilter;
    }

    public void setLightFilter(int lightFilter) {
        this.lightFilter = lightFilter;
    }

    public int getDampener() {
        return dampener;
    }

    public void setDampener(int dampener) {
        this.dampener = dampener;
    }

    public int getLidarEai() {
        return lidarEai;
    }

    public void setLidarEai(int lidarEai) {
        this.lidarEai = lidarEai;
    }

    public int getCplFilter() {
        return cplFilter;
    }

    public void setCplFilter(int cplFilter) {
        this.cplFilter = cplFilter;
    }

    public int getTopIr() {
        return topIr;
    }

    public void setTopIr(int topIr) {
        this.topIr = topIr;
    }

    public int getRgbdD430() {
        return rgbdD430;
    }

    public void setRgbdD430(int rgbdD430) {
        this.rgbdD430 = rgbdD430;
    }

    public int getHeightBody() {
        return heightBody;
    }

    public void setHeightBody(int rgbdInstalled) {
        this.heightBody = rgbdInstalled;
    }

    public int getLidarFov() {
        return lidarFov;
    }

    public void setLidarFov(int lidarFov) {
        this.lidarFov = lidarFov;
    }

    public int getChargeIr() {
        return chargeIr;
    }

    public void setChargeIr(int chargeIr) {
        this.chargeIr = chargeIr;
    }

    public int getRgbdFm1() {
        return rgbdFm1;
    }

    public void setRgbdFm1(int rgbdFm1) {
        this.rgbdFm1 = rgbdFm1;
    }

    public int getRgbdWf() {
        return rgbdWf;
    }

    public void setRgbdWf(int rgbdWf) {
        this.rgbdWf = rgbdWf;
    }

    public String getDepthType() {
        return depthType;
    }

    public void setDepthType(String depthType) {
        this.depthType = depthType;
    }

    public void setAutoDoor(int autoDoor) {
        this.autoDoor = autoDoor;
    }

    public int getAutoDoor() {
        return this.autoDoor;
    }

    public int getSsMono() {
        return ssMono;
    }

    public void setSsMono(int ssMono) {
        this.ssMono = ssMono;
    }

    public int getTopMono() {
        return topMono;
    }

    public void setTopMono(int topMono) {
        this.topMono = topMono;
    }

    @Override
    public String toString() {
        return "SubDeviceBean{" +
                "lora=" + lora +
                ", esp32=" + esp32 +
                ", lightFilter=" + lightFilter +
                ", dampener=" + dampener +
                ", lidarEai=" + lidarEai +
                ", cplFilter=" + cplFilter +
                ", topIr=" + topIr +
                ", rgbdD430=" + rgbdD430 +
                ", heightBody=" + heightBody +
                ", lidarFov=" + lidarFov +
                ", chargeIr=" + chargeIr +
                ", rgbdFm1=" + rgbdFm1 +
                ", rgbdWf=" + rgbdWf +
                ", depthType=" + depthType +
                ", autoDoor=" + autoDoor +
                ", ssMono=" + ssMono +
                ", topMono=" + topMono +
                '}';
    }

}
