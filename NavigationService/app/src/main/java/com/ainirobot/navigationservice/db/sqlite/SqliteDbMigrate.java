package com.ainirobot.navigationservice.db.sqlite;

import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_HAS_TARGET_DATA;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_LANGUAGE_TYPE;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_MAP_NAME;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_PLACE_ID;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_PLACE_NAME;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.TABLE_NAME_CHASSIS_INFO;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.TABLE_NAME_MAP_INFO;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.TABLE_NAME_PLACE_INFO;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.TABLE_NAME_PLACE_INFO_BACKUP;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.TABLE_NAME_PLACE_NAME;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.hardware.RobotCore;
import com.ainirobot.navigationservice.Defs.Def;
import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.ClientConfigManager;
import com.ainirobot.navigationservice.commonModule.configuration.ConfigManager;
import com.ainirobot.navigationservice.commonModule.data.DataManager;
import com.ainirobot.navigationservice.commonModule.data.utils.UpdateTimeUtils;
import com.ainirobot.navigationservice.commonModule.data.utils.UuidUtils;
import com.ainirobot.navigationservice.db.entity.ChassisInfo;
import com.ainirobot.navigationservice.db.entity.ExtraInfo;
import com.ainirobot.navigationservice.db.entity.MapInfo;
import com.ainirobot.navigationservice.db.entity.MappingInfo;
import com.ainirobot.navigationservice.db.entity.MultiFloorInfo;
import com.ainirobot.navigationservice.db.entity.PlaceInfo;
import com.ainirobot.navigationservice.db.entity.PlaceName;
import com.ainirobot.navigationservice.db.sqlite.utils.ConvertObjectHelper;
import com.ainirobot.navigationservice.db.sqlite.utils.PropertiesHelper;
import com.ainirobot.navigationservice.roversdkhelper.MapFileHelper;
import com.ainirobot.navigationservice.utils.MapUtils;
import com.ainirobot.navigationservice.utils.NavigationConfig;
import com.ainirobot.navigationservice.utils.SpecialPlaceUtil;
import com.google.gson.reflect.TypeToken;

import java.io.File;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

public class SqliteDbMigrate {
    private static final String TAG = Def.MAP_DB_PRE + SqliteDbMigrate.class.getSimpleName();

    private static final String STATE = "STATE";
    private static final String TYPE_MAP_LOCAL_VERSION = "_local_version";
    private static final String MANUAL_UPLOAD = "_manual_upload";
    private static final String MAP_NAME = "mapName";
    private static final String TYPE_PLACE_SET_VERSION = "_place_version";
    private SQLiteDatabase mReadDb;
    private SQLiteDatabase mWriteDb;
    private final ConvertObjectHelper mConvertObjectHelper;
    private final PropertiesHelper mPropertiesHelper;
    private static volatile boolean sHasInit = false;
    private static volatile boolean sHasUpgrade4 = false;

    public SqliteDbMigrate(Context context) {
        mConvertObjectHelper = new ConvertObjectHelper();
        mPropertiesHelper = new PropertiesHelper();
        MapDbOpenHelper dbOpenHelper = new MapDbOpenHelper(context, this);
        mReadDb = dbOpenHelper.getReadableDatabase();
        mWriteDb = dbOpenHelper.getWritableDatabase();
    }

    public SQLiteDatabase getReadDb() {
        return mReadDb;
    }

    public SQLiteDatabase getWriteDb() {
        return mWriteDb;
    }

    public void close() {
        if (mReadDb != null && mReadDb.isOpen()) {
            mReadDb.close();
            mReadDb = null;
        }
        if (mWriteDb != null && mWriteDb.isOpen()) {
            mWriteDb.close();
            mWriteDb = null;
        }
    }

    public List<ChassisInfo> getDbChassisInfoList() {
        Cursor cursor = getCursor(TABLE_NAME_CHASSIS_INFO);
        if (null == cursor) {
            return null;
        }
        List<ChassisInfo> list = new ArrayList<>();
        boolean getIndex = false;
        Map<String, Integer> map = null;
        while (cursor.moveToNext()) {
            if (!getIndex) {
                map = mConvertObjectHelper.getChassisInfoIndex(cursor);
                getIndex = true;
            }
            list.add(mConvertObjectHelper.cursorToChassisInfo(cursor, map));
        }
        cursor.close();
        return list;
    }

    public List<PlaceInfo> getDbPlaceList() {
        Cursor cursor = getCursor(TABLE_NAME_PLACE_INFO);
        if (null == cursor) {
            return null;
        }
        List<PlaceInfo> list = new ArrayList<>();
        boolean getIndex = false;
        Map<String, Integer> map = null;
        while (cursor.moveToNext()) {
            if (!getIndex) {
                map = mConvertObjectHelper.getPlaceInfoIndex(cursor);
                getIndex = true;
            }
            list.add(mConvertObjectHelper.cursorToPlaceInfo(cursor, map));
        }
        cursor.close();
        return list;
    }

    public List<PlaceName> getDbPlaceNameList() {
        Cursor cursor = getCursor(TABLE_NAME_PLACE_NAME);
        if (null == cursor) {
            return null;
        }
        List<PlaceName> list = new ArrayList<>();
        boolean getIndex = false;
        Map<String, Integer> map = null;
        while (cursor.moveToNext()) {
            if (!getIndex) {
                map = mConvertObjectHelper.getPlaceNameIndex(cursor);
                getIndex = true;
            }
            list.add(mConvertObjectHelper.cursorToPlaceName(cursor, map));
        }
        cursor.close();
        return list;
    }

    public List<MapInfo> getDbMapInfoList() {
        return getMapInfos(getCursor(TABLE_NAME_MAP_INFO));
    }

    public List<MultiFloorInfo> getDbMultiFloorInfoList() {
        Cursor cursor = getCursor(TableInfoDef.TABLE_NAME_MULTI_FLOOR_INFO);
        if (null == cursor) {
            return null;
        }
        List<MultiFloorInfo> list = new ArrayList<>();
        boolean getIndex = false;
        Map<String, Integer> map = null;
        Type type = new TypeToken<List<String>>() {
        }.getType();
        while (cursor.moveToNext()) {
            if (!getIndex) {
                map = mConvertObjectHelper.getMultiFloorInfoIndex(cursor);
                getIndex = true;
            }
            list.add(mConvertObjectHelper.cursorToMultiFloorInfo(cursor, map, type));
        }
        cursor.close();
        return list;
    }

    public List<MappingInfo> getDbMappingList() {
        Cursor cursor = getCursor(TableInfoDef.TABLE_NAME_MAPPING_INFO);
        if (null == cursor) {
            return null;
        }
        List<MappingInfo> list = new ArrayList<>();
        boolean getIndex = false;
        Map<String, Integer> map = null;
        while (cursor.moveToNext()) {
            if (!getIndex) {
                map = mConvertObjectHelper.getMappingInfoIndex(cursor);
                getIndex = true;
            }
            list.add(mConvertObjectHelper.cursorToMappingInfo(cursor, map));
        }
        cursor.close();
        return list;
    }

    public List<ExtraInfo> getDbExtraInfoList() {
        Cursor cursor = getCursor(TableInfoDef.TABLE_NAME_EXTRA_INFO);
        if (null == cursor) {
            return null;
        }
        List<ExtraInfo> list = new ArrayList<>();
        boolean getIndex = false;
        Map<String, Integer> map = null;
        while (cursor.moveToNext()) {
            if (!getIndex) {
                map = mConvertObjectHelper.getExtraInfoIndex(cursor);
                getIndex = true;
            }
            list.add(mConvertObjectHelper.cursorToExtraInfo(cursor, map));
        }
        cursor.close();
        return list;
    }

    public void initNewCreateDbData(SQLiteDatabase db) {
        Log.d(TAG, "initNewCreateDbData: sHasInit=" + sHasInit);
        if (sHasInit) return;
        sHasInit = true;
        initData(db);
    }

    public void initUpgradeDbDataVersion4(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.d(TAG, "initUpgradeDbDataVersion4: oldVersion=" + oldVersion + " newVersion=" + newVersion + " sHasInit=" + sHasUpgrade4);
        if (sHasUpgrade4) return;
        sHasUpgrade4 = true;

        Properties naviProp = mPropertiesHelper.getNaviProperties();
        if (oldVersion < 3) {
            Log.d(TAG, "initUpgradeDbDataVersion4:updateCurrentMapLanguage:Start");
            updateCurrentMapLanguage(db, naviProp);
            Log.d(TAG, "initUpgradeDbDataVersion4:updateCurrentMapLanguage:Success");
        }
        initUpgradeDataVersion4(db, naviProp);
    }

    /**
     * 更新所有MapInfo内的target信息
     *
     * @param db
     */
    public void addMapTargetState(SQLiteDatabase db) {
        Log.d(TAG, "addMapTargetState:start");
        List<String> mapInfoMapNames = getMapList(db);
        if (mapInfoMapNames == null || mapInfoMapNames.size() <= 0) {
            Log.d(TAG, "addMapTargetState:error: No local map");
            return;
        }
        for (String mapName : mapInfoMapNames) {
            boolean isHasTargetData = MapFileHelper.isTargetsJsonExists(mapName);
            int targetDataState = isHasTargetData ? MapInfo.TargetDataState.HAS_TARGET_DATA : MapInfo.TargetDataState.NO_TARGET_DATA;
            updateMapInfoByName(db, mapName, targetDataState);
        }
        Log.d(TAG, "addMapTargetState:end");
    }

    public Map<String, Integer> getMapInfoIndex(Cursor cursor) {
        return mConvertObjectHelper.getMapInfoIndex(cursor);
    }

    public MapInfo cursorToMapInfo(Cursor cursor, Map<String, Integer> map) {
        return mConvertObjectHelper.cursorToMapInfo(cursor, map);
    }

    public ContentValues mapInfoToContentValues(MapInfo mapInfo) {
        return mConvertObjectHelper.mapInfoToContentValues(mapInfo);
    }

    public Map<String, Integer> getExtraInfoIndex(Cursor cursor) {
        return mConvertObjectHelper.getExtraInfoIndex(cursor);
    }

    public ExtraInfo cursorToExtraInfo(Cursor cursor, Map<String, Integer> map) {
        return mConvertObjectHelper.cursorToExtraInfo(cursor, map);
    }

    public ContentValues extraInfoToContentValues(ExtraInfo extraInfo) {
        return mConvertObjectHelper.extraInfoToContentValues(extraInfo);
    }

    public Map<String, Integer> getMappingInfoIndex(Cursor cursor) {
        return mConvertObjectHelper.getMappingInfoIndex(cursor);
    }

    public MappingInfo cursorToMappingInfo(Cursor cursor, Map<String, Integer> map) {
        return mConvertObjectHelper.cursorToMappingInfo(cursor, map);
    }

    public ContentValues mappingInfoToContentValues(MappingInfo mappingInfo) {
        return mConvertObjectHelper.mappingInfoToContentValues(mappingInfo);
    }

    public Map<String, Integer> getMultiFloorInfoIndex(Cursor cursor) {
        return mConvertObjectHelper.getMultiFloorInfoIndex(cursor);
    }

    public MultiFloorInfo cursorToMultiFloorInfo(Cursor cursor, Map<String, Integer> map, Type type) {
        return mConvertObjectHelper.cursorToMultiFloorInfo(cursor, map, type);
    }

    public ContentValues multiFloorInfoToContentValues(MultiFloorInfo multiFloorInfo) {
        return mConvertObjectHelper.multiFloorInfoToContentValues(multiFloorInfo);
    }

    public Map<String, Integer> getPlaceNameIndex(Cursor cursor) {
        return mConvertObjectHelper.getPlaceNameIndex(cursor);
    }

    public PlaceName cursorToPlaceName(Cursor cursor, Map<String, Integer> map) {
        return mConvertObjectHelper.cursorToPlaceName(cursor, map);
    }

    public ContentValues placeNameToContentValues(PlaceName placeName) {
        return mConvertObjectHelper.placeNameContentValues(placeName);
    }

    public Map<String, Integer> getPlaceInfoIndex(Cursor cursor) {
        return mConvertObjectHelper.getPlaceInfoIndex(cursor);
    }

    public PlaceInfo cursorToPlaceInfo(Cursor cursor, Map<String, Integer> map) {
        return mConvertObjectHelper.cursorToPlaceInfo(cursor, map);
    }

    public ContentValues placeInfoToContentValues(PlaceInfo placeInfo) {
        return mConvertObjectHelper.placeInfoToContentValues(placeInfo);
    }

    public Map<String, Integer> getChassisInfoIndex(Cursor cursor) {
        return mConvertObjectHelper.getChassisInfoIndex(cursor);
    }

    public ContentValues chassisInfoToContentValues(ChassisInfo chassisInfo) {
        return mConvertObjectHelper.chassisInfoToContentValues(chassisInfo);
    }

    private boolean updateMapInfoByName(SQLiteDatabase db, String mapName, int targetState) {
        Log.d(TAG, "updateMapInfoByName: mapName=" + mapName);
        ContentValues contentValues = new ContentValues();
        contentValues.put(COLUMN_HAS_TARGET_DATA, targetState);
        int row = db.update(TABLE_NAME_MAP_INFO, contentValues, COLUMN_MAP_NAME + "=?", new String[]{mapName});
        Log.d(TAG, "updateMapInfoByName: mapName=" + mapName + " result:" + row);
        return row != -1;
    }

    private void initUpgradeDataVersion4(SQLiteDatabase db, Properties naviProp) {
        Log.d(TAG, "initUpgradeDataVersion4: Start <<=======================");
        boolean naviPropNull = isNaviPropNull(naviProp);
        addChassisInfoFromProperties(db, naviPropNull, naviProp);
        List<String> localMapList = MapFileHelper.getMapList();
        if (localMapList == null || localMapList.size() <= 0) {
            Log.d(TAG, "initUpgradeDataVersion4: No local map files");
            return;
        }
        if (naviPropNull) {
            Log.d(TAG, "initUpgradeDataVersion4: No navigation.properties file");
            return;
        }
        Log.d(TAG, "initUpgradeDataVersion4: localMapList=" + localMapList);
        Properties mapToolProp = mPropertiesHelper.getMapToolProperties();
        addMapInfoFromProperties(db, localMapList, naviProp, mapToolProp);
        addPlaceListFromTempTableVersion4(db);//从临时表恢复点位
        mPropertiesHelper.deleteAllPropertiesFile();
        Log.d(TAG, "initUpgradeDataVersion4: End <<=======================");
    }

    private void addPlaceListFromTempTableVersion4(SQLiteDatabase db) {
        Log.d(TAG, "addPlaceListFromTempTable:start");
        List<String> mapInfoMapNames = getMapList(db);
        if (mapInfoMapNames == null || mapInfoMapNames.size() <= 0) {
            Log.d(TAG, "addPlaceListFromTempTable:error: No local map");
            return;
        }
        for (String mapName : mapInfoMapNames) {
            List<PlaceInfo> backupList = getPlaceListFromTempTable(db, mapName);
            if (backupList == null || backupList.size() <= 0) {
                Log.d(TAG, "addPlaceListFromTempTable:error: No place in map " + mapName);
                continue;
            }
            for (PlaceInfo backupPlace : backupList) {
                addPlace(db, backupPlace);
            }
        }

        if (RobotCore.isConnected()) {
            RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, Definition.REPORT_NAVI_CONFIG, Definition.NAVI_UPDATE_FROM_OLD_PLACE);
        }
        Log.d(TAG, "addPlaceListFromProperties:end");
    }

    private List<String> getMapList(SQLiteDatabase db) {
        Cursor cursor = db.query(TABLE_NAME_MAP_INFO, new String[]{COLUMN_MAP_NAME}, null, null, COLUMN_MAP_NAME, null, null, null);
        List<String> mapNameList = new ArrayList<>();
        while (cursor.moveToNext()) {
            mapNameList.add(cursor.getString(0));
        }
        Log.d(TAG, "getMapByName: mapNameList=" + mapNameList);
        cursor.close();
        return mapNameList;
    }

    /**
     * 兼容没有地图主语言版本
     */
    private void updateCurrentMapLanguage(SQLiteDatabase db, Properties naviProp) {
        List<String> mapList = MapFileHelper.getMapList();
        boolean naviPropNull = isNaviPropNull(naviProp);
        if (!naviPropNull && mapList != null && mapList.size() > 0) {
            Log.d(TAG, "updateCurrentMapLanguage:Start");
            for (String mapName : mapList) {
                String mapLanguage = getMaxLanguageFromTempTable(db, mapName);
                naviProp.setProperty(DataManager.getInstance().getTypeMapLanguage(mapName), mapLanguage);
                Log.d(TAG, "updateCurrentMapLanguage: mapName=" + mapName + "  mapLanguage=" + mapLanguage);
                List<PlaceInfo> placeBeanList = getPlaceListFromTempTable(db, mapName);
                Log.d(TAG, "updateCurrentMapLanguage: mapName=" + mapName + "  placeBeanList=" + placeBeanList);
                //检查特殊点英文名并更新到临时表，新表必须有mapId才能插入
                checkSpecialPlace(db, placeBeanList);//兼容没有地图主语言版本
            }
            Log.d(TAG, "updateCurrentMapLanguage:saveProperties");
            mPropertiesHelper.saveNaviProperties(naviProp); //更新主语言到prop
        }
        if (RobotCore.isConnected()) {
            RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, Definition.REPORT_NAVI_CONFIG, Definition.NAVI_UPDATE_FROM_OLD_PLACE);
        }
    }

    private void checkSpecialPlace(SQLiteDatabase db, List<PlaceInfo> placeInfoList) {
        for (PlaceInfo placeInfo : placeInfoList) {
            checkSpecialPlace(placeInfo);////兼容没有地图主语言版本
        }
        for (PlaceInfo placeBean : placeInfoList) {
            if (updatePlaceInfoToTempTable(db, placeBean)) { //place_info_backup
                replacePlaceName(db, placeBean); //place_name
            }
        }
    }

    private synchronized void replacePlaceName(SQLiteDatabase db, PlaceInfo placeBean) {
        Log.d(TAG, "replacePlaceName: placeBean:" + placeBean.toString());
        int row = db.delete(TABLE_NAME_PLACE_NAME, COLUMN_PLACE_ID + "=?", new String[]{placeBean.getPlaceId()});
        Log.d(TAG, "replacePlaceName: delete row=" + row);
        for (Map.Entry<String, String> placeName : placeBean.getPlaceNameList().entrySet()) {
            ContentValues values = mConvertObjectHelper.placeNameContentValues(placeBean.getPlaceId(), placeName.getKey(), placeName.getValue());
            long insertRowId = db.insert(TABLE_NAME_PLACE_NAME, null, values);
            Log.d(TAG, "replacePlaceName: insertRowId=" + insertRowId);
        }
    }

    private boolean updatePlaceInfoToTempTable(SQLiteDatabase db, PlaceInfo placeInfo) {
        Log.d(TAG, "updatePlaceInfoToTempTable: placeInfo=" + placeInfo.toString());
        ContentValues values = mConvertObjectHelper.placeInfoToContentValues(placeInfo);
        int row = db.update(TABLE_NAME_PLACE_INFO_BACKUP, values, COLUMN_PLACE_ID + "=?", new String[]{placeInfo.getPlaceId()});
        Log.d(TAG, "updatePlaceInfoToTempTable: row=" + row);
        return row != -1;
    }

    private List<PlaceInfo> getPlaceListFromTempTable(SQLiteDatabase db, String mapName) {
        if (TextUtils.isEmpty(mapName)) {
            Log.d(TAG, "getPlaceListFromTempTable: mapName null");
            return new ArrayList<>();
        }
        List<PlaceInfo> placeList = new ArrayList<>();
        Cursor cursor = db.query(TABLE_NAME_PLACE_INFO_BACKUP, null, COLUMN_MAP_NAME + "=?", new String[]{mapName}, null, null, null);
        boolean updatePlaceInfoIndex = false;
        boolean updatePlaceNameIndex = false;
        Map<String, Integer> map = null;
        while (cursor.moveToNext()) {
            if (!updatePlaceInfoIndex) {
                map = mConvertObjectHelper.getPlaceInfoIndex(cursor);
                updatePlaceInfoIndex = true;
            }
            PlaceInfo place = mConvertObjectHelper.cursorToPlaceInfo(cursor, map);
            Cursor nameCursor = db.query(TABLE_NAME_PLACE_NAME, null, COLUMN_PLACE_ID + "=?", new String[]{place.getPlaceId()}, null, null, null);
            while (nameCursor.moveToNext()) {
                if (!updatePlaceNameIndex) {
                    map.put(COLUMN_LANGUAGE_TYPE, nameCursor.getColumnIndex(COLUMN_LANGUAGE_TYPE));
                    map.put(COLUMN_PLACE_NAME, nameCursor.getColumnIndex(COLUMN_PLACE_NAME));
                    updatePlaceNameIndex = true;
                }
                place.addPlaceName(mConvertObjectHelper.getCursorString(nameCursor, map, COLUMN_LANGUAGE_TYPE), mConvertObjectHelper.getCursorString(nameCursor, map, COLUMN_PLACE_NAME));
            }
            placeList.add(place);
        }
        Log.d(TAG, "getPlaceListFromTempTable: placeList=" + placeList);
        cursor.close();
        return placeList;
    }

    //获取点位名称中最多的语言类型
    private String getMaxLanguageFromTempTable(SQLiteDatabase db, String mapName) {
        String sql = "select language_type, max(num) from " + "(select language_type, count(1) as num from place_name where place_id in (" + "select place_id from place_info_backup where map_name=?)" + " group by language_type)";
        Cursor cursor = db.rawQuery(sql, new String[]{mapName});
        String language = "";
        if (cursor != null && cursor.moveToNext()) {
            Log.d(TAG, "getMaxLanguageFromTempTable: cursor not null");
            language = cursor.getString(cursor.getColumnIndex(COLUMN_LANGUAGE_TYPE));
        }
        Log.d(TAG, "getMaxLanguageFromTempTable: language=" + language);
        if (TextUtils.isEmpty(language)) {
            language = Locale.SIMPLIFIED_CHINESE.toString();
        }
        cursor.close();
        return language;
    }

    /**
     * 兼容properties存储数据版本和version<3的版本
     * <p>
     * 初始化顺序：chassis_info、map_info、place_info & place_name
     */
    private void initData(SQLiteDatabase db) {
        Log.d(TAG, "initData: Start <<=======================");
        Properties naviProp = mPropertiesHelper.getNaviProperties();
        boolean naviPropNull = isNaviPropNull(naviProp);
        addChassisInfoFromProperties(db, naviPropNull, naviProp);
        List<String> localMapList = MapFileHelper.getMapList();
        if (localMapList == null || localMapList.size() <= 0) {
            Log.d(TAG, "initData: No local map files");
            return;
        }
        if (naviPropNull) {
            Log.d(TAG, "initData: No navigation.properties file");
            return;
        }
        Properties mapToolProp = mPropertiesHelper.getMapToolProperties();
        Log.d(TAG, "initData: localMapList=" + localMapList);
        addMapInfoFromProperties(db, localMapList, naviProp, mapToolProp);
        addPlaceListFromProperties(db, naviProp, mapToolProp);//从properties文件恢复点位
        mPropertiesHelper.deleteAllPropertiesFile();
        Log.d(TAG, "initData: End <<=======================");
    }

    /**
     * place_info、place_name 表
     * 如果存在navigation.properties，点位信息转存到数据库
     */
    private void addPlaceListFromProperties(SQLiteDatabase db, Properties properties, Properties mapToolProp) {
        Log.d(TAG, "addPlaceListFromProperties:start--->>>");
        List<Pose> placeList = mPropertiesHelper.getAllPlaceListFromProperties(properties);
        List<PlaceInfo> placeBeanList = new ArrayList<>();
        for (Pose pose : placeList) {
            String name = pose.getName();
            int index = name.indexOf("-");
            if (index < 0) {
                continue;
            }
            int nameIndex = name.indexOf("_", index);
            if (nameIndex < 0) {
                continue;
            }
            String mapName = name.substring(0, nameIndex);
            String poseName = name.substring(nameIndex + 1);
            Log.d(TAG, "addPlaceListFromProperties: mapName=" + mapName + " poseName=" + poseName);
            pose.setName(poseName);
            MapInfo mapInfo = getMapByName(db, mapName);
            if (mapInfo == null) {
                Log.d(TAG, "addPlaceListFromProperties:error: No map: mapName=" + mapName);
                continue;
            }
            PlaceInfo placeBean = DataManager.getInstance().poseToPlaceBean(pose, Locale.SIMPLIFIED_CHINESE.toString(), mapName);
            String version = mPropertiesHelper.getFromProperties(mapToolProp, mapName + TYPE_PLACE_SET_VERSION);
            long updateTime = TextUtils.isEmpty(version) ? 0 : Long.parseLong(version);
            placeBean.setUpdateTime(updateTime);
            placeBeanList.add(placeBean);
        }
        for (PlaceInfo placeInfo : placeBeanList) {
            checkSpecialPlace(placeInfo);//addPlaceListFromProperties
            addPlace(db, placeInfo);
            addPlaceName(db, placeInfo);
        }

        List<MapInfo> mapList = getDbMapInfoList();
        if (null != mapList && mapList.size() > 0) {
            if (RobotCore.isConnected()) {
                RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, Definition.REPORT_NAVI_CONFIG, Definition.NAVI_UPDATE_FROM_OLD_PLACE);
            }
        }
        Log.d(TAG, "addPlaceListFromProperties:end <<<---");
    }

    private boolean addPlaceName(SQLiteDatabase db, PlaceInfo placeInfo) {
        Log.i(TAG, "addPlaceName: placeInfo=" + placeInfo.toString());
        for (Map.Entry<String, String> entry : placeInfo.getPlaceNameList().entrySet()) {
            long insertRowId = db.insert(TABLE_NAME_PLACE_NAME, null, mConvertObjectHelper.placeNameContentValues(placeInfo.getPlaceId(), entry.getKey(), entry.getValue()));
            if (insertRowId == -1) {
                Log.d(TAG, "addPlaceName:failed: placeId=" + placeInfo.getPlaceId());
                return false;
            }
        }
        Log.d(TAG, "addPlaceName: success");
        return true;
    }

    private boolean addPlace(SQLiteDatabase db, PlaceInfo placeInfo) {
        Log.d(TAG, "addPlaceInfo: placeInfo=" + placeInfo.toString());
        ContentValues values = mConvertObjectHelper.placeInfoToContentValues(placeInfo);
        long insertRowId = db.insert(TABLE_NAME_PLACE_INFO, null, values);
        Log.d(TAG, "addPlaceInfo: insertRowId=" + insertRowId);
        return insertRowId != -1;
    }

    /**
     * 特殊点位，添加英文名称
     * 这里是兼容 Properties 和 没有地图主语言的版本逻辑，不需要考虑电梯中心、电梯口这几个点的多语言配置，因为旧版本没有这几个点
     */
    private void checkSpecialPlace(PlaceInfo placeBean) {
        HashMap<String, List<String>> specialPlace = SpecialPlaceUtil.getInstance().getLangSpecialPlace();

        Map<String, String> placeNameList = placeBean.getPlaceNameList();
        if (placeNameList.size() == 1) {
            Iterator<Map.Entry<String, String>> it = placeNameList.entrySet().iterator();
            Map.Entry<String, String> name = it.next();
            for (List<String> nameList : specialPlace.values()) {
                int index = nameList.indexOf(name.getValue());
                if (index != -1) {
                    for (Map.Entry<String, List<String>> specialName : specialPlace.entrySet()) {
                        placeBean.addPlaceName(specialName.getKey(), specialName.getValue().get(index));
                    }
                }
            }
        }
    }

    private MapInfo getMapByName(SQLiteDatabase db, String mapName) {
        Log.d(TAG, "getMapByName: mapName=" + mapName);
        Cursor cursor = null;
        try {
            cursor = db.query(TABLE_NAME_MAP_INFO, null, COLUMN_MAP_NAME + "=?", new String[]{mapName}, null, null, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<MapInfo> mapInfos = getMapInfos(cursor);
        return mapInfos != null && mapInfos.size() > 0 ? mapInfos.get(0) : null;
    }

    /**
     * map_info 表
     * 如果存在 navigation.properties，地图信息转存到数据库
     */
    private void addMapInfoFromProperties(SQLiteDatabase db, List<String> mapNameList, Properties naviProp, Properties mapToolProp) {
        for (String name : mapNameList) {
            String md5 = mPropertiesHelper.getMapMd5FromProperties(naviProp, name);
            if (TextUtils.isEmpty(md5)) {
                Log.d(TAG, "addMapInfoFromProperties:error: Md5 empty");
                File mapPkg = MapFileHelper.getMapPgm(name);
                if (!mapPkg.exists()) {
                    Log.d(TAG, "addMapInfoFromProperties:error:pgm.zip not exists");
                    continue;
                }
                md5 = com.ainirobot.navigationservice.utils.MD5.getFileMd5(mapPkg.getAbsolutePath());
            }
            int mapType = "true".equals(mPropertiesHelper.getHasVisionFromProperties(naviProp, name)) ? MapInfo.MapType.VISION : MapInfo.MapType.NORMAL;
            String state = mPropertiesHelper.getFromProperties(mapToolProp, STATE + "_" + name);
            int finishState = TextUtils.isEmpty(state) ? 0 : Integer.parseInt(state);
            String version = mPropertiesHelper.getFromProperties(mapToolProp, name + TYPE_MAP_LOCAL_VERSION);
            long updateTime = TextUtils.isEmpty(version) ? 0 : Long.parseLong(version);
            //旧版本时间戳存储精度是秒
            String updateDate = UpdateTimeUtils.timestampToDateSeconds(updateTime);
            boolean isHasTargetData = MapUtils.isHasTargetsInfo(name);
            int targetDataState = isHasTargetData ? MapInfo.TargetDataState.HAS_TARGET_DATA : MapInfo.TargetDataState.NO_TARGET_DATA;
            Log.d(TAG, "addMapInfoFromProperties: version=" + version + " updateTime=" + updateTime + " updateDate=" + updateDate + " name:" + name + " target:" + targetDataState);
            int syncState = "true".equals(mPropertiesHelper.getFromProperties(mapToolProp, name + MANUAL_UPLOAD)) ? MapInfo.SyncState.NOT_UPLOADED : MapInfo.SyncState.UPLOADED;
            String curMapName = mPropertiesHelper.getFromProperties(naviProp, MAP_NAME);
            MapInfo mapInfo = new MapInfo();
            mapInfo.setMapName(name).
                    setMapId(UuidUtils.createMapId(name)).
                    setMd5(md5).setMapType(mapType).
                    setForbidLine(mPropertiesHelper.getForbidLineFlagFromProperties(naviProp, name)).
                    setPatrolRoute(mPropertiesHelper.getFromProperties(naviProp, name)).
                    setFinishState(finishState).setUpdateTime(updateDate).
                    setSyncState(syncState).
                    setUseState(name.equals(curMapName) ? MapInfo.UseState.IN_USE : MapInfo.UseState.NO_USE).
                    setMapLanguage(mPropertiesHelper.getSpecialMapLanguageFromProperties(naviProp, name)).
                    setTargetData(targetDataState);
            Log.d(TAG, "addMapInfoFromProperties: mapInfo=" + mapInfo);
            insertMapInfo(db, mapInfo);
        }
    }

    /**
     * chassis_info 表
     * 读 properties 或 生成默认配置
     */
    private void addChassisInfoFromProperties(SQLiteDatabase db, boolean naviPropNull, Properties properties) {
        ChassisInfo chassisInfo = new ChassisInfo();
        if (!naviPropNull) {
            Log.d(TAG, "addChassisInfoFromProperties: From properties");
            chassisInfo.setRoverConfig(mPropertiesHelper.getRoverConfigFromProperties(properties)).
                    setIpNavigation(mPropertiesHelper.getNavIpFromProperties(properties)).
                    setIpRos(mPropertiesHelper.getRosIpFromProperties(properties)).
                    setIpSdkRos(mPropertiesHelper.getSdkRosIpFromProperties(properties));
        } else {
            Log.d(TAG, "addChassisInfoFromProperties: Generate default");
            String roverConfig = "";
            //首次安装，数据库初始化时config还未解析，roverConfig等到onMount之后client初始时生成
            if (ConfigManager.getInstance().isChassisConfigReady()) {
                Log.d(TAG, "addChassisInfoFromProperties: getDefaultRoverConfig");
                roverConfig = ClientConfigManager.getInstance().getDefaultRoverConfig();
            }
            chassisInfo.setRoverConfig(roverConfig).
                    setIpNavigation(NavigationConfig.DEFAULT_NAVIGATION_IP).
                    setIpRos(NavigationConfig.DEFAULT_ROS_IP).
                    setIpSdkRos(NavigationConfig.DEFAULT_SDK_ROS_IP);
        }
        Log.d(TAG, "addChassisInfoFromProperties: insert new chassis info");
        insertChassisInfo(db, chassisInfo);
    }

    private boolean insertMapInfo(SQLiteDatabase db, MapInfo mapInfo) {
        Log.d(TAG, "insertMapInfo: mapInfo=" + mapInfo.toString());
        long insertRowId = db.insert(TABLE_NAME_MAP_INFO, null, mConvertObjectHelper.mapInfoToContentValues(mapInfo));
        Log.d(TAG, "insertMapInfo: insertRowId=" + insertRowId);
        return insertRowId != -1;
    }

    private boolean insertChassisInfo(SQLiteDatabase db, ChassisInfo chassisInfo) {
        Log.d(TAG, "insertChassisInfo: chassisInfo=" + chassisInfo.toString());
        long insertRowId = db.insert(TABLE_NAME_CHASSIS_INFO, null, mConvertObjectHelper.chassisInfoToContentValues(chassisInfo));
        Log.d(TAG, "insertChassisInfo: insertRowId=" + insertRowId);
        return insertRowId != -1;
    }

    //检查底盘properties属性文件是否为null
    private synchronized boolean isNaviPropNull(Properties properties) {
        if (properties == null) {
            Log.e(TAG, "isNaviPropNull: navigation.properties null");
            return true;
        }
        if (properties.size() == 0) {
            Log.e(TAG, "isNaviPropNull: navigation.properties empty");
            return true;
        }
        return false;
    }

    private List<MapInfo> getMapInfos(Cursor cursor) {
        if (null == cursor) {
            return null;
        }
        List<MapInfo> list = new ArrayList<>();
        boolean getIndex = false;
        Map<String, Integer> map = null;
        while (cursor.moveToNext()) {
            if (!getIndex) {
                map = getMapInfoIndex(cursor);
                getIndex = true;
            }
            list.add(mConvertObjectHelper.cursorToMapInfo(cursor, map));
        }
        cursor.close();
        return list;
    }

    private Cursor getCursor(String tableName) {
        try {
            String query = "SELECT * FROM " + tableName;
            return mReadDb.rawQuery(query, null);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
