package com.ainirobot.navigationservice.db.entity;

import io.objectbox.annotation.Entity;
import io.objectbox.annotation.Id;
import io.objectbox.annotation.TargetIdProperty;
import io.objectbox.relation.ToMany;
import io.objectbox.relation.ToOne;

/**
 * 特殊点位类型表
 */
@Entity
public class PlaceType {
    @Id
    public long id;
    /**
     * 特殊点位类型id
     */
    private int typeId;
    /**
     * 特殊点位类型名称 英文名称
     */
    private String typeName;
    /**
     * 特殊点位类型描述 中文名称
     */
    private String typeDescription;
    /**
     * 和TypeName数据表关联 一对多
     */
    public ToMany<PlaceName> placeNameToMany;

    public PlaceType() {
    }

    public PlaceType(int typeId, String typeName, String typeDescription) {
        this.typeId = typeId;
        this.typeName = typeName;
        this.typeDescription = typeDescription;
    }

    public void setTypeId(int typeId) {
        this.typeId = typeId;
    }

    public int getTypeId() {
        return typeId;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeDescription(String typeDescription) {
        this.typeDescription = typeDescription;
    }

    public String getTypeDescription() {
        return typeDescription;
    }
}
