package com.ainirobot.navigationservice.beans.waiter;

public class MultiRobotStatus {

    /**
     * extra_data base64加密字符串
     */
    private String extra_data_base64;

    /**
     * 当前点位
     */
    private BasePoseBean pose;

    /**
     * 目标点位
     */
    private BasePoseBean goal;

    /**
     * 机器人编号，当优先级相等时，机器人编号越大优先级越高
     */
    private int id;

    /**
     * 地图id
     */
    private String map_id;

    /**
     * 机器人优先级
     */
    private int priority;

    /**
     * 机器人地图是否匹配，暂不启用，属于预留字段
     */
    private boolean mapMatch;

    /**
     * 当前时间戳,用于判断消息是否失效
     */
    private long time;

    /**
     * 当前机器导航状态，1表示无定位状态，2未在导航状态 3表示在导航中
     */
    private int status;

    /**
     * 是否是当前机器，如果不是当前机器，errorstatus数据忽略
     */
    private boolean curRobot = false;

    /**
     * 机器人的异常状态，出现任何异常状态后机器人都不再运动，该状态只有当前机器才能收到
     * 0为正常 1为地图不匹配 2为多机版本不匹配 3为lora断连
     */
    private int errorStatus = 0;
    /**
     * 机器距离目标点距离
     */
    private double distance = 0;

    public MultiRobotStatus() {

    }

    public MultiRobotStatus(BasePoseBean pose, BasePoseBean goal, int id, int priority) {
        this.time = System.currentTimeMillis();
        this.pose = pose;
        this.goal = goal;
        this.id = id;
        this.priority = priority;
    }

    public MultiRobotStatus(BasePoseBean pose, BasePoseBean goal, int id, int priority,
                            boolean mapMatch, long time, int status) {
        this.pose = pose;
        this.goal = goal;
        this.id = id;
        this.priority = priority;
        this.mapMatch = mapMatch;
        this.time = time;
        this.status = status;
    }

    public MultiRobotStatus(BasePoseBean pose, BasePoseBean goal, int id, int priority,
                            boolean mapMatch, long time, int status, boolean curRobot,
                            int errorStatus, double distance) {
        this.pose = pose;
        this.goal = goal;
        this.id = id;
        this.priority = priority;
        this.mapMatch = mapMatch;
        this.time = time;
        this.status = status;
        this.curRobot = curRobot;
        this.errorStatus = errorStatus;
        this.distance = distance;
    }

    public MultiRobotStatus(BasePoseBean pose, BasePoseBean goal, int id, int priority,
                            boolean mapMatch, long time, int status, boolean curRobot,
                            int errorStatus, double distance, String mapId, String extraDataBase64) {
        this.pose = pose;
        this.goal = goal;
        this.id = id;
        this.map_id = mapId;
        this.priority = priority;
        this.mapMatch = mapMatch;
        this.time = time;
        this.status = status;
        this.curRobot = curRobot;
        this.errorStatus = errorStatus;
        this.distance = distance;
        this.extra_data_base64 = extraDataBase64;
    }

    public BasePoseBean getPose() {
        return pose;
    }

    public void setPose(BasePoseBean pose) {
        this.pose = pose;
    }

    public BasePoseBean getGoal() {
        return goal;
    }

    public void setGoal(BasePoseBean goal) {
        this.goal = goal;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public boolean isMapMatch() {
        return mapMatch;
    }

    public void setMapMatch(boolean mapMatch) {
        this.mapMatch = mapMatch;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public boolean isCurRobot() {
        return curRobot;
    }

    public void setCurRobot(boolean curRobot) {
        this.curRobot = curRobot;
    }

    public int getErrorStatus() {
        return errorStatus;
    }

    public void setErrorStatus(int errorStatus) {
        this.errorStatus = errorStatus;
    }

    public double getDistance() {
        return distance;
    }

    public void setDistance(double distance) {
        this.distance = distance;
    }

    public String getMap_id() {
        return map_id;
    }

    public void setMap_id(String map_id) {
        this.map_id = map_id;
    }

    public String getExtra_data_base64() {
        return extra_data_base64;
    }

    public void setExtra_data_base64(String extra_data_base64) {
        this.extra_data_base64 = extra_data_base64;
    }

    @Override
    public String toString() {
        return "MultiRobotStatus{" +
                "extra_data_base64=" + extra_data_base64 +
                "pose=" + pose +
                ", goal=" + goal +
                ", id=" + id +
                ", map_id=" + map_id +
                ", priority=" + priority +
                ", mapMatch=" + mapMatch +
                ", time=" + time +
                ", status=" + status +
                ", curRobot=" + curRobot +
                ", errorStatus=" + errorStatus +
                ", distance=" + distance +
                '}';
    }
}