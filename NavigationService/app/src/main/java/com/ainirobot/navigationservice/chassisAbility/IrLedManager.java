package com.ainirobot.navigationservice.chassisAbility;

import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.hardware.RobotCore;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.db.NavigationDataManager;
import com.ainirobot.navigationservice.db.entity.MapInfo;
import com.ainirobot.navigationservice.roversdkhelper.maptype.MapTypeHelper;
import com.ainirobot.navigationservice.roversdkhelper.maptype.NaviMapType;
import com.ainirobot.navigationservice.utils.ThreadUtils;

/**
 * Ir Led 灯管理类
 * 1. 非标签模式，保持关闭
 * 2. 标签模式，非移动状态下关闭, 移动状态下打开
 * 生效机型：招财标准版，ProductInfo.isSaiph()
 */
public class IrLedManager {
    private static final String TAG = "IrLedManager";

    private static IrLedManager instance;

    private IrLedManager() {
    }

    public static IrLedManager getInstance() {
        if (instance == null) {
            synchronized (IrLedManager.class) {
                if (instance == null) {
                    instance = new IrLedManager();
                }
            }
        }
        return instance;
    }

    //Ir led 灯开关状态，开始默认是打开的
    private boolean isIrLedOpen = true;
    //是否标签模式，地图加载成功后设置
    private boolean isTagMode = false;
    //里程计直线移动距离
    private double moveDistanceOffset = 0;
    //检查次数，每1s检查一次
    private int checkCount = 0;

    //是否正在创建地图
    private volatile boolean isCreatingMap = false;

    //正在创建的地图类型 NaviMapType
    private NaviMapType creatingMapType = null;

    /**
     * 开始监听里程计数据
     */
    public void startListenOdometer() {
        if (!ProductInfo.isSaiph()) {
            //非招财标准版本，不自动切换 Ir led 灯开关状态
            Log.d(TAG, "startListenOdometer: Not Saiph, return");
            return;
        }

        //监听里程计数据
        ChassisManager.getInstance().getChassisClient().setOdomUpdateListener(new IChassisClient.OdomUpdate() {
            @Override
            public void updateOdom(double move, double leftAcc, double rightAcc) {
                moveDistanceOffset += move;
            }
        });

        /**
         * 移动和静止状态的判断
         * 避免移动时led未打开影响导航，所以移动状态的条件要灵敏一些，静止的条件要稳定一些
         * 1. 移动状态：持续1s移动距离大于0.01m
         * 2. 静止状态：持续10s移动距离小于0.1m
         */
        ThreadUtils.getCpuService().submit(new Runnable() {
            @Override
            public void run() {
                while (true) {
                    // 线程休眠一段时间，模拟周期性检测
                    try {
                        Thread.sleep(1000); // 每1秒检测一次
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }

                    /**
                     * 未定位状态下，保持打开 IR LED，保证开始定位的时候 IR LED 是打开的。
                     * （折中方案，既保证定位的准确性，又满足当前省电需求，机器正常使用状态下都处于定位正常状态）
                     */
                    if (!ChassisManager.getInstance().getChassisClient().isPoseEstimate()) {
                        if (!isIrLedOpen) {
                            openIrLed();
                        }
                        continue;
                    }

                    //正在创建target地图，保持打开
                    if (isCreatingTargetMap()) {
                        if (!isIrLedOpen) {
                            openIrLed();
                        }
                        continue;
                    }

                    //非标签模式，保持关闭
                    if (!isTagMode) {
                        if (isIrLedOpen) {
                            closeIrLed();
                        }
                        continue;
                    }

                    //移动状态，打开 IR LED
                    if (moveDistanceOffset > 0.01) {
                        if (!isIrLedOpen) {
                            openIrLed();
                        }
                        checkCount = 0;
                        moveDistanceOffset = 0;
                        continue;
                    }

                    //静止状态，关闭 IR LED
                    if (checkCount < 10) {
                        checkCount++;
                        continue;
                    }
                    if (checkCount == 10) {
                        if (moveDistanceOffset < 0.1 && isIrLedOpen) {
                            closeIrLed();
                        }
                        checkCount = 0;
                        moveDistanceOffset = 0;
                    }
                }
            }
        });
    }

    /**
     * 设置是否标签模式
     * 出发时机：每次loadMap接口调用后设置，不管成功失败
     */
    public void setTagMode() {
        if (!ProductInfo.isSaiph()) {
            //非招财标准版本，不自动切换 Ir led 灯开关状态
            Log.d(TAG, "setTagMode: Not Saiph, return");
            return;
        }

        //标签模式判断
        MapInfo mapInfo = NavigationDataManager.getInstance().getNavMapInfo();
        if (mapInfo == null) {
            isTagMode = false;
            return;
        }
        boolean supportTagMode = MapTypeHelper.checkRobotAndMapSupportTarget(mapInfo.getMapType());
        Log.d(TAG, "setTagMode: supportTagMode=" + supportTagMode + ", isTagMode=" + isTagMode);
        isTagMode = supportTagMode;
    }

    public void openIrLed() {
        Log.d(TAG, "openIrLed: Open IR LED");
        isIrLedOpen = true;
        RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, "ir_led_status_change", 1 + "");
    }

    private void closeIrLed() {
        Log.d(TAG, "closeIrLed: Close IR LED");
        isIrLedOpen = false;
        RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, "ir_led_status_change", 0 + "");
    }

    public void setCreatingMap(boolean creatingMap) {
        Log.d(TAG, "setCreatingMap: creatingMap=" + creatingMap + ", isCreatingMap=" + isCreatingMap);
        isCreatingMap = creatingMap;
    }

    public void setCreatingMapType(NaviMapType creatingMapType) {
        Log.d(TAG, "setCreatingMapType: creatingMapType=" + creatingMapType);
        this.creatingMapType = creatingMapType;
    }

    /**
     * 是否正在创建target图
     */
    public boolean isCreatingTargetMap() {
        return isCreatingMap && creatingMapType != null && creatingMapType.hasTarget();
    }
}
