package com.ainirobot.navigationservice.chassisAbility.chassis.client.tk1;

import static com.ainirobot.navigationservice.Defs.Def.ResultCode.FAIL_NO_REASON;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.MOTION_AVOID_STOP;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.SUCCESS;
import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;
import static com.ainirobot.navigationservice.beans.tk1.WorkMode.CREATING_MAP;
import static com.ainirobot.navigationservice.beans.tk1.WorkMode.FREE;
import static com.ainirobot.navigationservice.beans.tk1.WorkMode.INIT_MODE;
import static com.ainirobot.navigationservice.beans.tk1.WorkMode.NAVIGATION;
import static com.ainirobot.navigationservice.beans.tk1.WorkMode.SELF_CHECKING;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_REPORT_STATISTIC;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_UPDATE_EVENT;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_UPDATE_GLOBAL_MAP;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_UPDATE_LASER;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_UPDATE_MAPPING_POSE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_UPDATE_POSE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_UPDATE_REALTIME_MAP;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_UPDATE_VELOCITY;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.EventTk1Def.EVENT_WORKING_MODE_STATE_CHANGE;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.navigationservice.Defs.NavigationResult;
import com.ainirobot.navigationservice.Defs.NavigationStatus;
import com.ainirobot.navigationservice.db.NavigationDataManager;
import com.ainirobot.navigationservice.beans.MappingPose;
import com.ainirobot.navigationservice.beans.tk1.Event;
import com.ainirobot.navigationservice.beans.tk1.Laser;
import com.ainirobot.navigationservice.beans.tk1.Map;
import com.ainirobot.navigationservice.beans.tk1.MapData;
import com.ainirobot.navigationservice.beans.tk1.Message;
import com.ainirobot.navigationservice.beans.tk1.MotionMode;
import com.ainirobot.navigationservice.beans.tk1.NavAcceleration;
import com.ainirobot.navigationservice.beans.tk1.NavVelocity;
import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.beans.tk1.PoseState;
import com.ainirobot.navigationservice.beans.tk1.RelocateMode;
import com.ainirobot.navigationservice.beans.tk1.RobotPose;
import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.tk1.Statistic;
import com.ainirobot.navigationservice.beans.tk1.TargetPose;
import com.ainirobot.navigationservice.beans.tk1.Velocity;
import com.ainirobot.navigationservice.beans.tk1.WorkMode;
import com.ainirobot.navigationservice.beans.tk1.WorkStateMode;
import com.ainirobot.navigationservice.beans.waiter.CameraBean;
import com.ainirobot.navigationservice.beans.waiter.MultiRobotConfigBean;
import com.ainirobot.navigationservice.beans.waiter.NaviPathDetail;
import com.ainirobot.navigationservice.beans.waiter.NaviPathInfo;
import com.ainirobot.navigationservice.business.rpc.MotionControl;
import com.ainirobot.navigationservice.business.rpc.SpeedBean;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.AbsChassisClient;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.tk1.customListener.CreateMapStop;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.x86.WheelControlX86;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.ChassisCommandTk1Impl;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.IChassisCommand;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.AddGlobalMap;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.CancelNavigation;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.GetGlobalMap;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.GetLogFile;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.GetMapList;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.GetSensorStatus;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.Oper;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.PackLog;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.RemoveTkMap;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.SetGoal;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.SetMotionMode;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.SetNavigationMap;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.SetPoseEstimate;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.SetWorkMode;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.StartCreateMap;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.StopCreateMap;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.Operations.TakeSnapshot;
import com.ainirobot.navigationservice.chassisAbility.chassis.connector.tk1.ChassisConnectTk1Impl;
import com.ainirobot.navigationservice.chassisAbility.chassis.connector.tk1.IChassisConnect;
import com.ainirobot.navigationservice.chassisAbility.controller.BaseAvoidPolicy;
import com.ainirobot.navigationservice.chassisAbility.controller.BasicMotionProcess;
import com.ainirobot.navigationservice.chassisAbility.controller.DynamicStoppingPolicy;
import com.ainirobot.navigationservice.db.entity.MapInfo;
import com.ainirobot.navigationservice.commonModule.logs.LogManager;
import com.ainirobot.navigationservice.roversdkhelper.MapFileHelper;
import com.ainirobot.navigationservice.roversdkhelper.maptype.NaviMapType;
import com.ainirobot.navigationservice.utils.DistanceUtils;
import com.ainirobot.navigationservice.utils.LogUtils;
import com.ainirobot.navigationservice.utils.MD5;
import com.ainirobot.navigationservice.utils.MotionPolicy;
import com.ainirobot.navigationservice.utils.OutMapUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.atomic.AtomicReference;

import ninjia.android.proto.ChassisPacketProtoWrapper;
import ninjia.android.roversdk.Result;

public class ChassisClientTk1Impl extends AbsChassisClient {
    private final static String TAG = TAGPRE + ChassisClientTk1Impl.class.getSimpleName();

    private volatile boolean isPoseEstimate;
    private IChassisConnect connector;
    private IChassisCommand commandParser;
    private ChassisEventListener mChassisEventListener;
    private WorkMode mCurrentMode = FREE;
    private Object curModeLock = new Object();
    private TargetPose mTargetPose;
    private MotionMode mCurrentMotionMode;
    private volatile boolean isStoppingCreateMap = false;
    private CreateMapStop mCreateMapStop;
    private String mNaviMapName;
    private ChassisResListener operationListener;
    private double mLinearSpeed = 0; //Current linear speed
    private double mAngularSpeed = 0; //Current angular speed
    private ChassisResListener mResetEstimate;
    private volatile boolean isRelocate = false;
    private OutMapUtils outMapUtils;
    //TODO 先忽略RosClient内容
//    private RosClient mRos;
//    private static final int ROS_PORT = 8085;
    private volatile boolean alreadyInit = false;//alreadyInit means first Connect to TK1，do some initial things

    private ArrayList<Laser> mLaserDatas;
    private Object laserUpdateLock = new Object();

    private boolean hasObstacle = false;
    private boolean avoidTag = false;
    private volatile boolean mMotionWithAvoid = false;
    private AtomicReference<Velocity> mRealtimeVelocity = new AtomicReference<>();
    private BaseAvoidPolicy mDynamicAvoidPolicy;

    public ChassisClientTk1Impl() {
        commandParser = new ChassisCommandTk1Impl();
        connector = new ChassisConnectTk1Impl();
    }

    @Override
    public void init(Context context) {
        super.init(context);
        if (commandParser != null) {
            commandParser.setCnnListener(cnnListener);
            commandParser.injectConnector(connector);
            commandParser.init();
            registerEventListener();
            mDynamicAvoidPolicy = new DynamicStoppingPolicy(this);
            mDynamicAvoidPolicy.addObserver(mAvoidCheckCallBack);
            /*mRos = new RosClient(NavigationConfig.getSdkRosIp(), ROS_PORT);
            mRos.bindNavigation(this);*/
        } else {
            throw new RuntimeException("init fail due to null cmd or connector");
        }
    }

    private IChassisCommand.OnCmdTk1CnnListener cnnListener = new IChassisCommand.OnCmdTk1CnnListener() {
        @Override
        public void onConnected() {
            Log.d(TAG, "cmdParser Connect in， alreadyInit = " + isAlreadyInit());
            settingManager.syncTimeToTk(System.currentTimeMillis());

            if (!isAlreadyInit()) {
                syncWorkStatusWithChassis();
            }
            if (mChassisEventListener != null) {
                mChassisEventListener.onChassisConnectStatus(null, Definition.STATUS_HW_CONNECTED);
            }

        }

        @Override
        public void onDisconnected(String channelName) {
            Log.d(TAG, "cmdParser disconnect in");
            setAlreadyInit(false);

            if (mChassisEventListener != null) {
                mChassisEventListener.onChassisDisconnect("chassis socket disconnect", channelName);
                mChassisEventListener.onChassisConnectStatus(channelName, Definition.STATUS_HW_DISCONNECTED);
            }
            biManager.disconnectNavigationReport(channelName);
            logManager.startCollectLogFlow(RobotSettings.getSystemSn() + "-" + System.currentTimeMillis(),
                    String.valueOf(System.currentTimeMillis()), LogManager.TYPE_SOCKET_DISCONNECT);
        }
    };


    public void setCurrentMotionMode(MotionMode mode) {
        if (mode == null) {
            return;
        }
        if (mCurrentMotionMode != mode) {
//            SettingUtils.setNaviMotionValue(mode.getValue());
            mCurrentMotionMode = mode;
        }

        if (mChassisEventListener != null) {
            mChassisEventListener.OnRadarUpdate(mode.getValue() != MotionMode.MOTION_MODE_SLEEP.getValue());
        }
    }

    private void reportTk1ServiceState(boolean state) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onChassisServiceState(state);
        }
    }

    public IChassisCommand getCommandParser() {
        return this.commandParser;
    }

    public boolean isAlreadyInit() {
        return alreadyInit;
    }

    public void setAlreadyInit(boolean alreadyInit) {
        Log.d(TAG, "setAlreadyInit = " + alreadyInit);
        this.alreadyInit = alreadyInit;
    }

    private void registerEventListener() {
        commandParser.registerEventListener(EVENT_UPDATE_GLOBAL_MAP, new IChassisCommand.EventListener() {
            @Override
            public void onEvent(Object param) {
                if (param instanceof MapData) {
                    onMapDataUpdate((MapData) param);
                }
            }
        });

        commandParser.registerEventListener(EVENT_REPORT_STATISTIC, new IChassisCommand.EventListener() {
            @Override
            public void onEvent(Object param) {
                if (param instanceof Statistic) {
                    onStatisticUpdate((Statistic) param);
                }
            }
        });

        commandParser.registerEventListener(EVENT_UPDATE_EVENT, new IChassisCommand.EventListener() {
            @Override
            public void onEvent(Object param) {
                if (param instanceof Event) {
                    onNavigationEvent((Event) param);
                }
            }
        });

        commandParser.registerEventListener(EVENT_UPDATE_REALTIME_MAP, new IChassisCommand.EventListener() {
            @Override
            public void onEvent(Object param) {
                if (param instanceof MapData) {
                    onMapDataUpdate((MapData) param);
                }
            }
        });

        commandParser.registerEventListener(EVENT_UPDATE_VELOCITY, new IChassisCommand.EventListener() {
            @Override
            public void onEvent(Object param) {
                if (param instanceof Velocity) {
                    onVelocityUpdate((Velocity) param);
                }
            }
        });

        commandParser.registerEventListener(EVENT_WORKING_MODE_STATE_CHANGE, new IChassisCommand.EventListener() {
            @Override
            public void onEvent(Object param) {
                if (param instanceof WorkStateMode) {
                    onWorkModeChange((WorkStateMode) param);
                }
            }
        });

        commandParser.registerEventListener(EVENT_UPDATE_POSE, new IChassisCommand.EventListener() {
            @Override
            public void onEvent(Object param) {
                if (param instanceof RobotPose) {
                    RobotPose robotPose = (RobotPose) param;
                    onPoseUpdate(robotPose.getX(), robotPose.getY(), robotPose.getTheta(), robotPose.getStatus());
                }
            }
        });

        commandParser.registerEventListener(EVENT_UPDATE_LASER, new IChassisCommand.EventListener() {
            @Override
            public void onEvent(Object param) {
                if (param instanceof ArrayList) {
                    onLaserDataIn((ArrayList<Laser>) param);
                    synchronized (laserUpdateLock) {
                        mLaserDatas = (ArrayList<Laser>) param;
                    }
                }
            }
        });

        commandParser.registerEventListener(EVENT_UPDATE_MAPPING_POSE, new IChassisCommand.EventListener() {
            @Override
            public void onEvent(Object param) {
                if(param instanceof List){
                    List<MappingPose> list = (List<MappingPose>) param;
                    onMappingPoseUpdate(list);
                }
            }
        });
    }

    private void onMappingPoseUpdate(List<MappingPose> poseList){
        if(mChassisEventListener != null){
            mChassisEventListener.onMappingPoseUpdate(poseList);
        }
    }

    BaseAvoidPolicy.AvoidObserver mAvoidCheckCallBack =
            new BaseAvoidPolicy.AvoidObserver() {
                @Override
                public void onStateUpdate(boolean avoid, int score) {
                    Log.i(TAG, "onStateChange, avoid=" + avoid + " score " + score);
                    onAvoidStateChange(avoid);
                    if (score == BaseAvoidPolicy.OBSTACLES_SCORE_PERILOUS) {
                        Log.e(TAG, "onStateUpdate , perilous");
                        motion(0, 0, 0, false);
                    } else if (avoid) {
                        Log.e(TAG, "onStateUpdate , Avoid stop error");
                        motionAvoidStop();
                    }
                }
            };

    private void onVelocityUpdate(Velocity velocity) {
        LogUtils.printLog(LogUtils.TYPE_VEL, TAG,
                "onVelocityUpdate x=" + velocity.getX() + ",z=" + velocity.getZ(), 3000);
        Velocity preVelocity = mVelocity.get();
        mRealtimeVelocity.set(velocity);
        if (isVelocityDiff(preVelocity, velocity)) {
            mVelocity.set(velocity);
            if (mChassisEventListener != null) {
                mChassisEventListener.onVelocityUpdate(velocity);
            }
        }

        if (isRelocate && isStill(velocity)) {
            Log.d(TAG, "start relocate");
            isRelocate = false;

            Pose pose = mCurPose.get();
            if (pose != null) {
                Log.d(TAG, "laser relocate");
                commandParser.setRelocate(RelocateMode.MANUAL, pose.getX(), pose.getY(), pose
                        .getTheta());
            }
        }
    }

    private boolean isStill(Velocity velocity) {
        return velocity.getX() <= MIN_VELOCITY && velocity.getZ() <= MIN_VELOCITY;
    }

    private boolean isLinearStill(Velocity velocity) {
        return velocity.getX() <= MIN_VELOCITY;
    }

    private boolean isVelocityDiff(Velocity preVelocity, Velocity velocity) {
        return preVelocity == null
                || Math.abs(velocity.getZ() - preVelocity.getZ()) > VELOCITY_DIFF
                || Math.abs(velocity.getX() - preVelocity.getX()) > VELOCITY_DIFF;
    }

    private void onPoseUpdate(double x, double y, double theta, final int status) {
        Pose pose = new Pose((float) x, (float) y, (float) theta, status);
        LogUtils.printLog(LogUtils.TYPE_POSE, TAG, "onPoseUpdate : " + pose.toString() + ",target pose:" +
                        (mTargetPose == null ? "null" : mTargetPose.isAvailable()) + ", isNavigationing = " + isNavigationing()
                , 2000);

        mCurPose.set(pose);

        if (mChassisEventListener != null) {
            mChassisEventListener.onPoseUpdate(pose);
        }

        //TODO 添加ros
        /*Pose robotPose = new Pose(Message.MSG_TYPE_ROBOT_POSE, (float) x, (float) y, (float) theta, status);
        if (mRos != null) {
            mRos.sendMessage(robotPose);
        }*/

        // 提示用户机器人在地图外、禁行线上
        if (!isInSpecialMode(CREATING_MAP) && mChassisEventListener != null) {
            OutMapUtils.getInstance().checkOutMap(status, checkOutMapListener);
        }

        if (!isNavigationing() && mLinearSpeed == 0 && !isInSpecialMode(CREATING_MAP) && mChassisEventListener != null) {
            DistanceUtils.pushRobotTooFarAway(x, y, new DistanceUtils.MoveListener() {
                @Override
                public void pushExceedDistance(String pushDistance) {
                    mChassisEventListener.onPushUpdate(pushDistance);
                }
            });
        }
    }

    private OutMapUtils.OutMapLiniser checkOutMapListener = new OutMapUtils.OutMapLiniser() {
        @Override
        public void outMap(int params) {
            mChassisEventListener.onOutMap(params);
        }
    };

    private Object workModeChangeLock = new Object();

    private void onWorkModeChange(WorkStateMode changeProto) {
        //这里的模式状态变化，仅仅是为了和TK1第一次连接时，判断ready进行initWhenFirstConnected用的，
        // 而后续的切模式，只要response就已经表示TK1 service Ready。不能用这个listener来判断，因为会造成短时间状态不匹配
        //更可怕的是，如果event通道被堵住，则会造成长时间821认为TK1 service 没有ready
        synchronized (workModeChangeLock) {
            if (changeProto != null) {
                Log.d(TAG, " onWorkModeChange mode value " + changeProto.getModeValue() +
                        " state value " + changeProto.getStateValue() + ", alreadyInit = " + isAlreadyInit());
                updateCurMode(changeProto.generateWorkModeFromValue());
                if (changeProto.getStateValue() == WorkStateMode.CHASSIS_READY) {
                    //if chassis ready ,notify checkCurNaviMap
                    reportTk1ServiceState(true);
                    settingManager.initRoverConfig();//everyTime when workModeReady init settingManager instead doing in remote_recover
                }

                if (changeProto.getStateValue() == WorkStateMode.CHASSIS_READY
                        && !isAlreadyInit()) {
                    // 只在第一次连接的时候初始化，只在第一次连接置状态为READY，后续都不起作用
                    commandParser.setWorkModeState(WorkStateMode.CHASSIS_READY);
                    initWhenFirstConnected();
                }
            }
        }
    }

    private boolean isRemoteRunning = true;

    public boolean isRemoteRunning() {
        return isRemoteRunning;
    }

    private void updateRemoteStatus(boolean isRemoteRunning) {
        this.isRemoteRunning = isRemoteRunning;
    }

    private void onNavigationEvent(Event event) {
        Log.d(TAG, "onEvent:" + event.getCode() + " + " + event.getMessage() + " + " + event
                .getAdditional());

        switch (event.getCode()) {
            case Event.GOAL_REACHED:
                if (mTargetPose != null) {
                    Log.d(TAG, "Navigation arrived : " + mTargetPose.toString());
                    updateTargetPose(TargetPose.RESULT_ARRIVED, NavigationResult.RESULT_NAVI_EVENT_RESULT_ARRIVED);
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;

            case Event.GOAL_INVALID:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_OUT_MAP,
                            NavigationStatus.STATUS_NAVI_EVENT_GOAL_INVALID, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                if (!isRemoteRunning() || !isServiceReady()) return;
                break;
            case Event.GOAL_IS_DANGEROUS:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_OUT_MAP,
                            NavigationStatus.STATUS_NAVI_EVENT_GOAL_IS_DANGEROUS, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                if (!isRemoteRunning() || !isServiceReady()) return;
                break;
            case Event.ROBOT_IS_OUT:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_OUT_MAP,
                            NavigationStatus.STATUS_NAVI_EVENT_ROBOT_IS_OUT, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                if (!isRemoteRunning() || !isServiceReady()) return;
                break;
            case Event.ROBOT_IS_OUT_OF_ROAD_ERROR:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_OUT_MAP,
                            NavigationStatus.STATUS_NAVI_EVENT_ROBOT_IS_OUT_OF_ROAD_ERROR, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                if (!isRemoteRunning() || !isServiceReady()) return;
                break;
            case Event.GOAL_IS_OUT_OF_ROAD_ERROR:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_OUT_MAP,
                            NavigationStatus.STATUS_NAVI_EVENT_GOAL_IS_OUT_OF_ROAD_ERROR, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }

                if (!isRemoteRunning() || !isServiceReady()) return;
                break;

            case Event.PATH_FAILED:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_GLOBAL_PATH_FAILED,
                            NavigationStatus.STATUS_NAVI_EVENT_GLOBAL_PATH_FAILED, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }

                //TODO 判断连接状态
                if (!isRemoteRunning() || !isServiceReady()) return;

                Pose pose = null;
                if (mCurPose != null) {
                    pose = mCurPose.get();
                }
                if (pose != null && pose.getStatus() == Pose.MAP_STATUS_OUTSIDE_AREA) {
                    Log.d(TAG, "current pose status is " + pose.getStatus());
                }

                Log.d(TAG, "global path failed");
                break;
            case Event.GRAPH_SEARCHER_FAILED_ERROR:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_GLOBAL_PATH_FAILED,
                            NavigationStatus.STATUS_NAVI_EVENT_GRAPH_SEARCHER_FAILED_ERROR, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }

                //TODO 判断连接状态
                if (!isRemoteRunning() || !isServiceReady()) return;

                Pose pose1 = null;
                if (mCurPose != null) {
                    pose1 = mCurPose.get();
                }
                if (pose1 != null && pose1.getStatus() == Pose.MAP_STATUS_OUTSIDE_AREA) {
                    Log.d(TAG, "current pose status is " + pose1.getStatus());
                }

                Log.d(TAG, "global path failed");
                break;
            case Event.GRAPH_ROAD_INVALID_ERROR:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_GLOBAL_PATH_FAILED,
                            NavigationStatus.STATUS_NAVI_EVENT_GRAPH_ROAD_INVALID_ERROR, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }

                //TODO 判断连接状态
                if (!isRemoteRunning() || !isServiceReady()) return;

                Pose pose2 = null;
                if (mCurPose != null) {
                    pose2 = mCurPose.get();
                }
                if (pose2 != null && pose2.getStatus() == Pose.MAP_STATUS_OUTSIDE_AREA) {
                    Log.d(TAG, "current pose status is " + pose2.getStatus());
                }

                Log.d(TAG, "global path failed");
                break;

            case Event.PATH_SUCCESS:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_PATH_SUCCESS,
                            NavigationStatus.STATUS_NAVI_EVENT_PATH_SUCCESS, event.getMessage());
                }
                break;

            case Event.LOCAL_GOAL_INVAILD:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_AVOID,
                            NavigationStatus.STATUS_NAVI_EVENT_LOCAL_GOAL_INVAILD, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;
            case Event.LOCAL_PATH_FAILED:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_AVOID,
                            NavigationStatus.STATUS_NAVI_EVENT_LOCAL_PATH_FAILED, event.getMessage());
                }
                break;

            case Event.OBSTACLES_AVOID:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_OBSTACLES_AVOID,
                            NavigationStatus.STATUS_NAVI_EVENT_OBSTACLES_AVOID, event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;

            case Event.ESTIMATE_SUCCESS:
                updatePoseEstimate(true);
                mCurrentMode = NAVIGATION;
                Log.d(TAG, "Rover estimate success");
                updateResetEstimate(true, SUCCESS, event.getMessage());
                biManager.pushNavigationReport(event.getCode(), event.getMessage());
                break;

            case Event.ESTIMATE_FAILED:
                updatePoseEstimate(false);
                Log.d(TAG, "Rover estimate failed");
                //only estimate failed event use getAdditional，because fail reason defined by it
                updateResetEstimate(false, FAIL_NO_REASON, event.getAdditional());
                biManager.pushNavigationReport(event.getCode(), event.getMessage());
                break;

            case Event.REMOTE_ERROR:
                updateRemoteStatus(false);
                updatePoseEstimate(false);
                setAlreadyInit(false);

                Log.d(TAG, "Rover remote service is died");
                biManager.pushNavigationReport(event.getCode(), event.getMessage());

                logManager.startCollectLogFlow(RobotSettings.getSystemSn() + "-" + System.currentTimeMillis(),
                        String.valueOf(System.currentTimeMillis()), LogManager.TYPE_REMOTE_DISCONNECT);
                break;

            case Event.REMOTE_RECOVER:
                updateRemoteStatus(true);

                // 在此开启原因：整机重启之前恰好5分钟还未来及消费就重启，需要在重启后及时通知tk1打包
                if (logManager.collectLogNotOpened()) {
                    Log.d(Definition.TAG_NAVI_LOG_REPORT, "algorithm process recover, try start collect task");
                    logManager.scheduleCollectTask();
                }

                Log.d(TAG, "Rover remote service is recovered");
                biManager.pushNavigationReport(event.getCode(), event.getMessage());

                syncWorkStatusWithChassis();
                break;

            case Event.ESTIMATE_LOST:
                Log.d(TAG, "Estimate lost");
                updatePoseEstimate(false);
                updateEvent(Definition.HW_NAVI_ESTIMATE_LOST, event.getMessage());
                reportException(Definition.HW_NAVI_ESTIMATE_LOST, event.getMessage());
                Pose currentPose;
                boolean isOutOfMap = false;
                if (mCurPose != null && (currentPose = mCurPose.get()) != null) {
                    isOutOfMap = currentPose.getStatus() == Pose.MAP_STATUS_OUTSIDE_AREA;
                }
                biManager.lostEventNavigationReport(isOutOfMap, event.getMessage());

                if (!isRemoteRunning() || !isServiceReady()) return;
                logManager.startCollectLogFlow(
                        RobotSettings.getSystemSn() + "-" + System.currentTimeMillis(),
                        String.valueOf(System.currentTimeMillis()), LogManager.TYPE_ESTIMATE_LOST);

                break;

            case Event.ESTIMATE_RECOVERY:
                Log.d(TAG, "Estimate recovery");
                updatePoseEstimate(true);
                updateResetEstimate(true, SUCCESS, event.getMessage());
                biManager.pushNavigationReport(event.getCode(), event.getMessage());
                break;

            case Event.EXPECTED_TIME_FOR_VISION:
                Log.d(TAG, "expected time");
                if (mCreateMapStop != null) {
                    mCreateMapStop.onStatusUpdate(CreateMapStop
                            .STATUS_STOP_CREATE_MAP_EXPECTED_TIME, event.getAdditional());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;

            case Event.ERROR_LOG:
                Log.d(TAG, "error log");
                //TK1 has remove tk1 error log event report on V4.8 2019.4.20
//                updateErrorLog(Definition.HW_NAVI_ERROR_LOG, event.getAdditional());
//                NavigationCmdReportUtils.pushNavigationReport(event.getCode(), event.getMessage());
                break;

            case Event.PACK_LOG_END:
                Log.d(TAG, "pack log end");
                updatePackLogEnd(Definition.HW_NAVI_PACK_LOG_END, event.getMessage());
                break;

            case Event.TAKE_SNAPSHOT_END:
                Log.d(TAG, "take snapshot end");
                updateTakeSnapshotEnd(Definition.HW_NAVI_TAKE_SNAPSHOT_END, event.getMessage(),
                        event.getAdditional());
                break;
            case Event.PROCESS_CREATE_MAP:
                updateCreateMapProcess(event.getAdditional());
                break;

            case Event.DETECT_SHAKE:
                biManager.reportDetectShakeFromTk1();
                break;

            case Event.DETECT_PEOPLE:
                Log.d(TAG, "detect people");
                onMonoInfoUpdate(event.getAdditional());
                break;

            case Event.PREVENT_FALL:
                biManager.pushNavigationReport(event.getCode(), event.getMessage());
                if (!isRemoteRunning() || !isServiceReady()) {
                    return;
                }
                break;

            case Event.FORCE_SNAPSHOT:
                biManager.pushNavigationReport(event.getCode(), event.getMessage());
                if (!isRemoteRunning() || !isServiceReady()) {
                    return;
                }
                logManager.startCollectLogFlow(
                        RobotSettings.getSystemSn() + "-" + System.currentTimeMillis(),
                        String.valueOf(System.currentTimeMillis()), LogManager.TYPE_FORCE_SNAPSHOT);
                break;

            case Event.MULTI_ROBOT_ACTIVE:
                this.settingManager.updateChassisMultiRobotMode(true);
                break;

            case Event.MULTI_ROBOT_INACTIVE:
                this.settingManager.updateChassisMultiRobotMode(false);
                break;
            case Event.MULTI_ROBOT_AVOID_WAITING:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_MULTI_AVOID_WAITING, event.getCode(), event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;
            case Event.MULTI_ROBOT_AVOID_WAITING_END:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_MULTI_AVOID_WAITING_END, event.getCode(), event.getMessage());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;
            default:
                break;
        }
    }

    private void onMonoInfoUpdate(String additional) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onMonoInfoUpdate(additional);
        }
    }

    private void onLaserDataIn(ArrayList<Laser> data) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onLaserDataUpdate(data);
        }
    }

    private void onAvoidStateChange(boolean isStopping) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onAvoidStateChange(isStopping);
        }
    }

    private void onObstacleReport() {
        if (mChassisEventListener != null) {
            mChassisEventListener.onObstacleReport();
        }
    }

    private void updateCreateMapProcess(String additional) {
        if (mChassisEventListener != null) {
            mChassisEventListener.OnUpdateCreateMapProcess(additional);
        }
    }

    private synchronized void updateTakeSnapshotEnd(String from, String fileID, String path) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onTakeSnapshotEnd(from, fileID, path);
        }
    }


    private void updatePoseEstimate(boolean isPoseEstimate) {
        if (this.isPoseEstimate == isPoseEstimate) {
            return;
        }
        this.isPoseEstimate = isPoseEstimate;

        if (mChassisEventListener != null) {
            mChassisEventListener.onPoseEstimate(isPoseEstimate);
        }
    }

    private synchronized void updatePackLogEnd(String from, Object status) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onPackLogEnd(from, status);
        }
    }

    private synchronized void updateEvent(String key, Object status) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onEvent(key, status);
        }
    }

    private synchronized void reportException(String type, String data) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onException(type, data);
        }
    }

    private void onStatisticUpdate(Statistic statistic) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onStatisticUpdate(statistic);
        }
    }

    private void onMapDataUpdate(MapData data) {
        int type;
        if (data.getType() == MapData.GLOBAL) {
            type = Message.MSG_TYPE_GLOBAL_MAP;
        } else {
            type = Message.MSG_TYPE_LOCAL_MAP;
        }
        Log.d(TAG, "onMapDataUpdate type = " + type);
        Map map = new Map(type);
        map.setWidth(data.getWidth());
        map.setHeight(data.getHeight());
        map.setResolution(data.getResolution());
        map.setOriginalX(data.getOriginalX());
        map.setOriginalY(data.getOriginalY());
        map.setData(data.getData());

        if (mChassisEventListener != null) {
            mChassisEventListener.onMapUpdate(map);
        }

        //TODO RosClient 添加
        /*if (mRos != null) {
            mRos.sendMessage(map);
        }*/
    }

    private void updateCurMode(WorkMode newMode) {
        synchronized (curModeLock) {
            Log.d(TAG, "updateCurMode oldMode = " + mCurrentMode + ", newMode = " + newMode);
            mCurrentMode = newMode;
        }
    }

    private boolean isInSpecialMode(WorkMode mode) {
        synchronized (curModeLock) {
            return mCurrentMode == mode;
        }
    }

    public synchronized void updateCurrentMotionMode(MotionMode mode) {
        Log.d(TAG, "updateCurrentMotionMode = " + mode.getValue());
        if (mode == null) {
            return;
        }
        if (mCurrentMotionMode != mode) {
            mCurrentMotionMode = mode;
        }
        if (mChassisEventListener != null) {
            mChassisEventListener.OnRadarUpdate(mode.getValue() != MotionMode.MOTION_MODE_SLEEP.getValue());
        }
    }

    public SetWorkMode newSetWorkMode(final WorkMode mode) {
        Log.d(TAG, "Rover switch mode : " + mode);
        return new SetWorkMode(mode) {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "SetWorkMode status:" + status + " resultCode:" + resultCode
                        + " result:" + result);
                if (resultCode != SUCCESS) {
                    return false;
                }
                updateCurMode(mode);
                if (mode != NAVIGATION) {
                    updatePoseEstimate(false);
                    mTargetPose = null;
                }
                return true;
            }
        };
    }

    public SetMotionMode newSetMotionMode(final MotionMode motionMode) {
        Log.d(TAG, "Rover switch motion mode: " + motionMode);
        return new SetMotionMode(motionMode) {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "SetMotionMode status:" + status + " resultCode:" + resultCode
                        + " result:" + result);
                if (resultCode == SUCCESS) {
                    updateCurrentMotionMode(motionMode);
                    return true;
                }
                return false;
            }
        };
    }

    @Override
    public boolean isSocketConnected() {
        //TODO 命令层负责监控socket连接
        return commandParser.isChannelConnected();
    }

    @Override
    public boolean isChassisReady() {
        //TODO 监听work mode change
        return commandParser.isWorkModeReady();
    }

    //TODO 连接状态判断
    @Override
    public boolean isServiceReady() {
        return isChassisReady() && isSocketConnected();
    }

    @Override
    public void setEventListener(ChassisEventListener listener) {
        mChassisEventListener = listener;
    }

    @Override
    public Pose getCurrentPose() {
        if (!isPoseEstimate) {
            Log.d(TAG, "not pose estimate");
            return null;
        }
        return mCurPose.get();
    }

    @Override
    public Pose getRealtimePose() {
        return null;
    }

    @Override
    public ChassisPacketProtoWrapper.RealtimeObsMapProto getRealtimeObsMapProto() {
        return null;
    }

    @Override
    public Pose getCurrentPoseWithoutEstimate() {
        return mCurPose.get();
    }

    @Override
    public Velocity getVelocity() {
        Velocity velocity = mVelocity.get();
        if (velocity == null) {
            velocity = new Velocity(0, 0);
        }
        return velocity;
    }

    @Override
    public Velocity getRealtimeVelocity() {
        return mRealtimeVelocity.get();
    }

    @Override
    public List<Laser> getLasersData() {
        return null;
    }

    @Override
    public void updateMotionAvoidState(boolean withAvoid) {
        Log.d(TAG, "updateMotionAvoidState , withAvoid=" + withAvoid);
        mMotionWithAvoid = withAvoid;
    }

    @Override
    public boolean isPoseEstimate() {
        return isPoseEstimate;
    }

    @Override
    public void checkCurNaviMap(final ChassisResListener listener) {
        final String mapName = NavigationDataManager.getInstance().getMapName();

        if (TextUtils.isEmpty(mapName)) {
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "821 has no naviMap");
            }
            return;
        }
        GetMapList getMapList = new GetMapList() {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "GetMapList onResponse status=" + status + " result=" + result);
                if (status) {
                    if (result != null && result instanceof List) {
                        List mapNames = (List) result;
//                        String naviMapName = ObjectBox.getInstance().getMapNaviInfoByName(mapName);
//                        Log.d(TAG, "checkCurNaviMap naviMapName=" + naviMapName);
//                        if (mapNames.size() > 0 && mapNames.contains(naviMapName)) {
//                            return true;
//                        }
                    }
                } else {
                    //TODO 执行失败，针对特定的resultCode 处理
                }

                if (listener != null) {
                    listener.onResponse(false, resultCode, "TK1 has no naviMap");
                }
                return false;
            }
        };

        ArrayDeque<Oper> opers = new ArrayDeque<>();
        opers.add(getMapList);
        commandParser.performOprations(opers, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
//                //TODO 从逻辑上看，一旦从这里触发，都是Operations里边自己返回的。
//                if (listener != null) {
//                    if (resultCode == SUCCESS) {
//                        listener.onResponse(EXE_SUC, result);
//                    } else {
//                        //TODO ,针对特定的resultCode转义
//                        //TODO 逻辑上看，这里的false，是有问题的，不应该来
//                    }
//                }
                listener.onResponse(status, resultCode, result);
            }
        });
    }

    @Override
    public boolean isMoving() {
        return isNavigationing();
    }

    @Override
    public void switchMap(String mapName, ChassisResListener listener) {
        Log.d(TAG, "Rover switch map " + mapName);
        if (TextUtils.isEmpty(mapName)) {
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "switchMap failed, mapName is null");
            }
            return;
        }
        String naviMapName = mapName;
//        String naviMapName = ObjectBox.getInstance().getMapNaviInfoByName(mapName);
//        Log.d(TAG, "getNaviMapName=" + naviMapName);
//        if (TextUtils.isEmpty(naviMapName)) {
//            naviMapName = buildNaviMapName();
//            ObjectBox.getInstance().updateMapNaviInfo(mapName, naviMapName);
//        }
        Log.d(TAG, "naviMapName=" + naviMapName);
        if (TextUtils.isEmpty(naviMapName)) {
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "switchMap failed, naviMapName is null");
            }
            return;
        }

        AddGlobalMap addGlobalMap = new AddGlobalMap(naviMapName, mapName) {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "AddGlobalMap onResponse status=" + status + " result=" + result);
                if (status) {
                    NavigationDataManager.getInstance().setMapName(mapName);
                    return true;
                }

                if (listener != null) {
                    listener.onResponse(false, resultCode, "add global map status false");
                }
                return false;
            }
        };
        SetNavigationMap setNavigationMap = new SetNavigationMap(naviMapName) {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "SetNavigationMap onResponse status=" + status + " result=" + result);

                if (status) {
                    if (result instanceof Boolean) {
                        boolean hasVision = ((Boolean) result);
                        Log.d(TAG, "hasVision=" + hasVision);
                        MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
                        if (null != mapInfo) {
                            int mapType = hasVision ? MapInfo.MapType.VISION : MapInfo.MapType.NORMAL;
                            mapInfo.setMapType(mapType);
                            NavigationDataManager.getInstance().updateMapInfo(mapInfo);
                        }
                    }
                    return true;
                }

                if (listener != null) {
                    listener.onResponse(false, resultCode, "set navigation map status false");
                }
                return false;
            }
        };
        ArrayDeque<Oper> opers = new ArrayDeque<>();
        opers.add(newSetWorkMode(FREE));
        opers.add(addGlobalMap);
        opers.add(setNavigationMap);
        opers.add(newSetWorkMode(NAVIGATION));
        commandParser.performOprations(opers, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
//                //TODO 从逻辑上看，一旦从这里触发，都是Operations里边自己返回的。
//                if (listener != null) {
//                    if (resultCode == SUCCESS) {
//                        listener.onResponse(EXE_SUC, result);
//                    } else {
//                        //TODO ,针对特定的resultCode转义
//                        //TODO 逻辑上看，这里的false，是有问题的，不应该来
//                    }
//                }
                listener.onResponse(status, resultCode, result);
            }
        });
    }

    @Override
    public void loadCurrentMap(boolean useCustomKeepPose, boolean keepPose, ChassisResListener listener) {
        this.setWorkMode(WorkMode.NAVIGATION, new ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (listener != null) {
                    if (status == true) {
                        listener.onResponse(true, SUCCESS, result);
                    } else {
                        listener.onResponse(false, FAIL_NO_REASON, result);
                    }
                }
            }
        });
    }

    private String buildNaviMapName() {
        StringBuilder builder = new StringBuilder();

        String sn = RobotSettings.getSystemSn();

        String currentTime = Long.toString(System.currentTimeMillis());

        String naviMapName = builder.append(sn).append("_").append(currentTime).toString();
        Log.d(TAG, "buildNaviMapName=" + naviMapName);

        return naviMapName;
    }

    @Override
    public void startCreatingMap(NaviMapType naviMapType, final ChassisResListener listener) {
        Log.e(TAG, "Rover start creating map");

        if (isInSpecialMode(CREATING_MAP)) {
            Log.e(TAG, "mode:" + mCurrentMode);
            if (listener != null) {
                listener.onResponse(true, SUCCESS, "is building");
            }
            return;
        }

        if (isStoppingCreateMap) {
            isStoppingCreateMap = false;
        }

        if (mCreateMapStop != null) {
            mCreateMapStop = null;
        }

        final WorkMode currWorkMode = mCurrentMode;

        final ArrayDeque<Oper> opers = new ArrayDeque<>();
        opers.add(newSetWorkMode(CREATING_MAP));
        mNaviMapName = buildNaviMapName();
        opers.add(new StartCreateMap(mNaviMapName) {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {
                Log.e(TAG, "StartCreateMap status:" + status + " resultCode:"
                        + resultCode + " result:" + result);
                if (resultCode == SUCCESS) {
                    return true;
                } else {
                    ArrayDeque<Oper> opers = new ArrayDeque<>();
                    opers.add(newSetWorkMode(currWorkMode));
                    commandParser.performOprations(opers, null);
                }
                if (listener != null) {
                    listener.onResponse(false, resultCode, result);
                }
                return false;
            }
        });
        opers.add(newSetMotionMode(MotionMode.UNLOCK_WHEEL));
        commandParser.performOprations(opers, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
//                //TODO 从逻辑上看，一旦从这里触发，都是Operations里边自己返回的。
//                if (listener != null) {
//                    if (resultCode == SUCCESS) {
//                        listener.onResponse(EXE_SUC, result);
//                    } else {
//                        //TODO ,针对特定的resultCode转义
//                        //TODO 逻辑上看，这里的false，是有问题的，不应该来
//                    }
//                }
                listener.onResponse(status, resultCode, result);
            }
        });
    }

    @Override
    public void stopCreatingMap(final String mapName, boolean save, String language, int type, int finishState, final CreateMapStop listener) {
        Log.e(TAG, "stop creating map mapName:" + mapName + " mCurrentMode:" + mCurrentMode
                + " isStoppingCreateMap:" + isStoppingCreateMap);

        if (mCurrentMode != CREATING_MAP) {
            listener.onResult(false, CreateMapStop.RESULT_NOT_CREATING_MAP_MODE);
            return;
        }

        if (isStoppingCreateMap) {
            listener.onResult(false, CreateMapStop.RESULT_ALREADY_STOPPING_CREATE_MAP);
            return;
        }

        isStoppingCreateMap = true;
        mCreateMapStop = listener;
        final boolean[] saveStatus = new boolean[1];

        ArrayDeque<Oper> opers = new ArrayDeque<>();

        opers.add(new Operations.SaveMap() {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {
                saveStatus[0] = status;
                if(result instanceof List){
                    List<MappingPose> list = (List<MappingPose>) result;
                    listener.onSaveMap(status, list);
                } else {
                    listener.onSaveMap(status, null);
                }
                return true;
            }
        });

        opers.add(new StopCreateMap() {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "stopCreatingMap onResponse status=" + status + " result=" + result);

                if (!saveStatus[0]) {
                    listener.onResult(false, "save map failure!!!");
                    return false;
                }

                boolean equals = false;
                if (status) {
                    if (result != null && result instanceof String) {
                        equals = TextUtils.equals((String) result, mNaviMapName);
                    }
                }
                isStoppingCreateMap = false;

                if (!equals) {
                    listener.onResult(false, result);
                }

                return equals;
            }
        });
        if (!TextUtils.isEmpty(mapName)) {
            opers.add(new GetGlobalMap(mNaviMapName, mapName) {
                @Override
                public boolean onResponse(boolean status, int resultCode, Object response) {
                    Log.d(TAG, "GetGlobalMap onResponse status=" + status + " response=" + response);
                    String result = null;
                    if (status) {
                        try {
                            JSONObject json = new JSONObject();
                            File dataFile = MapFileHelper.getMapData(mapName);
                            File pgmFile = MapFileHelper.getMapPgm(mapName);
                            String md5 = createFileMd5(pgmFile.getAbsolutePath(), mapName);
                            json.put("pgm", pgmFile.getAbsolutePath());
                            json.put("data", dataFile.getAbsolutePath());
                            json.put("md5", md5);
                            result = json.toString();
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                    listener.onResult(status, result);

                    return status;
                }
            });
        }
        opers.add(newSetWorkMode(FREE));
        commandParser.performOprations(opers, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
//                //TODO 从逻辑上看，一旦从这里触发，都是Operations里边自己返回的。
//                if (listener != null) {
//                    if (resultCode == SUCCESS) {
//                        listener.onResult(EXE_SUC, result);
//                    } else {
//                        //TODO ,针对特定的resultCode转义
//                        //TODO 逻辑上看，这里的false，是有问题的，不应该来
//                    }
//                }
                listener.onResult(status, result);
            }
        });
    }

    @Override
    public void stopExtendMap(String mapName, ChassisResListener listener) {

    }

    private String createFileMd5(String mapPath, String mapName) {
        File pgmFile = new File(mapPath);
        if (pgmFile.exists()) {
            String md5 = MD5.getFileMd5(mapPath);
            if (!TextUtils.isEmpty(md5)) {
                setMd5Config(md5, mapName);
                return md5;
            }
        }
        return "";
    }

    public void setMd5Config(String md5, String mapName) {
        MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
        if (null != mapInfo) {
            mapInfo.setMd5(md5);
            NavigationDataManager.getInstance().updateMapInfo(mapInfo);
        }
    }

    @Override
    public void go(TargetPose targetPose, ChassisResListener listener) {
        go(targetPose, null, null, listener);
    }

    private synchronized void updateTargetPose(int status, int navigationCode) {
        if (mTargetPose != null) {
            mTargetPose.onResult(status, navigationCode);
            DistanceUtils.resetCalculationCount();
        }
    }

    private Timer mDecTimer;
    private TimerTask mDecTimerTask;

    private synchronized void cancelDecTimer() {
        Log.d(TAG, "cancelDecTimer");
        if (mDecTimer != null && !Thread.currentThread().isInterrupted()) {
            mDecTimer.cancel();
            mDecTimerTask.cancel();
            mDecTimer = null;
            mDecTimerTask = null;
        }
    }


    private static final float ROBOT_SETTING_DEFAULT_LINEAR_SPEED = 0.7F;
    private static final float ROBOT_SETTING_DEFAULT_ANGULAR_SPEED = 1.2F;

    @Override
    public void go(TargetPose targetPose, NavVelocity navVelocity, NavAcceleration navAcc, ChassisResListener listener) {
        updateTargetPose(TargetPose.RESULT_REPLACE, NavigationResult.RESULT_NAVI_EVENT_RESULT_REPLACE);

        mTargetPose = targetPose;
        operationListener = listener;

        cancelDecTimer();
        mLinearSpeed = 0;
        mAngularSpeed = 0;

        Pose pose = targetPose.getPose();

        ArrayDeque<Oper> opers = new ArrayDeque<>();
        opers.add(newSetMotionMode(MotionMode.AUTO_MOVING));
        double navLinear;
        double navAngular;
        if (null == navVelocity) {
            navLinear = ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
            navAngular = ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
        } else {
            navLinear = navVelocity.getLinear();
            navAngular = navVelocity.getAngular();
        }
        int moveType = targetPose.getMoveType();
        int rotateType = targetPose.getRotateType();
        float desRange = (float) targetPose.getDestinationRange();
        Log.d(TAG, "go action pose:" + pose + " velocity:" + navVelocity
                + ", moveType:" + moveType + ", rotateType = " + rotateType
                + ", destinationRange = " + desRange);
        opers.add(new SetGoal(pose.getX(), pose.getY(), pose.getTheta(),
                navLinear, navAngular, moveType,
                rotateType, desRange));
        if (mTargetPose != null) {
            mTargetPose.onStatusUpdate(TargetPose.STATUS_STARTED, NavigationStatus.STATUS_NAVI_EVENT_START, "");
        }
        commandParser.performOprations(opers, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (!status) {
                    updateTargetPose(TargetPose.RESULT_FAILED, NavigationResult.RESULT_NAVI_EVENT_RESULT_FAILED);
                }
                if (operationListener != null) {
                    operationListener.onResponse(status, resultCode, result);
                }
            }
        });
    }

    private boolean isNavigationing() {
        return mTargetPose != null
                && mTargetPose.isAvailable();
    }

    @Override
    public void cancelNavigation(final ChassisResListener listener) {
        if (!isNavigationing()) {
            Log.d(TAG, "not in navigation mode, no need to cancel");
            listener.onResponse(true, SUCCESS, "not in navi mode");
            return;
        }

        ArrayDeque<Oper> opers = new ArrayDeque<>();
        opers.add(new CancelNavigation() {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {
                if (resultCode == SUCCESS) {
                    updateTargetPose(TargetPose.RESULT_CANCELED, NavigationResult.RESULT_NAVI_EVENT_RESULT_CANCELED);
                }
                Log.d(TAG, "Cancel navigation:" + status + " resultCode:" + resultCode);
                return status;
            }
        });
        commandParser.performOprations(opers, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
//                //TODO 从逻辑上看，一旦从这里触发，都是Operations里边自己返回的。
//                if (listener != null) {
//                    if (resultCode == SUCCESS) {
//                        listener.onResponse(EXE_SUC, result);
//                    } else {
//                        //TODO ,针对特定的resultCode转义
//                        //TODO 逻辑上看，这里的false，是有问题的，不应该来
//                    }
//                }
                listener.onResponse(status, resultCode, result);
            }
        });
    }

    private synchronized void updateResetEstimate(boolean result, int resultCode, String message) {
        if (mResetEstimate != null) {
            mResetEstimate.onResponse(result, resultCode, message);
            mResetEstimate = null;
        }
    }

    private synchronized void addResetEstimateListener(ChassisResListener listener) {
        mResetEstimate = listener;
    }

    @Override
    public void setPoseEstimate(Pose pose, ChassisResListener listener) {
        Log.d(TAG, "Set pose estimate : " + pose + ", mCurrentMode = " + mCurrentMode);

        if (mCurrentMode == CREATING_MAP) {
            listener.onResponse(false, FAIL_NO_REASON, "curMode is createMap");
            return;
        }

        if (pose != null) {
            addResetEstimateListener(listener);
            ArrayDeque<Oper> opers = new ArrayDeque<>();
            opers.add(newSetWorkMode(NAVIGATION));
            opers.add(new SetPoseEstimate(RelocateMode.MANUAL, pose.getX(), pose.getY(), pose.getTheta()) {
                @Override
                public boolean onResponse(boolean status, int resultCode, Object response) {
                    Log.d(TAG, "SetPoseEstimate status=" + status + " response=" + response);
                    return status;
                }
            });
            commandParser.performOprations(opers, null);
        }
    }

    @Override
    public void setFixedEstimate(Pose pose, ChassisResListener listener) {
        Log.d(TAG, "Set fixed estimate : " + pose);

        if (mCurrentMode == CREATING_MAP) {
            listener.onResponse(false, FAIL_NO_REASON, Definition.RESULT_ESTIMATE_FAIL_CREATING_MAP);
            return;
        }

        if (pose != null) {
            addResetEstimateListener(listener);
            ArrayDeque<Oper> opers = new ArrayDeque<>();
            opers.add(newSetWorkMode(NAVIGATION));
            opers.add(new SetPoseEstimate(RelocateMode.FIXED, pose.getX(), pose.getY(), pose.getTheta()) {
                @Override
                public boolean onResponse(boolean status, int resultCode, Object response) {
                    Log.d(TAG, "SetPoseEstimate status=" + status + " response=" + response);
                    return status;
                }
            });
            commandParser.performOprations(opers, null);
        }
    }

    @Override
    public boolean getHasVision() {
        return NavigationDataManager.getInstance().getHasVision();
    }

    @Override
    public void goCharge(boolean isFrontCamera, ChassisResListener listener) {
        Log.d(TAG, "goCharge result ");
    }

    @Override
    public void stopCharge(ChassisResListener listener) {
        Log.d(TAG, "stopCharge result ");
    }

    @Override
    public void setForceEstimate(Pose pose, ChassisResListener listener) {
        Log.d(TAG, "Set force estimate : " + pose);

        if (mCurrentMode == CREATING_MAP) {
            listener.onResponse(false, FAIL_NO_REASON, Definition.RESULT_ESTIMATE_FAIL_CREATING_MAP);
            return;
        }

        if (pose != null) {
            addResetEstimateListener(listener);
            ArrayDeque<Oper> opers = new ArrayDeque<>();
            opers.add(newSetWorkMode(NAVIGATION));
            opers.add(new SetPoseEstimate(RelocateMode.FORCE, pose.getX(), pose.getY(), pose.getTheta()) {
                @Override
                public boolean onResponse(boolean status, int resultCode, Object response) {
                    Log.d(TAG, "SetPoseEstimate status=" + status + " response=" + response);
                    return status;
                }
            });
            commandParser.performOprations(opers, null);
        }
    }

    @Override
    public String getMapStatus(String cmdType, String cmdParam) {
        return mCurrentMode.name();
    }

    @Override
    public void addMappingPose(final Pose pose, final ChassisResListener listener) {
        if (mCurrentMode != CREATING_MAP) {
            listener.onResponse(false, -1, "not in creatimg map mode");
            return;
        }
        Log.i(TAG, "add mapping pose: " + pose.toString());
        Double x = Double.valueOf(pose.getX());
        Double y = Double.valueOf(pose.getY());
        Double theta = Double.valueOf(pose.getTheta());
        commandParser.addMappingPose(x, y, theta, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if(status && result instanceof MappingPose){
                    ((MappingPose)result).setPose(pose);
                }
                listener.onResponse(status, resultCode, result);
            }
        });
    }

    @Override
    public void deleteMappingPose(int poseId, final ChassisResListener listener) {
        if (mCurrentMode != CREATING_MAP) {
            listener.onResponse(false, -1, "not in creatimg map mode");
            return;
        }
        commandParser.deleteMappingPose(poseId, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                listener.onResponse(status, resultCode, result);
            }
        });
    }

    @Override
    public void setVisionEstimate(ChassisResListener listener) {
        Log.d(TAG, "set vision relocate");
        if (isPoseEstimate) {
            if (listener != null) {
                listener.onResponse(true, SUCCESS, "already relocated");
            }
            return;
        }

        boolean hasVision = getHasVision();
        Log.d(TAG, "hasVision=" + hasVision);

        if (hasVision) {
            addResetEstimateListener(listener);
            Pose pose = mCurPose.get();
            if (pose != null) {
                commandParser.setRelocate(RelocateMode.VISION, pose.getX(), pose.getY(), pose
                        .getTheta());
            } else {
                commandParser.setRelocate(RelocateMode.VISION, Float.NaN, Float.NaN, Float.NaN);
            }
        } else {
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "not support vision locate");
            }
        }
    }


    @Override
    public void resetPoseEstimate(ChassisResListener listener) {
        Log.d(TAG, "resetPoseEstimate..");
        if (isPoseEstimate) {
            if (listener != null) {
                listener.onResponse(true, SUCCESS, "Already pose estimate");
            }
            return;
        }
        addResetEstimateListener(listener);
        isRelocate = true;
    }

    @Override
    public void setChassisRelocation(int type, Pose pose, ChassisResListener listener) {

    }

    @Override
    public void stopMove() {
        if (mMotionTimer != null) {
            mMotionTimer.cancel();
            ChassisResListener listener = mMotionTimer.getListener();
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "Cancel");
            }
        }
        motion(0, 0, 0, true);
    }

    @Override
    public void motion(double angularSpeed, double linearSpeed, double acceleration) {
        motion(angularSpeed, linearSpeed, acceleration, true);
    }

    @Override
    public void motion(double angularSpeed, double linearSpeed, double acceleration, final boolean hasAcceleration) {
        if (isNavigationing()) {
            return;
        }
        final double cLinearSpeed = correctLinearSpeed(linearSpeed);
        final double cAngularSpeed = correctAngularSpeed(angularSpeed);
        commandParser.setMotionMode(MotionMode.MANUAL_CONTROL, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (status) {
                    updateCurrentMotionMode(MotionMode.MANUAL_CONTROL);
                }
                startDecTimer(cAngularSpeed, cLinearSpeed, hasAcceleration);
            }
        });
    }

    @Override
    public void motionWithObstacles(double angularSpeed, double linearSpeed, double acceleration) {
        if (isNavigationing()) {
            return;
        }
        final double cLinearSpeed = correctLinearSpeed(linearSpeed);
        final double cAngularSpeed = correctAngularSpeed(angularSpeed);
        commandParser.setMotionMode(MotionMode.MANUAL_CONTROL, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (status) {
                    updateCurrentMotionMode(MotionMode.MANUAL_CONTROL);
                }
                double linearSpeed = cLinearSpeed;
                if (mDynamicAvoidPolicy.getState() && linearSpeed > 0) {
                    Log.d(TAG, "Avoid to set linear speed 0");
                    linearSpeed = 0;
                    updateMotionAvoidState(false);
                } else {
                    updateMotionAvoidState(true);
                }
                startDecTimer(cAngularSpeed, linearSpeed, true);
            }
        });
    }

    @Override
    public void motionWithObstacles(double angularSpeed, double linearSpeed, double acceleration, double minDistance) {
        if (isNavigationing()) {
            return;
        }
        final double cLinearSpeed = correctLinearSpeed(linearSpeed);
        final double cAngularSpeed = correctAngularSpeed(angularSpeed);
        commandParser.setMotionMode(MotionMode.MANUAL_CONTROL, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (status) {
                    updateCurrentMotionMode(MotionMode.MANUAL_CONTROL);
                }
                double linearSpeed = cLinearSpeed;
                if (mDynamicAvoidPolicy.getState(minDistance) && linearSpeed > 0) {
                    Log.d(TAG, "Avoid to set linear speed 0");
                    linearSpeed = 0;
                    updateMotionAvoidState(false);
                } else {
                    updateMotionAvoidState(true);
                }
                startDecTimer(cAngularSpeed, linearSpeed, true);
            }
        });
    }

    @Override
    public void motionWithStaticObstacles(double angularSpeed, double linearSpeed, double minDistance) {

    }

    @Override
    public void motionWithOnceObstacle(double angularSpeed, double linearSpeed, final boolean hasAcceleration) {
        if (isNavigationing()) {
            return;
        }
        updateMotionAvoidState(false);
        final double cLinearSpeed;
        final double cAngularSpeed = WheelControlX86.getInstance().correctAngularSpeed(angularSpeed);

        boolean avoidState = mDynamicAvoidPolicy.getState(1.0);
        Log.i(TAG, "avoid state: " + avoidState + " , last state: " + hasObstacle);

        if (avoidState && linearSpeed > 0) {
            onObstacleReport();
            if (!hasObstacle) {
                //appear obstacle
                Log.i(TAG, "appear obstacle");
                startAvoidTimer();
                cLinearSpeed = 0;
            } else if (avoidTag) {
                Log.i(TAG, "appear obstacle, continuing。。。。");
                cLinearSpeed = 0;
            } else {
                cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
            }
        } else {
            cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
        }
        hasObstacle = avoidState;
        WheelControlX86.getInstance().startDecTimer(cAngularSpeed, cLinearSpeed, 0, hasAcceleration);
    }

    private synchronized void startAvoidTimer() {
        avoidTag = true;
        DelayTask.cancel(this);
        DelayTask.submit(this, new Runnable() {
            @Override
            public void run() {
                Log.i(TAG, "avoid timer come, remote can continue control");
                avoidTag = false;
            }
        }, 3000);

    }

    @Override
    public Velocity getFollowVelocity(double distance, double angle, double headAngleSpeed, Velocity velocity, double latency) {
        return MotionPolicy.follow(distance, angle, headAngleSpeed, velocity, latency);
    }

    @Override
    public Velocity getBodyFollowVelocity(double distance, double angle, double latency, double headAngleSpeed,
                                          Velocity velocity, double maxLinSpeed, double maxAngSpeed, double safeDistance) {
        return MotionPolicy.bodyFollow(distance, angle, headAngleSpeed, velocity, latency);
    }

    @Override
    public Velocity getBodyFollowVelocity(double distance, double angle, double headAngleSpeed, Velocity velocity, double latency) {
        return MotionPolicy.bodyFollow(distance, angle, headAngleSpeed, velocity, latency);
    }

    @Override
    public Velocity getFollowVelocity(double angle, double latency) {
        return null;
    }

    private volatile boolean finishStop = true;
    private double lastAngularSpeed = 0d;
    private double lastLinearSpeed = 0d;
    private volatile long DEC_TIME = 50; // ms
    private static final double MIN_DEC = 0.05; //min deceleration
    private static final double MAX_DEC = 0.08; //max deceleration
    private volatile int DEFAULT_DEC_NUMBER = 8; //default deceleration number
    private static final double VELOCITY_DIFF = 0.03;
    private static final double MIN_VELOCITY = 0.05; //min velocity, below this velocity is still
    private static final double MAX_LIMIT_ANGULAR_SPEED = 1.0;

    private synchronized void startDecTimer(final double tAngularSpeed,
                                            final double tLinearSpeed, boolean hasAcceleration) {

        checkStopCmd(tAngularSpeed, tLinearSpeed, hasAcceleration);
        cancelDecTimer();
        lastAngularSpeed = tAngularSpeed;
        lastLinearSpeed = tLinearSpeed;

        startAcceleration(tAngularSpeed, tLinearSpeed);
        int decNumber = calculateDecNumber(tAngularSpeed, tLinearSpeed);

        //加速度 <0加速过程，>0减速过程
        final double linearDec = (mLinearSpeed - tLinearSpeed) / decNumber;
        final double angularDec = (mAngularSpeed - tAngularSpeed) / decNumber;

        Log.i(TAG, "startDecTimer: 加速次数：" + decNumber + " 目标速度：角：" + tAngularSpeed + " 线:" +
                tLinearSpeed);
        if (hasAcceleration) {
            mDecTimer = new Timer();
            mDecTimerTask = new TimerTask() {
                private volatile Thread thread;

                @Override
                public boolean cancel() {
                    Thread thread = this.thread;
                    if (thread != null) {
                        thread.interrupt();
                    }
                    return super.cancel();
                }

                @Override
                public void run() {
                    thread = Thread.currentThread();
                    //达到目标速度||模式切换
                    Log.d(TAG, "currentMotionMode = " + (mCurrentMotionMode == null ? "null" : mCurrentMotionMode.getValue())
                            + ", mLinearSpeed = " + mLinearSpeed
                            + ", mAngularSpeed = " + mAngularSpeed + ", isInterrupted = " + thread.isInterrupted());
                    if (((mLinearSpeed == tLinearSpeed && mAngularSpeed == tAngularSpeed) ||
                            (mCurrentMotionMode != MotionMode.MANUAL_CONTROL))
                            && mAngularSpeed != MAX_LIMIT_ANGULAR_SPEED
                            && !thread.isInterrupted()) {
                        finishStop = true;
                        cancelDecTimer();
                        return;
                    }
                    double linearSpeed = mLinearSpeed - linearDec;
                    double angularSpeed = mAngularSpeed - angularDec;

                    /* if it is decelerating and the speed is less than the target speed,
                       or it is accelerating and the speed is greater than the target speed,
                       set speed as the target speed */
                    if ((angularDec >= 0 && angularSpeed < tAngularSpeed)
                            || (angularDec <= 0 && angularSpeed > tAngularSpeed)) {
                        angularSpeed = tAngularSpeed;
                    }

                    if ((linearDec >= 0 && linearSpeed < tLinearSpeed)
                            || (linearDec <= 0 && linearSpeed > tLinearSpeed)) {
                        linearSpeed = tLinearSpeed;
                    }

                    //最后一次加减速处理
                    if (Math.abs(tLinearSpeed - mLinearSpeed) < Math.abs(linearDec)) {
                        linearSpeed = tLinearSpeed;
                    }

                    if (Math.abs(tAngularSpeed - mAngularSpeed) < Math.abs(angularDec)) {
                        angularSpeed = tAngularSpeed;
                    }

                    angularSpeed = correctAngularSpeed(angularSpeed);
                    linearSpeed = correctLinearSpeed(linearSpeed);
                    Log.i(TAG, "run: 目标速度：角：" + tAngularSpeed + " 线:" + tLinearSpeed
                            + "\n当前速度：角：" + mAngularSpeed + " 线：" + mLinearSpeed
                            + " \n角加速度：" + angularDec + " 线加速度" + linearDec);

                    Log.v(TAG, "Motion send linearSpeed=" + linearSpeed + "  angularSpeed=" +
                            angularSpeed);
                    commandParser.sendPrimitiveMovingCommand(angularSpeed, linearSpeed);
                    mLinearSpeed = linearSpeed;
                    mAngularSpeed = angularSpeed;
                }
            };
            mDecTimer.schedule(mDecTimerTask, 0, DEC_TIME);
            Log.d(TAG, "DecTimer start");

        } else {
            Log.d(TAG, "Motion mLinearSpeed=" + tAngularSpeed + "  mAngularSpeed=" + tLinearSpeed);
            commandParser.sendPrimitiveMovingCommand(tAngularSpeed, tLinearSpeed);
            Log.i(TAG, "startDecTimer: navigationService给地盘发命令完成");
            mLinearSpeed = tLinearSpeed;
            mAngularSpeed = tAngularSpeed;
        }

    }

    private static final float MOTION_SPEED = 0.5f;
    private static final float ANGULAR_SPEED = 0.9f;
    private static final float STOP_SPEED = 0f;
    private static final double MIN_LINEAR_SPEED = -1.2;
    private static final double MAX_LINEAR_SPEED = 1.2;
    private static final double MIN_ANGULAR_SPEED = -1.8;
    private static final double MAX_ANGULAR_SPEED = 1.8;//角度值约为 103

    private void startAcceleration(double tAngularSpeed, double tLinearSpeed) {
        if (mLinearSpeed == STOP_SPEED && mAngularSpeed == STOP_SPEED) {
            if (Math.abs(tLinearSpeed) >= MOTION_SPEED) {
                //线速度方向
                if (tLinearSpeed < STOP_SPEED) {
                    mLinearSpeed = -MOTION_SPEED;
                } else {
                    mLinearSpeed = MOTION_SPEED;
                }
            }

            if (Math.abs(tAngularSpeed) >= ANGULAR_SPEED) {
                //角速度方向
                if (tAngularSpeed < STOP_SPEED) {
                    mAngularSpeed = -ANGULAR_SPEED;
                } else {
                    mAngularSpeed = ANGULAR_SPEED;
                }
            }
            //预启动
            commandParser.sendPrimitiveMovingCommand(mAngularSpeed, mLinearSpeed);
            try {
                Thread.sleep(DEC_TIME);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    private int calculateDecNumber(double tAngularSpeed, double tLinearSpeed) {
        double diffLinearSpeed = Math.abs(mLinearSpeed - tLinearSpeed);
        double diffAngularSpeed = Math.abs(mAngularSpeed - tAngularSpeed);

        if (diffLinearSpeed < MIN_DEC && diffAngularSpeed < MIN_DEC) {
            return 1;
        }

        double linearDecSpeed = diffLinearSpeed / DEFAULT_DEC_NUMBER;
        double angularDecSpeed = diffAngularSpeed / DEFAULT_DEC_NUMBER;

        if (linearDecSpeed > MAX_DEC || angularDecSpeed > MAX_DEC) {
            return (int) Math.max(diffLinearSpeed / MAX_DEC, diffAngularSpeed / MAX_DEC);
        }

        if (linearDecSpeed < MIN_DEC && angularDecSpeed < MIN_DEC) {
            return (int) Math.max(diffLinearSpeed / MIN_DEC, diffAngularSpeed / MIN_DEC);
        }

        return DEFAULT_DEC_NUMBER;
    }

    private void checkStopCmd(final double tAngularSpeed, final double tLinearSpeed, boolean
            hasAcceleration) {
        //上条命令是停止
        boolean lastIsStopCmd = (lastAngularSpeed == 0 && lastLinearSpeed == 0);
        //目标命令是停止
        boolean targetIsStopCmd = (tAngularSpeed == 0 && tLinearSpeed == 0);

        //同向加减速
        boolean linearSpeedDic = (mLinearSpeed >= 0 && tLinearSpeed >= 0) || (mLinearSpeed <= 0
                && tLinearSpeed <= 0);

        int i = 0;
        if (hasAcceleration && lastIsStopCmd && !targetIsStopCmd && !linearSpeedDic) {
            //减速未完成，自旋转等待
            while (!finishStop) {
                Log.i(TAG, "checkStopCmd: 等待减速" + ++i);
                try {
                    Thread.sleep(20);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        if (targetIsStopCmd && !handlerTurnLeftAndRight(tLinearSpeed, hasAcceleration)) {
            DEC_TIME = 170;
        } else {
            DEC_TIME = 50;
        }
        if (targetIsStopCmd) {
            finishStop = false;
        }
    }

    private boolean handlerTurnLeftAndRight(final double tLinearSpeed, boolean hasAcceleration) {
        return mLinearSpeed == 0 && tLinearSpeed == 0 && hasAcceleration;
    }

    private double correctLinearSpeed(double speed) {
        if (speed < MIN_LINEAR_SPEED) {
            return MIN_LINEAR_SPEED;
        }
        if (speed > MAX_LINEAR_SPEED) {
            return MAX_LINEAR_SPEED;
        }
        return speed;
    }

    private double correctAngularSpeed(double speed) {
        if (speed < MIN_ANGULAR_SPEED) {
            return MIN_ANGULAR_SPEED;
        }
        if (speed > MAX_ANGULAR_SPEED) {
            return MAX_ANGULAR_SPEED;
        }
        return speed;
    }

    @Override
    public void motionSoft(double angularSpeed, double linearSpeed, boolean hasAcceleration) {
        if (isNavigationing()) {
            return;
        }
        final double cLinearSpeed = correctLinearSpeed(linearSpeed);
        final double cAngularSpeed = correctAngularSpeed(angularSpeed);

        startDecTimer(cAngularSpeed, cLinearSpeed, hasAcceleration);
    }

    public void setWorkMode(final WorkMode mode, final ChassisResListener listener) {
        Log.d(TAG, "setWorkMode mode=" + mode + " mCurrentMode=" + mCurrentMode);
        if (mCurrentMode == mode) {
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "already in " + mode + " mode!");
            }
            return;
        }
        commandParser.setWorkingMode(mode, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.i(TAG, "setWorkMode status=" + status + " resultCode=" + resultCode
                        + " result=" + result);
                if (status) {
                    mCurrentMode = mode;
                }

                if (listener != null) {
                    listener.onResponse(status, resultCode, result);
                }
            }
        });
    }

    public void getWorkingMode(final ChassisResListener listener) {
        commandParser.getWorkingMode(new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                listener.onResponse(status, resultCode, result);
            }
        });
    }

    public void switchMotionMode(final MotionMode mode) {
        if (mode == MotionMode.MOTION_MODE_SLEEP && mCurrentMode == CREATING_MAP) {
            Log.d(TAG, "can not sleep radar due to CREATING MAP");
            return;
        }
        commandParser.setMotionMode(mode, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (status) {
                    updateCurrentMotionMode(mode);
                }
            }
        });
    }

    public void switchMotionMode(MotionMode mode, final ChassisResListener listener) {
        if (mode == MotionMode.MOTION_MODE_SLEEP && mCurrentMode == CREATING_MAP) {
            Log.d(TAG, "can not sleep radar due to CREATING MAP");
            listener.onResponse(false, FAIL_NO_REASON, "creating map");
            return;
        }
        commandParser.setMotionMode(mode, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                listener.onResponse(status, resultCode, result);
            }
        });
    }

    public void getMotionMode(final ChassisResListener listener) {
        commandParser.getMotionMode(new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                listener.onResponse(status, resultCode, result);
            }
        });
    }

    @Override
    public void setCurrentWorkModeFree(final ChassisResListener listener) {
        ArrayDeque<Oper> opers = new ArrayDeque<>();
        opers.add(newSetWorkMode(FREE));
        commandParser.performOprations(opers, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                listener.onResponse(status, resultCode, result);
            }
        });
    }

    @Override
    public void startExtendMap(ChassisResListener listener) {

    }

    @Override
    public void setRadarState(boolean state, final ChassisResListener listener) {
        final MotionMode motionMode = state ? MotionMode.AUTO_MOVING :
                MotionMode.MOTION_MODE_SLEEP;
        this.switchMotionMode(motionMode, new ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (listener != null) {
                    listener.onResponse(status, resultCode, result);
                }
                if (resultCode == SUCCESS) {
                    updateCurrentMotionMode(motionMode);
                }
            }
        });
    }

    @Override
    public void getRadarState(final ChassisResListener listener) {
        this.getMotionMode(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, " getRadarState radarStatus staus=" + status + " resultCode=" + resultCode + " result=" + result);
                if (listener != null) {
                    if (resultCode == SUCCESS) {
                        int motionMode = (int) result;
                        boolean isSuc = motionMode != MotionMode.MOTION_MODE_SLEEP.getValue();
                        listener.onResponse(status, resultCode, isSuc);
                    } else {
                        listener.onResponse(status, FAIL_NO_REASON, "");
                    }
                }

            }
        });
    }

    @Override
    public void switchChargeMode() {
        this.switchMotionMode(MotionMode.AUTO_CHARGE);
    }

    @Override
    public void switchManualMode() {
        this.switchMotionMode(MotionMode.MANUAL_CONTROL);
    }

    @Override
    public void sendPrimitiveMovingSpeed(double angularSpeed, double linearSpeed) {

    }

    @Override
    public void getFullCheckStatus(final ChassisResListener listener) {
        ArrayDeque<Oper> opers = new ArrayDeque<>();
        if (isServiceReady()) {
            Log.i(TAG, "chassis ready, getFullCheckStatus set self-checking mode");
            opers.add(new SetWorkMode(SELF_CHECKING) {
                @Override
                public boolean onResponse(boolean status, int resultCode, Object result) {
                    if (status) {
                        updatePoseEstimate(false);
                    } else {
                        if (listener != null) {
                            listener.onResponse(status, resultCode, result);
                        }
                    }
                    return status;
                }
            });
        } else {
            Log.i(TAG, "chassis not ready, getFullCheckStatus set self-checking mode");
        }

        opers.add(new GetSensorStatus() {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {
                listener.onResponse(status, resultCode, result);
                return true;
            }
        });
        commandParser.performOprations(opers, null);
    }

    @Override
    public void getSensorStatus(final ChassisResListener listener) {
        ArrayDeque<Oper> opers = new ArrayDeque<>();
        opers.add(new GetSensorStatus() {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {
                if (listener != null) {
                    listener.onResponse(status, resultCode, result);
                }
                return true;
            }
        });
        commandParser.performOprations(opers, null);
    }

    @Override
    public boolean getLogFile(long startTime, long endTime, String cmdType, String fileType, String path, final ChassisResListener listener) {

        Log.i(TAG, "getLogFile: path:" + path);
        ArrayDeque<Oper> opers = new ArrayDeque<>();
        opers.add(new GetLogFile(path, fileType) {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {
                if (listener != null) {
                    listener.onResponse(status, resultCode, result);
                }
                return true;
            }
        });
        commandParser.performOprations(opers, null);
        return false;
    }

    @Override
    public void removeTkMap(String mapName, final ChassisResListener listener) {
        Log.i(TAG, "removeTkMap:" + mapName);
        ArrayDeque<Oper> opers = new ArrayDeque<>();
        opers.add(new RemoveTkMap(mapName) {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {
                Log.i(TAG, "removeTkMap resultCode:" + resultCode);
                if (listener != null) {
                    listener.onResponse(status, resultCode, result);
                }
                return status;
            }
        });
        commandParser.performOprations(opers, null);
    }

    @Override
    public boolean takeSnapshot(final String logID, final ChassisResListener listener) {
        Log.i(TAG, "takeSnapshot logID:" + logID);
        ArrayDeque<Oper> opers = new ArrayDeque<>();
        opers.add(new TakeSnapshot(logID) {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {
                Log.i(TAG, "takeSnapshot resultCode:" + resultCode);
                if (listener != null) {
                    listener.onResponse(status, resultCode, result);
                }
                return status;
            }
        });
        commandParser.performOprations(opers, null);
        return false;
    }

    @Override
    public boolean packLogFile(long startTime, long endTime, final ChassisResListener listener) {
        Log.i(TAG, "packLogFile startTime:" + startTime + " endTime:" + endTime);
        ArrayDeque<Oper> opers = new ArrayDeque<>();
        opers.add(new PackLog(startTime, endTime) {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {
                Log.i(TAG, "packLogFile resultCode:" + resultCode);
                if (listener != null) {
                    listener.onResponse(status, resultCode, result);
                }
                return status;
            }
        });
        commandParser.performOprations(opers, null);
        return false;
    }

    @Override
    public void setTime(long currTime, final ChassisResListener listener) {
        Log.i(TAG, "setTime:" + currTime);
        ArrayDeque<Oper> opers = new ArrayDeque<>();
        opers.add(new Operations.SetTime(currTime) {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {

                Log.i(TAG, "setTime code:" + resultCode + " result:" + result);
                if (listener != null) {
                    listener.onResponse(status, resultCode, result);
                }
                return status;
            }
        });
        commandParser.performOprations(opers, null);
    }

    @Override
    public void setRoverConfig(RoverConfig config, final ChassisResListener listener) {
        commandParser.setRoverConfig(config, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (listener != null) {
                    listener.onResponse(status, resultCode, result);
                }
            }
        });
    }


    @Override
    public void getRoverConfig(final ChassisResListener listener) {
        commandParser.getRoverConfig(new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (listener != null) {
                    listener.onResponse(status, resultCode, result);
                }
            }
        });
    }

    @Override
    public boolean startPlanRoute() {
        //TODO RosClient
        return false;
    }

    @Override
    public void savePlanRoute(String routeName, List<Pose> poseList) {
        //TODO RosClient
    }

    @Override
    public void getSystemInformation(final ChassisResListener listener) {
        commandParser.getSystemInformation(new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                listener.onResponse(status, resultCode, result);
            }
        });
    }

    @Override
    public boolean isCommunicating() {
        return isSocketConnected() && isRemoteRunning();
    }

    @Override
    public boolean hasObstacle(double startAngle, double endAngle, double distance) {
        synchronized (laserUpdateLock) {
            double arcStartAngle = startAngle * Math.PI / 180;
            double arcEndAngle = endAngle * Math.PI / 180;
            Log.d(TAG, "arcStartAngle = " + arcStartAngle + ", arcEndAngle = " + arcEndAngle);
            if (mLaserDatas == null) {
                return true;
            }
            for (Laser info : mLaserDatas) {
                Log.d(TAG, "laser data = " + info.toString());
                if (isInAngleRange(arcStartAngle, arcEndAngle, info.getAngle()) && info.getDistance() < distance) {
                    return true;
                }
            }

            return false;
        }
    }

    @Override
    public boolean hasObstacleInArea(double startAngle, double endAngle, double minDistance, double maxDistance) {
        return false;
    }

    @Override
    public void recoveryNavigation(final ChassisResListener listener) {
        Log.i(TAG, "recoveryNavigation:");
        ArrayDeque<Oper> opers = new ArrayDeque<>();
        opers.add(newSetWorkMode(INIT_MODE));
        opers.add(new Operations.Recovery() {
            @Override
            public boolean onResponse(boolean status, int resultCode, Object result) {
                Log.i(TAG, "recovery resultCode:" + resultCode);
                if (listener != null) {
                    listener.onResponse(status, resultCode, result);
                }
                return status;
            }
        });
        commandParser.performOprations(opers, null);
    }

    @Override
    public void turnLeft(double angle, double speed, double acceleration, boolean noNeedAcceleration, ChassisResListener listener) {
        motionRotate(angle, speed, noNeedAcceleration, listener);
    }

    @Override
    public void turnRight(double angle, double speed, double acceleration, boolean noNeedAcceleration, ChassisResListener listener) {
        motionRotate(angle, -speed, noNeedAcceleration, listener);
    }

    @Override
    public void forward(double distance, double speed, double acceleration, ChassisResListener listener) {
        motionLinear(distance, speed, false, listener);
    }

    @Override
    public void forward(double distance, double speed, double acceleration, boolean avoid, ChassisResListener listener) {
        motionLinear(distance, speed, avoid, listener);
    }

    @Override
    public void backward(double distance, double speed, double acceleration, ChassisResListener listener) {
        motionLinear(distance, -speed, false, listener);
    }

    @Override
    public void rotateInPlace(int direction, double angle, double speed, ChassisResListener listener) {

    }

    /**
     * Linear motion
     *
     * @param distance motion distance
     * @param speed    speed of motion
     */
    public void motionLinear(double distance, double speed, boolean avoid, ChassisResListener listener) {
        motion(0, speed, 0);

        if (distance != Double.MAX_VALUE) {
            long delay = calculateDecNumber(0, speed);
            long time = (long) ((distance / Math.abs(speed)) * 1000) + delay / 2;
            Log.d(TAG, "motionLinear delay : " + delay + ", time : " + time + ", speed : " + speed);
            startMotionTimer(time, listener);
        } else {
            if (listener != null) {
                listener.onResponse(true, SUCCESS, null);
            }
        }
    }

    private void motionRotate(double angle, double speed, boolean noNeedAcceleration, ChassisResListener listener) {
        motion(speed, 0, 0, !noNeedAcceleration);


        if (angle != Double.MAX_VALUE) {
            long delay = noNeedAcceleration ? 0 : calculateDecNumber(speed, 0);
            long time = (long) ((angle / Math.abs(speed)) * 1000) + delay / 2;
            if (time <= 600) {
                Log.e(TAG, "adjust timeTurn angle : " + angle + "  speed : " + speed + "  timeout : " + time);
                time += 600;
            }
            Log.e(TAG, "Turn angle : " + angle + "  speed : " + speed + "  timeout : " + time);
            startMotionTimer(time, listener);
        } else {
            if (listener != null) {
                listener.onResponse(true, SUCCESS, null);
            }
        }
    }

    private MotionTimer mMotionTimer;

    private void startMotionTimer(long time, ChassisResListener listener) {
        if (mMotionTimer != null) {
            mMotionTimer.cancel();
        }

        mMotionTimer = new MotionTimer(listener);
        mMotionTimer.schedule(time);
    }

    private class MotionTimer extends Timer {
        ChassisResListener mListener;

        public MotionTimer(ChassisResListener listener) {
            super();
            this.mListener = listener;
        }

        public void schedule(long delay) {
            if (delay <= 0) {
                if (mListener != null) {
                    mListener.onResponse(false, FAIL_NO_REASON, null);
                }
                return;
            }
            this.schedule(new TimerTask() {
                @Override
                public void run() {
                    Log.e(TAG, "Motion time out, Stop move");
                    motion(0, 0, 0, true);
                    if (mListener != null) {
                        mListener.onResponse(true, SUCCESS, null);
                        mListener = null;
                    }
                }
            }, delay);
        }

        @Override
        public void cancel() {
            Log.e(TAG, "Motion canceled");
            super.cancel();
        }

        public ChassisResListener getListener() {
            return mListener;
        }
    }


    private boolean isInAngleRange(double startAngle, double endAngle, double aimAngle) {
        return (aimAngle >= startAngle && aimAngle <= endAngle);
    }

    private synchronized void syncWorkStatusWithChassis() {
        Log.i(TAG, "sync work status begin");

        // Allow access to many times, but there is no switch mode for many times
        getWorkingMode(new ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.i(TAG, "sync work status with chassis, status " + status);
                if (status) {
                    WorkStateMode workStateMode = WorkStateMode.parseFrom(result);
                    if (workStateMode == null) {
                        return;
                    }

                    WorkMode workMode = workStateMode.generateWorkModeFromValue();
                    if (workMode != null) {
                        updateCurMode(workMode);
                    }

                    Log.i(TAG, "sync work status, state value " + workStateMode.getStateValue());

                    if (workStateMode.getStateValue() == WorkStateMode.CHASSIS_READY
                            && !isAlreadyInit()) {
                        // 只在第一次连接的时候初始化，只在第一次连接置状态为READY，后续都不起作用
                        commandParser.setWorkModeState(WorkStateMode.CHASSIS_READY);
                        initWhenFirstConnected();
                    }
                }
            }
        });
    }

    private synchronized void initWhenFirstConnected() {
        //避免多次init，所以进来就设置为true
        setAlreadyInit(true);
        updatePoseStateWithChassis();
        final String mapName = NavigationDataManager.getInstance().getMapName();
        Log.i(TAG, "change navigation mode begin, map name " + mapName);

        if (TextUtils.isEmpty(mapName)) {
            switchMotionModeAfterConnect();
            return;
        }

        if (isInSpecialMode(null) || isInSpecialMode(FREE)) {
            setWorkMode(WorkMode.NAVIGATION, new ChassisResListener() {
                @Override
                public void onResponse(boolean status, int resultCode, Object result) {
                    Log.i(TAG, "set work navigation mode , resultCode " + resultCode + " status " + status);
                    if (!status) {
                        setAlreadyInit(false);
                    }
                }
            });
        }
    }

    private synchronized void updatePoseStateWithChassis() {
        updatePoseStateWithChassis(new ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.i(TAG, "updatePoseStateWithChassis status " + status + " resultCode " + resultCode);
                if (!(result instanceof PoseState)) {
                    return;
                }

                PoseState poseState = (PoseState) result;
                switch (poseState.getPoseState()) {
                    case PoseState.POSE_STATE_NORMAL:
                        updatePoseEstimate(true);
                        Log.i(TAG, "locate normal");
                        break;
                    default:
                        updatePoseEstimate(false);
                        Log.i(TAG, "need to update the location status to lost");
                        break;
                }
            }
        });
    }

    public void updatePoseStateWithChassis(final ChassisResListener listener) {
        commandParser.updatePoseState(new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                listener.onResponse(status, resultCode, result);
            }
        });
    }

    private void switchMotionModeAfterConnect() {
        commandParser.setMotionMode(MotionMode.AUTO_MOVING, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (resultCode == SUCCESS) {
                    setCurrentMotionMode(MotionMode.AUTO_MOVING);
                }
            }
        });
    }

    @Override
    public void motionControlWithObstacle(double angularSpeed, double linearSpeed, final double minDistance) {
        if (isNavigationing()) {
            return;
        }
        final double cLinearSpeed = correctLinearSpeed(linearSpeed);
        final double cAngularSpeed = correctAngularSpeed(angularSpeed);
        commandParser.setMotionMode(MotionMode.MANUAL_CONTROL, new IChassisCommand.ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (status) {
                    updateCurrentMotionMode(MotionMode.MANUAL_CONTROL);
                }
                double linearSpeed = cLinearSpeed;
                if (mDynamicAvoidPolicy.getState(minDistance)) {
                    Log.d(TAG, "Avoid to set linear speed 0");
                    linearSpeed = 0;
                }
                BigDecimal linearSpeeds = new BigDecimal(Float.toString((float) linearSpeed));
                BigDecimal angularSpeeds = new BigDecimal(Float.toString((float) cAngularSpeed));
                MotionControl.getInstance().motion(new SpeedBean(linearSpeeds, angularSpeeds));
            }
        });

    }

    @Override
    public void setMinObstaclesDistance(double distance) {

    }

    @Override
    public void resetMinObstaclesDistance() {

    }

    @Override
    public void setSimpleEventListener(SimpleEventListener mSimpleEventListener) {

    }

    /**
     * 基础运动避停监听
     */
    private void motionAvoidStop() {
        if (isNavigationing()) {
            Log.e(TAG, "motionAvoidStop: Is navigationing");
            return;
        }
        if (!mMotionWithAvoid) {
            Log.d(TAG, "motionAvoidStop , Not motion avoid state");
            return;
        }
        Velocity realVelocity = mRealtimeVelocity.get();
        if (realVelocity == null || isLinearStill(realVelocity)) {
            Log.d(TAG, "motionAvoidStop , Robot is linear still");
            return;
        }

        double tAngularSpeed = mAngularSpeed;
        Log.d(TAG, "motionAvoidStop , Stop motion : realVelocity=" + realVelocity.toString()
                + ", tAngularSpeed=" + tAngularSpeed);
        BasicMotionProcess.getInstance().stopMotionTask(false, MOTION_AVOID_STOP, "Avoid stop");
        motion(tAngularSpeed, 0, 0, true);
        updateMotionAvoidState(false);
    }

    @Override
    public void motionPid(double angularSpeed, double linearSpeed) {

    }

    @Override
    public void setCameraEnable(int cameraType, boolean enable, ChassisResListener listener) {

    }

    @Override
    public CameraBean queryCameraEnableState(int cameraType) {
        return null;
    }

    @Override
    public void setMultiRobotSettingConfigData(MultiRobotConfigBean configBean, ChassisResListener listener) {

    }

    @Override
    public void sendLoraMsgData(String dataInfo, ChassisResListener listener) {

    }

    @Override
    public boolean setNavigationPriority(int priority) {
        return false;
    }

    @Override
    public void sendLoraTestMsg(ChassisResListener listener) {

    }

    @Override
    public void setLoraTestMode(boolean enable, ChassisResListener listener) {

    }

    @Override
    public void resetLoraDefaultConfig(ChassisResListener listener) {

    }

    @Override
    public void setWheelControlMode(boolean isRelease, ChassisResListener listener) {

    }

    @Override
    public void calcNaviPathInfo(List<NaviPathInfo> pathInfos, ChassisResListener listener) {

    }

    @Override
    public void calcNaviPathDetail(List<NaviPathDetail> pathInfos, ChassisResListener listener) {

    }

    @Override
    public void setMultiRobotWriteExtraData(byte[] data, double time, ChassisResListener listener) {

    }

    @Override
    public void setMultiRobotWriteExternalData(byte[] data, double time, ChassisResListener listener) {

    }
    @Override
    public void enableReportLineData(boolean enable) {

    }

    @Override
    public void enableReportDepthImage(boolean enable, int deviceId) {

    }

    @Override
    public void enableReportIRImage(boolean enable, int deviceId) {

    }

    @Override
    public void startDataSetRecord(String sensorString) {

    }

    @Override
    public void stopDataSetRecord(boolean isLocalData) {

    }

    @Override
    public void uploadNaviDataSet(String sensorString) {

    }

    @Override
    public void autoDrawRoadGraph(String mapName, ChassisResListener listener) {

    }

    public void setOdomUpdateListener(OdomUpdate odomUpdate) {
    }
    @Override
    public void getNaviParams(String type, String params, ChassisResListener listener) {

    }

    @Override
    public void naviPause(boolean isPause, ChassisResListener listener) {

    }

    public void gotoAlign(Pose pose, ChassisResListener listener) {
        Log.d(TAG, "gotoAlign result ");
    }

    @Override
    public void cancelAlign(ChassisResListener listener) {
        Log.d(TAG, "cancelAlign result ");
    }

    @Override
    public void startHumanFollowing(String followId, int lostFindTimeout, ChassisResListener listener) {
    }

    @Override
    public void stopHumanFollowing(ChassisResListener listener) {
    }

    @Override
    public void detectQrCodeByPic(String path, ChassisResListener listener) {

    }
}
