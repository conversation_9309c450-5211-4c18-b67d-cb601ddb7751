package com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard;

import com.ainirobot.navigationservice.beans.standard.LandMarkBean;
import com.ainirobot.navigationservice.beans.standard.NaviSpeedParam;
import com.ainirobot.navigationservice.beans.standard.PoseInfo;
import com.ainirobot.navigationservice.chassisAbility.chassis.connector.standard.ConnectApi;

public interface CmdStandardApi {
    void enterManualMode(ResponseListener listener);
    void exitManualMode(ResponseListener listener);

    void enterRemoteControlMode(double vX, double vTheta, ResponseListener listener);
    void exitRemoteControlMode(ResponseListener listener);

    void enterFixPointMode(ResponseListener listener);
    void exitFixPointMode(ResponseListener listener);

    void exeChargePileRecognize(ResponseListener listener);
    void cancelChargePileRecognize(ResponseListener listener);

    void exeChargePileSearch(ResponseListener listener);
    void cancelChargePileSearch(ResponseListener listener);

    void exeChargePileDocking(ResponseListener listener);
    void cancelChargePileDocking(ResponseListener listener);

    void exeForceForwardMove(double distance, double vX, ResponseListener listener);
    void cancelForceForwardMove(ResponseListener listener);

    void exeForceRotationMove(double angle, double vTheta, ResponseListener listener);
    void cancelForceRotationMove(ResponseListener listener);

    void exeNaviGoPlace(PoseInfo pose, ResponseListener listener);
    //TODO 是否需要参数
    void cancelNaviGoPlace(ResponseListener listener);

    void saveMappingPose(int index, String fileName, ResponseListener listener);
    //TODO 是否需要参数
    void cancelSaveMappingPose(int index, String fileName, ResponseListener listener);

    void getSensorStatus(ResponseListener listener);
    void cancelGetSensorStatus(ResponseListener listener);

    void deleteMap(String mapId, ResponseListener listener);

    void startCreateMap(String mapName, int floor, String tag, ResponseListener listener);

    void getMapInfo(String mapId, ResponseListener listener);

    void getLandMarkList(String mapId, ResponseListener listener);

    void modifyLandmarkInfo(String mapId, String landMarkId, LandMarkBean newLandMark, ResponseListener listener);

    void deleteLandMark(String mapId, String landMarkId, ResponseListener listener);

    void addNewLandMark(String mapId, LandMarkBean newLandMark, ResponseListener listener);

    void getCurMapInfo(ResponseListener listener);

    void switchMap(String mapId, ResponseListener listener);

    void getRealTimeMap(ResponseListener listener);

    void getMapList(ResponseListener listener);

    void stopCreateMap(boolean save, ResponseListener listener);

    void setRelocalization(PoseInfo pose, ResponseListener listener);

    void getRelocalizationState(ResponseListener listener);

    void setNaviSpeedParam(NaviSpeedParam param, ResponseListener listener);


    void injectConnector(ConnectApi connectApi);

    void registerEventListener(String type, EventListener listener);

    void unRegisterEventListener(String type);

    class EventListener {
        public void onEvent(int type, String msg, Object param) {

        }
    }

    class ResponseListener {
        public void onResult(int resultCode, String msg, Object param) {

        }

        public void onError(int resultCode, String msg) {

        }
        public void onStatusUpdate(int stage, Object param) {

        }
    }
}
