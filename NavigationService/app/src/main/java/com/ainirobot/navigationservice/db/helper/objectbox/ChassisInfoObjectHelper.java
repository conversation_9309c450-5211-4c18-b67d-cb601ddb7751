package com.ainirobot.navigationservice.db.helper.objectbox;

import android.content.Context;
import android.provider.Settings;
import android.util.Log;

import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.waiter.MultiRobotConfigBean;
import com.ainirobot.navigationservice.db.entity.ChassisInfo;
import com.ainirobot.navigationservice.db.entity.ChassisInfo_;
import com.ainirobot.navigationservice.db.helper.iml.ChassisInfoHelperIml;
import com.ainirobot.navigationservice.utils.GsonUtil;

import java.util.List;

import io.objectbox.Box;
import io.objectbox.BoxStore;
import io.objectbox.Property;
import io.objectbox.query.Query;

public class ChassisInfoObjectHelper extends BaseObjectHelper<ChassisInfo> implements ChassisInfoHelperIml {
    public ChassisInfoObjectHelper(BoxStore boxStore) {
        super(boxStore);
    }

    @Override
    public String getRoverConfig() {
        return getChassisInfoString(ChassisInfo_.roverConfig);
    }

    @Override
    public String getIpNavigation() {
        return getChassisInfoString(ChassisInfo_.ipNavigation);
    }

    @Override
    public String getMultiRobotConfig() {
        return getChassisInfoString(ChassisInfo_.multiRobotConfig);
    }

    @Override
    public String getIpSdkRos() {
        return getChassisInfoString(ChassisInfo_.ipSdkRos);
    }

    @Override
    public void initChassisInfoData(Context context, List<ChassisInfo> chassisInfos) {
        if (null == chassisInfos || chassisInfos.isEmpty()) {
            Log.d(TAG, "initChassisInfoData: list is null");
            return;
        }
        Log.d(TAG, "initChassisInfoData: start");
        Box<ChassisInfo> chassisInfoBox = getBox();
        chassisInfoBox.removeAll();
        chassisInfoBox.put(chassisInfos);
        Log.d(TAG, "initChassisInfoData: " + chassisInfoBox.count());

        ChassisInfo info;
        MultiRobotConfigBean configBean;
        if ((info = chassisInfos.get(0)) != null && (configBean =
                GsonUtil.fromJson(info.getMultiRobotConfig(), MultiRobotConfigBean.class)) != null) {
            Settings.Global.putInt(context.getContentResolver(), "multi_robot_lora_enable", configBean.isEnable() ? 1 : 0);
        }
    }

    @Override
    public boolean updateIpNavigation(String ipNavigation) {
        ChassisInfo chassisInfo = getChassisInfo();
        if (null == chassisInfo) {
            Log.d(TAG, "updateIpNavigation Database configuration does not exist");
            chassisInfo = new ChassisInfo();
        }
        chassisInfo.setIpNavigation(ipNavigation);
        return updateChassisInfo(chassisInfo);
    }

    @Override
    public boolean updateIpSdkRos(String ip) {
        ChassisInfo chassisInfo = getChassisInfo();
        if (null == chassisInfo) {
            Log.d(TAG, "updateIpSdkRos Database configuration does not exist");
            chassisInfo = new ChassisInfo();
        }
        chassisInfo.setIpSdkRos(ip);
        return updateChassisInfo(chassisInfo);
    }

    @Override
    public boolean updateMultiRobotConfig(RoverConfig roverConfig) {
        ChassisInfo chassisInfo = getChassisInfo();
        if (null == chassisInfo) {
            Log.d(TAG, "updateMultiRobotConfig RoverConfig Database configuration does not exist");
            chassisInfo = new ChassisInfo();
        }
        chassisInfo.setRoverConfig(GsonUtil.toJson(roverConfig));
        return updateChassisInfo(chassisInfo);
    }

    @Override
    public boolean updateMultiRobotConfig(MultiRobotConfigBean multiRobotConfigBean) {
        Log.d(TAG, "updateMultiRobotConfig: multiRobotConfigBean=" + (multiRobotConfigBean != null ? multiRobotConfigBean.toString() : "null"));
        ChassisInfo chassisInfo = getChassisInfo();
        if (null == chassisInfo) {
            Log.d(TAG, "updateMultiRobotConfig Database configuration does not exist");
            chassisInfo = new ChassisInfo();
        }
        chassisInfo.setMultiRobotConfig(GsonUtil.toJson(multiRobotConfigBean));
        return updateChassisInfo(chassisInfo);
    }

    private boolean updateChassisInfo(ChassisInfo chassisInfo) {
        Log.d(TAG, "updateChassisInfo: " + (chassisInfo != null ? chassisInfo.toString() : "null"));
        Box<ChassisInfo> chassisInfoBox = getBox();
        return chassisInfoBox.put(chassisInfo) > 0;
    }

    private ChassisInfo getChassisInfo() {
        Query<ChassisInfo> infoQuery = getBox().query().build();
        ChassisInfo chassisInfo = infoQuery.findFirst();
        infoQuery.close();
        return chassisInfo;
    }

    private String getChassisInfoString(Property<ChassisInfo> property) {
        Query<ChassisInfo> infoQuery = getBox().query().build();
        String propertyStr = infoQuery.property(property).findString();
        infoQuery.close();
        return propertyStr;
    }
}
