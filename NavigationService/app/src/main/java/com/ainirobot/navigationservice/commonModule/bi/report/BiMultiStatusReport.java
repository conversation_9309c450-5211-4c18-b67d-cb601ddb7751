package com.ainirobot.navigationservice.commonModule.bi.report;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

/**
 * 此埋点有两处，一个在NavigationService中，由底盘主动上报。
 * 另一个在MapTool中，每次进入设置界面手动ping多机状态上报。
 */
public class BiMultiStatusReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_multi";
    private static final String MULTI_STATUS = "multi_status";
    private static final String CTIME = "ctime";

    public BiMultiStatusReport() {
        super(TABLE_NAME);
        initData();
    }

    private void initData() {
        addData(MULTI_STATUS, false);
        addData(CTIME, "");
    }

    public BiMultiStatusReport addMultiStatus(boolean status) {
        addData(MULTI_STATUS, status);
        return this;
    }

    private void addCTime() {
        addData(CTIME, System.currentTimeMillis());
    }

    @Override
    public void report() {
        addCTime();
        super.report();
    }


}
