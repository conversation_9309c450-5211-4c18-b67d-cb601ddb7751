package com.ainirobot.navigationservice.commonModule.logs.beans;

import org.json.JSONException;
import org.json.JSONObject;

public class LogCacheBean {

    private String cacheId;

    private String time;

    private String type;

    private int status;

    private String tk1Version;

    private String expandField = "";

    private String expandField2 = "";

    private String expandField3 = "";

    private String expandField4 = "";

    private String expandField5 = "";

    public LogCacheBean() {
    }

    public LogCacheBean(String cacheId, String time, String type, int status, String tk1Version,
                        String expandField) {
        this.cacheId = cacheId;
        this.time = time;
        this.type = type;
        this.status = status;
        this.tk1Version = tk1Version;
        this.expandField = expandField;
    }

    public String getCacheId() {
        return cacheId;
    }

    public void setCacheId(String cacheId) {
        this.cacheId = cacheId;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getExpandField() {
        return expandField;
    }

    public void setExpandField(String expandField) {
        this.expandField = expandField;
    }

    public String getTk1Version() {
        return tk1Version;
    }

    public void setTk1Version(String tk1Version) {
        this.tk1Version = tk1Version;
    }

    public String getExpandField2() {
        return expandField2;
    }

    public void setExpandField2(String expandField2) {
        this.expandField2 = expandField2;
    }

    public String getExpandField3() {
        return expandField3;
    }

    public void setExpandField3(String expandField3) {
        this.expandField3 = expandField3;
    }

    public String getExpandField4() {
        return expandField4;
    }

    public void setExpandField4(String expandField4) {
        this.expandField4 = expandField4;
    }

    public String getExpandField5() {
        return expandField5;
    }

    public void setExpandField5(String expandField5) {
        this.expandField5 = expandField5;
    }

    @Override
    public String toString() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cacheId", this.cacheId);
            jsonObject.put("time", this.time);
            jsonObject.put("type", this.type);
            jsonObject.put("status", this.status);
            jsonObject.put("tk1Version", this.tk1Version);
            jsonObject.put("expandField", this.expandField);
            jsonObject.put("expandField2", this.expandField2);
            jsonObject.put("expandField3", this.expandField3);
            jsonObject.put("expandField4", this.expandField4);
            jsonObject.put("expandField5", this.expandField5);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject.toString();
    }
}
