package com.ainirobot.navigationservice.chassisAbility.chassis.client.x86;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.navigationservice.Defs.Def;
import com.ainirobot.navigationservice.beans.tk1.Event;
import com.ainirobot.navigationservice.beans.tk1.Laser;
import com.ainirobot.navigationservice.beans.tk1.Map;
import com.ainirobot.navigationservice.beans.tk1.MapData;
import com.ainirobot.navigationservice.beans.tk1.Message;
import com.ainirobot.navigationservice.beans.tk1.MotionMode;
import com.ainirobot.navigationservice.beans.tk1.NavVelocity;
import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.tk1.SensorStatus;
import com.ainirobot.navigationservice.beans.tk1.Statistic;
import com.ainirobot.navigationservice.beans.tk1.SystemData;
import com.ainirobot.navigationservice.beans.tk1.TargetPose;
import com.ainirobot.navigationservice.beans.tk1.Velocity;
import com.ainirobot.navigationservice.beans.tk1.WorkMode;
import com.ainirobot.navigationservice.business.rpc.MotionControl;
import com.ainirobot.navigationservice.business.rpc.SpeedBean;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.AbsChassisClient;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.tk1.customListener.CreateMapStop;
import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.IChassisCommand;
import com.ainirobot.navigationservice.chassisAbility.controller.AvoidStoppingPolicy;
import com.ainirobot.navigationservice.chassisAbility.controller.BasicMotionProcess;
import com.ainirobot.navigationservice.chassisAbility.controller.MotionPolicyMini;
import com.ainirobot.navigationservice.commonModule.bi.report.MotionCallChainReporter;
import com.ainirobot.navigationservice.commonModule.configuration.ConfigManager;
import com.ainirobot.navigationservice.commonModule.data.place.PlaceDataManager;
import com.ainirobot.navigationservice.commonModule.logs.LogManager;
import com.ainirobot.navigationservice.utils.DistanceUtils;
import com.ainirobot.navigationservice.utils.FileUtils;
import com.ainirobot.navigationservice.utils.GsonUtil;
import com.ainirobot.navigationservice.utils.LogUtils;
import com.ainirobot.navigationservice.utils.MD5;
import com.ainirobot.navigationservice.utils.NavigationConfig;
import com.ainirobot.navigationservice.utils.OutMapUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.atomic.AtomicReference;

import ninjia.android.proto.ChassisFileProtoWrapper;
import ninjia.android.proto.CommonProtoWrapper;
import ninjia.android.proto.CostmapProtoWrapper;
import ninjia.android.proto.GetMapListProtoWrapper;
import ninjia.android.proto.GetSystemInformationProtoWrapper;
import ninjia.android.proto.Pose2dProtoWrapper;
import ninjia.android.proto.RelocateDataProtoWrapper;
import ninjia.android.proto.ReportStatisticProtoWrapper;
import ninjia.android.proto.RoverConfigProtoWrapper;
import ninjia.android.proto.RoverConfigProtoWrapper.RoverConfigProto;
import ninjia.android.proto.SelfCheckResultProtoWrapper;
import ninjia.android.proto.SetGoalProtoWrapper;
import ninjia.android.proto.UpdateEventProtoWrapper;
import ninjia.android.proto.UpdateLaserProtoWrapper;
import ninjia.android.proto.VelocityProtoWrapper;
import ninjia.android.proto.WorkingModeProtoWrapper;
import ninjia.android.roversdk.Result;
import ninjia.android.roversdk.RoverClientFactory;
import ninjia.android.roversdk.iface.ICommandHandle;
import ninjia.android.roversdk.iface.IRoverClient;
import ninjia.android.roversdk.listener.SdkListener;
import ninjia.android.roversdk.listener.UpdateListener;
import ninjia.android.roversdk.sdk.SdkError;

import static com.ainirobot.navigationservice.Defs.Def.ResultCode.DESTROY_NO_NEED;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.FAIL_NO_REASON;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.MOTION_AVOID_STOP;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.SUCCESS;
import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;
import static com.ainirobot.navigationservice.beans.tk1.WorkMode.CREATING_MAP;
import static com.ainirobot.navigationservice.beans.tk1.WorkMode.FREE;
import static com.ainirobot.navigationservice.beans.tk1.WorkMode.NAVIGATION;
import static com.ainirobot.navigationservice.beans.tk1.WorkMode.SELF_CHECKING;



//TODO 1. 视觉地图接口 2. 雷达数据上报
public class ChassisClientX86Impl extends AbsChassisClient {

    public static final String TAG = TAGPRE + ChassisClientX86Impl.class.getSimpleName();
    public static final String TAG_AVOID = TAG + "motion_avoid";


    private static final String API_IN = "api__in";
    private static final String API_BACK = "api__back";

    //X86 Client
    private IRoverClient mRoverClient;
    private volatile boolean mRoverSdkReady = true;
    private ICommandHandle commandApi;

    private volatile boolean isPoseEstimate;
    private String mNavIp;

    private TargetPose mTargetPose;
    private ChassisEventListener mChassisEventListener;
    private WorkMode mCurrentMode = FREE;
    private MotionMode mCurrentMotionMode = MotionMode.MANUAL_CONTROL;

    private CreateMapStop mCreateMapStop;

    private ArrayList<Laser> mLaserDatas;
    private static final Object laserUpdateLock = new Object();
    private ChassisResListener mGoChargelistener;

    private boolean hasObstacle = false;
    private boolean avoidTag = false;
    private SimpleEventListener mSimpleEventListener;

    public ChassisClientX86Impl() {
        mNavIp = ConfigManager.getInstance().getDeviceIP();
    }

    @Override
    public void init(Context context) {
        super.init(context);

        storeRoverConfigIfNeed();

        startInitRoverClient();

        AvoidStoppingPolicy.getInstance().addObserver(mAvoidObserver);

        BasicMotionProcess.getInstance().init(this);
    }


    private void storeRoverConfigIfNeed() {
        String config = dataManager.getRoverConfig();
        int deviceTN = ConfigManager.getInstance().getDeviceTypeNumber();
        if (!TextUtils.isEmpty(config)) {
            Log.d(TAG, "Already store rover config : " + config);
            RoverConfig config1 = GsonUtil.fromJson(config, RoverConfig.class);
            if (config1 != null && config1.getDeviceType() != deviceTN) {
                Log.d(TAG, "Update rover config : new deviceTN = " + deviceTN);
                config1.setDeviceType(deviceTN);
                dataManager.setRoverConfig(GsonUtil.toJson(config1));
            }
            return;
        }

        RoverConfig roverConfig = new RoverConfig("");
        roverConfig.setEnableCamera(true);
        roverConfig.setDeviceType(deviceTN);
        roverConfig.setScenesType(RoverConfig.SCENES_DEFAULT);
        roverConfig.setEnableFishEye(false);
        roverConfig.setEnableRgbd(true);
        roverConfig.setEnableSonar(false);
        roverConfig.setEnableIR(false);
        roverConfig.setMapWithRecord(false);
        roverConfig.setRecordMono(true);
        roverConfig.setRecordRgbd(true);
        roverConfig.setEnableObstaclesAvoid(false);
        roverConfig.setLethalRadius(RoverConfig.NORMAL);
        roverConfig.setOpenMaxLostDistance(false);
        roverConfig.setEnableVision(true);
        Log.d(TAG, "Update rover config : " + roverConfig.toString());

        dataManager.setRoverConfig(GsonUtil.toJson(roverConfig));
    }


    private void startInitRoverClient() {
        Log.d(TAG, "startInitRoverClient");
        mRoverClient = RoverClientFactory.create(mContext, mNavIp, RoverClientFactory.TYPE_PRO);
        mRoverClient.setSdkListener(sdkListener);
        mRoverClient.setUpdateListener(mUpdateListener);
        new Thread(new Runnable() {
            @Override
            public void run() {
                mRoverClient.init();
            }
        }).start();

        commandApi = mRoverClient.getCommandHandle();
    }

    private SdkListener sdkListener = new SdkListener() {
        @Override
        public void onRemoteStateChange(int i) {
            Log.d(TAG, "onRemoteStateChange:" + i);
        }

        @Override
        public void onSdkReady(Boolean aBoolean) {
            Log.d(TAG, "onSdkReady:" + aBoolean);
            mRoverSdkReady = aBoolean;
            if (mRoverSdkReady) {
                settingManager.syncTimeToTk(System.currentTimeMillis());
                if (mChassisEventListener != null) {
                    mChassisEventListener.onChassisConnectStatus(null, Definition.STATUS_HW_CONNECTED);
                }

                initRoverService();
                notifyInspect(null,Definition.STATUS_HW_CONNECTED);
            } else {
                if (mChassisEventListener != null) {
                    mChassisEventListener.onChassisDisconnect("chassis socket disconnect", "rover SDK");
                    mChassisEventListener.onChassisConnectStatus("rover SDK", Definition.STATUS_HW_DISCONNECTED);
                }
                biManager.disconnectNavigationReport("rover SDK");
                logManager.startCollectLogFlow(RobotSettings.getSystemSn() + "-" + System.currentTimeMillis(),
                        String.valueOf(System.currentTimeMillis()), LogManager.TYPE_SOCKET_DISCONNECT);
            }
        }

        @Override
        public void onError(SdkError sdkError) {
            Log.d(TAG, "onError:" + sdkError.toString());
        }
    };

    private void initRoverService() {
        final String mapName = dataManager.getMapName();
        Log.i(TAG, "initRoverService, map name " + mapName);

        Result result;
        if (TextUtils.isEmpty(mapName)) {
            result = commandApi.init(WorkingModeProtoWrapper.WorkingModeProto.FREE_MODE, generateRoverConfigProtoFromConfigFile(), "");
        } else {
            result = commandApi.init(WorkingModeProtoWrapper.WorkingModeProto.KNOWN_MAP_NAVIGATION
                    , generateRoverConfigProtoFromConfigFile(), dataManager.getNaviMapName(dataManager.getMapName()));
        }
        Log.d(TAG, "initRoverService result = " + result.toString());
    }

    private void notifyInspect(String name, String status) {
        if (mSimpleEventListener != null) {
            mSimpleEventListener.onUpdateChassisConnectStatus(name, status);
        }
    }

    private RoverConfigProto generateRoverConfigProtoFromConfigFile() {
        String roverConfig = dataManager.getRoverConfig();
        if (TextUtils.isEmpty(roverConfig)) {
            throw new RuntimeException("no roverConfig in navigation.properties");
        }

        RoverConfig config = GsonUtil.fromJson(roverConfig, RoverConfig.class);

        Log.d(TAG, "generate RoverConfig = " + config.toString());
        RoverConfigProto roverConfigProto = RoverConfigProto.newBuilder()
                .setRosServerIp(config.getRosIp())
                .setEnableCamera(config.isEnableCamera())
                .setDeviceType(generateDeviceProto(config.getDeviceType()))
                .setEnableFishEye(config.isEnableFishEye())
                .setEnableRgbd(config.isEnableRgbd())
                .setEnableSonar(config.isEnableSonar())
                .setEnableIr(config.isEnableIR())
                .setMapWithRecord(config.isMapWithRecord())
                .setRecordMono(config.isRecordMono())
                .setRecordRgbd(config.isRecordRgbd())
                .setEnableObstaclesAvoid(config.isEnableObstaclesAvoid())
                .setLethalRadius(generateLethalRadiusProto(config.getLethalRadius()))
                .setMaxLostDistance(generateMaxLostDistanceProto(config.isOpenMaxLostDistance()))
                .setEnableVision(config.isEnableVision())
                .setScenesType(generateScenceTypeProto(config.getScenesType()))
                .build();

        return roverConfigProto;
    }

    private RoverConfigProtoWrapper.ScenesTypeProto generateScenceTypeProto(int scenceType) {
        RoverConfigProtoWrapper.ScenesTypeProto scenesTypeProto;
        switch (scenceType) {
            case RoverConfig.SCENES_DEFAULT:
                scenesTypeProto = RoverConfigProtoWrapper.ScenesTypeProto.SCENES_DEFAULT;
                break;
            case RoverConfig.SCENES_KTV:
                scenesTypeProto = RoverConfigProtoWrapper.ScenesTypeProto.SCENES_KTV;
                break;
            default:
                scenesTypeProto = RoverConfigProtoWrapper.ScenesTypeProto.SCENES_DEFAULT;
                break;
        }
        return scenesTypeProto;
    }

    private RoverConfigProtoWrapper.MaxLostDistanceProto generateMaxLostDistanceProto(boolean openMaxLostDistance) {
        return openMaxLostDistance ? RoverConfigProtoWrapper.MaxLostDistanceProto.LOST_DISTANCE_LEVEL3 :
                RoverConfigProtoWrapper.MaxLostDistanceProto.LOST_DISTANCE_LEVEL1;
    }

    private RoverConfigProtoWrapper.LethalRadiusProto generateLethalRadiusProto(int lethalRadius) {
        RoverConfigProtoWrapper.LethalRadiusProto lethalRadiusProto;
        switch (lethalRadius) {
            case RoverConfig.NORMAL:
                lethalRadiusProto = RoverConfigProtoWrapper.LethalRadiusProto.NORMAL;
                break;
            case RoverConfig.RISKY:
                lethalRadiusProto = RoverConfigProtoWrapper.LethalRadiusProto.RISKY;
                break;
            case RoverConfig.UNSAFE:
                lethalRadiusProto = RoverConfigProtoWrapper.LethalRadiusProto.UNSAFE;
                break;
            case RoverConfig.UNRECOGNIZED:
                lethalRadiusProto = RoverConfigProtoWrapper.LethalRadiusProto.UNRECOGNIZED;
                break;
            default:
                lethalRadiusProto = RoverConfigProtoWrapper.LethalRadiusProto.NORMAL;
                break;
        }
        return lethalRadiusProto;
    }

    private RoverConfigProtoWrapper.DeviceTypeProto generateDeviceProto(int deviceType) {
        RoverConfigProtoWrapper.DeviceTypeProto deviceTypeProto;
        switch (deviceType) {
            case RoverConfig.DEVICE_CUSTOM:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_CUSTOM;
                break;
            case RoverConfig.DEVICE_KTV:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_KTV;
                break;
            case RoverConfig.DEVICE_MESSIA_LH:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MESSIA_LH;
                break;
            case RoverConfig.DEVICE_X86_MINI:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MINI;
                break;
            case RoverConfig.DEVICE_X86_MINI2:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MINI2;
                break;
            case RoverConfig.DEVICE_KTV2:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_KTV2;
                break;
            default:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MESSIA_LH;
                break;
        }
        return deviceTypeProto;
    }


    @Override
    public void setRoverConfig(RoverConfig roverConfig, ChassisResListener listener) {
        //todo
        dataManager.setRoverConfig(GsonUtil.toJson(roverConfig));
    }

    @Override
    public void getRoverConfig(ChassisResListener listener) {
        //todo
        String roverConfig = dataManager.getRoverConfig();
        if (TextUtils.isEmpty(roverConfig)) {
            throw new RuntimeException("no roverConfig in navigation.properties");
        }

        RoverConfig config = GsonUtil.fromJson(roverConfig, RoverConfig.class);
        listener.onResponse(true, SUCCESS, config);
    }

    @Override
    public void setTime(final long time, final ChassisResListener listener) {
        Log.d(TAG, API_IN + "setTime = " + time);
        Result result = commandApi.setTime(time);
        Log.d(TAG, API_BACK + "setTime result = " + result.toString());

        if (listener != null) {
            if (result.getCode() == 0) {
                listener.onResponse(true, result.getCode(), "Success");
            } else {
                listener.onResponse(false, result.getCode(), "Failed");
            }
        }

    }

    @Override
    public boolean isCommunicating() {
        return isSocketConnected() && isRemoteRunning();
    }

    @Override
    public boolean takeSnapshot(String logID, ChassisResListener listener) {
        //todo
        return false;
    }

    @Override
    public boolean isServiceReady() {
        Log.e(TAG, "isServiceReady:" + mRoverSdkReady);
        return mRoverSdkReady;
    }

    @Override
    public boolean isSocketConnected() {
        Log.e(TAG, "mRoverSdkReady:" + mRoverSdkReady);
        return mRoverSdkReady;
    }

    @Override
    public boolean isChassisReady() {
        Log.e(TAG, "isChassisReady:" + mRoverSdkReady);
        return mRoverSdkReady;
    }

    @Override
    public void setEventListener(ChassisEventListener listener) {
        mChassisEventListener = listener;
    }

    @Override
    public Pose getCurrentPose() {
        if (!isPoseEstimate) {
            Log.d(TAG, "not pose estimate");
            return null;
        }
        return mCurPose.get();
    }

    @Override
    public Velocity getVelocity() {
        Velocity velocity = mVelocity.get();
        if (velocity == null) {
            velocity = new Velocity(0, 0);
        }
        return velocity;
    }

    @Override
    public boolean isPoseEstimate() {
        return isPoseEstimate;
    }

    private void updatePoseEstimate(boolean isPoseEstimate) {
        Log.d(TAG, "updatePoseEstimate: " + isPoseEstimate);
        if (this.isPoseEstimate == isPoseEstimate) {
            return;
        }
        this.isPoseEstimate = isPoseEstimate;

        if (mChassisEventListener != null) {
            mChassisEventListener.onPoseEstimate(isPoseEstimate);
        }
    }

    @Override
    public void checkCurNaviMap(ChassisResListener listener) {
        final String mapName = dataManager.getMapName();
        Log.d(TAG, "checkCurNaviMap mapName=" + mapName);
        if (TextUtils.isEmpty(mapName)) {
            listener.onResponse(false, FAIL_NO_REASON, "821 has no naviMap");
            return;
        }

        if (!checkNaviMap(mapName)) {
            listener.onResponse(false, FAIL_NO_REASON, "Chassis has no map");
            return;
        }
        listener.onResponse(true, SUCCESS, "Chassis has map");
    }

    @Override
    public void goCharge(ChassisResListener listener) {
        Result result = commandApi.gotoCharge();
        Log.d(TAG, "goCharge result = " + result.toString());
        this.mGoChargelistener = listener;
        ChargeResult chargeResult = new ChargeResult();
        int code = result.getCode();
        chargeResult.setCode(code);
        if (null != listener) {
            if (code == 0) {
                chargeResult.setMessage("Go charge command success");
                listener.onResponse(true, code, GsonUtil.toJson(chargeResult));
            } else {
                chargeResult.setMessage("Go charge command failed");
                listener.onResponse(false, code, GsonUtil.toJson(chargeResult));
            }
        }
    }

    @Override
    public void stopCharge(ChassisResListener listener) {
        Result result = commandApi.cancelCharge();
        Log.d(TAG, "stopCharge result = " + result.toString());
        ChargeResult chargeResult = new ChargeResult();
        int code = result.getCode();
        chargeResult.setCode(code);
        if (null != listener) {
            if (code == 0) {
                chargeResult.setMessage("Stop charge command success");
                listener.onResponse(true, code, GsonUtil.toJson(chargeResult));
            } else {
                chargeResult.setMessage("Stop charge command failed");
                listener.onResponse(false, code, GsonUtil.toJson(chargeResult));
            }
        }
    }

    @Override
    public Velocity getFollowVelocity(double distance, double angle, double headAngleSpeed, Velocity velocity, double latency) {
        return MotionPolicyMini.follow(distance, angle, latency, velocity);
    }

    @Override
    public Velocity getBodyFollowVelocity(double distance, double angle, double latency,double headAngleSpeed,
                                          Velocity velocity, double maxLinSpeed, double maxAngSpeed, double safeDistance) {
        return MotionPolicyMini.bodyFollow(distance, angle, latency, velocity,
                maxLinSpeed, maxAngSpeed, safeDistance);
    }

    @Override
    public Velocity getBodyFollowVelocity(double distance, double angle, double headAngleSpeed, Velocity velocity, double latency) {
        return MotionPolicyMini.bodyFollow(distance, angle, latency, velocity);
    }

    /**
     * 指定名称地图是否存在于底盘
     *
     * @param name 用户在 MapTool 设定的地图名
     * @return true 存在
     */
    private boolean checkNaviMap(String name) {
        String naviMapName = dataManager.getNaviMapName(name);
        Result<GetMapListProtoWrapper.MapListProto> result = commandApi.getMapList();
        Log.d(TAG, "Map list result : " + result.toString());
        if (result.getCode() == Result.CODE_SUCCESS) {
            GetMapListProtoWrapper.MapListProto mapListProto = result.getPayload();
            for (int i = 0; i < mapListProto.getNamesCount(); i++) {
                if (TextUtils.equals(naviMapName, mapListProto.getNames(i))) {
                    Log.d(TAG, "Map " + name + " is in navigation");
                    return true;
                }
            }
        }
        Log.d(TAG, "Map " + name + " does not in navigation");
        return false;
    }

    @Override
    public void loadCurrentMap(final ChassisResListener listener) {
        final String mapName = dataManager.getMapName();
        Log.d(TAG, "loadCurrentMap mapName=" + mapName);
        if (TextUtils.isEmpty(mapName)) {
            listener.onResponse(false, FAIL_NO_REASON, "821 has no map");
            return;
        }

        if (!checkNaviMap(mapName)) {
            listener.onResponse(false, FAIL_NO_REASON, "Chassis has no map");
            return;
        }

        changeNaviMode(NAVIGATION, new ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                listener.onResponse(status, resultCode, result);
            }
        });
    }

    @Override
    public boolean isMoving() {
        return isNavigationing();
    }


    private boolean isNavigationing() {
        return mTargetPose != null
                && mTargetPose.isAvailable();
    }

    private String buildNaviMapName() {
        StringBuilder builder = new StringBuilder();

        String sn = RobotSettings.getSystemSn();

        String currentTime = Long.toString(System.currentTimeMillis());

        String naviMapName = builder.append(sn).append("_").append(currentTime).toString();
        Log.d(TAG, "buildNaviMapName=" + naviMapName);

        return naviMapName;
    }

    @Override
    public void loadMap(String mapName, ChassisResListener listener) {
        Log.d(TAG, "Rover switch map " + mapName);
        if (TextUtils.isEmpty(mapName)) {
            listener.onResponse(false, FAIL_NO_REASON, "mapName is null");
            return;
        }

        if (!changeNaviMode(FREE)) {
            listener.onResponse(false, FAIL_NO_REASON, "Change FREE mode fail");
            return;
        }

        String naviMapName = dataManager.getNaviMapName(mapName);
        if (TextUtils.isEmpty(naviMapName)) {
            Log.d(TAG, "Build navi map name");
            naviMapName = buildNaviMapName();
            dataManager.setNaviMapName(naviMapName, mapName);
        }
        Log.d(TAG, "naviMapName=" + naviMapName);

        Result mapData = commandApi.addMap(naviMapName, FileUtils.getMapData(mapName));
        Log.d(TAG, "Add map data result : " + mapData.toString());
        if (mapData.getCode() != Result.CODE_SUCCESS) {
            listener.onResponse(false, FAIL_NO_REASON, "Add map data fail");
            return;
        }
        Result mapPgm = commandApi.addMap(naviMapName, FileUtils.getMapPgm(mapName));
        Log.d(TAG, "Add map pgm result : " + mapPgm.toString());
        if (mapPgm.getCode() != Result.CODE_SUCCESS) {
            listener.onResponse(false, FAIL_NO_REASON, "Add map pgm fail");
            return;
        }
        if (!changeNaviMode(WorkMode.NAVIGATION, naviMapName)) {
            listener.onResponse(false, FAIL_NO_REASON, "Change navigation mode fail");
            return;
        }
        dataManager.setMapName(mapName);
        listener.onResponse(true, SUCCESS, "Switch success");
    }

    private volatile boolean isStoppingCreateMap = false;
    private String mBuildingNaviMapName;

    @Override
    public void startCreatingMap(ChassisResListener listener) {
        Log.e(TAG, "Rover start creating map");
        if (isInSpecialMode(CREATING_MAP)) {
            Log.e(TAG, "mode:" + mCurrentMode);
            listener.onResponse(true, SUCCESS, "is building");
            return;
        }

        if (isStoppingCreateMap) {
            isStoppingCreateMap = false;
        }
        if (mCreateMapStop != null) {
            mCreateMapStop = null;
        }

        final WorkMode currWorkMode = mCurrentMode;
        if (!changeNaviMode(WorkMode.CREATING_MAP)) {
            listener.onResponse(false, FAIL_NO_REASON, "change CREATING_MAP mode fail");
            return;
        }
        updateCurMode(CREATING_MAP);
        mBuildingNaviMapName = buildNaviMapName();
        Result resultStart = commandApi.startCreateMap(mBuildingNaviMapName);
        Log.d(TAG, "Start create map result : " + resultStart.toString());
        if (resultStart.getCode() != Result.CODE_SUCCESS) {
            listener.onResponse(false, resultStart.getCode(), resultStart.getPayload());
            changeNaviMode(currWorkMode);
            return;
        }
        listener.onResponse(true, SUCCESS, "start create map success");
    }

    @Override
    public void stopCreatingMap(String mapName, boolean save, CreateMapStop listener) {
        Log.e(TAG, "ChassisRelyApiImpl - stopCreatingMap:" + mapName + " mCurrentMode:" + mCurrentMode
                + " isStoppingCreateMap:" + isStoppingCreateMap);
        if (mCurrentMode != CREATING_MAP) {
            listener.onResult(false, CreateMapStop.RESULT_NOT_CREATING_MAP_MODE);
            return;
        }
        if (isStoppingCreateMap) {
            listener.onResult(false, CreateMapStop.RESULT_ALREADY_STOPPING_CREATE_MAP);
            return;
        }
        isStoppingCreateMap = true;
        mCreateMapStop = listener;
        boolean saveMap = true;

        Result<CommonProtoWrapper.TargetGroupPointsProto> saveMapResult = commandApi.saveMap();
        Log.d(TAG, "stopCreatingMap - commandApi.saveMap(): " + "saveMapResult: " + saveMapResult.toString());

        if (saveMapResult.getCode() == SUCCESS && saveMapResult.getPayload()
                instanceof CommonProtoWrapper.TargetGroupPointsProto) {
            saveMap = true;
            CommonProtoWrapper.TargetGroupPointsProto proto = saveMapResult.getPayload();
            List<CommonProtoWrapper.TargetGroupPointProto> list = proto.getPointsList();
            Log.d(TAG, "stopCreatingMap - commandApi.saveMap(): " + "getPointsList: " + list.size());

            List<PlaceDataManager.MappingPose> mappingPoseList = new ArrayList<>();
            for (CommonProtoWrapper.TargetGroupPointProto target : list) {
                CommonProtoWrapper.TargetPointProto point = target.getPoint();
                PlaceDataManager.MappingPose mappingPose = new PlaceDataManager.MappingPose();

                boolean _valid = point.getValid();
                int id = point.getId();
                Pose2dProtoWrapper.Pose2dProto poseProto = point.getPose();

                mappingPose.setValid(_valid);
                mappingPose.setId(id);
                Pose pose = new Pose((float) poseProto.getX(), (float) poseProto.getY(),
                        (float) poseProto.getT());
                mappingPose.setPose(pose);
                mappingPoseList.add(mappingPose);
            }
            mCreateMapStop.onSaveMap(true, mappingPoseList);

        } else {
            saveMap = false;
            mCreateMapStop.onSaveMap(false, null);
        }

        Result resultStop = commandApi.stopCreateMap();
        Log.d(TAG, "Stop create map result : " + resultStop.toString());
        isStoppingCreateMap = false;

        if (!saveMap) {
            listener.onResult(false, "stop creating map, save map fail");
            return;
        }

        if (resultStop.getCode() != SUCCESS) {
            listener.onResult(false, "Stop fail");
            return;
        }
        boolean status = false;
        String result = "";
        if (!TextUtils.isEmpty(mapName)) {
            Result getPgm = commandApi.getMapPgm(mBuildingNaviMapName, FileUtils.getMapPgm(mapName));
            Result getData = commandApi.getMapData(mBuildingNaviMapName, FileUtils.getMapData(mapName));
            Log.d(TAG, "Get map result : getPgm=" + getPgm.toString()
                    + "\n" + " getData=" + getData.toString());
            if (getPgm.getCode() == SUCCESS && getData.getCode() == SUCCESS) {
                try {
                    dataManager.setNaviMapName(mBuildingNaviMapName, mapName);
                    JSONObject json = new JSONObject();
                    File dataFile = FileUtils.getMapData(mapName);
                    File pgmFile = FileUtils.getMapPgm(mapName);
                    String md5 = createFileMd5(pgmFile.getAbsolutePath(), mapName);
                    json.put("pgm", pgmFile.getAbsolutePath());
                    json.put("data", dataFile.getAbsolutePath());
                    json.put("md5", md5);
                    result = json.toString();
                    status = true;
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else {
                result = "Get map fail";
            }
        }
        listener.onResult(status, result);
        changeNaviMode(FREE);
    }


    private String createFileMd5(String mapPath, String mapName) {
        File pgmFile = new File(mapPath);
        if (pgmFile.exists()) {
            String md5 = MD5.getFileMd5(mapPath);
            if (!TextUtils.isEmpty(md5)) {
                setMd5Config(md5, mapName);
                return md5;
            }
        }
        return "";
    }

    public void setMd5Config(String md5, String mapName) {
        dataManager.setMapMd5(md5, mapName);
    }

    public void changeNaviMode(WorkMode workMode, ChassisResListener listener) {
        Log.d(TAG, API_IN + "changeNaviMode" + " destroy");
        Result result1 = commandApi.destroy();
        Log.d(TAG, API_BACK + "changeNaviMode" + " destroy result1 = " + result1.toString());
        if (result1.getCode() != Result.CODE_SUCCESS && result1.getCode() != DESTROY_NO_NEED) {
            listener.onResponse(false, result1.getCode(), result1.getPayload());
            return;
        }

        Log.d(TAG, API_IN + "changeNaviMode " + "init");
        Result result2 = commandApi.init(generateWorkModeProto(workMode), generateRoverConfigProtoFromConfigFile()
                , dataManager.getNaviMapName(dataManager.getMapName()));
        Log.d(TAG, API_BACK + "changeNaviMode " + "init result2 = " + result2.toString());

        if (result2.getCode() != Result.CODE_SUCCESS) {
            updateCurMode(workMode);
            listener.onResponse(false, result2.getCode(), result2.getPayload());
        } else {
            listener.onResponse(true, result2.getCode(), result2.getPayload());
        }

    }

    private boolean changeNaviMode(WorkMode workMode) {
        return changeNaviMode(workMode, dataManager.getNaviMapName(dataManager.getMapName()));
    }

    /**
     * 切模式
     *
     * @param workMode 底盘工作模式
     * @param name     初始化加载地图名称
     * @return
     */
    private boolean changeNaviMode(WorkMode workMode, String name) {
        Log.d(TAG, "Change Mode : workMode=" + workMode + " name=" + name);
        Result destroy = commandApi.destroy();
        Log.d(TAG, "Destroy result : " + destroy.toString());
        if (destroy.getCode() != Result.CODE_SUCCESS && destroy.getCode() != DESTROY_NO_NEED) {
            return false;
        }
        if (TextUtils.isEmpty(name)) {
            name = "test";
        }
        Result init = commandApi.init(generateWorkModeProto(workMode), generateRoverConfigProtoFromConfigFile(), name);
        Log.d(TAG, "Init result : " + init.toString());
        if (init.getCode() != Result.CODE_SUCCESS) {
            return false;
        } else {
            if (workMode != NAVIGATION) {
                updatePoseEstimate(false);
            }
            updateCurMode(workMode);
            return true;
        }
    }

    private WorkingModeProtoWrapper.WorkingModeProto generateWorkModeProto(WorkMode workMode) {
        if (workMode == WorkMode.NAVIGATION) {
            return WorkingModeProtoWrapper.WorkingModeProto.KNOWN_MAP_NAVIGATION;
        }
        if (workMode == WorkMode.CREATING_MAP) {
            return WorkingModeProtoWrapper.WorkingModeProto.MAP_CREATING;
        }

        if (workMode == WorkMode.SELF_CHECKING) {
            return WorkingModeProtoWrapper.WorkingModeProto.SELF_CHECKING;
        }

        if (workMode == WorkMode.FREE) {
            return WorkingModeProtoWrapper.WorkingModeProto.FREE_MODE;
        }

        return WorkingModeProtoWrapper.WorkingModeProto.FREE_MODE;
    }

    private synchronized void updateTargetPose(int status) {
        if (mTargetPose != null) {
            mTargetPose.onResult(status);
            DistanceUtils.resetCalculationCount();
        }
    }

    @Override
    public void go(TargetPose targetPose, ChassisResListener listener) {
        go(targetPose, null, listener);
    }

    private ChassisResListener operationListener;
    private static final float ROBOT_SETTING_DEFAULT_LINEAR_SPEED = 0.7F;
    private static final float ROBOT_SETTING_DEFAULT_ANGULAR_SPEED = 1.2F;

    @Override
    public void go(TargetPose targetPose, NavVelocity navVelocity, ChassisResListener listener) {
        Log.d(TAG, "go : targetPose=" + targetPose.toString()
                + " navVelocity=" + (navVelocity == null ? "null" : navVelocity.toString()));
        updateTargetPose(TargetPose.RESULT_REPLACE);
        mTargetPose = targetPose;
        operationListener = listener;
        WheelControlX86.getInstance().cancelDecTimer();
        WheelControlX86.getInstance().resetSpeed();
        updateMotionAvoidState(false);

        Pose pose = targetPose.getPose();

        double navLinear;
        double navAngular;
        if (null == navVelocity) {
            navLinear = ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
            navAngular = ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
        } else {
            navLinear = navVelocity.getLinear();
            navAngular = navVelocity.getAngular();
        }
        int moveType = targetPose.getMoveType();
        int rotateType = targetPose.getRotateType();
        boolean keepMove = targetPose.getIsKeepMove();
        Log.d(TAG, "go action pose:" + pose + " velocity:" + navVelocity
                + ", moveType:" + moveType + ", rotateType = " + rotateType
                + ", keepMove = " + keepMove);


        VelocityProtoWrapper.VelocityProto velocity = VelocityProtoWrapper.VelocityProto.newBuilder()
                .setAngular(navAngular)
                .setLiner(navLinear)
                .build();
        Pose2dProtoWrapper.Pose2dProto pose2dProto = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                .setX(pose.getX())
                .setY(pose.getY())
                .setT(pose.getTheta())
                .build();

        SetGoalProtoWrapper.MoveTypeProto moveTypeParam = SetGoalProtoWrapper.MoveTypeProto.forNumber(moveType);
        SetGoalProtoWrapper.RotationTypeProto rotateTypeParam = SetGoalProtoWrapper
                .RotationTypeProto.forNumber(rotateType);

        SetGoalProtoWrapper.GoalProto goal = SetGoalProtoWrapper.GoalProto.newBuilder()
                .setVelocity(velocity)
                .setMoveType(moveTypeParam)
                .setRotationType(rotateTypeParam)
                .setPose(pose2dProto)
                .setKeepMove(keepMove)
                .build();

        if (mTargetPose != null) {
            mTargetPose.onStatusUpdate(TargetPose.STATUS_STARTED);
        }

        Log.d(TAG, API_IN + "go naviToGoal");
        Result result1 = commandApi.naviToGoal(goal);
        Log.d(TAG, API_BACK + "go " + "naviToGoal result1 = " + result1.toString());

        if (result1.getCode() != Result.CODE_SUCCESS) {
            updateTargetPose(TargetPose.RESULT_FAILED);
            listener.onResponse(false, result1.getCode(), result1.getPayload());
        } else {
            if (operationListener != null) {
                operationListener.onResponse(true, result1.getCode(), result1.getPayload());
            }
        }
        biManager.reportNavigationCmd(result1.getCode());
    }


    @Override
    public void cancelNavigation(ChassisResListener listener) {
        if (!isNavigationing()) {
            Log.d(TAG, "not in navigation mode, no need to cancel");
            if (listener != null){
                listener.onResponse(true, SUCCESS, "not in navi mode");
            }
            return;
        }

        Result result1 = commandApi.cancelNavi();
        Log.d(TAG, "cancelNavigation result1 = " + result1.toString());
        if (result1.getCode() != Result.CODE_SUCCESS) {
            if (listener != null){
                listener.onResponse(false, result1.getCode(), result1.getPayload());
            }
        } else {
            updateTargetPose(TargetPose.RESULT_CANCELED);
            if (listener != null){
                listener.onResponse(true, result1.getCode(), result1.getPayload());
            }
        }
        biManager.reportNavigationCmd(result1.getCode());
    }

    private Object curModeLock = new Object();

    private void updateCurMode(WorkMode newMode) {
        synchronized (curModeLock) {
            Log.d(TAG, "updateCurMode oldMode = " + mCurrentMode + ", newMode = " + newMode);
            mCurrentMode = newMode;
        }
    }

    private boolean isInSpecialMode(WorkMode mode) {
        synchronized (curModeLock) {
            return mCurrentMode == mode;
        }
    }

    private ChassisResListener mResetEstimate;

    private synchronized void addResetEstimateListener(ChassisResListener listener) {
        mResetEstimate = listener;
    }

    private synchronized void updateResetEstimate(boolean result, int resultCode, String message) {
        if (mResetEstimate != null) {
            mResetEstimate.onResponse(result, resultCode, message);
            mResetEstimate = null;
        }
    }

    @Override
    public void setPoseEstimate(Pose pose, ChassisResListener listener) {
        Log.d(TAG, "Set pose estimate : " + pose + ", mCurrentMode = " + mCurrentMode);
        if (mCurrentMode == CREATING_MAP) {
            listener.onResponse(false, FAIL_NO_REASON, "curMode is createMap");
            return;
        }

        if (pose != null) {
            addResetEstimateListener(listener);

            Pose2dProtoWrapper.Pose2dProto pose2dProto = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                    .setX(pose.getX())
                    .setY(pose.getY())
                    .setT(pose.getTheta())
                    .build();
            RelocateDataProtoWrapper.RelocateDataProto relocateDataProto = RelocateDataProtoWrapper.RelocateDataProto.newBuilder()
                    .setMode(RelocateDataProtoWrapper.RelocateModeProto.MANUAL)
                    .setPose(pose2dProto)
                    .build();
            Log.d(TAG, API_IN + "setPoseEstimate" + " relocate");
            Result result3 = commandApi.relocate(relocateDataProto);
            Log.d(TAG, API_BACK + "setPoseEstimate" + " relocate" + ", result3 = " + result3.toString());

            biManager.reportNavigationCmd(result3.getCode());
        }
    }

    @Override
    public void setFixedEstimate(Pose pose, ChassisResListener listener) {
        Log.d(TAG, "Set fixed estimate : " + pose + ", mCurrentMode = " + mCurrentMode);
        if (mCurrentMode == CREATING_MAP) {
            listener.onResponse(false, FAIL_NO_REASON, "curMode is createMap");
            return;
        }

        if (pose != null) {
            addResetEstimateListener(listener);
            Log.d(TAG, API_IN + "setFixedEstimate" + " destroy");
            Result result1 = commandApi.destroy();
            Log.d(TAG, API_BACK + "setFixedEstimate" + " destroy" + ", result1 = " + result1.toString());
            if (result1.getCode() != Result.CODE_SUCCESS && result1.getCode() != DESTROY_NO_NEED) {
                listener.onResponse(false, result1.getCode(), result1.getPayload());
                return;
            }

            String mapName = dataManager.getMapName();
            Log.d(TAG, API_IN + "setFixedEstimate" + " init" + ", mapName = " + mapName);
            Result result2 = commandApi.init(WorkingModeProtoWrapper.WorkingModeProto.KNOWN_MAP_NAVIGATION,
                    generateRoverConfigProtoFromConfigFile(), dataManager.getNaviMapName(dataManager.getMapName()));
            Log.d(TAG, API_BACK + "setFixedEstimate" + " init result2 = " + result2.toString());

            if (result2.getCode() != Result.CODE_SUCCESS) {
                listener.onResponse(false, result2.getCode(), result2.getPayload());
                return;
            } else {
                updateCurMode(WorkMode.NAVIGATION);
            }

            Pose2dProtoWrapper.Pose2dProto pose2dProto = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                    .setX(pose.getX())
                    .setY(pose.getY())
                    .setT(pose.getTheta())
                    .build();
            RelocateDataProtoWrapper.RelocateDataProto relocateDataProto = RelocateDataProtoWrapper.RelocateDataProto.newBuilder()
                    .setMode(RelocateDataProtoWrapper.RelocateModeProto.FIXED)
                    .setPose(pose2dProto)
                    .build();
            Log.d(TAG, API_IN + "setFixedEstimate" + " relocate");
            Result result3 = commandApi.relocate(relocateDataProto);
            Log.d(TAG, API_BACK + "setFixedEstimate" + " relocate" + ", result3 = " + result3.toString());

            biManager.reportNavigationCmd(result3.getCode());
        }
    }

    @Override
    public void setForceEstimate(Pose pose, ChassisResListener listener) {

    }

    @Override
    public void setVisionEstimate(ChassisResListener listener) {
        Log.d(TAG, "setVisionEstimate");
        if (isPoseEstimate) {
            if (listener != null) {
                listener.onResponse(true, SUCCESS, "already relocated");
            }
            return;
        }

        String hasVision = getHasVision();
        Log.d(TAG, "hasVision = " + hasVision);
        if (TextUtils.equals(hasVision, "true")) {
            addResetEstimateListener(listener);
            Pose pose = mCurPose.get();
            Pose2dProtoWrapper.Pose2dProto pose2dProto;
            if (pose != null) {
                pose2dProto = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                        .setX(pose.getX())
                        .setY(pose.getY())
                        .setT(pose.getTheta())
                        .build();

            } else {
                pose2dProto = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                        .setX(Float.NaN)
                        .setY(Float.NaN)
                        .setT(Float.NaN)
                        .build();
            }

            RelocateDataProtoWrapper.RelocateDataProto relocateDataProto = RelocateDataProtoWrapper.RelocateDataProto.newBuilder()
                    .setMode(RelocateDataProtoWrapper.RelocateModeProto.FIXED)
                    .setPose(pose2dProto)
                    .build();
            Log.d(TAG, API_IN + "setVisionEstimate" + " relocate");
            Result result3 = commandApi.relocate(relocateDataProto);
            Log.d(TAG, API_BACK + "setVisionEstimate" + " relocate" + ", result3 = " + result3.toString());
        } else {
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "not support vision locate");
            }
        }
    }

    private volatile boolean isRelocate = false;

    @Override
    public void resetPoseEstimate(ChassisResListener listener) {
        Log.d(TAG, "resetPoseEstimate");
        if (isPoseEstimate) {
            if (listener != null) {
                listener.onResponse(true, SUCCESS, "Already pose estimate");
            }
            return;
        }
        addResetEstimateListener(listener);
        isRelocate = true;
    }

    private void onVelocityUpdate(Velocity velocity) {
        LogUtils.printLog(LogUtils.TYPE_VEL, TAG, "onVelocityUpdate x=" + velocity.getX()
                + ",z=" + velocity.getZ(), 4000);
        //        Log.d(TAG, "onVelocityUpdate x=" + velocity.getX() + ",z=" + velocity.getZ());
        mRealtimeVelocity.set(velocity);
        Velocity preVelocity = mVelocity.get();
        if (WheelControlX86.getInstance().isVelocityDiff(preVelocity, velocity)) {
            mVelocity.set(velocity);
            if (mChassisEventListener != null) {
                mChassisEventListener.onVelocityUpdate(velocity);
            }
        }

        if (isRelocate && WheelControlX86.getInstance().isStill(velocity)) {
            Log.d(TAG, "start relocate");
            isRelocate = false;

            Pose pose = mCurPose.get();
            if (pose != null) {
                Pose2dProtoWrapper.Pose2dProto pose2dProto = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                        .setX(pose.getX())
                        .setY(pose.getY())
                        .setT(pose.getTheta())
                        .build();
                RelocateDataProtoWrapper.RelocateDataProto relocateDataProto = RelocateDataProtoWrapper.RelocateDataProto.newBuilder()
                        .setMode(RelocateDataProtoWrapper.RelocateModeProto.MANUAL)
                        .setPose(pose2dProto)
                        .build();
                Log.d(TAG, API_IN + "laser" + " relocate");
                Result result1 = commandApi.relocate(relocateDataProto);
                Log.d(TAG, API_BACK + "laser" + " relocate" + ", result1 = " + result1.toString());
            }
        }
    }

    private MotionTimer mMotionTimer;

    private void startMotionTimer(long time, ChassisResListener listener) {
        if (mMotionTimer != null) {
            mMotionTimer.cancel();
        }

        mMotionTimer = new MotionTimer(listener);
        mMotionTimer.schedule(time);
    }

    private class MotionTimer extends Timer {
        ChassisResListener mListener;

        public MotionTimer(ChassisResListener listener) {
            super();
            this.mListener = listener;
        }

        public void schedule(long delay) {
            if (delay <= 0) {
                Log.e(TAG, "motionLinear-avoid, delay less than 0");
                if (mListener != null) {
                    mListener.onResponse(false, FAIL_NO_REASON, null);
                }
                updateMotionAvoidState(false);//MotionTimer
                return;
            }
            this.schedule(new TimerTask() {
                @Override
                public void run() {
                    Log.e(TAG, "motionLinear-avoid, Motion time out, Stop move");
                    motion(0, 0, true);
                    if (mListener != null) {
                        mListener.onResponse(true, SUCCESS, null);
                        mListener = null;
                    }
                    updateMotionAvoidState(false);//MotionTimer
                }
            }, delay);
        }

        @Override
        public void cancel() {
            Log.e(TAG, "Motion canceled");
            super.cancel();
        }

        public ChassisResListener getListener() {
            return mListener;
        }
    }

    @Override
    public void stopMove() {
        Log.d(TAG, "stopMove");
        MotionCallChainReporter.stopMoveReport(TAG + "_stopMove");
        updateMotionAvoidState(false);//stopMove
        BasicMotionProcess.getInstance().stopMotionTask(false, FAIL_NO_REASON, "Cancel");
        motion(0, 0, true);
    }

    @Override
    public void motion(double angularSpeed, double linearSpeed) {
        motion(angularSpeed, linearSpeed, true);
    }

    @Override
    public void sendPrimitiveMovingSpeed(double angularSpeed, double linearSpeed){
        this.sendPrimitiveMovingCommand(angularSpeed, linearSpeed);
    }

    public Result sendPrimitiveMovingCommand(double angularSpeed, double linearSpeed) {
        VelocityProtoWrapper.VelocityProto velocity = VelocityProtoWrapper.VelocityProto.newBuilder()
                .setLiner(linearSpeed)
                .setAngular(angularSpeed)
                .build();
        Log.d(TAG, API_IN + ", controlWheel angularSpeed=" + angularSpeed
                + ", linearSpeed=" + linearSpeed);
        MotionCallChainReporter.controlWheelCallReport(TAG + "_controlWheel", angularSpeed, linearSpeed);
        Result result = commandApi.controlWheel(velocity);
        MotionCallChainReporter.controlWheelResultReport(TAG + "_controlWheel", result.toString());
        Log.d(TAG, API_BACK + ", controlWheel result = " + result.toString());
        return result;
    }

    @Override
    public void motion(double angularSpeed, double linearSpeed, boolean hasAcceleration) {
        if (isNavigationing()) {
            Log.e(TAG, "motion: Is navigationing");
            return;
        }

        updateMotionAvoidState(false);
        final double cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
        final double cAngularSpeed = WheelControlX86.getInstance().correctAngularSpeed(angularSpeed);
        WheelControlX86.getInstance().startDecTimer(cAngularSpeed, cLinearSpeed, hasAcceleration);
    }

    @Override
    public void motionWithObstacles(double angularSpeed, double linearSpeed) {
        if (isNavigationing()) {
            Log.e(TAG, "motionWithObstacles, Is navigationing");
            return;
        }

        final double cLinearSpeed;
        final double cAngularSpeed = WheelControlX86.getInstance().correctAngularSpeed(angularSpeed);
        if (AvoidStoppingPolicy.getInstance().getAvoidState() && linearSpeed > 0) {
            Log.d(TAG, "Avoid to set linear speed 0");
            onObstacleReport();
            cLinearSpeed = 0;
            updateMotionAvoidState(false);
        } else {
            cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
            updateMotionAvoidState(true);
        }
        WheelControlX86.getInstance().startDecTimer(cAngularSpeed, cLinearSpeed, true);
    }

    @Override
    public void motionWithObstacles(double angularSpeed, double linearSpeed, double minDistance) {
        MotionCallChainReporter.motionWithObstaclesReport(TAG + "_motionWithObstacles",
                angularSpeed, linearSpeed, minDistance);
        if (isNavigationing()) {
            Log.e(TAG, "motionWithObstacles, Is navigationing");
            return;
        }
        Log.d(TAG, "motionWithObstacles, speeds : " + linearSpeed + " " + angularSpeed);
        final double cLinearSpeed;
        final double cAngularSpeed = WheelControlX86.getInstance().correctAngularSpeed(angularSpeed);
        if (AvoidStoppingPolicy.getInstance().getAvoidState(minDistance) && linearSpeed > 0) {
            onObstacleReport();
            cLinearSpeed = 0;
            updateMotionAvoidState(false);
        } else {
            cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
            updateMotionAvoidState(true);
        }
        Log.d(TAG, "motionWithObstacles, speeds final : " + cLinearSpeed + " " + cAngularSpeed);
        WheelControlX86.getInstance().startDecTimer(cAngularSpeed, cLinearSpeed, true);
    }

    @Override
    public void motionWithOnceObstacle(double angularSpeed, double linearSpeed, boolean hasAcceleration) {
        if (isNavigationing()) {
            Log.e(TAG, "motionWithObstacles, Is navigationing");
            return;
        }

        updateMotionAvoidState(false);
        final double cLinearSpeed;
        final double cAngularSpeed = WheelControlX86.getInstance().correctAngularSpeed(angularSpeed);

        boolean avoidState = AvoidStoppingPolicy.getInstance().getAvoidState(1.0);
        Log.i(TAG, "avoid state: " + avoidState + " , last state: " + hasObstacle);

        if (avoidState && linearSpeed > 0) {
            onObstacleReport();
            if (!hasObstacle) {
                //appear obstacle
                Log.i(TAG, "appear obstacle");
                startAvoidTimer();
                cLinearSpeed = 0;
            } else if (avoidTag) {
                Log.i(TAG, "appear obstacle, continuing。。。。");
                cLinearSpeed = 0;
            } else {
                cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
            }
        } else {
            cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
        }
        hasObstacle = avoidState;
        WheelControlX86.getInstance().startDecTimer(cAngularSpeed, cLinearSpeed, hasAcceleration);
    }

    @Override
    public void motionControlWithObstacle(double angularSpeed, double linearSpeed, double minDistance) {
        MotionCallChainReporter.motionWithObstaclesReport(TAG + "_motionWithObstacles",
                angularSpeed, linearSpeed, minDistance);
        if (isNavigationing()) {
            Log.e(TAG, "motionWithObstacles, Is navigationing");
            return;
        }
        Log.d(TAG, "motionWithObstacles, speeds : " + linearSpeed + " " + angularSpeed);
        final double cLinearSpeed;
        final double cAngularSpeed = WheelControlX86.getInstance().correctAngularSpeed(angularSpeed);
        if (AvoidStoppingPolicy.getInstance().getAvoidState(minDistance) && linearSpeed > 0) {
            onObstacleReport();
            cLinearSpeed = 0;
            updateMotionAvoidState(false);
        } else {
            cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
            updateMotionAvoidState(true);
        }
        Log.d(TAG, "motionWithObstacles, speeds final : " + cLinearSpeed + " " + cAngularSpeed);
        //        startDecTimer(cAngularSpeed, cLinearSpeed, hasAcceleration);
        Log.e(TAG, "motion: x86 ------- ");
        BigDecimal linearSpeeds = new BigDecimal(Float.toString((float) cLinearSpeed));
        BigDecimal angularSpeeds = new BigDecimal(Float.toString((float) cAngularSpeed));
        MotionControl.getInstance().motion(new SpeedBean(linearSpeeds, angularSpeeds));
    }

    @Override
    public void motionSoft(double angularSpeed, double linearSpeed, boolean hasAcceleration) {
        if (isNavigationing()) {
            Log.e(TAG, "motionSoft, Is navigationing");
            return;
        }
        final double cLinearSpeed = WheelControlX86.getInstance().correctLinearSpeed(linearSpeed);
        final double cAngularSpeed = WheelControlX86.getInstance().correctAngularSpeed(angularSpeed);

        updateMotionAvoidState(false);
        WheelControlX86.getInstance().startDecTimer(cAngularSpeed, cLinearSpeed, hasAcceleration);
    }

    @Override
    public void turnLeft(double angle, double speed, boolean noNeedAcceleration, ChassisResListener listener) {
        motionRotate(angle, speed, noNeedAcceleration, listener);
    }

    @Override
    public void turnRight(double angle, double speed, boolean noNeedAcceleration, ChassisResListener listener) {
        motionRotate(angle, -speed, noNeedAcceleration, listener);
    }

    @Override
    public void forward(double distance, double speed, ChassisResListener listener) {
        motionLinear(distance, speed, false, listener);
    }

    @Override
    public void forward(double distance, double speed, boolean avoid, ChassisResListener listener) {
        motionLinear(distance, speed, avoid, listener);
    }

    @Override
    public void backward(double distance, double speed, ChassisResListener listener) {
        motionLinear(distance, -speed, false, listener);
    }

    private synchronized void startAvoidTimer() {
        avoidTag = true;
        DelayTask.cancel(this);
        DelayTask.submit(this, new Runnable() {
            @Override
            public void run() {
                Log.i(TAG, "avoid timer come, remote can continue control");
                avoidTag = false;
            }
        }, 3000);
    }

    private void motionRotate(double angle, double speed, boolean noNeedAcceleration, ChassisResListener listener) {
        Log.e(TAG, "motionRotate : angle=" + angle + "  speed=" + speed
                + " noNeedAcceleration=" + noNeedAcceleration);
        updateMotionAvoidState(false);//motionRotate
        if (noNeedAcceleration) {
            motion(speed, 0, false);
        } else {
            motion(speed, 0, true);
        }

        if (angle != Double.MAX_VALUE) {
//            long delay = noNeedAcceleration ? 0 :
//                    WheelControlX86.getInstance().calculateDecNumber(speed, 0);
//            long time = (long) ((angle / Math.abs(speed)) * 1000) + delay / 2;
//            if (time <= 600) {
//                Log.e(TAG, "adjust timeTurn angle : " + angle + "  speed : " + speed + "  timeout : " + time);
//                time += 600;
//            }
//            Log.e(TAG, "Turn angle : " + angle + "  speed : " + speed + "  timeout : " + time);
//            startMotionTimer(time, listener);
            BasicMotionProcess.getInstance().startAngularTask(angle, speed, listener);
        } else {
            if (listener != null) {
                listener.onResponse(true, SUCCESS, null);
            }
        }
    }

    public void motionLinear(double distance, double speed, boolean avoid, ChassisResListener listener) {
        Log.d(TAG, "motionLinear ：distance=" + distance
                + " speed=" + speed + " avoid=" + avoid);
        if (avoid && AvoidStoppingPolicy.getInstance().getAvoidState()) {
            if (listener != null) {
                listener.onResponse(false, MOTION_AVOID_STOP, "Avoid stop");
            }
            motionLinearAvoidStop();
            return;
        }

        motion(0, speed);
        updateMotionAvoidState(avoid); //motionLinear

        if (distance != Double.MAX_VALUE) {
//            long delay = WheelControlX86.getInstance().calculateDecNumber(0, speed);
//            long time = (long) ((distance / Math.abs(speed)) * 1000) + delay / 2;
//            Log.d(TAG, "motionLinear-avoid delay : " + delay + ", time : " + time + ", speed : " + speed);
//            startMotionTimer(time, listener);
            BasicMotionProcess.getInstance().startLinearTask(distance, speed, listener);
        } else {
            Log.d(TAG, "motionLinear-avoid : MAX_VALUE distance");
            if (listener != null) {
                listener.onResponse(true, SUCCESS, null);
            }
        }
    }

    public void setWorkMode(WorkMode mode, ChassisResListener listener) {
        Log.d(TAG, "setWorkMode mode=" + mode + " mCurrentMode=" + mCurrentMode);
        if (mCurrentMode == mode) {
            if (listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "already in " + mode + " mode!");
            }
            return;
        }

        Log.d(TAG, API_IN + "setWorkMode destroy");
        Result result1 = commandApi.destroy();
        Log.d(TAG, API_BACK + "setWorkMode destroy, result = " + result1.toString());

        Log.d(TAG, API_IN + "setWorkMode init");
        Result result2 = commandApi.init(generateWorkModeProto(mode),
                generateRoverConfigProtoFromConfigFile(), dataManager.getNaviMapName(dataManager.getMapName()));
        Log.d(TAG, API_BACK + "setWorkMode init, result1 = " + result2.toString());
        if (result2.getCode() != Result.CODE_SUCCESS) {
            updateCurMode(mode);
            listener.onResponse(false, result2.getCode(), result2.getPayload());
        } else {
            listener.onResponse(true, result2.getCode(), result2.getPayload());
        }
        biManager.reportNavigationCmd(result2.getCode());
    }

    /**
     * motion mode sleep 对应雷达休眠
     * motion mode autoMoving 对应雷达打开
     * motion mode Auto charge 对应release wheel true
     * motion mode Manual control对应release wheel false
     * 当调用controlWheel成功后，自动设置为ManualControl
     * 当调用Navigationn成功后，自动设置为AutoMoving
     *
     * @param mode
     */
    public void switchMotionMode(MotionMode mode) {
        //TODO no need to implement this, no use for X86
        //1. AUTO_CHARGE 怎么办
        //2. 雷达休眠，雷达唤醒怎么办？
        //

        if (mode == MotionMode.MOTION_MODE_SLEEP && mCurrentMode == CREATING_MAP) {
            Log.d(TAG, "can not sleep radar due to CREATING MAP");
            return;
        }
        Log.d(TAG, "switchMotionMode = " + mode);
        Result result;
        switch (mode) {
            case MOTION_MODE_SLEEP:
                Log.d(TAG, API_IN + "switchMotionMode stopLidar");
                result = commandApi.stopLidar(true);
                Log.d(TAG, API_IN + "switchMotionMode stopLidar, result = " + result.toString());
                if (result.getCode() == Result.CODE_SUCCESS) {
                    updateCurrentMotionMode(MotionMode.MOTION_MODE_SLEEP);
                }
                break;
            case AUTO_MOVING:
                Log.d(TAG, API_IN + "switchMotionMode stopLidar");
                result = commandApi.stopLidar(false);
                Log.d(TAG, API_IN + "switchMotionMode stopLidar, result = " + result.toString());
                if (result.getCode() == Result.CODE_SUCCESS) {
                    updateCurrentMotionMode(MotionMode.AUTO_MOVING);
                }
                break;
            case AUTO_CHARGE:
                Log.d(TAG, API_IN + "switchMotionMode releaseWheel");
                result = commandApi.releaseWheel(true);
                Log.d(TAG, API_IN + "switchMotionMode releaseWheel, result = " + result.toString());
                if (result.getCode() == Result.CODE_SUCCESS) {
                    updateCurrentMotionMode(MotionMode.AUTO_CHARGE);
                }
                break;
            case MANUAL_CONTROL:
                Log.d(TAG, API_IN + "switchMotionMode releaseWheel");
                result = commandApi.releaseWheel(false);
                Log.d(TAG, API_IN + "switchMotionMode releaseWheel, result = " + result.toString());
                if (result.getCode() == Result.CODE_SUCCESS) {
                    updateCurrentMotionMode(MotionMode.MANUAL_CONTROL);
                }
                break;
            default:
                break;

        }
    }

    public void switchMotionMode(MotionMode mode, ChassisResListener listener) {
        if (mode == MotionMode.MOTION_MODE_SLEEP && mCurrentMode == CREATING_MAP) {
            Log.d(TAG, "can not sleep radar due to CREATING MAP");
            listener.onResponse(false, FAIL_NO_REASON, "creating map");
            return;
        }
        Log.d(TAG, "switchMotionMode = " + mode);
        Result result;
        switch (mode) {
            case MOTION_MODE_SLEEP:
                Log.d(TAG, API_IN + "switchMotionMode stopLidar");
                result = commandApi.stopLidar(true);
                Log.d(TAG, API_IN + "switchMotionMode stopLidar, result = " + result.toString());
                if (result.getCode() == Result.CODE_SUCCESS) {
                    updateCurrentMotionMode(MotionMode.MOTION_MODE_SLEEP);
                    listener.onResponse(true, result.getCode(), result.getPayload());
                } else {
                    listener.onResponse(false, result.getCode(), result.getPayload());
                }
                break;
            case AUTO_MOVING:
                Log.d(TAG, API_IN + "switchMotionMode stopLidar");
                result = commandApi.stopLidar(false);
                Log.d(TAG, API_IN + "switchMotionMode stopLidar, result = " + result.toString());
                if (result.getCode() == Result.CODE_SUCCESS) {
                    listener.onResponse(true, result.getCode(), result.getPayload());
                    updateCurrentMotionMode(MotionMode.AUTO_MOVING);
                } else {
                    listener.onResponse(false, result.getCode(), result.getPayload());
                }
                break;
            case AUTO_CHARGE:
                Log.d(TAG, API_IN + "switchMotionMode releaseWheel");
                result = commandApi.releaseWheel(true);
                Log.d(TAG, API_IN + "switchMotionMode releaseWheel, result = " + result.toString());
                if (result.getCode() == Result.CODE_SUCCESS) {
                    updateCurrentMotionMode(MotionMode.AUTO_CHARGE);
                    listener.onResponse(true, result.getCode(), result.getPayload());
                } else {
                    listener.onResponse(false, result.getCode(), result.getPayload());
                }
                break;
            case MANUAL_CONTROL:
                Log.d(TAG, API_IN + "switchMotionMode releaseWheel");
                result = commandApi.releaseWheel(false);
                Log.d(TAG, API_IN + "switchMotionMode releaseWheel, result = " + result.toString());
                if (result.getCode() == Result.CODE_SUCCESS) {
                    updateCurrentMotionMode(MotionMode.MANUAL_CONTROL);
                    listener.onResponse(true, result.getCode(), result.getPayload());
                } else {
                    listener.onResponse(false, result.getCode(), result.getPayload());
                }
                break;
            default:
                break;

        }
    }

    public void updateCurrentMotionMode(MotionMode mode) {
        Log.d(TAG, "updateCurrentMotionMode = " + mode.getValue());
        if (mode == null) {
            return;
        }
        if (mCurrentMotionMode != mode) {
            mCurrentMotionMode = mode;
        }
        if (mChassisEventListener != null) {
            mChassisEventListener.OnRadarUpdate(mode.getValue() != MotionMode.MOTION_MODE_SLEEP.getValue());
        }
    }

    public void getMotionMode(ChassisResListener listener) {
        // 用本地缓存的motion mode 直接返回给上层。
        listener.onResponse(true, SUCCESS, mCurrentMotionMode.getValue());
    }

    @Override
    public void setCurrentWorkModeFree(final ChassisResListener listener) {
        Log.d(TAG, API_IN + "changeNaviMode" + " destroy");
        WorkMode workMode = FREE;
        Result result1 = commandApi.destroy();
        Log.d(TAG, API_BACK + "changeNaviMode" + " destroy result1 = " + result1.toString());
        if (result1.getCode() != Result.CODE_SUCCESS && result1.getCode() != DESTROY_NO_NEED) {
            listener.onResponse(false, result1.getCode(), result1.getPayload());
            return;
        }

        Log.d(TAG, API_IN + "changeNaviMode " + "init");
        Result result2 = commandApi.init(generateWorkModeProto(workMode), generateRoverConfigProtoFromConfigFile()
                , dataManager.getNaviMapName(dataManager.getMapName()));
        Log.d(TAG, API_BACK + "changeNaviMode " + "init result2 = " + result2.toString());

        if (result2.getCode() != Result.CODE_SUCCESS) {
            updateCurMode(workMode);
            listener.onResponse(false, result2.getCode(), result2.getPayload());
        } else {
            listener.onResponse(true, result2.getCode(), result2.getPayload());
        }
    }

    @Override
    public void reloadMap(final ChassisResListener listener) {
        this.setWorkMode(WorkMode.NAVIGATION, new ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (listener != null) {
                    if (status == true) {
                        listener.onResponse(true, SUCCESS, result);
                    }else {
                        listener.onResponse(false, FAIL_NO_REASON, result);
                    }
                }
            }
        });
    }

    @Override
    public void startExtendMap(ChassisResListener listener) {

    }

    @Override
    public void setRadarState(boolean state, final ChassisResListener listener) {
        final MotionMode motionMode = state ? MotionMode.AUTO_MOVING :
                MotionMode.MOTION_MODE_SLEEP;
        this.switchMotionMode(motionMode, new ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (listener != null){
                    listener.onResponse(status, resultCode, result);
                }
                if (resultCode == SUCCESS) {
                    updateCurrentMotionMode(motionMode);
                }
            }
        });
    }

    @Override
    public void getRadarState(final ChassisResListener listener) {
        this.getMotionMode(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, " getRadarState radarStatus staus=" + status + " resultCode=" + resultCode +" result="+result);
                if (listener != null){
                    if (resultCode == SUCCESS) {
                        int motionMode = (int) result;
                        boolean isSuc = motionMode != MotionMode.MOTION_MODE_SLEEP.getValue();
                        listener.onResponse(status, resultCode, isSuc);
                    } else {
                        listener.onResponse(status, FAIL_NO_REASON, "");
                    }
                }

            }
        });
    }

    @Override
    public void switchChargeMode() {
        this.switchMotionMode(MotionMode.AUTO_CHARGE);
    }

    @Override
    public void switchManualMode() {
        this.switchMotionMode(MotionMode.MANUAL_CONTROL);
    }

    @Override
    public void getFullCheckStatus(ChassisResListener listener) {
        //todo
        Log.d(TAG, "getFullCheckStatus");
        boolean changeMode = changeNaviMode(SELF_CHECKING);
        if (!changeMode) {
            listener.onResponse(false, FAIL_NO_REASON, "change SELF_CHECK error");
        }
        Log.d(TAG, API_IN + " self check");
        Result result = commandApi.selfCheck();
        Log.d(TAG, API_BACK + " self check, result = " + result.toString());

        if (result.getCode() != Result.CODE_SUCCESS) {
            listener.onResponse(false, result.getCode(), result.getPayload());
        } else {
            listener.onResponse(true, result.getCode(),
                    generateSensorStatus((SelfCheckResultProtoWrapper.SelfCheckResultProto) result.getPayload()));
        }
    }

    private SensorStatus generateSensorStatus(SelfCheckResultProtoWrapper.SelfCheckResultProto resultProto) {
        SensorStatus status = new SensorStatus();
        status.setFishEyeReady(resultProto.getIsFishEyeReady());
        status.setIrReady(resultProto.getIsIrReady());
        status.setLaserReady(resultProto.getIsLaserReady());
        status.setOdomReady(resultProto.getIsOdomReady());
        status.setRgbdReady(resultProto.getIsRgbdReady());
        status.setLaserAvailable(resultProto.getIsLaserDataValid());
        status.setIscalibrationready(resultProto.getIsCalibrationReady());
        status.setIsharddiskspaceok(resultProto.getIsHardDiskSpaceOk());
        status.setCanControlReady(resultProto.getIsCanControlReady());
        status.setMonoImage(parseImagePath(resultProto.getMonoImage()));
        status.setDepthImage(parseImagePath(resultProto.getDepthImage()));
        status.setIrImage(parseImagePath(resultProto.getIrImage()));
        return status;
    }

    private String parseImagePath(ChassisFileProtoWrapper.ChassisFileProto proto) {
        Log.d(TAG, "parseImagePath: " + proto.toString());
        return proto.getPath();
    }

    @Override
    public void getSensorStatus(ChassisResListener listener) {
        //todo

        Log.d(TAG, API_IN + "self check");
        Result result = commandApi.selfCheck();
        Log.d(TAG, API_BACK + "self check, result = " + result.toString());

        if (result.getCode() != Result.CODE_SUCCESS) {
            listener.onResponse(false, result.getCode(), result.getPayload());
        } else {
            listener.onResponse(true, result.getCode(),
                    generateSensorStatus((SelfCheckResultProtoWrapper.SelfCheckResultProto) result.getPayload()));
        }

    }

    @Override
    public boolean getLogFile(String path, String fileType, ChassisResListener listener) {
        //todo
        return false;
    }

    @Override
    public void removeTkMap(String mapName, ChassisResListener listener) {
        Log.i(TAG, "Remove map : " + mapName);
        Result result = commandApi.deleteMap(mapName);
        Log.i(TAG, "removeTkMap: result:" + result);
        if (result.getCode() == Result.CODE_SUCCESS) {
            listener.onResponse(true, result.getCode(), result.getPayload());
        } else {
            listener.onResponse(false, result.getCode(), result.getPayload());
        }

    }

    @Override
    public boolean packLogFile(long startTime, long endTime, ChassisResListener listener) {
        //todo
        return false;
    }

    @Override
    public boolean startPlanRoute() {
        //todo
        return false;
    }

    @Override
    public void savePlanRoute(String routeName, List<Pose> poseList) {
        //todo
    }

    @Override
    public void getSystemInformation(ChassisResListener listener) {
        Log.d(TAG, API_IN + " getSystemInfo");
        Result result = commandApi.getSystemInformation();
        Log.d(TAG, API_BACK + " getSystemInfo, result = " + result.toString());
        if (result.getCode() != Result.CODE_SUCCESS) {
            listener.onResponse(false, result.getCode(), result.getPayload());
        } else {
            if (result.getPayload() instanceof GetSystemInformationProtoWrapper.SystemInformationProto) {
                listener.onResponse(true, result.getCode(),
                        new SystemData((GetSystemInformationProtoWrapper.SystemInformationProto) result.getPayload()));
            } else {
                listener.onResponse(false, result.getCode(), "data error");
            }
        }
    }

    @Override
    public boolean hasObstacle(double startAngle, double endAngle, double distance) {
        synchronized (laserUpdateLock) {
            double arcStartAngle = startAngle * Math.PI / 180;
            double arcEndAngle = endAngle * Math.PI / 180;
            Log.d(TAG, "arcStartAngle = " + arcStartAngle + ", arcEndAngle = " + arcEndAngle);
            if (mLaserDatas == null) {
                return true;
            }
            for (Laser info : mLaserDatas) {
                Log.d(TAG, "laser data = " + info.toString());
                if (isInAngleRange(arcStartAngle, arcEndAngle, info.getAngle()) && info.getDistance() < distance) {
                    return true;
                }
            }

            return false;
        }
    }

    @Override
    public String getHasVision() {
        return dataManager.getHasVision(dataManager.getMapName());
    }

    @Override
    public String getMapStatus(String cmdType, String cmdParam) {
        return mCurrentMode.name();
    }

    @Override
    public void addMappingPose(final Pose pose, ChassisResListener listener) {

        if (mCurrentMode != CREATING_MAP) {
            listener.onResponse(false, -1, "not in creatimg map mode");
            return;
        }

        Pose2dProtoWrapper.Pose2dProto pose2dProto = Pose2dProtoWrapper.Pose2dProto.newBuilder()
                .setX(pose.getX())
                .setY(pose.getY())
                .setT(pose.getTheta())
                .build();
        Result<CommonProtoWrapper.TargetResultProto> result = commandApi.addTarget(pose2dProto);

        if (result.getCode() != Result.CODE_SUCCESS) {
            listener.onResponse(false, result.getCode(), result.getPayload());
        } else {
            if (result.getPayload() instanceof CommonProtoWrapper.TargetResultProto) {
                PlaceDataManager.MappingPose mappingPose = new PlaceDataManager.MappingPose();
                mappingPose.setId(result.getPayload().getId());
                mappingPose.setValid(result.getPayload().getValid());
                mappingPose.setPose(pose);
                listener.onResponse(true, result.getCode(), mappingPose);
            } else {
                listener.onResponse(false, result.getCode(), "data error");
            }
        }
    }

    @Override
    public void deleteMappingPose(int poseId, ChassisResListener listener) {
        if (mCurrentMode != CREATING_MAP) {
            listener.onResponse(false, -1, "not in creatimg map mode");
            return;
        }

        Result result = commandApi.removeTarget(poseId);
        if (result.getCode() != Result.CODE_SUCCESS) {
            listener.onResponse(false, result.getCode(), result.getPayload());
        } else {
            listener.onResponse(true, result.getCode(), result.getPayload());
        }
    }

    @Override
    public void recoveryNavigation(ChassisResListener listener) {
        //TODO
        if (listener != null) {
            listener.onResponse(true, 0, "");
        }
    }

    private boolean isInAngleRange(double startAngle, double endAngle, double aimAngle) {
        return (aimAngle >= startAngle && aimAngle <= endAngle);
    }

    private UpdateListener mUpdateListener = new UpdateListener() {

        @Override
        public void onUpdateLaser(UpdateLaserProtoWrapper.LaserDataProto laserDataProto) {
            List<UpdateLaserProtoWrapper.LaserProto> laserList = laserDataProto.getLasersList();
            ArrayList<Laser> lasers = new ArrayList<>();

            for (UpdateLaserProtoWrapper.LaserProto laser : laserList) {
                if (laser.getDistance() == 0) {
                    lasers.add(new Laser(laser.getAngle(), 100));
                } else {
                    lasers.add(new Laser(laser.getAngle(), laser.getDistance()));
                }
            }

            AvoidStoppingPolicy.getInstance().handleObstaclesInfo(lasers);
            onLaserDataIn(lasers);
            synchronized (laserUpdateLock) {
                mLaserDatas = lasers;
            }
        }

        @Override
        public void onUpdateVelocity(VelocityProtoWrapper.VelocityProto velocityProto) {
            onVelocityUpdate(new Velocity(velocityProto.getLiner(), velocityProto.getAngular()));
        }

        @Override
        public void onUpdatePose(ChassisPacketProtoWrapper.PoseDataProto poseDataProto) {
            onPoseUpdate(poseDataProto.getPose().getX(), poseDataProto.getPose().getY(), poseDataProto.getPose().getT(), poseDataProto.getTypeValue());
        }

        @Override
        public void onUpdateGlobalMap(CostmapProtoWrapper.CostmapProto costmapProto) {
            Log.d(TAG, "onUpdateGlobalMap in");
            onMapDataUpdate(new MapData(MapData.GLOBAL,
                    costmapProto.getWidth(),
                    costmapProto.getHeight(),
                    costmapProto.getResolution(),
                    costmapProto.getOriginX(),
                    costmapProto.getOriginY(),
                    costmapProto.getData().toByteArray()));
        }

        @Override
        public void onUpdateEvent(UpdateEventProtoWrapper.RoverEventProto roverEvent) {
            Log.d(TAG, "onUpdateEvent in");
            onNavigationEvent(new Event(roverEvent.getCodeValue(), roverEvent.getMessage(), roverEvent.getAdditional()));
        }

        @Override
        public void onReportStatistic(ReportStatisticProtoWrapper.StatisticProto statistic) {
            Log.d(TAG, "onReportStatistic in");
            onStatisticUpdate(new Statistic(statistic.getType(),
                    statistic.getInt1(),
                    statistic.getInt2(),
                    statistic.getInt3(),
                    statistic.getInt4(),
                    statistic.getDouble1(),
                    statistic.getDouble2(),
                    statistic.getDouble3(),
                    statistic.getDouble4(),
                    statistic.getStringValue()));
        }

        @Override
        public void onUpdateTargets(CommonProtoWrapper.TargetPointsProto targetPointsProto) {

            List<CommonProtoWrapper.TargetPointProto> list = targetPointsProto.getPointsList();

            if (list == null || list.size() == 0) {
                return;
            }

            List<PlaceDataManager.MappingPose> mappingPoseList = new ArrayList<>();
            for (CommonProtoWrapper.TargetPointProto point : list) {
                PlaceDataManager.MappingPose mappingPose = new PlaceDataManager.MappingPose();
                mappingPose.setValid(point.getValid());
                mappingPose.setId(point.getId());
                Pose pose = new Pose((float) point.getPose().getX(), (float) point.getPose().getY(),
                        (float) point.getPose().getT());
                mappingPose.setPose(pose);
                mappingPoseList.add(mappingPose);
            }

            if (mChassisEventListener != null) {
                mChassisEventListener.onMappingPoseUpdate(mappingPoseList);
            }
        }

        @Override
        public void onUpdateOdom(CommonProtoWrapper.OdomDataProto odomDataProto) {
            BasicMotionProcess.getInstance().handleOdomData(odomDataProto.getMove(),
                    odomDataProto.getLeftAcc(), odomDataProto.getRightAcc());
            if (mChassisEventListener != null) {
                mChassisEventListener.onOdometerUpdate(odomDataProto.getMove(),
                        odomDataProto.getLeftAcc(), odomDataProto.getRightAcc());
            }
        }
    };

    private void onStatisticUpdate(Statistic statistic) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onStatisticUpdate(statistic);
        }
    }

    private void onMapDataUpdate(MapData data) {
        int type;
        if (data.getType() == MapData.GLOBAL) {
            type = Message.MSG_TYPE_GLOBAL_MAP;
        } else {
            type = Message.MSG_TYPE_LOCAL_MAP;
        }
        Log.d(TAG, "onMapDataUpdate type = " + type);
        Map map = new Map(type);
        map.setWidth(data.getWidth());
        map.setHeight(data.getHeight());
        map.setResolution(data.getResolution());
        map.setOriginalX(data.getOriginalX());
        map.setOriginalY(data.getOriginalY());
        map.setData(data.getData());

        if (mChassisEventListener != null) {
            Log.i(TAG, "onMapDataUpdate: send map data");
            mChassisEventListener.onMapUpdate(map);
        }
    }

    private OutMapUtils.OutMapLiniser checkOutMapListener = new OutMapUtils.OutMapLiniser() {
        @Override
        public void outMap(int params) {
            mChassisEventListener.onOutMap(params);
        }
    };

    private void onPoseUpdate(double x, double y, double theta, final int status) {
        Pose pose = new Pose((float) x, (float) y, (float) theta, status);
        //Log.i(TAG, "onPoseUpdate: nav pose="+pose);
        LogUtils.printLog(LogUtils.TYPE_POSE, TAG, "onPoseUpdate : " + pose.toString() + ",target pose:" +
                        (mTargetPose == null ? "null" : mTargetPose.isAvailable()) + ", isNavigationing = " + isNavigationing()
                , 2000);

        mCurPose.set(pose);

        if (mChassisEventListener != null) {
            mChassisEventListener.onPoseUpdate(pose);
        }

        //TODO 添加ros
        /*Pose robotPose = new Pose(Message.MSG_TYPE_ROBOT_POSE, (float) x, (float) y, (float) theta, status);
        if (mRos != null) {
            mRos.sendMessage(robotPose);
        }*/
        if (!isInSpecialMode(CREATING_MAP) && mChassisEventListener != null) {
            OutMapUtils.getInstance().checkOutMap(status, checkOutMapListener);
        }

        if (!isNavigationing() && WheelControlX86.getInstance().getLinearSpeed() == 0
                && !isInSpecialMode(CREATING_MAP) && mChassisEventListener != null) {
            DistanceUtils.pushRobotTooFarAway(x, y, new DistanceUtils.MoveListener() {
                @Override
                public void pushExceedDistance(String pushDistance) {
                    mChassisEventListener.onPushUpdate(pushDistance);
                }
            });
        }

        if (mTargetPose == null || !mTargetPose.isAvailable()) {
            return;
        }

        biManager.upload(x, y);

    }


    private boolean isRemoteRunning = true;

    public boolean isRemoteRunning() {
        return isRemoteRunning;
    }

    private void updateRemoteStatus(boolean isRemoteRunning) {
        this.isRemoteRunning = isRemoteRunning;
    }

    private void onNavigationEvent(Event event) {
        Log.d(TAG, "onEvent:" + event.getCode() + " + " + event.getMessage() + " + " + event
                .getAdditional());

        switch (event.getCode()) {
            case Event.GOAL_REACHED:
                if (mTargetPose != null) {
                    Log.d(TAG, "Navigation arrived : " + mTargetPose.toString());
                    updateTargetPose(TargetPose.RESULT_ARRIVED);
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;

            case Event.GOAL_INVALID:
            case Event.GOAL_IS_DANGEROUS:
            case Event.ROBOT_IS_OUT:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_OUT_MAP);
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }

                if (!isRemoteRunning() || !isServiceReady()) return;
                logManager.startCollectLogFlow(
                        RobotSettings.getSystemSn() + "-" + System.currentTimeMillis(),
                        String.valueOf(System.currentTimeMillis()), LogManager.TYPE_GOAL_INVALID);
                break;

            case Event.PATH_FAILED:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_GLOBAL_PATH_FAILED);
                }

                //TODO 判断连接状态
                if (!isRemoteRunning() || !isServiceReady()) return;

                String eventFlag = LogManager.TYPE_GLOBAL_PATH_FAILED;
                Pose pose = null;
                if (mCurPose != null) {
                    pose = mCurPose.get();
                }
                if (pose != null && pose.getStatus() == Pose.MAP_STATUS_OUTSIDE_AREA) {
                    Log.d(TAG, "current pose status is " + pose.getStatus());
                    eventFlag = LogManager.TYPE_GLOBAL_PATH_FAILED_OUT_MAP;
                }

                Log.d(TAG, "global path failed, the specific type is " + eventFlag);

                logManager.startCollectLogFlow(RobotSettings.getSystemSn() + "-" + System.currentTimeMillis(),
                        String.valueOf(System.currentTimeMillis()), eventFlag);
                break;

            case Event.PATH_SUCCESS:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_PATH_SUCCESS);
                }
                break;

            case Event.LOCAL_GOAL_INVAILD:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_AVOID);
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;
            case Event.LOCAL_PATH_FAILED:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_AVOID);
                }
                break;

            case Event.OBSTACLES_AVOID:
                if (mTargetPose != null) {
                    mTargetPose.onStatusUpdate(TargetPose.STATUS_OBSTACLES_AVOID);
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;

            case Event.ESTIMATE_SUCCESS:
                updatePoseEstimate(true);
                updateCurMode(NAVIGATION);
                Log.d(TAG, "Rover estimate success");
                updateResetEstimate(true, SUCCESS, event.getMessage());
                biManager.pushNavigationReport(event.getCode(), event.getMessage());
                break;

            case Event.ESTIMATE_FAILED:
                updatePoseEstimate(false);
                Log.d(TAG, "Rover estimate failed");
                //only estimate failed event use getAdditional，because fail reason defined by it
                updateResetEstimate(false, FAIL_NO_REASON, event.getAdditional());
                biManager.pushNavigationReport(event.getCode(), event.getMessage());
                break;

            case Event.REMOTE_ERROR:
                updateRemoteStatus(false);
                updatePoseEstimate(false);
//                setAlreadyInit(false);

                Log.d(TAG, "Rover remote service is died");
                biManager.pushNavigationReport(event.getCode(), event.getMessage());

                logManager.startCollectLogFlow(RobotSettings.getSystemSn() + "-" + System.currentTimeMillis(),
                        String.valueOf(System.currentTimeMillis()), LogManager.TYPE_REMOTE_DISCONNECT);
                break;

            case Event.REMOTE_RECOVER:
                updateRemoteStatus(true);

                // 在此开启原因：整机重启之前恰好5分钟还未来及消费就重启，需要在重启后及时通知tk1打包
                if (logManager.collectLogNotOpened()) {
                    Log.d(Definition.TAG_NAVI_LOG_REPORT, "algorithm process recover, try start collect task");
                    logManager.scheduleCollectTask();
                }

                Log.d(TAG, "Rover remote service is recovered");
                biManager.pushNavigationReport(event.getCode(), event.getMessage());

//                syncWorkStatusWithChassis();
                break;

            case Event.ESTIMATE_LOST:
                Log.d(TAG, "Estimate lost");
                updatePoseEstimate(false);
                updateEvent(Definition.HW_NAVI_ESTIMATE_LOST, event.getMessage());
                reportException(Definition.HW_NAVI_ESTIMATE_LOST, event.getMessage());
                Pose currentPose;
                boolean isOutOfMap = false;
                if (mCurPose != null && (currentPose = mCurPose.get()) != null) {
                    isOutOfMap = currentPose.getStatus() == Pose.MAP_STATUS_OUTSIDE_AREA;
                }
                biManager.lostEventNavigationReport(isOutOfMap, event.getMessage());

                if (!isRemoteRunning() || !isServiceReady()) return;
                logManager.startCollectLogFlow(
                        RobotSettings.getSystemSn() + "-" + System.currentTimeMillis(),
                        String.valueOf(System.currentTimeMillis()), LogManager.TYPE_ESTIMATE_LOST);

                break;

            case Event.ESTIMATE_RECOVERY:
                Log.d(TAG, "Estimate recovery");
                updatePoseEstimate(true);
                updateResetEstimate(true, SUCCESS, event.getMessage());
                biManager.pushNavigationReport(event.getCode(), event.getMessage());
                break;

            case Event.EXPECTED_TIME_FOR_VISION:
                Log.d(TAG, "expected time");
                if (mCreateMapStop != null) {
                    mCreateMapStop.onStatusUpdate(CreateMapStop
                            .STATUS_STOP_CREATE_MAP_EXPECTED_TIME, event.getAdditional());
                    biManager.pushNavigationReport(event.getCode(), event.getMessage());
                }
                break;

            case Event.ERROR_LOG:
                Log.d(TAG, "error log");
                //TK1 has remove tk1 error log event report on V4.8 2019.4.20
//                updateErrorLog(Definition.HW_NAVI_ERROR_LOG, event.getAdditional());
//                NavigationCmdReportUtils.pushNavigationReport(event.getCode(), event.getMessage());
                break;

            case Event.PACK_LOG_END:
                Log.d(TAG, "pack log end");
                updatePackLogEnd(Definition.HW_NAVI_PACK_LOG_END, event.getMessage());
                break;

            case Event.TAKE_SNAPSHOT_END:
                Log.d(TAG, "take snapshot end");
                updateTakeSnapshotEnd(Definition.HW_NAVI_TAKE_SNAPSHOT_END, event.getMessage(),
                        event.getAdditional());
                break;
            case Event.PROCESS_CREATE_MAP:
                updateCreateMapProcess(event.getAdditional());
                break;

            case Event.DETECT_SHAKE:
                biManager.reportDetectShakeFromTk1();
                break;

            case Event.DETECT_PEOPLE:
                Log.d(TAG, "detect people");
                onMonoInfoUpdate(event.getAdditional());
                break;
            case Event.GOTO_CHARGE_SUCCESS: {
                Log.d(TAG, "goto charge success");
                if (null != mGoChargelistener) {
                    ChargeResult chargeResult = new ChargeResult();
                    chargeResult.setCode(event.getCode());
                    chargeResult.setMessage("Go charge success");
                    mGoChargelistener.onResponse(true, event.getCode(), GsonUtil.toJson(chargeResult));
                }
            }

            break;
            case Event.GOTO_CHARGE_FAILED: {
                Log.d(TAG, "goto charge fail");
                if (null != mGoChargelistener) {
                    ChargeResult chargeResult = new ChargeResult();
                    chargeResult.setCode(event.getCode());
                    chargeResult.setMessage("Go charge failed");
                    mGoChargelistener.onResponse(false, event.getCode(), GsonUtil.toJson(chargeResult));
                }
            }
            break;
            default:
                break;
        }
    }

    private synchronized void updateEvent(String key, Object status) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onEvent(key, status);
        }
    }

    private synchronized void reportException(String type, String data) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onException(type, data);
        }
    }

    private synchronized void updatePackLogEnd(String from, Object status) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onPackLogEnd(from, status);
        }
    }

    private void updateCreateMapProcess(String additional) {
        if (mChassisEventListener != null) {
            mChassisEventListener.OnUpdateCreateMapProcess(additional);
        }
    }

    private synchronized void updateTakeSnapshotEnd(String from, String fileID, String path) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onTakeSnapshotEnd(from, fileID, path);
        }
    }

    private void onMonoInfoUpdate(String additional) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onMonoInfoUpdate(additional);
        }
    }

    private void onLaserDataIn(ArrayList<Laser> data) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onLaserDataUpdate(data);
        }
    }

    private void onObstacleReport() {
        if (mChassisEventListener != null) {
            mChassisEventListener.onObstacleReport();
        }
    }

    private void onAvoidStateReport(boolean isStopping) {
        if (mChassisEventListener != null) {
            mChassisEventListener.onAvoidStateChange(isStopping);
        }
    }

    AvoidStoppingPolicy.AvoidObserver mAvoidObserver = new AvoidStoppingPolicy.AvoidObserver() {
        @Override
        public void onStateUpdate(boolean avoid, int score) {
            Log.i(TAG_AVOID, "onStateUpdate, avoid=" + avoid + ", score=" + score);
            onAvoidStateReport(avoid);
            if (score == AvoidStoppingPolicy.OBSTACLES_SCORE_PERILOUS) {
                Log.e(TAG_AVOID, "onStateUpdate , perilous");
                motion(0, 0, false);
            } else if (avoid) {
                Log.e(TAG_AVOID, "onStateUpdate , Avoid stop error");
                motionAvoidStop();
            }
        }
    };

    /**
     * 基础运动避停监听
     */
    private void motionAvoidStop() {
        if (isNavigationing()) {
            Log.e(TAG, "motionAvoidStop: Is navigationing");
            return;
        }
        if (!mMotionWithAvoid) {
            Log.d(TAG_AVOID, "motionAvoidStop , Not motion avoid state");
            return;
        }
        Velocity realVelocity = mRealtimeVelocity.get();
        if (realVelocity == null || WheelControlX86.getInstance().isLinearStill(realVelocity)) {
            Log.d(TAG_AVOID, "motionAvoidStop , Robot is linear still");
            return;
        }

        double tAngularSpeed = WheelControlX86.getInstance().getTargetAngularSpeed();
        Log.d(TAG_AVOID, "motionAvoidStop , Stop motion : realVelocity=" + realVelocity.toString()
                + ", tAngularSpeed=" + tAngularSpeed);
        BasicMotionProcess.getInstance().stopMotionTask(false, MOTION_AVOID_STOP, "Avoid stop");
        motion(tAngularSpeed, 0, true);
        updateMotionAvoidState(false);
    }

    private void motionLinearAvoidStop() {
        Log.d(TAG_AVOID, "motionLinearAvoidStop , mMotionWithAvoid=" + mMotionWithAvoid);
        BasicMotionProcess.getInstance().stopMotionTask(false, MOTION_AVOID_STOP, "Avoid stop");
        motion(0, 0, true);
        updateMotionAvoidState(false);
    }

    private volatile boolean mMotionWithAvoid = false;
    private AtomicReference<Velocity> mRealtimeVelocity = new AtomicReference<>();

    @Override
    public Velocity getRealtimeVelocity() {
        return mRealtimeVelocity.get();
    }

    @Override
    public void updateMotionAvoidState(boolean withAvoid) {
        Log.d(TAG_AVOID, "updateMotionAvoidState , withAvoid=" + withAvoid);
        mMotionWithAvoid = withAvoid;
    }

    @Override
    public void setSimpleEventListener(SimpleEventListener mSimpleEventListener) {
        this.mSimpleEventListener = mSimpleEventListener;
    }
}