package com.ainirobot.navigationservice;

import static com.ainirobot.coreservice.client.Definition.CMD_NAVI_CHECK_OBSTACLE;
import static com.ainirobot.coreservice.client.Definition.CMD_NAVI_GET_LOG_BY_ID;
import static com.ainirobot.coreservice.client.Definition.CMD_NAVI_GET_LOG_FILE;
import static com.ainirobot.coreservice.client.Definition.CMD_NAVI_GET_PLACELIST_WITH_NAMELIST;
import static com.ainirobot.coreservice.client.Definition.CMD_NAVI_GET_POSITION;
import static com.ainirobot.coreservice.client.Definition.CMD_NAVI_IS_IN_NAVIGATION;
import static com.ainirobot.coreservice.client.Definition.CMD_NAVI_PACK_LOG_FILE;
import static com.ainirobot.coreservice.client.Definition.CMD_NAVI_QUERY_RADAR_STATUS;
import static com.ainirobot.coreservice.client.Definition.CMD_NAVI_RESET_ESTIMATE;
import static com.ainirobot.coreservice.client.Definition.CMD_NAVI_SAVE_ESTIMATE;
import static com.ainirobot.coreservice.client.Definition.CMD_NAVI_TIME_OUT_MSG_DELETE;
import static com.ainirobot.coreservice.client.Definition.CMD_NAVI_TIME_OUT_REPORT;
import static com.ainirobot.coreservice.client.Definition.CMD_NAVI_UPDATE_LOG_STATUS_BY_ID;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.LOG_FILE;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Environment;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.CheckObstacleBean;
import com.ainirobot.coreservice.client.actionbean.NaviCmdTimeOutBean;
import com.ainirobot.navigationservice.beans.tk1.PackLogBean;
import com.ainirobot.navigationservice.business.ChassisNoRelyApi;
import com.ainirobot.navigationservice.business.ChassisRelyApi;
import com.ainirobot.navigationservice.utils.GsonUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

public class TestReceiver extends BroadcastReceiver {

    private final static String MAP_DIR = "/robot/map";
    private final static String ROBOT_MAP_DIR = Environment.getExternalStorageDirectory() + MAP_DIR;
    ChassisNoRelyApi chassisNoRelyApi;
    ChassisRelyApi chassisRelyApi;

    public TestReceiver(ChassisNoRelyApi chassisNoRelyApi, ChassisRelyApi chassisRelyApi) {
        this.chassisNoRelyApi = chassisNoRelyApi;
        this.chassisRelyApi = chassisRelyApi;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        String Action = intent.getAction();
        String cmdType = intent.getStringExtra("cmd");
        Log.d("TAGTEST", "onReceived = " + cmdType);
        switch (cmdType) {
            case CMD_NAVI_RESET_ESTIMATE:
                chassisRelyApi.resetPoseEstimate(cmdType);
                break;
            case CMD_NAVI_PACK_LOG_FILE:
                PackLogBean packLogBean = new PackLogBean(System.currentTimeMillis()- 1000*60*60*3, System.currentTimeMillis()- 1000*60*60*1);
                chassisRelyApi.packLogFile(cmdType, GsonUtil.toJson(packLogBean));
                break;
            case CMD_NAVI_GET_LOG_FILE:
                chassisRelyApi.getLogFile(cmdType, ROBOT_MAP_DIR, LOG_FILE);
                break;
            case CMD_NAVI_QUERY_RADAR_STATUS:
                chassisRelyApi.getRadarState(cmdType);
                break;
            case CMD_NAVI_IS_IN_NAVIGATION:
                chassisNoRelyApi.getNavigationStatus(cmdType);
                break;
            case CMD_NAVI_GET_PLACELIST_WITH_NAMELIST:
                ArrayList<String> arrayList = new ArrayList<>();
                arrayList.add("充电桩");
                arrayList.add("回充点");
                chassisNoRelyApi.getPlaceListWithNameList(cmdType, GsonUtil.toJson(arrayList));
                break;
            case CMD_NAVI_SAVE_ESTIMATE:
                chassisNoRelyApi.savePoseEstimate(cmdType);
                break;
            case CMD_NAVI_GET_POSITION:
                chassisNoRelyApi.getCurrentPose(cmdType);
                break;

            case CMD_NAVI_TIME_OUT_REPORT:
                NaviCmdTimeOutBean bean = new NaviCmdTimeOutBean(System.currentTimeMillis() - 1000*60*60
                        , "cacheId test", 0, CMD_NAVI_GET_POSITION, "params nothing");
                chassisNoRelyApi.reportCmdTimeOut(cmdType, GsonUtil.toJson(bean));
                break;
            case CMD_NAVI_TIME_OUT_MSG_DELETE:
                try {
                    JSONObject json = new JSONObject();
                    json.put(Definition.JSON_NAVI_TIME_OUT_MSG_ID, "cacheId test");
                    chassisNoRelyApi.timeOutMsgDelete(cmdType, json.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                break;
            case CMD_NAVI_GET_LOG_BY_ID:
                chassisNoRelyApi.getLogTaskById(cmdType, "cacheId test");
                break;
            case CMD_NAVI_UPDATE_LOG_STATUS_BY_ID:
                try {
                    JSONObject json = new JSONObject();
                    json.put(Definition.JSON_NAVI_LOG_CACHE_ID, "cacheId test");
                    json.put(Definition.JSON_NAVI_LOG_CACHE_STATUS, 123);
                    chassisNoRelyApi.updateLogStatusById(cmdType, json.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                break;
            case CMD_NAVI_CHECK_OBSTACLE:
                CheckObstacleBean bean1 = new CheckObstacleBean(-45, 45, 1.5);
                chassisNoRelyApi.checkObstacle(cmdType, GsonUtil.toJson(bean1));
                break;
            default:
                break;
        }
    }


}
