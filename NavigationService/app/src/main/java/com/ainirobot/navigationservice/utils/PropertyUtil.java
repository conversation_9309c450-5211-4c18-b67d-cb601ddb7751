package com.ainirobot.navigationservice.utils;

import java.lang.reflect.Method;

public class PropertyUtil {

    public static String getSystemProperties(String key, String defaultVal) {
        try {
            Method systemProperties_get =
                    Class.forName("android.os.SystemProperties").getMethod("get"
                            , String.class, String.class);

            String ret = (String) systemProperties_get.invoke(null, key, defaultVal);

            if (ret != null)
                return ret;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return defaultVal;
    }
}
