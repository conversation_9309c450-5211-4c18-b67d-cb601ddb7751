package com.ainirobot.navigationservice.utils;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.google.gson.JsonElement;

import java.lang.reflect.Type;

public class GsonUtil {
    private static final Gson gson = new Gson();


    public static String toJson(@NonNull Object object) {
        return gson.toJson(object);
    }

    public static <T> T from<PERSON><PERSON>(@NonNull String json, @NonNull Class<T> aClass) {
        try {
            return gson.fromJson(json, aClass);
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    public static <T> T fromJson(@NonNull JsonElement element, @NonNull Class<T> aClass) {
        try {
            return gson.fromJson(element, aClass);
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    public static <T> T fromJson(@NonNull String json, @NonNull Type typeOfT) {
        try {
            return gson.fromJson(json, typeOfT);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Gson getGson(){
        return gson;
    }
}
