package com.ainirobot.navigationservice.chassisAbility.controller;

import android.util.Log;

import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.navigationservice.beans.tk1.Laser;
import com.ainirobot.navigationservice.utils.LogUtils;

import java.util.ArrayList;
import java.util.Vector;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Created by Orion on 2019/12/19.
 */
public class AvoidStoppingPolicy {
    private static final String TAG = "AvoidStoppingPolicy";

    private Vector<AvoidObserver> mObservables = new Vector<>();
    private volatile boolean mHasObstacle = false;
    private volatile double mMinObstacleDistance = Double.MAX_VALUE;
    private AtomicReference<Laser> mDangerousLaser = new AtomicReference<>();
    private volatile double mSafeDistance = DEFAULT_SAFE_DISTANCE;

    /**
     * 机器人半径，
     * 小秘 0.244，
     * mini 0.205，
     * mini2 0.205 (100cm*41cm*41cm)
     */
    private static final double ROBOT_RADIUS = 0.244f;

    /**
     * 障碍物水平偏移容错距离
     */
    private static final double SAFE_OFFSET = 0.03f;

    /**
     * 默认障碍物安全距离 （开始减速距离）
     * mini 0.6
     * 小秘 0.8或1.0
     */
    private static final double DEFAULT_SAFE_DISTANCE = 0.8f;

    /**
     * 默认障碍物危险距离
     */
    private static final double DEFAULT_DANGEROUS_DISTANCE = 0.35f;

    /**
     * 障碍物状态消除超时时间
     */
    private static final long OBSTACLES_CHECK_TIME = (long) (0.5 * 1000);

    /**
     * 障碍物危险度评估分数
     */
    public static final int OBSTACLES_SCORE_SAFE = 0;
    public static final int OBSTACLES_SCORE_DANGEROUS = 1;
    public static final int OBSTACLES_SCORE_PERILOUS = 2;

    private AvoidStoppingPolicy() {

    }

    private static class InstanceHolder {
        public static final AvoidStoppingPolicy instance = new AvoidStoppingPolicy();
    }

    public static AvoidStoppingPolicy getInstance() {
        return InstanceHolder.instance;
    }

    public interface AvoidObserver {
        void onStateUpdate(boolean avoid, int score);
    }

    public void handleObstaclesInfo(ArrayList<Laser> data) {
        boolean obstacle = hasObstacle(data);
        int score = obstacleScoreAdj(data);
        final Laser dangerousLaser = mDangerousLaser.get();

        LogUtils.printLog(LogUtils.TYPE_LASER, TAG,
                "Has obstacle=" + obstacle
                        + ", Pre state=" + mHasObstacle
                        + ", mSafeDistance=" + mSafeDistance
                        + ", mMinObstacleDistance=" + mMinObstacleDistance
                        + ", score=" + score+" ,data.length: "+data.size(),
                1000);
        if (score > 0) {
            startCheckObstaclesTimer();
            if (!mHasObstacle) {
                mHasObstacle = true;
                notifyAllObservers(score);
            }
        }
    }

    private int obstacleScoreAdj(ArrayList<Laser> lasers) {
        if (lasers == null || lasers.size() <= 0) {
            return OBSTACLES_SCORE_SAFE;
        }

        double minDistance = Double.MAX_VALUE;
        Laser dangerousLaser = null;
        for (Laser laser : lasers) {
            double laserX = Math.sin(Math.abs(laser.getAngle())) * laser.getDistance();
            if (laserX <= ROBOT_RADIUS + SAFE_OFFSET) {
                double laserY = Math.cos(Math.abs(laser.getAngle())) * laser.getDistance();
//                minDistance = Math.min(minDistance, laserY);
                if (laserY < minDistance) {
                    minDistance = laserY;
                    dangerousLaser = laser;
                }
            }
        }

        if (minDistance <= Math.min(DEFAULT_DANGEROUS_DISTANCE, mSafeDistance)) {
            return OBSTACLES_SCORE_PERILOUS;
        } else if (minDistance <= mSafeDistance) {
            mMinObstacleDistance = minDistance;
            mDangerousLaser.set(dangerousLaser);
            return OBSTACLES_SCORE_DANGEROUS;
        }

        mMinObstacleDistance = Double.MAX_VALUE;
        mDangerousLaser.set(null);
        return OBSTACLES_SCORE_SAFE;
    }

    private boolean hasObstacle(ArrayList<Laser> lasers) {
        if (lasers == null || lasers.size() <= 0) {
            return false;
        }

        double minDistance = Double.MAX_VALUE;
        Laser dangerousLaser = null;
        for (Laser laser : lasers) {
            double laserX = Math.sin(Math.abs(laser.getAngle())) * laser.getDistance();
            if (laserX <= ROBOT_RADIUS + SAFE_OFFSET) {
                double laserY = Math.cos(Math.abs(laser.getAngle())) * laser.getDistance();
//                minDistance = Math.min(minDistance, laserY);
                if (laserY < minDistance) {
                    minDistance = laserY;
                    dangerousLaser = laser;
                }
            }
        }

        if (minDistance <= mSafeDistance) {
            mMinObstacleDistance = minDistance;
            mDangerousLaser.set(dangerousLaser);
            return true;
        }

        mMinObstacleDistance = Double.MAX_VALUE;
        mDangerousLaser.set(null);
        return false;
    }

    /**
     * 障碍物消失计时
     */
    private void startCheckObstaclesTimer() {
//        Log.d(TAG, "startCheckObstaclesTimer");
        DelayTask.cancel(this);
        DelayTask.submit(this, new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "Check obstacles timer run, mHasObstacle=" + mHasObstacle);
                if (mHasObstacle) {
                    mHasObstacle = false;
                    notifyAllObservers();
                }
            }
        }, OBSTACLES_CHECK_TIME);
    }

    public boolean getAvoidState() {
        Log.d(TAG, "getAvoidState : mHasObstacle= " + mHasObstacle);
        return mHasObstacle;
    }

    public boolean getAvoidState(double distance) {
        Log.d(TAG, "getAvoidState : distance=" + distance
                + ", mMinObstacleDistance=" + mMinObstacleDistance
                + ", mHasObstacle=" + mHasObstacle);
        if (distance < mMinObstacleDistance) {
            return false;
        }
        return mHasObstacle;
    }

    public double getMinObstacleDistance() {
        return mMinObstacleDistance;
    }

    public Laser getDangerousLaser() {
        return mDangerousLaser.get();
    }

    public double getSafeDistance() {
        return mSafeDistance;
    }

    public void setSafeDistance(double distance) {
        Log.d(TAG, "setSafeDistance : new distance=" + distance
                + ", old distance=" + mSafeDistance);
        this.mSafeDistance = distance;
    }

    public void resetSafeDistance() {
        Log.d(TAG, "resetSafeDistance : old distance=" + mSafeDistance);
        this.mSafeDistance = DEFAULT_SAFE_DISTANCE;
    }

    public void addObserver(AvoidObserver observer) {
        if (mObservables.contains(observer)) {
            Log.d(TAG, "Already in observables");
            return;
        }
        mObservables.addElement(observer);
        notifyAllObservers();
    }

    public boolean removeObserver(AvoidObserver observer) {
        return mObservables.removeElement(observer);
    }

    private void notifyAllObservers() {
        notifyAllObservers(OBSTACLES_SCORE_DANGEROUS);
    }

    private void notifyAllObservers(int score) {
        for (AvoidObserver observer : mObservables) {
            observer.onStateUpdate(mHasObstacle, score);
        }
    }

    /**
     * @param pose 当前pose
     * @param 当前速度
     * @param 时间间隔
     *
     * @return 目标pose
     */
//    static Pose2d CalcPose(const Pose2d& pose, const Velocity2d& vel,
//                           double time) {
//        if (time <= 0) {
//            return pose;
//        }
//        Pose2d new_pose;
//        double d_theta_half = vel.angular_yaw * time / 2.;
//        double theta = pose.yaw() + d_theta_half;
//        double k = d_theta_half < 1e-6 ? 1. : sin(d_theta_half) / d_theta_half;
//        double d_x = vel.linear_x * k;
//        double d_y = vel.linear_y * k;
//        new_pose.set_position(
//                pose.position().x() + (d_x * cos(theta) - d_y * sin(theta)) * time,
//                pose.position().y() + (d_x * sin(theta) + d_y * cos(theta)) * time);
//        new_pose.set_yaw(generic::NormalizeAngle(theta + d_theta_half));
//        return new_pose;
//    }

}
