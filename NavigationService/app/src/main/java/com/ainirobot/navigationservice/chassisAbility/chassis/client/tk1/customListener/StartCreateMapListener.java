package com.ainirobot.navigationservice.chassisAbility.chassis.client.tk1.customListener;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.hardware.RobotCore;

public class StartCreateMapListener extends AbsResponseListener {

    private String mCmdType;

    public StartCreateMapListener(String cmdType) {
        this.mCmdType = cmdType;
    }

    @Override
    public void doResponse(String response) {
        RobotCore.sendAsyncResponse(mCmdType, Definition.RESULT_SUCCEED, response);
    }

    @Override
    public void onResponse(boolean status, int resultCode, Object result) {
        doResponse(getResponse(resultCode));
    }
}
