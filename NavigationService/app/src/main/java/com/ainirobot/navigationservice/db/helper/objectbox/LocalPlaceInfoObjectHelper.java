package com.ainirobot.navigationservice.db.helper.objectbox;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.navigationservice.commonModule.data.utils.UuidUtils;
import com.ainirobot.navigationservice.db.entity.LocalPlaceInfo;
import com.ainirobot.navigationservice.db.entity.LocalPlaceInfo_;
import com.ainirobot.navigationservice.db.entity.MapInfo;
import com.ainirobot.navigationservice.db.entity.MapInfo_;
import com.ainirobot.navigationservice.db.entity.PlaceInfo;
import com.ainirobot.navigationservice.db.entity.PlaceInfo_;
import com.ainirobot.navigationservice.db.entity.PlaceName;
import com.ainirobot.navigationservice.db.entity.PlaceName_;
import com.ainirobot.navigationservice.db.helper.iml.LocalPlaceInfoIml;

import java.util.ArrayList;
import java.util.List;

import io.objectbox.Box;
import io.objectbox.BoxStore;
import io.objectbox.exception.NonUniqueResultException;
import io.objectbox.query.Query;
import io.objectbox.query.QueryBuilder;

public class LocalPlaceInfoObjectHelper extends BaseObjectHelper<LocalPlaceInfo> implements LocalPlaceInfoIml {

    private static final String TAG = LocalPlaceInfoObjectHelper.class.getSimpleName();
    public LocalPlaceInfoObjectHelper(BoxStore boxStore) {
        super(boxStore);
    }

    @Override
    public void initLocalPlaceInfoData() {
        Box<LocalPlaceInfo> localPlaceInfoBox = getBox();
        localPlaceInfoBox.removeAll();
        Box<MapInfo> mapInfoBox = boxStore.boxFor(MapInfo.class);
        Box<PlaceInfo> placeInfoBox = boxStore.boxFor(PlaceInfo.class);
        Box<PlaceName> placeNameBox = boxStore.boxFor(PlaceName.class);
        List<MapInfo> mapInfoList = mapInfoBox.query().build().find();
        if (mapInfoList.isEmpty()) {
            return;
        }
        for (MapInfo mapInfo : mapInfoList) {
            String mapName = mapInfo.getMapName();
            String mapLanguage = mapInfo.getMapLanguage();
            QueryBuilder<PlaceInfo> placeInfoQuery = placeInfoBox
                .query(PlaceInfo_.mapName.equal(mapName).and(PlaceInfo_.priority.equal(Definition.SPECIAL_PLACE_HIGH_PRIORITY)));
            List<PlaceInfo> placeInfoList = placeInfoQuery
                .between(PlaceInfo_.typeId, Definition.CHARGING_POINT_TYPE, Definition.GATE_OUTER_TYPE).build().find();
            List<LocalPlaceInfo> localPlaceInfoList = new ArrayList<>();
            if (!placeInfoList.isEmpty()) {
                for (PlaceInfo placeInfo : placeInfoList) {
                    localPlaceInfoList.add(convertToLocalPlaceInfo(placeInfo, mapLanguage, placeNameBox));
                }
                localPlaceInfoBox.put(localPlaceInfoList);
            }
            placeInfoQuery.close();
        }
    }

    @NonNull
    private List<PlaceName> getPlaceNames(List<LocalPlaceInfo> localPlaceInfoList, String mapLanguage) {
        List<PlaceName> placeNameList = new ArrayList<>();
        for (LocalPlaceInfo localPlaceInfo : localPlaceInfoList) {
            PlaceName placeName = new PlaceName();
            placeName.setPlaceId(localPlaceInfo.getPlaceId());
            placeName.setLanguageType(mapLanguage);
            placeName.setPlaceName(localPlaceInfo.getPlaceName());
            placeNameList.add(placeName);
        }
        return placeNameList;
    }

    @Override
    public void updateLocalPlaceInfo(List<LocalPlaceInfo> localPlaceInfoList) {
        Box<LocalPlaceInfo> localPlaceInfoBox = getBox();
        localPlaceInfoBox.put(localPlaceInfoList);
    }

    @Override
    public void updateLocalPlaceInfo(LocalPlaceInfo localPlaceInfo) {
        Box<LocalPlaceInfo> localPlaceInfoBox = getBox();
        Box<PlaceName> placeNameBox = boxStore.boxFor(PlaceName.class);
        Query<PlaceName> placeNameQuery = placeNameBox.query(PlaceName_.placeId.equal(localPlaceInfo.getPlaceId())).build();
        PlaceName placeName = placeNameQuery.findFirst();
        if (placeName != null) {
            placeNameBox.put(placeName);
        }
        placeNameQuery.close();
        localPlaceInfoBox.put(localPlaceInfo);
    }

    @Override
    public void updateLocalPlaceInfo(String mapName) {
        Box<LocalPlaceInfo> localPlaceInfoBox = getBox();
        Box<PlaceInfo> placeInfoBox = boxStore.boxFor(PlaceInfo.class);
        Box<PlaceName> placeNameBox = boxStore.boxFor(PlaceName.class);
        Box<MapInfo> mapInfoBox = boxStore.boxFor(MapInfo.class);
        Query<MapInfo> mapInfoQuery = mapInfoBox.query(MapInfo_.mapName.equal(mapName)).build();
        QueryBuilder<PlaceInfo> placeInfoQuery = placeInfoBox
            .query(PlaceInfo_.mapName.equal(mapName)
                .and(PlaceInfo_.priority.equal(Definition.SPECIAL_PLACE_HIGH_PRIORITY)));
        MapInfo mapInfo = mapInfoQuery.findFirst();
        String mapLanguage = mapInfo != null ? mapInfo.getMapLanguage() : "zh_CN";
        List<PlaceInfo> placeInfoList = placeInfoQuery
            .between(PlaceInfo_.typeId, Definition.CHARGING_POINT_TYPE, Definition.GATE_OUTER_TYPE).build().find();
        if (!placeInfoList.isEmpty()) {
            List<LocalPlaceInfo> localPlaceInfoList = new ArrayList<>();
            for (PlaceInfo placeInfo : placeInfoList) {
                localPlaceInfoList.add(convertToLocalPlaceInfo(placeInfo, mapLanguage, placeNameBox));
            }
            localPlaceInfoBox.put(localPlaceInfoList);
        }
        mapInfoQuery.close();
        placeInfoQuery.close();
    }

    @Override
    public String[] deleteLocalPlaceInfoByIds(String mapName, String[] placeIds) {
        Query<LocalPlaceInfo> placeInfoQuery = getBox()
                .query(LocalPlaceInfo_.placeId.oneOf(placeIds))
                .equal(LocalPlaceInfo_.mapName, mapName, QueryBuilder.StringOrder.CASE_SENSITIVE)
                .build();
        return deletePlaceAndGetPlaceIds(placeInfoQuery);
    }

    @Override
    public String[] deleteLocalPlaceInfo(String mapName) {
        Query<LocalPlaceInfo> localPlaceInfoQuery = getBox()
            .query(LocalPlaceInfo_.mapName.equal(mapName)).build();
        return deletePlaceAndGetPlaceIds(localPlaceInfoQuery);
    }

    @Override
    public String[] deleteLocalPlaceByTypeId(String mapName, int typeId) {
        Query<LocalPlaceInfo> localPlaceInfoQuery = getBox()
            .query(LocalPlaceInfo_.mapName.equal(mapName)
                .and(LocalPlaceInfo_.typeId.equal(typeId))).build();
        return deletePlaceAndGetPlaceIds(localPlaceInfoQuery);
    }

    @Nullable
    private String[] deletePlaceAndGetPlaceIds(Query<LocalPlaceInfo> localPlaceInfoQuery) {
        String[] placeIds = localPlaceInfoQuery.property(LocalPlaceInfo_.placeId).findStrings();
        boolean remove = localPlaceInfoQuery.remove() > 0;
        localPlaceInfoQuery.close();
        Log.d(TAG, "getLocalPlaceIds : " + remove);
        return placeIds;
    }

    @Override
    public List<LocalPlaceInfo> getLocalPlaceInfo(String mapName) {
        Box<LocalPlaceInfo> localPlaceInfoQuery = getBox();
        Query<LocalPlaceInfo> query = localPlaceInfoQuery
                .query(LocalPlaceInfo_.mapName.equal(mapName))
                .build();
        List<LocalPlaceInfo> localPlaceInfoList = query.find();
        query.close();
        return localPlaceInfoList;
    }

    @Override
    public LocalPlaceInfo getLocalPlaceInfoByTypeId(String mapName, int typeId) {
        Box<LocalPlaceInfo> localPlaceInfoQuery = getBox();
        Query<LocalPlaceInfo> query = localPlaceInfoQuery
            .query(LocalPlaceInfo_.mapName.equal(mapName).and(LocalPlaceInfo_.typeId.equal(typeId)))
            .build();
        LocalPlaceInfo localPlaceInfo = query.findFirst();
        query.close();
        return localPlaceInfo;
    }

    @Override
    public boolean containsLocalPlaceInfoByMapName(String mapName) {
        Box<LocalPlaceInfo> localPlaceInfoQuery = getBox();
        Query<LocalPlaceInfo> query = localPlaceInfoQuery.
                query(LocalPlaceInfo_.mapName.equal(mapName)).build();
        List<LocalPlaceInfo> localPlaceInfoList = query.find();
        query.close();
        return !localPlaceInfoList.isEmpty();
    }

    @Override
    public boolean hasLocalPlaceInfoData() {
        Box<LocalPlaceInfo> localPlaceInfoQuery = getBox();
        long count = localPlaceInfoQuery.count();
        return count != 0;
    }

    private LocalPlaceInfo convertToLocalPlaceInfo(PlaceInfo placeInfo, String mapLanguage, Box<PlaceName> placeNameBox) {
        LocalPlaceInfo localPlaceInfo = new LocalPlaceInfo();
        localPlaceInfo.setPlaceStatus(placeInfo.getPlaceStatus());
        localPlaceInfo.setPointTheta(placeInfo.getPointTheta());
        localPlaceInfo.setPointX(placeInfo.getPointX());
        localPlaceInfo.setPointY(placeInfo.getPointY());
        localPlaceInfo.setMapName(placeInfo.getMapName());
        localPlaceInfo.setAlias(placeInfo.getAlias());
        localPlaceInfo.setTypeId(placeInfo.getTypeId());
        localPlaceInfo.setIgnoreDistance(placeInfo.getIgnoreDistance());
        localPlaceInfo.setNoDirectionalParking(placeInfo.getNoDirectionalParking());
        localPlaceInfo.setSafeDistance(placeInfo.getSafeDistance());
        localPlaceInfo.setPlaceId(placeInfo.getPlaceId());

        Query<PlaceName> placeNameQuery = placeNameBox
            .query(PlaceName_.placeId.equal(placeInfo.getPlaceId()).and(PlaceName_.languageType.equal(mapLanguage)))
            .build();
        PlaceName placeName;
        try {
            placeName = placeNameQuery.findUnique();
            if (placeName != null) {
                localPlaceInfo.setPlaceName(placeName.getPlaceName());
            }
        } catch (NonUniqueResultException e) {
            Log.d(TAG, "convertToLocalPlaceInfo error message: " + e.getMessage());
            placeName = placeNameQuery.findFirst();
            if (placeName != null) {
                localPlaceInfo.setPlaceName(placeName.getPlaceName());
            }
        }
        placeNameQuery.close();
        return localPlaceInfo;
    }
}
