package com.ainirobot.navigationservice.beans.standard;

public class SensorStatus {
    int radarSensor;
    int odom;
    int infraredCamera;
    int backMonoImage;
    int binocularImage;
    int miniSRgbd;
    int miniRgbd;
    int ultraSound;

    public SensorStatus(int radarSensor, int odom, int infraredCamera, int backMonoImage
            , int binocularImage, int miniSRgbd, int miniRgbd, int ultraSound) {
        this.radarSensor = radarSensor;
        this.odom = odom;
        this.infraredCamera = infraredCamera;
        this.backMonoImage = backMonoImage;
        this.binocularImage = binocularImage;
        this.miniSRgbd = miniSRgbd;
        this.miniRgbd = miniRgbd;
        this.ultraSound = ultraSound;
    }

    public int getUltraSound() {
        return ultraSound;
    }

    public void setUltraSound(int ultraSound) {
        this.ultraSound = ultraSound;
    }

    public int getRadarSensor() {
        return radarSensor;
    }

    public void setRadarSensor(int radarSensor) {
        this.radarSensor = radarSensor;
    }

    public int getOdom() {
        return odom;
    }

    public void setOdom(int odom) {
        this.odom = odom;
    }

    public int getInfraredCamera() {
        return infraredCamera;
    }

    public void setInfraredCamera(int infraredCamera) {
        this.infraredCamera = infraredCamera;
    }

    public int getBackMonoImage() {
        return backMonoImage;
    }

    public void setBackMonoImage(int backMonoImage) {
        this.backMonoImage = backMonoImage;
    }

    public int getBinocularImage() {
        return binocularImage;
    }

    public void setBinocularImage(int binocularImage) {
        this.binocularImage = binocularImage;
    }

    public int getMiniSRgbd() {
        return miniSRgbd;
    }

    public void setMiniSRgbd(int miniSRgbd) {
        this.miniSRgbd = miniSRgbd;
    }

    public int getMiniRgbd() {
        return miniRgbd;
    }

    public void setMiniRgbd(int miniRgbd) {
        this.miniRgbd = miniRgbd;
    }

    @Override
    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append("radarSensor = " + this.radarSensor);
        sb.append(", odom = " + this.odom);
        sb.append(", infraredCamera = " + this.infraredCamera);
        sb.append(", backMonoImage = " + this.backMonoImage);
        sb.append(", binocularImage = " + this.binocularImage);
        sb.append(", miniSRgbd = " + this.miniSRgbd);
        sb.append(", miniRgbd = " + this.miniRgbd);
        sb.append(", ultraSound = " + this.ultraSound);
        return sb.toString();
    }
}
