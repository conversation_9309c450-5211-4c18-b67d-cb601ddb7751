package com.ainirobot.navigationservice.roversdkhelper.maptype;

import android.util.Log;

import androidx.annotation.NonNull;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.navigationservice.commonModule.configuration.ConfigManager;

import java.util.HashMap;
import java.util.Map;

import ninjia.android.proto.CommonProtoWrapper;

/**
 * 地图类型管理
 *
 *
 * <p> 地图类型说明，和地图类型相关的属性包括雷达、标识码、视觉。
 * 雷达，激光地图基础数据，是必须存在的，无论选择哪种地图类型，雷达激光数据都会保存；
 * 标识码，即 target 码，房顶上的标签，帮助机器在运动中实时校准定位；
 * 视觉，视觉信息和标识码作用是一样的，但视觉信息较大，优点是不需要额外贴专用的标识码；
 *
 *
 * <p>
 * 组合类型定义：{@link com.ainirobot.coreservice.client.Definition.MapType}，
 * 本地序列化和服务端上传的都是这个统一的组合类型。
 * <p>
 * 视觉类型定义：{@link com.ainirobot.coreservice.client.Definition.VisionType}，底盘对应值的映射，调用底盘
 * 接口时通过{@link NaviMapTypeMapping}找到映射值传给底盘。
 * <p>
 * 标识码类型定义：{@link com.ainirobot.coreservice.client.Definition.TargetType}，底盘对应值的映射。
 *
 *
 * <p> 2022.12.28 之前版本，不允许视觉和标识码同时存在；
 * | —————————————————————————————————————————————————————————————————————————————————————————
 * | 端上类型  底盘类型  类型描述
 * |  0        无     未知类型（异常处理）
 * |  1        0      纯激光（NORMAL）
 * |  2        3      纯视觉（VISION）
 * |  3        1      猎户码（TARGET_NORMAL）
 * |  4        2      擎朗码（TARGET_CUSTOM_QL）
 * |  5        4      普渡码（TARGET_CUSTOM_PD）
 * | —————————————————————————————————————————————————————————————————————————————————————————
 *
 *
 * <p> 2022.12.28 更新，允许视觉和 target 同时存在的组合，此之前的版本只有“纯视觉”和“激光+标识码”，
 * 并不支持“视觉+标识码”的组合；
 * 底盘定义的地图类型分为两段：
 * 段一，视觉类型：  visionType: 0-none， 1-vision
 * 段二，标识码类型：targetType: 0-none， 1-normal， 2-QL， 3-PD
 * 注意：标识码是必须存在的数据，视觉和标识码都不使用(为 0)则表示“纯激光”
 * 端上映射处理：旧类型（1，2，3，4，5）保留，兼容旧版本地图和未升级新版本的机器；
 * 新增组合类型，组合方式：mapType = visionType*10 + targetType，即视觉类型作为十位数，标识码类型作为个位数；
 * | 所有组合类型如下：
 * | —————————————————————————————————————————————————————————————————————————————————————————
 * | 组合类型描述   底盘视觉类型  底盘标识码类型  组合类型  旧版本类型  新版本支持类型
 * | 纯激光          0           0           0  《==》 1  =====  1
 * | 无视觉+猎户码    0           1           1  《==》 3  =====  3
 * | 无视觉+擎朗码    0           2           2  《==》 4  =====  4
 * | 无视觉+普渡码    0           3           3  《==》 5  =====  5
 * | 纯视觉          1           0           10 《==》 2  =====  2
 * | 视觉+猎户码      1           1           11  =============  11
 * | 视觉+擎朗码      1           2           12  =============  12
 * | 视觉+普渡码      1           3           13  =============  13
 * | —————————————————————————————————————————————————————————————————————————————————————————
 * | 后续扩展方式：已存在类型递增，新增类型占用“百位”、“千位”......
 * | 底盘定义的类型值可能会变，所以 Navi 做一层映射，便于后续对底盘修改类型值的兼容处理；
 * 旧版本兼容方式：对于 <=10 的组合类型，做一次旧版本类型转换（0->1,1->3,2->4,3->5,10->2），
 * 端上扔把 0 作为未知的异常类型处理;
 *
 * <p>
 */
public class MapTypeHelper {
    private static final String TAG = "MapTypeHelper";

    /**
     * 需要兼容旧版本类型的映射表，把新的组合类型映射到旧版本支持的单一类型，即（0->1,1->3,2->4,3->5,10->2）
     */
    private static HashMap<Integer, Integer> OLD_TYPE_COMPATIBLE_MAP = new
            HashMap<Integer, Integer>() {
                {
                    put(0, 1);
                    put(1, 3);
                    put(2, 4);
                    put(3, 5);
                    put(10, 2);
                }
            };

    /**
     * 端上类型 ==》转底盘类型，只在loadMap时调用此方法做转换
     *
     * @param mapType 数据来源：1.数据库 2.mapInfo.json
     * @return 底盘对应的地图类型值
     */
    public static CommonProtoWrapper.MapConfigProto generateLoadMapModeProto(int mapType) {
        Log.d(TAG, "generateMapModeProto: mapType=" + mapType);
        NaviMapType naviMapType = toNaviMapType(mapType);
        Log.d(TAG, "generateMapModeProto: ----> Origin naviMapType=" + naviMapType);
        naviMapType = fixMapTypeWithProductInfo(naviMapType);
        Log.d(TAG, "generateMapModeProto: ----> Fixed naviMapType=" + naviMapType);
        CommonProtoWrapper.MapConfigProto proto = naviTypeToProto(naviMapType);
        Log.d(TAG, "generateMapModeProto: 加载地图接口传给底盘的类型: mapConfigProto=" +
                getMapConfigProtoString(proto));
        return proto;
    }

    /**
     * 根据不同平台修改地图类型。
     * <p>注意：此转换逻辑，只在设置给底盘时使用；
     * <p>增加此逻辑的初期原因是：不同类型机器使用同一张地图跑多机，
     * 但底盘在不同硬件机型上可支持的地图类型是有限制的，选择让机器端做一层适配逻辑；
     *
     * <p>旧的兼容逻辑
     * 1. mini非topIr版本，把标识码和视觉图按照纯激光加载；
     * 2. 招财豹、豹小秘Plus版本，把视觉和纯激光图按照猎户码加载；
     * 3. 擎朗、普渡码地图，如果服务端禁止使用，则按照纯激光模式加载；
     * 4. 消毒豹，开启多楼层，任何类型的地图都加载为纯激光；
     *
     * <p>新的兼容逻辑，2023.3.1之后版本。
     * 只根据本机型兼容规则对地图类型进行降级处理
     *
     * @param naviMapType
     * @return
     */
    private static NaviMapType fixMapTypeWithProductInfo(NaviMapType naviMapType) {
        Log.d(TAG, "fixMapTypeWithProductInfo: mSupportVisionTypes=" +
                ConfigManager.sSupportVisionTypes.toString() + " mSupportTargetTypes=" +
                ConfigManager.sSupportTargetTypes.toString());
        if (!ConfigManager.sSupportVisionTypes.contains(naviMapType.getVisionType())) {
            Log.d(TAG, "fixMapTypeWithProductInfo: ----> Set vision none!");
            naviMapType.setVisionType(Definition.VisionType.TYPE_NONE.getValue());
        }
        if (!ConfigManager.sSupportTargetTypes.contains(naviMapType.getTargetType())) {
            Log.d(TAG, "fixMapTypeWithProductInfo: ----> Set target none!");
            naviMapType.setTargetType(Definition.TargetType.TYPE_TARGET_NONE.getValue());
        }
        return naviMapType;
    }

    private static String getMapConfigProtoString(CommonProtoWrapper.MapConfigProto mapConfigProto) {
        if (mapConfigProto == null) {
            return "Null";
        }
        return mapConfigProto.getVisionMode() + " & " + mapConfigProto.getTargetMode();
    }

    /**
     * 开始建图，把手动设置的组合类型转为传递给底盘。
     *
     * @param naviMapType 数据来源：MapTool新建地图业务传过来的组合类型
     * @return
     */
    public static CommonProtoWrapper.MapConfigProto generateMapModeProtoByNaviMapType(NaviMapType naviMapType) {
        Log.d(TAG, "generateMapModeProtoByNaviMapType: naviMapType=" + naviMapType);
        int mapType = toRobotMapType(naviMapType);//generateMapModeProtoByNaviMapType
        Log.d(TAG, "generateMapModeProtoByNaviMapType: mapType=" + mapType);
        CommonProtoWrapper.MapConfigProto mapConfigProto = mapTypeToProto(mapType);
        Log.d(TAG, "generateMapModeProtoByNaviMapType: mapConfigProto=" +
                (mapConfigProto != null ? mapConfigProto.toString() : "null"));
        return mapConfigProto;
    }

    public static CommonProtoWrapper.MapConfigProto generateMapModeProtoByNaviMapType(int mapType) {
        Log.d(TAG, "generateMapModeProtoByNaviMapType: mapType=" + mapType);
        CommonProtoWrapper.MapConfigProto mapConfigProto = mapTypeToProto(mapType);
        Log.d(TAG, "generateMapModeProtoByNaviMapType: mapConfigProto=" +
                (mapConfigProto != null ? mapConfigProto.toString() : "null"));
        return mapConfigProto;
    }

    /**
     * 结束建图，生成本地序列化存储的类型
     *
     * @param naviMapType 数据来源：MapTool新建地图业务传过来的组合类型
     * @return
     */
    public static int generateMapTypeToStore(NaviMapType naviMapType) {
        Log.d(TAG, "generateMapTypeToStore: 新建地图业务选择类型=" + naviMapType);
        int mapType = toRobotMapType(naviMapType);//generateMapTypeToStore
        Log.d(TAG, "generateMapTypeToStore: 本地序列化存储类型=" + mapType);
        return mapType;
    }

    /**
     * 组合类型 ==》机器端业务使用类型
     *
     * @param naviMapType 组合类型，机器端值
     * @return
     */
    private static int toRobotMapType(NaviMapType naviMapType) {
        Log.d(TAG, "toRobotMapType:");
        int robotType = Definition.MapType.TYPE_NORMAL.getValue();
        if (naviMapType == null) return robotType;
        Log.d(TAG, "toRobotMapType: 组合类型:naviMapType=" + naviMapType);

        int visionType = naviMapType.getVisionType();
        int targetType = naviMapType.getTargetType();
        int mapType = (visionType * 10 + targetType);
        Log.d(TAG, "toRobotMapType: 组合后数值:mapType=" + mapType);

        if (OLD_TYPE_COMPATIBLE_MAP.containsKey(mapType)) {
            //兼容旧版本类型，映射到旧的单一类型
            robotType = OLD_TYPE_COMPATIBLE_MAP.get(mapType);
        } else {
            //新版本增加的组合类型
            robotType = mapType;
        }
        Log.d(TAG, "toRobotMapType: 组合后最终取值:robotType=" + robotType);
        return robotType;
    }

    /**
     * 机器端业务使用类型 ==》组合类型
     *
     * @param robotMapType 机器端数据库以及上传服务端的本地序列化值
     * @return 组合类型，机器端值
     */
    public static NaviMapType toNaviMapType(int robotMapType) {
        Log.d(TAG, "toNaviMapType: 本地序列化值，robotMapType=" + robotMapType);
        NaviMapType naviMapType = new NaviMapType();
        int mapType;
        if (OLD_TYPE_COMPATIBLE_MAP.containsValue(robotMapType)) {
            mapType = getMapKey(OLD_TYPE_COMPATIBLE_MAP, robotMapType);
        } else {
            mapType = robotMapType;
        }
        Log.d(TAG, "toNaviMapType: 组合类型数值，mapType=" + mapType);
        int visionType = mapType / 10 % 10;
        int targetType = mapType % 10;
        Log.d(TAG, "toNaviMapType: 用于底盘和业务的组合类型: visionType=" + visionType +
                " targetType=" + targetType);
        naviMapType.setVisionType(visionType).
                setTargetType(targetType);
        return naviMapType;
    }

    /**
     * 检测机器和地图是否都支持视觉
     *
     * @param robotMapType 地图的type，机器端数据库以及上传服务端的本地序列化值，
     * @return
     */
    public static boolean checkRobotAndMapSupportVision(int robotMapType) {
        NaviMapType naviMapType = toNaviMapType(robotMapType);
        return ConfigManager.sSupportVisionTypes.contains(Definition.VisionType.TYPE_VISION.getValue()) && naviMapType.hasVision();
    }

    /**
     * 检测机器和地图是否都支持target
     *
     * @param robotMapType 地图的type，机器端数据库以及上传服务端的本地序列化值，
     * @return
     */
    public static boolean checkRobotAndMapSupportTarget(int robotMapType) {
        NaviMapType naviMapType = toNaviMapType(robotMapType);
        return ConfigManager.sSupportTargetTypes.contains(naviMapType.getTargetType()) && naviMapType.hasTarget();
    }

    /**
     * 地图类型是否包含视觉
     *
     * @param robotMapType 机器端数据库以及上传服务端的本地序列化值，
     * @return
     */
    public static boolean isTypeSupportVision(int robotMapType) {
        Log.d(TAG, "isTypeSupportVision: robotMapType=" + robotMapType);
        NaviMapType naviMapType = toNaviMapType(robotMapType);//isTypeSupportVision
        Log.d(TAG, "isTypeSupportVision: naviMapType=" + naviMapType);
        return naviMapType.hasVision();
    }

    /**
     * 地图类型是否包含target
     *
     * @param robotMapType 机器端数据库以及上传服务端的本地序列化值，
     * @return
     */
    public static boolean isTypeSupportTarget(int robotMapType) {
        Log.d(TAG, "isTypeSupportTarget: robotMapType=" + robotMapType);
        NaviMapType naviMapType = toNaviMapType(robotMapType);//isTypeSupportTarget
        Log.d(TAG, "isTypeSupportTarget: naviMapType=" + naviMapType);
        return naviMapType.hasTarget();
    }

    /**
     * 机器端整体类型==》底盘组合类型
     *
     * @param mapType
     * @return
     */
    @NonNull
    private static CommonProtoWrapper.MapConfigProto mapTypeToProto(int mapType) {
        Log.d(TAG, "mapTypeToProto: mapType=" + mapType);
        NaviMapType naviMapType = toNaviMapType(mapType);//mapTypeToProto
        return naviTypeToProto(naviMapType);
    }

    private static CommonProtoWrapper.MapConfigProto naviTypeToProto(NaviMapType naviMapType) {
        Log.d(TAG, "naviTypeToProto: naviMapType=" + naviMapType);
        //获取底盘真实映射值
        int realNaviVisionMode = NaviMapTypeMapping.
                fromRobotVisionType(naviMapType.getVisionType()).getNaviType();
        int realNaviTargetMode = NaviMapTypeMapping.
                fromRobotTargetType(naviMapType.getTargetType()).getNaviType();
        Log.d(TAG, "naviTypeToProto: realNaviVisionMode=" + realNaviVisionMode +
                " realNaviTargetMode=" + realNaviTargetMode);
        //创建proto
        CommonProtoWrapper.MapConfigProto proto = CommonProtoWrapper.MapConfigProto.
                newBuilder().
                setVisionMode(CommonProtoWrapper.MapConfigProto.VisionModeProto.
                        forNumber(realNaviVisionMode)).
                setTargetMode(CommonProtoWrapper.MapConfigProto.TargetModeProto.
                        forNumber(realNaviTargetMode)).
                build();
        Log.d(TAG, "naviTypeToProto: mapConfigProto=" + getMapConfigProtoString(proto));
        return proto;
    }

    /**
     * 是否开启了电梯功能
     * added-by-sixiangqian-2022.07.13
     */
    private static boolean isEnableMultiNavigation() {
        return RobotSettingApi.getInstance().
                getRobotInt(Definition.ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED) ==
                Definition.ROBOT_SETTING_ENABLE;
    }

    /**
     * 根据 HashMap value 值获取 key 值。（不考虑多个 key 值对应同一个 value 值情况）
     */
    private static int getMapKey(Map<Integer, Integer> map, int value) {
        for (int key : map.keySet()) {
            int curValue = map.get(key);
            if (curValue == value) {
                return key;
            }
        }
        return -1;
    }

}
