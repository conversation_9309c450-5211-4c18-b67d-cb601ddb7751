package com.ainirobot.navigationservice.db.helper.sqlite;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import androidx.annotation.Nullable;

import com.ainirobot.navigationservice.db.entity.ExtraInfo;
import com.ainirobot.navigationservice.db.helper.iml.ExtraInfoHelperIml;
import com.ainirobot.navigationservice.db.sqlite.TableInfoDef;
import com.ainirobot.navigationservice.db.sqlite.SqliteDbMigrate;

import java.util.List;
import java.util.Map;

public class ExtraInfoSqliteHelper extends BaseSqliteHelper<ExtraInfo> implements ExtraInfoHelperIml {

    public ExtraInfoSqliteHelper(SqliteDbMigrate sqliteDbMigrate) {
        super(sqliteDbMigrate, TableInfoDef.TABLE_NAME_EXTRA_INFO);
    }

    @Override
    protected Map<String, Integer> updateCursorIndexMap(Cursor cursor) {
        return sqliteDbMigrate.getExtraInfoIndex(cursor);
    }

    @Override
    public boolean deleteExtraData(String mapName) {
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        int rowsDeleted = writeDb.delete(mTableName, TableInfoDef.COLUMN_MAP_NAME + " = ?", new String[]{mapName});
        return rowsDeleted > 0;
    }

    @Nullable
    @Override
    public ExtraInfo getExtraInfo(String mapName) {
        SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
        Cursor cursor = null;
        ExtraInfo extraInfo = null;
        try {
            cursor = readDb.query(mTableName, null, TableInfoDef.COLUMN_MAP_NAME + " = ?", new String[]{mapName}, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                extraInfo = sqliteDbMigrate.cursorToExtraInfo(cursor, getCursorIndexMap(cursor));
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return extraInfo;
    }

    @Override
    public void initExtraInfoData(List<ExtraInfo> extraInfoList) {
        if (extraInfoList == null || extraInfoList.isEmpty()) {
            Log.d(TAG, "initExtraInfoData: list is null");
            return;
        }
        Log.d(TAG, "initExtraInfoData: start");
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        writeDb.beginTransaction();
        try {
            writeDb.delete(mTableName, null, null);
            for (ExtraInfo extraInfo : extraInfoList) {
                ContentValues values = sqliteDbMigrate.extraInfoToContentValues(extraInfo);
                long rowId = writeDb.insert(mTableName, null, values);
                Log.d(TAG, "initExtraInfoData: insert rowId = " + rowId + " extraInfo = " + extraInfo);
            }
            writeDb.setTransactionSuccessful();
        } finally {
            writeDb.endTransaction();
        }
    }

    @Override
    public boolean updateExtraInfo(ExtraInfo extraInfo) {
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        ContentValues values = sqliteDbMigrate.extraInfoToContentValues(extraInfo);
        String selection = TableInfoDef.COLUMN_MAP_NAME + " = ?";
        String[] selectionArgs = {String.valueOf(extraInfo.getMapName())};
        // 尝试更新数据
        int rowsUpdated = writeDb.update(mTableName, values, selection, selectionArgs);
        // 如果更新失败（没有记录被更新），尝试插入新记录
        if (rowsUpdated == 0) {
            long rowId = writeDb.insert(mTableName, null, values);
            Log.d(TAG, "updateExtraInfo: insert rowId = " + rowId + " extraInfo = " + extraInfo);
            return rowId != -1;
        } else {
            Log.d(TAG, "updateExtraInfo: update extraInfo = " + extraInfo);
            return true;
        }
    }
}
