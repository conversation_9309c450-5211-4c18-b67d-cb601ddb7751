package com.ainirobot.navigationservice.db.helper.objectbox;

import android.util.Log;

import com.ainirobot.navigationservice.db.entity.PlaceName;
import com.ainirobot.navigationservice.db.entity.PlaceName_;
import com.ainirobot.navigationservice.db.entity.PlaceType;
import com.ainirobot.navigationservice.db.helper.iml.PlaceTypeHelperIml;

import java.util.ArrayList;
import java.util.List;

import io.objectbox.Box;
import io.objectbox.BoxStore;
import io.objectbox.query.Query;

public class PlaceTypeObjectHelper extends BaseObjectHelper<PlaceType> implements PlaceTypeHelperIml {
    private final String[][] specialPlaces = {
            {"1", "Charging Point", "回充点"}, {"2", "Charging Pole", "充电桩"},
            {"3", "Stand-by Point", "待机点"}, {"4", "Positioning Point", "定位点"},
            {"5", "Elevator Center", "电梯中心"}, {"6", "Elevator Gate", "电梯门"},
            {"7", "Elevator Entrance", "电梯口"}, {"8", "Reception Point", "接待点"},
            {"9", "Gate Entrance", "闸机入口"}, {"10", "Gate Exit", "闸机出口"}};

    public PlaceTypeObjectHelper(BoxStore boxStore) {
        super(boxStore);
    }

    public void initPlaceTypeData() {
        List<PlaceType> placeTypeList = createPlaceTypeList();
        Log.d(TAG, "initPlaceTypeData: start");
        Box<PlaceType> placeTypeBox = getBox();
        placeTypeBox.removeAll();
        placeTypeBox.put(placeTypeList);
        Log.d(TAG, "initPlaceTypeData: " + placeTypeBox.count());
    }

    @Override
    public void updatePlaceTypeList() {
        Box<PlaceType> placeTypeBox = getBox();
        Box<PlaceName> placeNameBox = boxStore.boxFor(PlaceName.class);
        Query<PlaceType> query = placeTypeBox.query().build();
        List<PlaceType> placeTypeList = query.find();
        for (PlaceType placeType : placeTypeList) {
            Query<PlaceName> queryPlaceName = placeNameBox
                    .query(PlaceName_.placeName.equal(placeType.getTypeName())
                            .and(PlaceName_.languageType.equal("en_US")))
                    .build();
            List<PlaceName> placeNameList = queryPlaceName.find();
            for (PlaceName placeName : placeNameList) {
                if (placeName != null) {
                    placeType.placeNameToMany.add(placeName);
                    //打印关联信息
                    Log.d(TAG, "updatePlaceTypeList:Debug: " + placeName + " <-> " + placeType.getTypeId());
                }
            }
            placeTypeBox.put(placeType);
            queryPlaceName.close();
        }
        query.close();
    }

    private List<PlaceType> createPlaceTypeList() {
        List<PlaceType> placeTypeList = new ArrayList<>();
        for (String[] specialPlace : specialPlaces) {
            int typeId = Integer.parseInt(specialPlace[0]);
            String typeName = specialPlace[1];
            String typeDescription = specialPlace[2];
            PlaceType placeType = new PlaceType(typeId, typeName, typeDescription);
            placeTypeList.add(placeType);
        }
        return placeTypeList;
    }

}
