/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.waiter;

import com.ainirobot.coreservice.client.Definition;

import ninjia.android.proto.RoadGraphProtoWrapper;

public class RoadGraphEdge {

    private int id;
    /**
     * 0为默认 1为前行 2为后退  3为多通
     */
    private int rule;
    private int node_start_id;
    private int node_end_id;
    /**
     * 巡线特殊区域标记
     */
    private int scene_type = 0;
    /**
     * WARNING 由于服务端设置的speed值问题导致该字段废弃，用linear_speed_limit替代
     */
    private double speed_limit;
    /**
     * WARNING 由于服务端设置的road_width值问题导致该字段废弃，用line_width字段替代
     */
    private double road_width;
    private double linear_speed_limit;
    private double line_width;

    private double parking_cost = 0;
    private double retrograde_cost = 5;

    public RoadGraphEdge() {

    }

    public RoadGraphEdge(int id, int rule, int node_start_id, int node_end_id, double speed_limit
            , double road_width, double linear_speed_limit, double line_width, double retrograde_cost, double parking_cost) {
        this.id = id;
        this.rule = rule;
        this.node_start_id = node_start_id;
        this.node_end_id = node_end_id;
        this.speed_limit = speed_limit;
        this.road_width = road_width;
        this.linear_speed_limit = linear_speed_limit;
        this.line_width = line_width;
        this.retrograde_cost = retrograde_cost;
        this.parking_cost = parking_cost;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getRule() {
        return rule;
    }

    public void setRule(int rule) {
        this.rule = rule;
    }

    public int getNode_start_id() {
        return node_start_id;
    }

    public void setNode_start_id(int node_start_id) {
        this.node_start_id = node_start_id;
    }

    public int getNode_end_id() {
        return node_end_id;
    }

    public void setNode_end_id(int node_end_id) {
        this.node_end_id = node_end_id;
    }

    public double getSpeed_limit() {
        return speed_limit;
    }

    public void setSpeed_limit(double speed_limit) {
        this.speed_limit = speed_limit;
    }

    public double getRoad_width() {
        return road_width;
    }

    public void setRoad_width(double road_width) {
        this.road_width = road_width;
    }

    public double getLinear_speed_limit() {
        return linear_speed_limit;
    }

    public void setLinear_speed_limit(double linear_speed_limit) {
        this.linear_speed_limit = linear_speed_limit;
    }

    public double getLine_width() {
        return line_width;
    }

    public void setLine_width(double line_width) {
        this.line_width = line_width;
    }

    public double getRetrograde_cost() {
        return retrograde_cost;
    }

    public void setRetrograde_cost(double retrograde_cost) {
        this.retrograde_cost = retrograde_cost;
    }

    public double getParking_cost() {
        return parking_cost;
    }

    public void setParking_cost(double parking_cost) {
        this.parking_cost = parking_cost;
    }

    public RoadGraphProtoWrapper.RoadGraphEdgeProto.SceneType getScene_type() {
        switch (scene_type) {
            case Definition.SCENE_LARGE_PEOPLE:
                return RoadGraphProtoWrapper.RoadGraphEdgeProto.SceneType.kSceneLargePeople;
            default:
                return RoadGraphProtoWrapper.RoadGraphEdgeProto.SceneType.kSceneNormal;
        }
    }

    public void setScene_type(int scene_type) {
        this.scene_type = scene_type;
    }

    @Override
    public String toString() {
        return "RoadGraphEdge{" +
                "id=" + id +
                ", rule=" + rule +
                ", node_start_id=" + node_start_id +
                ", node_end_id=" + node_end_id +
                ", speed_limit=" + speed_limit +
                ", road_width=" + road_width +
                ", linear_speed_limit=" + linear_speed_limit +
                ", line_width=" + line_width +
                ", retrograde_cost=" + retrograde_cost +
                ", parking_cost=" + parking_cost +
                ", scene_type=" + scene_type +
                '}';
    }
}
