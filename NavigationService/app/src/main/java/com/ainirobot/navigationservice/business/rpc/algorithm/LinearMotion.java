package com.ainirobot.navigationservice.business.rpc.algorithm;

import android.util.Log;

import com.ainirobot.navigationservice.business.rpc.SpeedBean;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * single linear speed change
 * <p>
 * mode 1:
 * <p>
 * max acc:0.1
 * unit time: 125
 * <p>
 * mode 2:
 * <p>
 * max acc:0.15
 * <p>
 * unit time: 180
 *
 * @version V1.0.0
 * @date 2019/11/15 15:00
 */
public class LinearMotion extends MotionAlgorithm {

    private static final int INCREASE_SPEED_TIME_PERIOD = 180;
    private static final String LINEAR_SPEED_MAX_ACC = "0.15";
    private static final String MAX_INCREASE_TIMES = "5";

    private static final BigDecimal MAX_ACC = new BigDecimal(LINEAR_SPEED_MAX_ACC);
    private static final BigDecimal ACC_TIMES = new BigDecimal(MAX_INCREASE_TIMES);
    private volatile boolean isCancel = false;

    public LinearMotion(SpeedBean target) {
        super(target);
    }

    private BigDecimal calculateAcc(BigDecimal diff) {
        BigDecimal acc = diff.divide(ACC_TIMES, 2, RoundingMode.UP);
        int compare = acc.abs().compareTo(MAX_ACC);
        if (compare > 0) {
            Log.i(TAG, "calculateAcc: acc more than max :" + acc);
            acc = acc.compareTo(BigDecimal.ZERO) >= 0 ? MAX_ACC : MAX_ACC.negate();
        }
        Log.i(TAG, "calculateAcc:acc=" + acc);
        return acc;
    }

    private BigDecimal calculateAccTimes(BigDecimal acc, BigDecimal diff) {
        if (acc.abs().compareTo(MAX_ACC) < 0) {
            return ACC_TIMES;
        }
        return diff.abs().divide(MAX_ACC, 0, RoundingMode.UP);
    }

    @Override
    public void motion() {
        BigDecimal tar = target.getLinearSpeed();
        BigDecimal angularSpeed = target.getAngularSpeed();
        BigDecimal cur = current.getLinearSpeed();

        BigDecimal diff = tar.subtract(cur);
        if (diff.abs().compareTo(MAX_ACC) <= 0) {
            motion(tar.floatValue(), angularSpeed.floatValue());
            Log.i(TAG, "speed diff=" + diff);
            return;
        }
        BigDecimal acc = calculateAcc(diff);
        Log.i(TAG, "motion: acc=" + acc);
        BigDecimal accTimes = calculateAccTimes(acc, diff);
        Log.i(TAG, "motion: accTimes:" + accTimes.floatValue());

        for (int i = 0; i < accTimes.intValue() && !this.isCancel; i++) {
            cur = cur.add(acc);
            if (i == accTimes.intValue() - 1) {
                Log.i(TAG, "motion: last acc");
                cur = tar;
            }
            Log.i(TAG, "motion: currentLS=" + cur);
            motion(cur.floatValue(), angularSpeed.floatValue());
            try {
                Thread.sleep(INCREASE_SPEED_TIME_PERIOD);
            } catch (InterruptedException interruptedException) {
                isCancel = true;
                Log.i(TAG, "motion: LinearMotion interrupted!");
            }
        }
    }
}
