package com.ainirobot.navigationservice.business;

import static com.ainirobot.coreservice.client.Definition.FAILED;
import static com.ainirobot.coreservice.client.Definition.JSON_NAVI_IS_FRONT_CHARGE;
import static com.ainirobot.coreservice.client.Definition.JSON_NAVI_IS_REVERSE_POSE_THETA;
import static com.ainirobot.coreservice.client.Definition.JSON_NAVI_PRIORITY;
import static com.ainirobot.coreservice.client.Definition.NAVI_OTA_UPDATE_DONE;
import static com.ainirobot.coreservice.client.Definition.POSE;
import static com.ainirobot.coreservice.client.Definition.SUCCEED;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.MOTION_AVOID_STOP;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.START_EXPANSION_MAP_FAILED;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.STOP_EXPANSION_MAP_FAILED;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.SUCCESS;
import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.bean.GateLineNode;
import com.ainirobot.coreservice.bean.LoadMapBean;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.actionbean.GateLineUnit;
import com.ainirobot.coreservice.client.actionbean.GatePairPose;
import com.ainirobot.coreservice.client.actionbean.StartCreateMapBean;
import com.ainirobot.coreservice.client.actionbean.StopCreateMapBean;
import com.ainirobot.coreservice.client.hardware.RobotCore;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.navigationservice.Defs.Def;
import com.ainirobot.navigationservice.beans.MappingPose;
import com.ainirobot.navigationservice.beans.tk1.BaseEvent;
import com.ainirobot.navigationservice.beans.tk1.NavAcceleration;
import com.ainirobot.navigationservice.beans.tk1.NavMode;
import com.ainirobot.navigationservice.beans.tk1.NavVelocity;
import com.ainirobot.navigationservice.beans.tk1.PackLogBean;
import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.tk1.TargetPose;
import com.ainirobot.navigationservice.beans.tk1.Velocity;
import com.ainirobot.navigationservice.beans.waiter.GatePathPrepareBean;
import com.ainirobot.navigationservice.beans.waiter.HumanFollowBean;
import com.ainirobot.navigationservice.beans.waiter.MultiRobotConfigBean;
import com.ainirobot.navigationservice.beans.waiter.NaviPathDetail;
import com.ainirobot.navigationservice.beans.waiter.NaviPathInfo;
import com.ainirobot.navigationservice.beans.waiter.Vector2d;
import com.ainirobot.navigationservice.chassisAbility.ChassisManager;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.tk1.customListener.CreateMapStop;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.tk1.customListener.StartCreateMapListener;
import com.ainirobot.navigationservice.chassisAbility.ota.client.IOtaClient;
import com.ainirobot.navigationservice.commonModule.bi.BiManager;
import com.ainirobot.navigationservice.commonModule.bi.report.MotionCallChainReporter;
import com.ainirobot.navigationservice.commonModule.data.DataManager;
import com.ainirobot.navigationservice.db.NavigationDataManager;
import com.ainirobot.navigationservice.db.entity.GateRelationInfo;
import com.ainirobot.navigationservice.db.entity.MapInfo;
import com.ainirobot.navigationservice.db.entity.PlaceInfo;
import com.ainirobot.navigationservice.roversdkhelper.MapFileHelper;
import com.ainirobot.navigationservice.roversdkhelper.maptype.MapTypeHelper;
import com.ainirobot.navigationservice.roversdkhelper.maptype.NaviMapType;
import com.ainirobot.navigationservice.utils.GsonUtil;
import com.ainirobot.navigationservice.utils.MapUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import ninjia.android.proto.UpdateRawImageProtoWrapper;

public class ChassisRelyApiImpl implements ChassisRelyApi {
    private final static String TAG = TAGPRE + ChassisRelyApiImpl.class.getSimpleName();

    private static final String ROBOT_SETTINGS_ISADJUSTANGLE = "robot_settings_isAdjustAngle";

    private IChassisClient chassisClient;

    private IOtaClient otaClient;

    private BiManager biManager;

    private Context mContext;

    private volatile boolean mHasReport;

    private Gson mGson;

    private final String PARAMS_ERROR = "params error";

    private final String PATROL_FINISHED = "finished";
    private final String PATROL_ABORTED = "aborted";
    private final String PATROL_CANCELED = "canceled";

    private static final String MATCH_ERR = "match_err";
    private static final String CHECK_ERR = "check_err";
    private static final String POSE_NULL = Definition.RESULT_ESTIMATE_FAIL_POSE_NULL;

    public ChassisRelyApiImpl() {
    }

    @Override
    public void init(Context mContext) {
        this.mContext = mContext;
        chassisClient = ChassisManager.getInstance().getChassisClient();
        otaClient = ChassisManager.getInstance().getOtaClient();
        biManager = BiManager.getInstance();
        mGson = new Gson();
    }

    @Override
    public boolean removeMap(final String cmdType, final String mapName) {
        Log.d(TAG, "Command removeMap:" + mapName);
        boolean succeed = removeMapToolMap(mapName);
        Log.d(TAG, "removeMap: result=" + succeed);
        sendAsyncResponse(cmdType, succeed ? SUCCEED : FAILED);
        return true;
    }

    @Override
    public boolean goLocation(String cmdType, String cmdParam) {
        Log.d(TAG, "Command go location custom speed isReady : " + isChassisReady());
        if (!isChassisReady()) {
            sendAsyncResponse(cmdType, "not ready");
            return false;
        }

        Pose pose = stringToDestination(cmdParam);
        if (pose == null) {
            sendAsyncResponse(cmdType, "not exits");
            return false;
        }
        NavVelocity navVelocity = stringToNavVelocity(cmdParam);
        boolean adjustAngle = getIsAdjustAngle(cmdParam);
        Log.d(TAG, "goLocation_getIsAdjustAngle:" + adjustAngle);
        double desRange = getNavigationDestinationRange(cmdParam);
        double obsDistance = getNavigationObsDistance(cmdParam);
        int priority = getTaskPriority(cmdParam);
        NavAcceleration navAcc = stringToAcceleration(cmdParam);
        NavMode navMode = stringToNavMode(cmdParam);
        double posTolerance = getPosTolerance(cmdParam);
        double angleTolerance = getAngleTolerance(cmdParam);
        boolean isReversePoseTheta = getIsReversePoseTheta(cmdParam);
        if (isReversePoseTheta) {
            double theta = pose.getTheta();
            theta = theta > 0 ? theta - Math.PI : theta + Math.PI;
            pose.setTheta(((float) theta));
        }
        int roadMode = getRoadMode(cmdParam);

        goNormal(cmdType, pose, navVelocity, adjustAngle, false,
                TargetPose.TURN_DEFAULT, desRange, obsDistance, priority, navAcc, navMode,
                posTolerance, angleTolerance, roadMode);
        return true;
    }

    @Override
    public boolean cancelNavigation(final String cmdType) {
        Log.d(TAG, "Command cancel navigation");
        chassisClient.cancelNavigation(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                sendAsyncResponse(cmdType, SUCCEED);
            }
        });
        return true;
    }

    @Override
    public boolean stopMove(String cmdType) {
        Log.d(TAG, "Command stop move");
        chassisClient.stopMove();
        sendAsyncResponse(cmdType, SUCCEED);
        biManager.setCmd(cmdType);
        return true;
    }

    @Override
    public boolean startPatrol(final String cmdType, final String params) {
        biManager.setCmd(cmdType);
        Log.d(TAG, "Command start patrol");
        Type type = new TypeToken<List<Pose>>() {
        }.getType();

        List<Pose> points;
        try {
            JSONObject json = new JSONObject(params);
            points = mGson.fromJson(json.getString("content"), type);
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, "Command start patrol params : " + params);
            sendAsyncResponse(cmdType, PARAMS_ERROR);
            return false;
        }

        Log.d(TAG, "Command start patrol point size : " + points.size());
        startPatrol(points, false, new PatrolListener() {
            @Override
            public void onNext(Pose pose) {
                Log.d(TAG, "Patrol next");
            }

            @Override
            public void onFinish() {
                Log.d(TAG, "Patrol finished");
                sendAsyncResponse(cmdType, PATROL_FINISHED);
            }

            @Override
            public void onCanceled() {
                Log.d(TAG, "Patrol canceled");
                sendAsyncResponse(cmdType, PATROL_CANCELED);
            }

            @Override
            public void onAborted() {
                Log.d(TAG, "Patrol aborted");
                sendAsyncResponse(cmdType, PATROL_ABORTED);
            }
        });
        return true;
    }

    @Override
    public boolean stopPatrol(String cmdType) {
        Log.d(TAG, "Command stop patrol");
        biManager.setCmd(cmdType);
        mPatrolRoute.clear();
        chassisClient.cancelNavigation(null);
        sendAsyncResponse(cmdType, SUCCEED);
        return true;
    }

    @Override
    public boolean resetLocation(String cmdType, String params) {
        Log.d(TAG, "Command reset location");
        Pose pose = stringToPose(params);
        if (pose != null) {
            chassisClient.setPoseEstimate(pose, null);
            return true;
        }
        biManager.setCmd(cmdType);
        sendAsyncResponse(cmdType, PARAMS_ERROR);
        return false;
    }

    @Override
    public boolean rotateInPlace(String cmdType, String cmdParam) {
        Log.d(TAG, "rotateInPlace: cmdType=" + cmdType + " cmdParam=" + cmdParam);
        try {
            JSONObject json = new JSONObject(cmdParam);
            int direction = json.optInt(Definition.JSON_NAVI_DERICTION);
            double distance = json.optDouble(Definition.JSON_NAVI_DISTANCE);
            double speed = json.optDouble(Definition.JSON_NAVI_LINEAR_SPEED);
            if (Double.compare(speed, Math.PI) > 0) {
                Log.d(TAG, "rotateInPlace: Speed too fast");
                sendAsyncResponse(cmdType, "Speed too fast");
                return false;
            }

            IChassisClient.ChassisResListener listener = new IChassisClient.ChassisResListener() {
                @Override
                public void onResponse(boolean status, int resultCode, Object result) {
                    if (resultCode == MOTION_AVOID_STOP) {
                        sendAsyncResponse(cmdType, Definition.ERROR_MOTION_AVOID_STOP);
                    } else {
                        sendAsyncResponse(cmdType, status ? SUCCEED : FAILED);
                    }
                }
            };

            chassisClient.rotateInPlace(direction, distance, speed, listener);

            return true;
        } catch (JSONException e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, PARAMS_ERROR);
        }
        return false;
    }

    @Override
    public boolean moveDirection(final String cmdType, final String params) {
        Log.d(TAG, "Command move direction : " + params);
        boolean noNeedAcceleration = false;
        try {
            JSONObject json = new JSONObject(params);
            String direction = json.optString(Definition.JSON_NAVI_DERICTION);
            double distance = json.optDouble(Definition.JSON_NAVI_DISTANCE);
            double speed = json.optDouble(Definition.JSON_NAVI_LINEAR_SPEED);
            boolean isWakeUpAction = json.optBoolean(Definition.JSON_NAVI_WAKEUP_ACTION);
            boolean avoid = json.optBoolean(Definition.JSON_NAVI_FORWARD_AVOID);
            noNeedAcceleration = json.optBoolean(Definition.JSON_NAVI_NO_NEED_ACCELERATION);
            double acceleration = json.optDouble(Definition.JSON_NAVI_ACCELERATION_VALUE, 0);
            if (Double.compare(speed, Math.PI) > 0) {
                sendAsyncResponse(cmdType, "Speed too fast");
                return false;
            }

            IChassisClient.ChassisResListener listener = new IChassisClient.ChassisResListener() {
                @Override
                public void onResponse(boolean status, int resultCode, Object result) {
                    if (resultCode == MOTION_AVOID_STOP) {
                        sendAsyncResponse(cmdType, Definition.ERROR_MOTION_AVOID_STOP);
                    } else {
                        sendAsyncResponse(cmdType, status ? SUCCEED : FAILED);
                    }
                }
            };

            Log.d(TAG, "isFromWakeUpAciton = " + isWakeUpAction);
            if (isWakeUpAction && isPoseAvailable(chassisClient.getCurrentPose())) {// if come from wakeUpAction, optimize when estimate OK
                Log.d(TAG, "isPoseEstimate = " + chassisClient.isPoseEstimate());
                switch (direction) {
                    case Definition.CMD_NAVI_MOVE_SUB_TURN_LEFT:
                        if (chassisClient.isPoseEstimate()) {
                            Pose curPose = chassisClient.getCurrentPose();
                            double destinationTheta = curPose.getTheta() + distance;
                            Log.d(TAG, "turn left: curPose theta=" + curPose.getTheta() +
                                    " distance=" + distance + " destinationTheta=" + destinationTheta);
                            if (destinationTheta > Math.PI) {
                                destinationTheta = -Math.PI + (destinationTheta - Math.PI);
                            }
                            Log.d(TAG, "turn left: final destinationTheta = " + destinationTheta);

                            curPose.setTheta((float) destinationTheta);
//                            go(cmdType, curPose);
//                            go(cmdType, curPose, null, false, TargetPose.TURN_LEFT);
                            NavVelocity navVelocity = new NavVelocity(0.7, 1.8);
//                            go(cmdType, curPose, navVelocity, false, TargetPose.TURN_LEFT);
                            goPureRotation(cmdType, curPose, navVelocity, false, TargetPose.TURN_DEFAULT);
                        } else {
                            chassisClient.turnLeft(distance, speed, acceleration, noNeedAcceleration, listener);
                        }
                        return true;

                    case Definition.CMD_NAVI_MOVE_SUB_TURN_RIGHT:
                        if (chassisClient.isPoseEstimate()) {
                            Pose curPose = chassisClient.getCurrentPose();
                            double destinationTheta = curPose.getTheta() - distance;
                            Log.d(TAG, "turn right: curPose theta=" + curPose.getTheta() +
                                    " distance=" + distance + " destinationTheta=" + destinationTheta);
                            if (destinationTheta < -Math.PI) {
                                destinationTheta = Math.PI + (destinationTheta + Math.PI);
                            }
                            Log.d(TAG, "turn right: destinationTheta = " + destinationTheta);
                            curPose.setTheta((float) destinationTheta);
//                            go(cmdType, curPose, null, false, TargetPose.TURN_RIGHT);
                            NavVelocity navVelocity = new NavVelocity(0.7, 1.8);
//                            go(cmdType, curPose, navVelocity, false, TargetPose.TURN_RIGHT);
                            goPureRotation(cmdType, curPose, navVelocity, false, TargetPose.TURN_DEFAULT);
                        } else {
                            chassisClient.turnRight(distance, speed, acceleration, noNeedAcceleration, listener);
                        }
                        return true;
                    default:
                        return false;
                }
            }

            switch (direction) {
                case Definition.CMD_NAVI_MOVE_SUB_BACKWARD:
                    chassisClient.backward(distance, speed, acceleration, listener);
                    return true;

                case Definition.CMD_NAVI_MOVE_SUB_FORWARD:
                    chassisClient.forward(distance, speed, acceleration, avoid, listener);
                    return true;

                case Definition.CMD_NAVI_MOVE_SUB_TURN_BACK:
                    chassisClient.turnRight(distance, speed, acceleration, false, listener);
                    return true;

                case Definition.CMD_NAVI_MOVE_SUB_TURN_LEFT:
                    chassisClient.turnLeft(distance, speed, acceleration, noNeedAcceleration, listener);
                    return true;

                case Definition.CMD_NAVI_MOVE_SUB_TURN_RIGHT:
                    chassisClient.turnRight(distance, speed, acceleration, noNeedAcceleration, listener);
                    return true;

                case Definition.CMD_NAVI_MOVE_SUB_ROTATE:
                    chassisClient.turnRight(Double.MAX_VALUE, speed, acceleration, false, listener);
                    return true;

                case Definition.CMD_NAVI_MOVE_SUB_STOP:
                    chassisClient.stopMove();
                    return true;
                default:
                    return false;
            }
        } catch (JSONException e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, PARAMS_ERROR);
            return false;
        }
    }

    @Override
    public boolean motionArc(String params) {
//        Log.d(TAG, "Command motion arc");
        try {
            JSONObject json = new JSONObject(params);
            double distance = json.optDouble(Definition.JSON_NAVI_DISTANCE);
            double angle = json.optDouble(Definition.JSON_NAVI_ANGLE);
            double headAngleSpeed = json.optDouble(Definition.JSON_NAVI_ANGULAR_SPEED);
            double latency = json.optDouble(Definition.JSON_NAVI_LATENCY);
            Velocity navVelocity = chassisClient.getVelocity();
            boolean bodyFollow = json.optBoolean(Definition.JSON_NAVI_BODY_FOLLOW);

            Velocity velocity;
            if (bodyFollow) {
                velocity = chassisClient.getBodyFollowVelocity(distance, angle, headAngleSpeed, navVelocity, latency);
            } else {
                velocity = chassisClient.getFollowVelocity(distance, angle, headAngleSpeed, navVelocity, latency);
            }
            if (velocity != null) {
                chassisClient.motion(velocity.getZ(), velocity.getX(), 0);
            } else {
                chassisClient.stopMove();
            }
            return true;
        } catch (JSONException e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean motionArcWithObstacles(String cmdParam) {
        Log.d(TAG, "Command motionArcWithObstacles, cmdParam=" + cmdParam);
        MotionCallChainReporter.motionArcWithObstaclesReport(TAG + "_motionArcWithObstacles",
                cmdParam);
        try {
            JSONObject json = new JSONObject(cmdParam);
            double distance = json.optDouble(Definition.JSON_NAVI_DISTANCE);
            double angle = json.optDouble(Definition.JSON_NAVI_ANGLE);
            double headAngleSpeed = json.optDouble(Definition.JSON_NAVI_ANGULAR_SPEED);
            double latency = json.optDouble(Definition.JSON_NAVI_LATENCY);
            Velocity navVelocity = chassisClient.getVelocity();
            boolean bodyFollow = json.optBoolean(Definition.JSON_NAVI_BODY_FOLLOW);
            double minDistance = json.optDouble(Definition.JSON_NAVI_MIN_OBSTACLES_DISTANCE);
            double maxLinearSpeed = json.optDouble(Definition.JSON_NAVI_MAX_LINEAR_SPEED);
            double maxAngularSpeed = json.optDouble(Definition.JSON_NAVI_MAX_ANGULAR_SPEED);

            Velocity velocity;
            if (bodyFollow) {
                velocity = chassisClient.getBodyFollowVelocity(distance, angle, latency, headAngleSpeed,
                        navVelocity, maxLinearSpeed, maxAngularSpeed, minDistance);
            } else {
                velocity = chassisClient.getFollowVelocity(distance, angle, headAngleSpeed, navVelocity, latency);
            }

            if (velocity != null) {
                if (bodyFollow) {
                    chassisClient.motionWithStaticObstacles(velocity.getZ(), velocity.getX(), minDistance);
                } else {
                    chassisClient.motionWithObstacles(velocity.getZ(), velocity.getX(), 0, minDistance);
                }
            } else {
                chassisClient.stopMove();
            }
            return true;
        } catch (JSONException e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean startCreatingMap(String cmdType, final String cmdParam) {
        Log.d(TAG, "startCreatingMap: cmdParam=" + cmdParam);
        biManager.setCmd(cmdType);
        MappingPose.init();
        StartCreateMapBean mapInfo = mGson.fromJson(cmdParam, StartCreateMapBean.class);
        NaviMapType naviMapType = new NaviMapType(mapInfo.getVisionType(), mapInfo.getTargetType());
        chassisClient.startCreatingMap(naviMapType, new StartCreateMapListener(cmdType));
        return true;
    }

    @Override
    public boolean stopCreatingMap(final String cmdType, final String cmdParam) {
        Log.d(TAG, "stopCreatingMap: cmdParam=" + cmdParam);
        if (TextUtils.isEmpty(cmdParam)) {
            Log.e(TAG, "stopCreatingMap: Param null");
            sendAsyncResponse(cmdType, "Param null");
            return true;
        }
        StopCreateMapBean bean = mGson.fromJson(cmdParam, StopCreateMapBean.class);
        if (bean == null) {
            Log.e(TAG, "stopCreatingMap: Param error");
            sendAsyncResponse(cmdType, "Param error");
            return true;
        }
        final String mapName = bean.getMapName();
        final String language = bean.getMapLanguage();
        final int visionType = bean.getVisionType();
        final int targetType = bean.getTargetType();
        final int finishState = bean.getFinishState();
        final List<com.ainirobot.coreservice.client.actionbean.Pose> poseList = bean.getSpecialPoseList();
        //结束建图时，mapType 用来本地序列化存储.
        final int mapType = MapTypeHelper.
                generateMapTypeToStore(new NaviMapType(visionType, targetType));//stopCreatingMap

        CreateMapStop createMapStop = new CreateMapStop();
        createMapStop.setResponseListener(new CreateMapStop.ResponseListener() {
            @Override
            public void onResult(boolean result, Object value) {
                Log.d(TAG, "stopCreatingMap:onResult: result=" + result + " value=" + value);
                String resultStr = FAILED;
//                String resultStr = value != null ? value.toString() : FAILED;
                if (result) {
                    try {
                        JSONObject object = new JSONObject();
                        object.put("position",
                                MappingPose.getMappingPoseListJson());
                        resultStr = object.toString();

                        //结束建图并保存地图文件成功之后，存储点位到数据库
                        MappingPose.saveMappingPoseToLocal(
                                NavigationDataManager.getInstance().getMapByName(mapName));
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
                sendAsyncResponse(cmdType, resultStr);
                MappingPose.destroy();//map-info 清空内存中点位列表
            }

            @Override
            public void onStatusUpdate(int status, String value) {
                switch (status) {
                    case CreateMapStop.STATUS_STOP_CREATE_MAP_EXPECTED_TIME:
                        sendAsyncStatus(cmdType, value);
                        break;
                }
            }

            @Override
            public void onSaveMap(boolean saveResult, List<MappingPose> list) {
                Log.d(TAG, "stopCreatingMap:onSaveMap: saveResult=" + saveResult);
                if (!saveResult) {
                    return;
                }
                if(list == null || list.size() == 0){
                    return;
                }
                Log.i(TAG, "stopCreatingMap:onSaveMap: size=" + list.size());
                Log.i(TAG, "stopCreatingMap:onSaveMap: poseList=" + poseList);
                if (poseList != null && !poseList.isEmpty()) {
                    MappingPose.updateSpecialPoses(poseList);
                }
                MappingPose.updatePoses(list);//onSaveMap
            }
        });
        chassisClient.stopCreatingMap(mapName, true, language, mapType, finishState, createMapStop);
        return true;
    }

    @Override
    public boolean cancelCreateMap(final String cmdType, String cmdParam) {
        Log.d(TAG, "cancelCreateMap: mapName=" + cmdParam);
        chassisClient.stopCreatingMap("", false, "", 0, 0, new CreateMapStop() {
            @Override
            public void onResult(boolean result, Object value) {
                Log.d(TAG, "onResult: " + result + ", value = " + value);
                sendAsyncResponse(cmdType, result ? SUCCEED : FAILED);
            }
        });
        return false;
    }

    @Override
    public boolean startExtendMap(final String cmdType, String cmdParam) {
        Log.d(TAG, "startExtendMap: cmdParam=" + cmdParam);
        chassisClient.startExtendMap(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "startExtendMap:onResponse: " + status + " " + resultCode + " " + result);
                if (resultCode == START_EXPANSION_MAP_FAILED) {
                    sendAsyncResponse(cmdType, "expansion_start_failed");
                } else {
                    sendAsyncResponse(cmdType, status ? SUCCEED : FAILED);
                }
            }
        });
        return true;
    }

    @Override
    public boolean stopExtendMap(String cmdType, String cmdParam) {
        Log.d(TAG, "stopExtendMap: mapName=" + cmdParam);
        if (TextUtils.isEmpty(cmdParam)) {
            Log.e(TAG, "stopExtendMap: Param null");
            sendAsyncResponse(cmdType, "Param null");
            return true;
        }
        chassisClient.stopExtendMap(cmdParam, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "stopExtendMap:onResponse: " + status + " " + resultCode + " " + result);
                if (resultCode == STOP_EXPANSION_MAP_FAILED) {
                    sendAsyncResponse(cmdType, resultCode, "expansion_stop_failed");
                } else {
                    sendAsyncResponse(cmdType, resultCode, status ? SUCCEED : FAILED);
                }
            }
        });
        return true;
    }

    @Override
    public boolean setPoseEstimate(final String cmdType, final String value) {
        Log.d(TAG, "Command set pose estimate:" + value);
        Pose pose = null;

        if (!TextUtils.isEmpty(value)) {
            pose = stringToPose(value);
        }

        if (pose == null) {
            Log.d(TAG, "Command set default estimate");
            pose = NavigationDataManager.getInstance().getPoseEstimate();
        }

        if (pose == null) {
            sendAsyncResponse(cmdType, FAILED);
            return true;
        }

        Log.d(TAG, "Command set pose estimate pose:" + pose.toString());
        chassisClient.setPoseEstimate(pose, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                sendAsyncResponse(cmdType, status ? SUCCEED : FAILED);
            }
        });
        return true;
    }

    @Override
    public boolean setFixedEstimate(final String cmdType, final String value) {
        Log.d(TAG, "Command set pose estimate:" + value);
        Pose pose = null;

        if (!TextUtils.isEmpty(value)) {
            pose = stringToPose(value);
        }

        if (pose == null) {
            Log.d(TAG, "Command set default estimate");
            pose = NavigationDataManager.getInstance().getPoseEstimate();
        }

        if (pose == null) {
            sendAsyncResponse(cmdType, POSE_NULL);
            return true;
        }

        Log.d(TAG, "Command set pose estimate pose:" + pose.toString());
        chassisClient.setFixedEstimate(pose, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {

                sendAsyncResponse(cmdType, status ? SUCCEED : getEstimateFailMsg(result));
                biManager.relocationFunctionReport(status, BiManager.CHARGE_PILE, (String) result);
                if (!status) {
                    JSONObject jsonObject = new JSONObject();
                    try {
                        jsonObject.put("msg", "charging_pile_relocation_fail");
                        sendStatusReport(Definition.STATUS_CHARGING_PILE_RELOCATION_EVENT
                                , jsonObject.toString());
                        Log.i(TAG, "ew: charging_pile_relocation_fail");
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
        });
        return true;
    }

    @Override
    public boolean setForceEstimate(final String cmdType, final String value) {
        Log.d(TAG, "Command set force estimate:" + value);
        Pose pose = null;

        if (!TextUtils.isEmpty(value)) {
            pose = stringToPose(value);
        }

        if (pose == null) {
            Log.d(TAG, "Command set default estimate");
            pose = NavigationDataManager.getInstance().getPoseEstimate();
        }

        if (pose == null) {
            sendAsyncResponse(cmdType, POSE_NULL);
            return true;
        }

        Log.d(TAG, "Command set force estimate pose:" + pose.toString());
        chassisClient.setForceEstimate(pose, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                sendAsyncResponse(cmdType, status ? SUCCEED : getEstimateFailMsg(result));
            }
        });
        return true;
    }

    @Override
    public boolean setChassisRelocation(final String cmdType, String cmdParam) {
        Log.d(TAG, "setChassisRelocation:" + cmdParam);
        Pose pose = null;
        int relocationMode = -1;
        if (!TextUtils.isEmpty(cmdParam)) {
            try {
                JSONObject json = new JSONObject(cmdParam);
                relocationMode = json.optInt(Definition.JSON_NAVI_RELOCATION_TYPE, -1);
                JSONObject poseJson = json.optJSONObject(Definition.JSON_NAVI_RELOCATION_POSE);
                if (poseJson != null) {
                    float x = poseJson.has("x") ? Float.valueOf(poseJson.get("x").toString())
                            : Float.valueOf(poseJson.get("px").toString());
                    float y = poseJson.has("y") ? Float.valueOf(poseJson.get("y").toString())
                            : Float.valueOf(poseJson.get("py").toString());
                    float theta = Float.valueOf(poseJson.get("theta").toString());
                    //TODO 点位名称信息是预留，暂不支持，后期可以通过点位名称进行定位
                    String name = poseJson.has("name") ? poseJson.optString("name") : "";
                    pose = new Pose(x, y, theta);
                    pose.setName(name);
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        if (pose == null) {
            Log.d(TAG, "setChassisRelocation: Pose null. Get estimate pose form DB!");
            pose = NavigationDataManager.getInstance().getPoseEstimate();
        }

        chassisClient.setChassisRelocation(relocationMode, pose, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (!status){
                    Log.d(TAG, "setChassisRelocation failed code:" + resultCode + " msg:" + result);
                }
                sendAsyncResponse(cmdType, status ? SUCCEED : FAILED);
            }
        });
        return true;
    }

    @Override
    public boolean switchMap(final String cmdType, final String mapName) {
        Log.d(TAG, "Command switch map : mapName=" + mapName);
        chassisClient.switchMap(mapName, new IChassisClient.ChassisResListener() {//switchMap
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "switch map :onResponse: mapName=" + mapName +
                        " status=" + status + " resultCode=" + resultCode + " result=" + (String) result);
                if (status) {
                    sendStatusReport(Definition.ACTION_SWITCH_MAP, MapUtils.getMapInfo(mapName));
                }
                sendAsyncResponse(cmdType, status ? SUCCEED : FAILED);
            }
        });
        return true;
    }

    @Override
    public boolean goPosition(final String cmdType, String value) {
        Log.d(TAG, "Command go position isReady:" + isChassisReady() + ", value:" + value);
        if (!isChassisReady()) {
            sendAsyncResponse(cmdType, "not ready");
            return false;
        }

        Pose pose = stringToPose(value);
        if (pose == null) {
            Log.d(TAG, "Command go position pose err");
            sendAsyncResponse(cmdType, "not exits");
            return false;
        }
        NavVelocity navVelocity = stringToNavVelocity(value);
        boolean adjustAngle = getIsAdjustAngle(value);
        Log.d(TAG, "goPosition_getIsAdjustAngle:" + adjustAngle);
        boolean keepMove = getIsKeepMove(value);
        double desRange = getNavigationDestinationRange(value);
        double obsDistance = getNavigationObsDistance(value);
        int priority = getTaskPriority(value);
        NavAcceleration navAcc = stringToAcceleration(value);
        NavMode navMode = stringToNavMode(value);
        double posTolerance = getPosTolerance(value);
        double angleTolerance = getAngleTolerance(value);
        int roadMode = getRoadMode(value);

        goNormal(cmdType, pose, navVelocity, adjustAngle, keepMove,
                TargetPose.TURN_DEFAULT, desRange, obsDistance, priority, navAcc, navMode,
                posTolerance, angleTolerance, roadMode);
        return true;
    }

    @Override
    public boolean goPositionByType(String cmdType, String param) {
        Log.d(TAG, "Command go goPositionByType isReady:" + isChassisReady() + ", param:" + param);
        int typedId = 0;
        int priority = 0;
        boolean isReversePoseTheta = false;
        try {
            JSONObject jsonObject = new JSONObject(param);
            typedId = jsonObject.getInt(Definition.JSON_NAVI_TYPE_ID);
            priority = jsonObject.getInt(Definition.JSON_NAVI_PRIORITY);
            isReversePoseTheta = jsonObject.getBoolean(JSON_NAVI_IS_REVERSE_POSE_THETA);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        // 1. 通过数据库查询当前要去的特殊点位是否存在
        PlaceInfo placeInfo = NavigationDataManager.getInstance().getPlaceByType(typedId, priority);
        if (placeInfo == null) {
            Log.d(TAG, "command goPositionByType: placeInfo not found. If priority value all zero with typeId: " + typedId + " , return zero");
            sendAsyncResponse(cmdType, "not exits");
            return false;
        }
        // 2. 创建Pose对象
        Pose pose = new Pose();
        pose.setX(placeInfo.getPointX());
        pose.setY(placeInfo.getPointY());
        if (isReversePoseTheta) {
            double theta = placeInfo.getPointTheta();
            theta = theta > 0 ? theta - Math.PI : theta + Math.PI;
            pose.setTheta(((float) theta));
        } else {
            pose.setTheta(placeInfo.getPointTheta());
        }

        NavVelocity navVelocity = stringToNavVelocity(param);
        boolean adjustAngle = getIsAdjustAngle(param);
        Log.d(TAG, "goPositionByType_getIsAdjustAngle:" + adjustAngle);
        boolean keepMove = getIsKeepMove(param);
        double desRange = getNavigationDestinationRange(param);
        double obsDistance = getNavigationObsDistance(param);
        int taskPriority = getTaskPriority(param);
        NavAcceleration navAcc = stringToAcceleration(param);
        NavMode navMode = stringToNavMode(param);
        double posTolerance = getPosTolerance(param);
        double angleTolerance = getAngleTolerance(param);
        int roadMode = getRoadMode(param);
        goNormal(cmdType, pose, navVelocity, adjustAngle, keepMove,
                TargetPose.TURN_DEFAULT, desRange, obsDistance, taskPriority, navAcc, navMode,
                posTolerance, angleTolerance, roadMode);
        return true;
    }

    @Override
    public boolean getVersion(final String cmdType) {
        Log.d(TAG, "Command get version");
        otaClient.getVersion(new IOtaClient.OtaResListener() {
            @Override
            public void onResult(String result) {
                sendAsyncResponse(cmdType, result);
            }
        });
        return true;
    }

    @Override
    public boolean startUpdate(final String cmdType, final String params) {
        Log.d(TAG, "Command start update");
        JSONArray jsonArray = null;
        JSONObject json = null;
        String[] packagePathList = new String[3];
        String packagePath = null;
        String boardName = null;
        try {
            jsonArray = new JSONArray(params);
            for (int i = 0; i < jsonArray.length(); i++) {
                json = jsonArray.getJSONObject(i);
                packagePath = json.getString("name");
                long fileSize = json.getLong("filelength");
                File file = new File(packagePath);
                if (!file.exists() || file.length() != fileSize) {
                    Log.d(TAG, "Command start update : file not exist or size error");
                    return false;
                }
                boardName = json.getString("board");
                if (boardName == null) {
                    Log.d(TAG, "no board name exist!");
                    return false;
                }

                switch (boardName) {
                    case "tk1":
                        packagePathList[0] = packagePath;
                        break;
                    case "motor_left":
                        packagePathList[1] = packagePath;
                        break;
                    case "motor_right":
                        packagePathList[2] = packagePath;
                        break;
                    default:
                        Log.d(TAG, "unknown board params!");
                        break;
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
            Log.d(TAG, "Command start update params error : " + params);
            return false;
        }

        // if estimate OK， notify to save pose
        if (chassisClient.isPoseEstimate()) {
            mContext.sendBroadcast
                    ((new Intent(NAVI_OTA_UPDATE_DONE)).putExtra(POSE, getCurPoseJsonStr()));
        }

        Log.d(TAG, "Command start update");
        otaClient.startUpdate(params, packagePathList, new IOtaClient.OtaResListener() {

            @Override
            public void onResult(String result) {
                if (result == null) {
                    startUpdate(cmdType, params);
                } else {
                    sendAsyncResponse(cmdType, result);
                }
            }
        });
        return true;
    }

    @Override
    public boolean getUpdateParams(final String cmdType) {
        Log.d(TAG, "Command get update params");
        otaClient.getUpdateParams(new IOtaClient.OtaResListener() {
            @Override
            public void onResult(String result) {
                sendAsyncResponse(cmdType, result);
            }
        });
        return true;
    }

    @Override
    public boolean motion(String params) {
        Log.d(TAG, "Command motion");
        try {
            JSONObject json = new JSONObject(params);
            double angularSpeed = json.getDouble(Definition.JSON_NAVI_ANGULAR_SPEED);
            double linearSpeed = json.getDouble(Definition.JSON_NAVI_LINEAR_SPEED);
            boolean hasAcceleration = json.optBoolean(Definition.JSON_NAVI_ACCELERATION, true);
            chassisClient.motion(angularSpeed, linearSpeed, 0, hasAcceleration);
            return true;
        } catch (JSONException e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean motionControlWithObstacles(String params) {
        Log.d(TAG, "Command motion");
        try {
            JSONObject json = new JSONObject(params);
            double angularSpeed = json.getDouble(Definition.JSON_NAVI_ANGULAR_SPEED);
            double linearSpeed = json.getDouble(Definition.JSON_NAVI_LINEAR_SPEED);
            double minDistance = json.getDouble(Definition.JSON_NAVI_MIN_DISTANCE);
            chassisClient.motionControlWithObstacle(angularSpeed, linearSpeed, minDistance);
            return true;
        } catch (JSONException e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean motionWithObstacles(String params) {
        Log.d(TAG, "Command motion with obstacles : params=" + params);
        try {
            JSONObject json = new JSONObject(params);
            double angularSpeed = json.getDouble(Definition.JSON_NAVI_ANGULAR_SPEED);
            double linearSpeed = json.getDouble(Definition.JSON_NAVI_LINEAR_SPEED);
            double acceleration = json.optDouble(Definition.JSON_NAVI_ACCELERATION_VALUE, 0);
            if (json.has(Definition.JSON_NAVI_MIN_OBSTACLES_DISTANCE)) {
                double minDistance = json.optDouble(Definition.JSON_NAVI_MIN_OBSTACLES_DISTANCE);
                chassisClient.motionWithObstacles(angularSpeed, linearSpeed, acceleration, minDistance);
            } else {
                chassisClient.motionWithObstacles(angularSpeed, linearSpeed, acceleration);
            }
            return true;
        } catch (JSONException e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean motionSoft(String params) {
        Log.d(TAG, "Command motion soft");
        try {
            JSONObject json = new JSONObject(params);
            double angularSpeed = json.getDouble(Definition.JSON_NAVI_ANGULAR_SPEED);
            double linearSpeed = json.getDouble(Definition.JSON_NAVI_LINEAR_SPEED);
            boolean hasAcceleration = json.optBoolean(Definition.JSON_NAVI_ACCELERATION, true);
            chassisClient.motionSoft(angularSpeed, linearSpeed, hasAcceleration);
            return true;
        } catch (JSONException e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    @Deprecated
    public boolean goDefaultTheta(String cmdType) {
        biManager.setCmd(cmdType);
        Log.d(TAG, "Command go default theta");
        if (!isChassisReady()) {
            sendAsyncResponse(cmdType, "not ready");
            return false;
        }

        Pose pose = getCurrentPoseDefaultTheta();
        if (pose == null) {
            Log.d(TAG, "theta pose null");
            sendAsyncResponse(cmdType, "not found");
            return false;
        }

        goPureRotation(cmdType, pose, null, false, TargetPose.TURN_DEFAULT);
        return true;
    }

    @Override
    public boolean getFullCheckStatus(final String cmdType) {
        Log.d(TAG, "Command get full check status");
        chassisClient.getFullCheckStatus(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "full check response status=" + status + " result=" + result);
                if (status && null != result) {
                    sendAsyncResponse(cmdType, mGson.toJson(result));
                }
            }
        });
        return true;
    }

    @Override
    public boolean getSensorStatus(final String cmdType) {
        Log.d(TAG, "Command get sensor status");
        chassisClient.getSensorStatus(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (status && null != result) {
                    Log.d(TAG, "onResponse sensor status result= " + mGson.toJson(result));
                    sendAsyncResponse(cmdType, mGson.toJson(result));
                } else {
                    sendAsyncResponse(cmdType, FAILED);
                }
            }
        });
        return true;
    }

    @Override
    public boolean switchChargeMode(String cmdType) {
        chassisClient.switchChargeMode();
        return true;
    }

    @Override
    public boolean switchManualMode(String cmdType) {
        chassisClient.switchManualMode();
        return true;
    }

    @Override
    public boolean resetPoseEstimate(final String cmdType) {
        Log.d(TAG, "Command reset estimate");
        chassisClient.resetPoseEstimate(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                sendAsyncResponse(cmdType, status ? SUCCEED : FAILED);
            }
        });
        return true;
    }

    @Override
    public boolean setRoverConfig(String cmdType, String params) {
        Log.d(TAG, "Command setRoverConfig" + params);
        try {
            RoverConfig roverConfig = mGson.fromJson(params, RoverConfig.class);
            chassisClient.setRoverConfig(roverConfig, null);
            sendAsyncResponse(cmdType, SUCCEED);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }

        sendAsyncResponse(cmdType, FAILED);
        return false;
    }

    @Override
    public boolean getRoverConfig(final String cmdType) {
        Log.d(TAG, "Command getRoverConfig");

        chassisClient.getRoverConfig(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (status && null != result) {
                    Log.d(TAG, "onResponse getRoverConfig status result= " + mGson.toJson(result));
                    sendAsyncResponse(cmdType, mGson.toJson(result));
                }
            }
        });
        return true;
    }

    @Override
    public boolean getSystemInformation(final String cmdType) {
        Log.d(TAG, "Command get system information");
        chassisClient.getSystemInformation(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                sendAsyncResponse(cmdType, mGson.toJson(result));
            }
        });
        return true;
    }

    @Override
    public boolean resumeSpecialPlaceTheta(String cmdType, String cmdParam) {
        Log.d(TAG, "resumeSpecialPlaceTheta, place is " + cmdParam);
        if (!isChassisReady()) {
            sendAsyncResponse(cmdType, "not ready");
            return false;
        }

        String placeName = cmdParam;
        int typeId = 0;
        int priority = 0;
        try {
            JSONObject jsonObject = new JSONObject(cmdParam);
            placeName = jsonObject.has(Definition.JSON_NAVI_TARGET_PLACE_NAME) ? jsonObject.optString(Definition.JSON_NAVI_TARGET_PLACE_NAME) : "";
            typeId = jsonObject.has(Definition.JSON_NAVI_TYPE_ID) ? jsonObject.optInt(Definition.JSON_NAVI_TYPE_ID) : typeId;
            priority = jsonObject.has(Definition.JSON_NAVI_PRIORITY) ? jsonObject.optInt(JSON_NAVI_PRIORITY) : priority;
        } catch (Exception e) {
            Log.e(TAG, "Failed to parse cmdParam as JSON: " + cmdParam, e);
            e.printStackTrace();
        }

        PlaceInfo placeBean = null;
        if (typeId != Definition.NORMAL_POINT_TYPE) {
            placeBean = NavigationDataManager.getInstance().getPlaceByType(typeId, priority);
        } else {
            placeBean = NavigationDataManager.getInstance().getPlaceByName(cmdParam);
        }
        Pose specialPlace = DataManager.placeBeanToPose(placeBean);
        if (specialPlace == null) {
            Log.d(TAG, "place " + cmdParam + " theta pose is null");
            sendAsyncResponse(cmdType, "not found special place");
            return false;
        }
        // specialPlace.setName(cmdParam); 设置名字是非必要的
        Pose curPose = chassisClient.getCurrentPose();
        Log.d(TAG, "resumeSpecialPlaceTheta, curPose theta=" + curPose.getTheta() +
                " specialPlace theta=" + specialPlace.getTheta());
        double thetaDiff = specialPlace.getTheta() - curPose.getTheta();
        Log.d(TAG, "resumeSpecialPlaceTheta, thetaDiff : " + thetaDiff);

        if (thetaDiff > Math.PI) {
            thetaDiff = thetaDiff - 2 * Math.PI;
        } else if (thetaDiff < -Math.PI) {
            thetaDiff = thetaDiff + 2 * Math.PI;
        } else {
            Log.d(TAG, "theta not beyond Math.PI");
        }
        Log.d(TAG, "resumeSpecialPlaceTheta, final thetaDiff : " + thetaDiff);

        //未定位情况下当前 pose 不准确，不需要归正
        double angular = Math.toRadians(40);
        NavVelocity navVelocity = new NavVelocity(0.0, angular);
        int direction = thetaDiff > 0 ? TargetPose.TURN_LEFT : TargetPose.TURN_RIGHT;
        Log.d(TAG, "resumeSpecialPlaceTheta, angular= " + angular + ", direction= " + direction);
//        go(cmdType, specialPlace, navVelocity, false, direction);
        goPureRotation(cmdType, specialPlace, navVelocity, false, TargetPose.TURN_DEFAULT);

        return true;
    }

    @Override
    public boolean loadCurrentMap(String cmdType, String cmdParam) {
        LoadMapBean loadMapBean = mGson.fromJson(cmdParam, LoadMapBean.class);
        if (null == loadMapBean) {
            sendAsyncResponse(cmdType, FAILED);
            return true;
        }
        chassisClient.loadCurrentMap(loadMapBean.isUseCustomKeepPose(), loadMapBean.isKeepPose(), new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "onResponse: " + resultCode + ", result = " + result);
                if (resultCode == SUCCESS) {
                    MapInfo navMapInfo = NavigationDataManager.getInstance().getNavMapInfo();
                    if (navMapInfo != null) {
                        sendStatusReport(Definition.STATUS_RELOAD_MAP, MapUtils.getMapInfo(navMapInfo.getMapName()));
                    }
                    sendAsyncResponse(cmdType, SUCCEED);
                } else {
                    sendAsyncResponse(cmdType, FAILED);
                }
            }
        });
        return true;
    }

    @Override
    public boolean clearCurNaviMap(final String cmdType) {
        Log.d(TAG, "Command clear cur navi map");
        NavigationDataManager.getInstance().clearMapName();
        sendAsyncResponse(cmdType, SUCCEED);
        return true;
    }

    @Override
    public boolean getLogFile(final String cmdType, String cmdParam, String fileType) {
        Log.d(TAG, "Command get log file fileType:" + fileType + " param:" + cmdParam);
        if (TextUtils.isEmpty(cmdParam)) {
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        long startTime = -1;
        long endTime = -1;
        String type = "";
        String savePath = "";

        try {
            JSONObject jsonObject = new JSONObject(cmdParam);
            startTime = jsonObject.optLong(Definition.JSON_START_TIME);
            endTime = jsonObject.optLong(Definition.JSON_END_TIME);
            type = jsonObject.optString(Definition.JSON_CMD_TYPE);
            savePath = jsonObject.optString(Definition.JSON_SAVE_PATH);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        chassisClient.getLogFile(startTime, endTime, type, fileType, savePath, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                sendAsyncResponse(cmdType, resultCode == SUCCESS ? ((String)
                        result) : FAILED);
            }
        });
        return true;
    }

    @Override
    public boolean packLogFile(final String cmdType, String cmdParam) {
        Log.d(TAG, "Command pack log file:" + cmdParam);
        PackLogBean packLogBean = null;
        if (!TextUtils.isEmpty(cmdParam)) {
            packLogBean = stringToBean(cmdParam);
        }
        if (packLogBean == null) {
            sendAsyncResponse(cmdType, "param error");
            return false;
        }
        chassisClient.packLogFile(packLogBean.startTime, packLogBean.endTime, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                sendAsyncResponse(cmdType, resultCode == SUCCESS ? Definition
                        .SUCCEED : Definition.FAILED);
            }
        });
        return true;
    }

    @Override
    public boolean checkCurNaviMap(final String cmdType, String cmdParam) {
        Log.d(TAG, "checkCurNaviMap cmdType = " + cmdType);
        //String mapName = dataManager.getMapName();
        MapInfo mapInfo = NavigationDataManager.getInstance().getNavMapInfo();
        Log.i(TAG, "checkCurNaviMap: mapInfo=" + mapInfo);
        if (mapInfo == null) {
            sendAsyncResponse(cmdType, FAILED);
            return true;
        }

        chassisClient.checkCurNaviMap(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "checkCurNaviMap status = " + status + ", resultCode = " + resultCode);
                sendAsyncResponse(cmdType, status ? SUCCEED : FAILED);
            }
        });
        return true;
    }

    @Override
    public boolean setRadarState(final String cmdType, String cmdParam) {
        Log.d(TAG, "setRadarState: cmdParam=" + cmdParam);
        if (TextUtils.isEmpty(cmdParam)) {
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }

        boolean openRadar = true;
        try {
            JSONObject jsonObject = new JSONObject(cmdParam);
            openRadar = jsonObject.optBoolean(Definition.JSON_NAVI_OPEN_RADAR);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Log.d(TAG, "setRadarState: openRadar=" + openRadar);
        chassisClient.setRadarState(openRadar, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                String message = (String) result;
                message = TextUtils.isEmpty(message) ? FAILED : message;
                sendAsyncResponse(cmdType, resultCode == SUCCESS ? SUCCEED : message);
            }
        });
        return true;
    }

    @Override
    public boolean getRadarState(final String cmdType) {
        Log.d(TAG, "getRadarState: ");
        chassisClient.getRadarState(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "getRadarState：onResponse : status=" + status
                        + ", resultCode=" + resultCode + ", result=" + result);
                if (resultCode == SUCCESS) {
                    sendAsyncResponse(cmdType, (String) result);
                } else {
                    sendAsyncResponse(cmdType, FAILED);
                }
            }
        });
        return true;
    }

    @Override
    public boolean turnByNavigation(String cmdType, String value) {
        if (!isChassisReady()) {
            sendAsyncResponse(cmdType, "not ready");
            return false;
        }

        if (!chassisClient.isPoseEstimate()) {
            sendAsyncResponse(cmdType, "not estimate");
            return false;
        }

        Pose pose = stringToPose(value);
        if (pose == null) {
            Log.d(TAG, "Command go position pose err");
            sendAsyncResponse(cmdType, "not exits");
            return false;
        }
        NavVelocity navVelocity = stringToNavVelocity(value);
        int turnParam = getTurnParam(value);
        goPureRotation(cmdType, pose, navVelocity, false, turnParam);
        return true;
    }

    @Override
    public boolean setLocateVision(final String cmdType) {
        Log.d(TAG, "Command vision relocate");
        chassisClient.setVisionEstimate(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (!status){
                    Log.d(TAG, "setLocateVision failed code:" + resultCode + " msg:" + result);
                }
                sendAsyncResponse(cmdType, status ? SUCCEED : FAILED);
                biManager.relocationFunctionReport(status, BiManager.VISION, "");
            }
        });
        return true;
    }

    @Override
    public boolean recoveryNavigation(final String cmdType) {
        Log.d(TAG, "recoveryNavigation cmdtype is " + cmdType);
        chassisClient.recoveryNavigation(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "recoveryNavigation status = " + status + ", resultCode = " + resultCode);
                sendAsyncResponse(cmdType, status ? SUCCEED : FAILED);
            }
        });
        return true;
    }

    @Override
    public boolean setMinObstaclesDistance(String cmdType, String cmdParam) {
        Log.d(TAG, " : " + cmdType + ", " + cmdParam);
        if (!TextUtils.isEmpty(cmdParam)) {
            try {
                JSONObject object = new JSONObject(cmdParam);
                double distance = object.optDouble(Definition.JSON_NAVI_MIN_OBSTACLES_DISTANCE);
                chassisClient.setMinObstaclesDistance(distance);
                sendAsyncResponse(cmdType, SUCCEED);
                return true;
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        sendAsyncResponse(cmdType, FAILED);
        return false;
    }

    @Override
    public boolean resetMinObstaclesDistance(String cmdType) {
        Log.d(TAG, " : " + cmdType);
        chassisClient.resetMinObstaclesDistance();
        sendAsyncResponse(cmdType, SUCCEED);
        return true;
    }

    @Override
    public boolean getMapStatus(String cmdType, String cmdParam) {
        String mapStatus = chassisClient.getMapStatus(cmdType, cmdParam);
        sendAsyncResponse(cmdType, mapStatus);
        return true;
    }

    /**
     * 边建图边设点，通知底盘添加该Pose的映射并实时返回映射列表
     * {@link ninjia.android.roversdk.listener.UpdateListener#onUpdateTargets}
     * 建图中只操作内存中维护的映射点列表{@link MappingPose}，建图结束才存储到本地
     *
     * @param cmdType
     * @param param
     * @return
     */
    @Override
    public boolean addMappingPose(final String cmdType, final String param) {
        Log.d(TAG, "ChassisRelyApiImpl - addMappingPose: " + "cmdType: " + cmdType + ", param: " + param);
        Pose pose = stringToPose(param);
        final String name = pose.getName();
        if (TextUtils.isEmpty(name)) {
            Log.d(TAG, "ChassisRelyApiImpl - addMappingPose[failure, name is empty]");
            sendAsyncResponse(cmdType, Definition.RESULT_FAILED, "failure, name is empty");
            return false;
        }

        // 普通点名称判断，不能重复。特殊点可以(10.3版本以后) 这个里加条件用typeId为0认为是普通点
        for (MappingPose mappingPose : MappingPose.getPoseList()) {
            if (mappingPose.getPose() != null && name.equals(mappingPose.getPose().getName())) {
                Log.d(TAG, "ChassisRelyApiImpl - addMappingPose[failure, duplicate name]: " +
                        MappingPose.getMappingPoseListJson());
                sendAsyncResponse(cmdType, Definition.RESULT_FAILED, "failure, duplicate name");
                return false;
            }
        }

        chassisClient.addMappingPose(pose, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "ChassisRelyApiImpl - addMappingPose response: " +
                        "status: " + status + ", resultCode: " + resultCode + ", result: " + result);
                if (status && result instanceof MappingPose) {
                    MappingPose mappingPose = (MappingPose) result;
                    boolean ignoreDistance = pose.getIgnoreDistance();
                    boolean noDirectionalParking = pose.getNoDirectionalParking();
                    int safeDistance = pose.getSafeDistance();
                    int typeId = pose.getTypeId();
                    int priority = pose.getPriority();
                    Pose pose = mappingPose.getPose();
                    if (pose == null) {
                        pose = new Pose();
                    }
                    pose.setName(name);
                    pose.setNoDirectionalParking(noDirectionalParking);
                    pose.setIgnoreDistance(ignoreDistance);
                    pose.setSafeDistance(safeDistance);
                    pose.setTypeId(typeId);
                    pose.setPriority(priority);
                    mappingPose.setPose(pose);
                    MappingPose.addPose(mappingPose);
                    String message = mappingPose.toJson();
                    sendAsyncResponse(cmdType, Definition.RESULT_SUCCEED, message);
                } else {
                    sendAsyncResponse(cmdType, Definition.RESULT_FAILED,
                            "failure, errorcode: " + resultCode);
                }
            }
        });
        return true;
    }

    /**
     * 删除映射点，边建图边设点时使用
     * 建图中只操作内存中维护的映射点列表{@link MappingPose}，建图结束才存储到本地
     *
     * @param cmdType
     * @param name    点位名称
     * @return
     */
    @Override
    public boolean deleteMappingPose(final String cmdType, final String name) {
        int id = -1;

        for (MappingPose pose : MappingPose.getPoseList()) {
            if (pose.getPose() != null && name.equals(pose.getPose().getName())) {
                id = pose.getId();
                break;
            }
        }

        if (id == -1) {
            Log.i(TAG, "remove mapping: failure, name can not find");
            sendAsyncResponse(cmdType, Definition.RESULT_FAILED, "failure, name can not find");
            return false;
        }
        Log.i(TAG, "remove mapping pose id: " + id);
        final int finalId = id;
        chassisClient.deleteMappingPose(id, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (status) {
                    MappingPose.deletePose(finalId);
                    sendAsyncResponse(cmdType, Definition.RESULT_SUCCEED, "success");
                } else {
                    sendAsyncResponse(cmdType, Definition.RESULT_FAILED, "faliure, errorcode: " + resultCode);
                }
            }
        });
        return true;
    }

    /**
     * 重命名映射点名称，边建图边设点时使用
     * 建图中只操作内存中维护的映射点列表{@link MappingPose}，建图结束才存储到本地
     *
     * @param cmdType
     * @param cmdParam
     * @return
     */
    @Override
    public boolean renameMappingPose(final String cmdType, String cmdParam) {
        String newPoseName = "";
        String oldPoseName = "";
        boolean ignoreDistance = false;
        boolean noDirectionalParking = false;
        int safeDistance = Definition.POSE_SAFE_DISTANCE_DEFAULT;
        int typeId = 0;
        int priority = 0;

        try {
            JSONObject json = new JSONObject(cmdParam);
            newPoseName = json.optString("newName", "");
            noDirectionalParking = json.optBoolean("noDirectionalParking", false);
            oldPoseName = json.optString("oldName", "");
            ignoreDistance = json.optBoolean("ignoreDistance", false);
            safeDistance = json.optInt("safeDistance", Definition.POSE_SAFE_DISTANCE_DEFAULT);
            typeId = json.optInt("typeId", 0);
            priority = json.optInt("priority", priority);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(newPoseName) || TextUtils.isEmpty(oldPoseName)) {
            Log.i(TAG, "rename mapping: failure, pose name is empty");
            sendAsyncResponse(cmdType, Definition.RESULT_FAILED, "failure, pose name is empty");
            return false;
        }

        int id = -1;
        Pose oldPose = null;
        for (MappingPose pose : MappingPose.getPoseList()) {
            if (pose.getPose() != null && oldPoseName.equals(pose.getPose().getName())) {
                id = pose.getId();
                oldPose = pose.getPose();
                break;
            }
        }

        if (id == -1) {
            Log.i(TAG, "rename mapping: failure, name can not find");
            sendAsyncResponse(cmdType, Definition.RESULT_FAILED, "failure, name can not find");
            return false;
        }
        Log.i(TAG, "rename mapping pose id: " + id);
        final int finalId = id;
        final String finalNewPoseName = newPoseName;
        final String finalOldPoseName = oldPoseName;
        final Pose finalOldPose = oldPose;
        final boolean finalIgnoreDistance = ignoreDistance;
        final boolean finalNoDirectionalParking = noDirectionalParking;
        final int finalSafeDistance = safeDistance;
        final int finalTypeId = typeId;
        final int finalPriority = priority;
        // TODO: rename目前由于底盘没有提供重命名api，只能先删除老的Pose，再在老Pose的基础上改了名字再新增，api分段会有数据一致性的问题，最好是底盘提供独立api
        chassisClient.deleteMappingPose(id, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (status) {
                    MappingPose.deletePose(finalId);
                    finalOldPose.setName(finalNewPoseName);
                    chassisClient.addMappingPose(finalOldPose, new IChassisClient.ChassisResListener() {
                        @Override
                        public void onResponse(boolean status, int resultCode, Object result) {
                            if (status && result instanceof MappingPose) {
                                MappingPose mappingPose = (MappingPose) result;
                                Pose pose = mappingPose.getPose();
                                if (pose == null) {
                                    pose = new Pose();
                                }
                                pose.setName(finalNewPoseName);
                                pose.setIgnoreDistance(finalIgnoreDistance);
                                pose.setNoDirectionalParking(finalNoDirectionalParking);
                                pose.setSafeDistance(finalSafeDistance);
                                pose.setTypeId(finalTypeId);
                                pose.setPriority(finalPriority);
                                mappingPose.setPose(pose);
                                MappingPose.addPose(mappingPose);
                                sendAsyncResponse(cmdType, Definition.RESULT_SUCCEED, "success");
                            } else {
                                sendAsyncResponse(cmdType, Definition.RESULT_FAILED, "failure, errorcode: " + resultCode);
                            }
                        }
                    });
                } else {
                    sendAsyncResponse(cmdType, Definition.RESULT_FAILED, "faliure, errorcode: " + resultCode);
                }
            }
        });
        return true;
    }

    @Override
    public int onGetHWStatus(String cmdType) {
        if (isChassisReady()) {
            return Definition.HW_STATUS_IDLE;
        } else if (!chassisClient.isSocketConnected()) {
            return Definition.HW_STATUS_SOCKET_DISCONNECT;
        } else if (!chassisClient.isServiceReady()) {
            return Definition.HW_STATUS_SERVICE_UNUSABLE;
        } else {
            return Definition.HW_STATUS_NOT_AVAILABLE;
        }
    }

    private PackLogBean stringToBean(String cmdParam) {
        PackLogBean packLogBean = null;
        try {
            JSONObject json = new JSONObject(cmdParam);
            long startTime = json.has("start_time") ? json.getLong("start_time") : 0L;
            long endTime = json.has("end_time") ? json.getLong("end_time") : 0L;
            packLogBean = new PackLogBean(startTime, endTime);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return packLogBean;
    }

    @Deprecated
    private Pose getCurrentPoseDefaultTheta() {
        //原有逻辑未适配，RobotApi、SystemApi也没有接口使用
        return null;
//        Pose mCurrentPose = chassisClient.getCurrentPose();
//        Pose poseTheta = dataManager.getDefaultTheta();
//        if (poseTheta == null) {
//            return null;
//        }
//        float x = mCurrentPose.getX();
//        float y = mCurrentPose.getY();
//        float theta = poseTheta.getTheta();
//        Pose pose = new Pose(x, y, theta);
//        Log.d(TAG, "getCurrentPoseDefaultTheta mCurrentPose:" + mCurrentPose.toString()
//                + ", poseTheta:" + poseTheta.toString() + ", pose:" + pose.toString());
//        return pose;
    }

    private String getCurPoseJsonStr() {

        Pose pose = chassisClient.getCurrentPose();
        if (pose != null) {
            try {
                JSONObject json = new JSONObject();
                json.put("px", pose.getX());
                json.put("py", pose.getY());
                json.put("theta", pose.getTheta());
                return json.toString();
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    private String getEstimateFailMsg(Object result) {
        String rslt = result != null ? result.toString() : "";
        if (!TextUtils.isEmpty(rslt) && rslt.equals(MATCH_ERR)) {
            return Definition.RESULT_ESTIMATE_FAIL_MATCH_ERR;
        }
        if (!TextUtils.isEmpty(rslt) && rslt.equals(CHECK_ERR)) {
            return Definition.RESULT_ESTIMATE_FAIL_CHECK_ERR;
        }
        return FAILED;
    }

    private boolean isPoseAvailable(Pose curPose) {
        return curPose != null && curPose.getStatus() == Pose.MAP_STATUS_NORMAL_AREA;
    }

    private Pose stringToPose(String jsonString) {
        Pose pose = null;
        try {
            JSONObject json = new JSONObject(jsonString);

            float x = json.has(Definition.JSON_NAVI_DIRECTION_X) ? Float.valueOf(json.get(Definition.JSON_NAVI_DIRECTION_X).toString())
                    : Float.valueOf(json.get(Definition.JSON_NAVI_POSITION_X).toString());
            float y = json.has(Definition.JSON_NAVI_DIRECTION_Y) ? Float.valueOf(json.get(Definition.JSON_NAVI_DIRECTION_Y).toString())
                    : Float.valueOf(json.get(Definition.JSON_NAVI_POSITION_Y).toString());
            float theta = Float.parseFloat(json.get(Definition.JSON_NAVI_POSITION_THETA).toString());
            String name = json.has(Definition.NEWPLACENAME) ? json.optString(Definition.NEWPLACENAME) : "";
            boolean ignoreDistance = json.has(Definition.NEWPLACE_IGNORE_DISTANCE) && json.optBoolean(Definition.NEWPLACE_IGNORE_DISTANCE);
            int safeDistance = json.has(Definition.NEWPLACE_SAFE_DISTANCE) ? json.optInt(Definition.NEWPLACE_SAFE_DISTANCE) : Definition.POSE_SAFE_DISTANCE_DEFAULT;
            int typeId = json.has(Definition.NEWPLACE_TYPE_ID) ? json.optInt(Definition.NEWPLACE_TYPE_ID) : 0;
            int priority = json.has(Definition.NEWPLACE_PRIORITY) ? json.optInt(Definition.NEWPLACE_PRIORITY) : 0;
            boolean noDirectionalParking = json.has(Definition.NO_DIRECTIONAL_PARKING) && json.optBoolean(Definition.NO_DIRECTIONAL_PARKING);
            pose = new Pose(x, y, theta);
            pose.setName(name);
            pose.setIgnoreDistance(ignoreDistance);
            pose.setNoDirectionalParking(noDirectionalParking);
            pose.setSafeDistance(safeDistance);
            pose.setTypeId(typeId);
            pose.setPriority(priority);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return pose;
    }

    private List<Pose> stringToPoseList(String jsonString) {
        List<Pose> poseList = new ArrayList<>();
        try {
            JSONArray jsonArray = new JSONArray(jsonString);
            for (int i=0; i<jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                Pose pose = stringToPose(jsonObject.toString());
                if (pose != null) {
                    poseList.add(pose);
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
            Log.i(TAG, "stringToPoseList parse error");
        }
        return poseList;
    }

    private TargetPose mPatrolPose;
    private List<Pose> mPatrolRoute = new ArrayList<>();
    private PatrolListener mPatrolListener;
    private boolean isRepeatPatrol = false;

    private void startPatrol(List<Pose> points, boolean isRepeat, PatrolListener listener) {
        Log.d(TAG, "Start patrol ***************** ");

        mPatrolListener = listener;
        mPatrolRoute = points;
        if (mPatrolRoute == null || mPatrolRoute.isEmpty()) {
            return;
        }

        this.isRepeatPatrol = isRepeat;
        sendPatrolPose(mPatrolRoute.get(0));
    }

    private void goNextPatrolPose() {
        Pose nextPose = getNextPatrolPose();
        Log.d(TAG, "Patrol goNextPatrolPose : " + (nextPose == null ? null : nextPose.toString()));
        if (nextPose != null) {
            sendPatrolPose(nextPose);
            mPatrolListener.onNext(nextPose);
        } else {
            mPatrolListener.onFinish();
        }
    }

    private Pose getNextPatrolPose() {
        int index = mPatrolRoute.indexOf(mPatrolPose.getPose());
        Log.d(TAG, "Patrol getNextPatrolPose index : " + index);
        if (index < mPatrolRoute.size() - 1) {
            return mPatrolRoute.get(index + 1);
        }
        if (isRepeatPatrol) {
            return mPatrolRoute.get(0);
        }
        return null;
    }

    private void sendPatrolPose(Pose pose) {
        Log.d(TAG, "Patrol start : " + pose.toString());

        mPatrolPose = new TargetPose(pose);
        mPatrolPose.setResponseListener(new TargetPose.ResponseListener() {

            @Override
            public void onStatusUpdate(int status, BaseEvent event) {
                Log.d(TAG, "Command patrol location status : " + status);
                switch (status) {
                    case TargetPose.STATUS_OCCLUDED:
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onResult(int result, BaseEvent event) {
                Log.d(TAG, "Command patrol location result : " + result);
                switch (result) {
                    case TargetPose.RESULT_ARRIVED:
                        Log.d(TAG, "Command Command Patrol arrived");
                        goNextPatrolPose();
                        break;
                    case TargetPose.RESULT_ABORTED:
                        Log.d(TAG, "Command Patrol aborted");
                        mPatrolListener.onAborted();
                        break;
                    case TargetPose.RESULT_CANCELED:
                        Log.d(TAG, "Command Patrol canceled");
                        break;
                }
            }
        });
        chassisClient.go(mPatrolPose, new IChassisClient.ChassisResListener() {

            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (!status) {
                    //底盘naviToGoal接口异常(非Result.CODE_SUCCESS的情况)时，发送消息给上层进行通知．
                    sendStatusReport(Definition.STATUS_NAVI_TO_GOAL_EXCEPTION, String.valueOf(status));
                }
            }
        });
    }

    public interface PatrolListener {
        void onNext(Pose pose);

        void onFinish();

        void onCanceled();

        void onAborted();
    }

    private synchronized void reportToServer() {
        if (mHasReport) {
            mHasReport = false;
            report2Remote("path_planning_failure");
        }
    }

    private void report2Remote(String value) {
        sendStatusReport(Definition.STATUS_WARNING_REPORT, value);
    }

    /**
     * 使用导航接口原地转动
     */
    private void goPureRotation(final String cmdType, Pose pose, NavVelocity navVelocity
            , boolean adjustAngle, int turnParam) {
        go(cmdType, pose, navVelocity, adjustAngle, false, turnParam,
                Def.ROBOT_NAVIGATION_DEFAULT_DESTINATION_RANGE,
                Def.ROBOT_NAVIGATION_DEFAULT_OBS_DISTANCE,
                TargetPose.MOVE_PURE_ROTATION,
                TargetPose.DEFAULT_NAVIGAITON_PRIORITY,
                null, TargetPose.DEFAULT_NAV_MODE,
                TargetPose.POS_TOLERANCE_DEFAULT, TargetPose.ANGLE_TOLERANCE_DEFAULT,
                TargetPose.ROAD_MODE_MAP_PATH);
    }

    /**
     * 常规导航
     */
    private void goNormal(final String cmdType, Pose pose, NavVelocity navVelocity,
                          boolean adjustAngle, boolean keepMove, int turnParam,
                          double desRange, double obsDistance,
                          int priority, NavAcceleration navAcc, NavMode navMode,
                          double posTolerance, double angleTolerance, int roadMode) {
        go(cmdType, pose, navVelocity, adjustAngle, keepMove, turnParam, desRange, obsDistance,
                TargetPose.MOVE_NORMAL, priority, navAcc, navMode, posTolerance, angleTolerance, roadMode);
    }

    private void go(final String cmdType, Pose pose, NavVelocity navVelocity, boolean adjustAngle,
                    boolean keepMove, int turnParam, double desRange, double obsDistance,
                    int moveType, int priority, NavAcceleration navAcceleration, NavMode navMode,
                    double posTolerance, double angleTolerance, int roadMode) {
        if (pose == null) {
            sendAsyncStatus(cmdType, "pose not exits");
            return;
        }
        mHasReport = true;
        // 这个打印要注意，有两个obsDistance
        // 接口的参数会下发导航，影响到达判断，默认0.75
        // Pose对象内部的用于poseUpdate时设置默认值，不会下发给导航影响业务，默认0.1
        // 已经把Pose对象的打印key修改为poseObsDistance，避免歧义

        Log.d(TAG, "Command go pose:" + pose.toString()
                + "  cmdType= " + cmdType
                + " navVelocity=" + (navVelocity == null ? "null" : navVelocity.toString())
                + " adjustAngle= " + adjustAngle + " keepMove= " + keepMove
                + ", turnParam = " + turnParam + ", desnationRange= " + desRange
                + ", obsDistance= " + obsDistance
                + " moveType=" + moveType + " priority:" + priority
                + " navMode=" + (navMode == null ? "null" : navMode.toString())
                + " posTolerance=" + posTolerance
                + " angleTolerance=" + angleTolerance
                + " roadMode=" + roadMode);
        TargetPose targetPose = new TargetPose(pose, adjustAngle, keepMove,
                turnParam, desRange, obsDistance, priority, navMode);
        targetPose.setMoveType(moveType);
        targetPose.setPosTolerance(posTolerance);
        targetPose.setAngleTolerance(angleTolerance);
        targetPose.setRoadMode(roadMode);
        targetPose.setResponseListener(new TargetPose.ResponseListener() {

            @Override
            public void onStatusUpdate(int status, BaseEvent event) {
                Log.d(TAG, "Go location status : " + status + "  " + cmdType);
                String extraJson = "";
                if (event != null) {
                    if (mGson == null) {
                        mGson = new Gson();
                    }
                    extraJson = mGson.toJson(event);
                }
                switch (status) {
                    case TargetPose.STATUS_OCCLUDED:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_OCCLUDED, extraJson);
                        reportToServer();
                        break;

                    case TargetPose.STATUS_OCCLUDED_END:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_OCCLUDED_END, extraJson);
                        break;

                    case TargetPose.STATUS_AVOID:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_AVOID, extraJson);
                        reportToServer();
                        break;

                    case TargetPose.STATUS_AVOID_END:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_AVOID_END, extraJson);
                        break;

                    case TargetPose.STATUS_OUT_MAP:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_OUT_MAP, extraJson);
                        reportToServer();
                        break;

                    case TargetPose.STATUS_GLOBAL_PATH_FAILED:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_GLOBAL_PATH_FAILED, extraJson);
                        report2Remote("global_path_planning_failure");
                        break;

                    case TargetPose.STATUS_PATH_SUCCESS:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_PATH_SUCCESS, extraJson);
                        break;

                    case TargetPose.STATUS_OUT_MAP_END:
                        break;

                    case TargetPose.STATUS_OBSTACLES_AVOID:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_OBSTACLES_AVOID, extraJson);
                        break;

                    case TargetPose.STATUS_STARTED:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_STARTED, extraJson);
                        break;

                    case TargetPose.STATUS_MULTI_AVOID_WAITING:
                    case TargetPose.STATUS_PATH_WAITING:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_MULTI_ROBOT_WAITING, extraJson);
                        break;

                    case TargetPose.STATUS_MULTI_AVOID_WAITING_END:
                    case TargetPose.STATUS_PATH_WAITING_END:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_MULTI_ROBOT_WAITING_END, extraJson);
                        break;

                    case TargetPose.STATUS_GO_STRAIGHT:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_GO_STRAIGHT, extraJson);
                        break;

                    case TargetPose.STATUS_TURN_LEFT:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_TURN_LEFT, extraJson);
                        break;

                    case TargetPose.STATUS_TURN_RIGHT:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_TURN_RIGHT, extraJson);
                        break;

                    case TargetPose.STATUS_NOT_MOVING_LONG_TIME:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_NOT_MOVING_LONG_TIME, extraJson);
                        break;
                    case TargetPose.STATUS_PRIORITY_SET_FAILED:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_SET_PRIORITY_FAILED, extraJson);
                        break;
                    case TargetPose.STATUS_MULTIPLE_MAP_NOT_MATCH:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_MULTI_MAP_NOT_MATCH, extraJson);
                        break;
                    case TargetPose.STATUS_MULTIPLE_LORA_DISCONNECT:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_MULTI_LORA_DISCONNECT, extraJson);
                        break;
                    case TargetPose.STATUS_MULTIPLE_LORA_CONFIG_FAIL:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_MULTI_LORA_CONFIG_FAIL, extraJson);
                        break;
                    case TargetPose.STATUS_MULTIPLE_VERSION_NOT_MATCH:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_MULTI_VERSION_NOT_MATCH, extraJson);
                        break;
                    case TargetPose.STATUS_WHEEL_SLIP:
                        sendAsyncStatus(cmdType, Definition.NAVIGATION_WHEEL_SLIP, extraJson);
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onResult(int result, BaseEvent event) {
                Log.d(TAG, "Go location result : " + result + "  " + cmdType);
                String response = "";
                String extraJson = "";
                if (event != null) {
                    extraJson = mGson.toJson(event);
                }
                switch (result) {
                    case TargetPose.RESULT_ARRIVED:
                        report2Remote("path_planning_succ");
                        response = "true";
                        sendAsyncResponse(cmdType, Definition.RESULT_SUCCEED, response, extraJson);

                        break;
                    case TargetPose.RESULT_ABORTED:
                        report2Remote("path_planning_failure");
                        response = "false";
                        sendAsyncResponse(cmdType, Definition.RESULT_SUCCEED, response, extraJson);

                        break;
                    case TargetPose.RESULT_CANCELED: // RESULT_CANCELED ,只有CoreService api层主动取消导航时,才有该回调.
                        report2Remote("path_planning_succ");
                        break;
                    case TargetPose.RESULT_REPLACE:
                        report2Remote("path_planning_succ");
                        break;
                    case TargetPose.RESULT_FAILED:
                        response = "false";
                        sendAsyncResponse(cmdType, Definition.RESULT_SUCCEED, response, extraJson);
                        break;
                    default:
                        sendAsyncResponse(cmdType, Definition.RESULT_SUCCEED, response, extraJson);
                        break;
                }

            }
        });

        chassisClient.go(targetPose, navVelocity, navAcceleration, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (!status) {
                    //底盘naviToGoal接口异常(非Result.CODE_SUCCESS的情况)时，上层进行UI通知．
                    sendStatusReport(Definition.STATUS_NAVI_TO_GOAL_EXCEPTION, String.valueOf(status));
                }
            }
        });
    }

    private boolean removeMapToolMap(String mapName) {
        Log.d(TAG, "removeMapToolMap: mapName=" + mapName);
        boolean succeed = MapFileHelper.removeMapDir(mapName);
        Log.d(TAG, "removeMapToolMap: removeMapDir result=" + succeed);
        if (succeed) {
            boolean result = NavigationDataManager.getInstance().deleteMapAllDataByName(mapName);
            Log.d(TAG, "removeMapToolMap: Remove db data result=" + result);
            DataManager.getInstance().sendPlaceDataUpdateReport();//removeMapToolMap
        }
        return succeed;
    }

    private boolean hasPlaceName(String jsonString) {
        if (TextUtils.isEmpty(jsonString)) {
            return false;
        }
        String placeName = null;
        try {
            JSONObject json = new JSONObject(jsonString);
            placeName = json.optString(Definition.JSON_NAVI_DESTINATION_NAME);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return !TextUtils.isEmpty(placeName);
    }

    private Pose stringToDestination(String jsonString) {
        if (TextUtils.isEmpty(jsonString)) {
            return null;
        }
        try {
            JSONObject json = new JSONObject(jsonString);
            String placeId = json.optString(Definition.JSON_NAVI_PLACE_ID);
            String placeName = json.optString(Definition.JSON_NAVI_DESTINATION_NAME);
            // 地点名称映射 兼容特殊点名称
            placeName = NavigationDataManager.getInstance().getPlaceNameHighPriority(placeName);
            PlaceInfo placeBean;
            if (TextUtils.isEmpty(placeId)) {
                placeBean = NavigationDataManager.getInstance().getPlaceByName(placeName);
            } else {
                placeBean = NavigationDataManager.getInstance().getPlaceById(placeId);
            }
            Pose pose = DataManager.placeBeanToPose(placeBean);
            pose.setName(placeName);
            return pose;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @param jsonStr
     * @return int true isLeft , false isRight
     */
    private int getTurnParam(String jsonStr) {
        try {

            JSONObject json = new JSONObject(jsonStr);
            boolean isLeft = json.has(Definition.JSON_NAVI_LEFT_OR_RIGHT)
                    ? Boolean.valueOf(json.get(Definition.JSON_NAVI_LEFT_OR_RIGHT).toString()) : true;
            return isLeft ? TargetPose.TURN_LEFT : TargetPose.TURN_RIGHT;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return TargetPose.TURN_DEFAULT;
    }

    private NavVelocity stringToNavVelocity(String jsonString) {
        NavVelocity velocity = null;
        try {
            JSONObject json = new JSONObject(jsonString);
            double linear = json.has(Definition.JSON_NAVI_LINEAR_SPEED)
                    ? Double.valueOf(json.get(Definition.JSON_NAVI_LINEAR_SPEED).toString()) : 0.0d;
            double angular = json.has(Definition.JSON_NAVI_ANGULAR_SPEED)
                    ? Double.valueOf(json.get(Definition.JSON_NAVI_ANGULAR_SPEED).toString()) : 0.0d;
            velocity = new NavVelocity(linear, angular);
        } catch (JSONException e) {
            e.printStackTrace();
            Log.e(TAG, "stringToNavVelocity exception:" + jsonString);
            velocity = null;
        }
        return velocity;
    }

    private NavAcceleration stringToAcceleration(String jsonString) {
        NavAcceleration navAcc = null;
        try {
            JSONObject json = new JSONObject(jsonString);
            double linearAcc = json.has(Definition.JSON_NAVI_LINEAR_ACCELERATION)
                    ? Double.valueOf(json.get(Definition.JSON_NAVI_LINEAR_ACCELERATION).toString()) : 0.0d;
            double angularAcc = json.has(Definition.JSON_NAVI_ANGULAR_ACCELERATION)
                    ? Double.valueOf(json.get(Definition.JSON_NAVI_ANGULAR_ACCELERATION).toString()) : 0.0d;
            navAcc = new NavAcceleration(linearAcc, angularAcc);
        } catch (JSONException e) {
            e.printStackTrace();
            Log.e(TAG, "stringToAcceleration exception:" + jsonString);
            navAcc = null;
        }
        return navAcc;
    }

    private NavMode stringToNavMode(String jsonString) {
        NavMode navMode = null;
        try {
            JSONObject json = new JSONObject(jsonString);
            int startModeLevel = json.has(Definition.JSON_NAVI_START_MODE_LEVEL)
                    ? Integer.valueOf(json.get(Definition.JSON_NAVI_START_MODE_LEVEL).toString()) : 0;
            int brakeModeLevel = json.has(Definition.JSON_NAVI_BRAKE_MODE_LEVEL)
                    ? Integer.valueOf(json.get(Definition.JSON_NAVI_BRAKE_MODE_LEVEL).toString()) : 0;
            navMode = new NavMode(startModeLevel, brakeModeLevel);
        } catch (JSONException e) {
            e.printStackTrace();
            Log.e(TAG, "stringToNavMode exception:" + jsonString);
            navMode = null;
        }
        return navMode;
    }

    /**
     * 获取是否需要调整角度
     * @param jsonString 导航参数 JSON 字符串
     * @return true 如果需要调整角度，false 如果不需要
     */
    private boolean getIsAdjustAngle(String jsonString) {
        Log.d(TAG, "getIsAdjustAngle:" + jsonString);
        if (TextUtils.isEmpty(jsonString)) {
            return false;
        }
        try {
            JSONObject jsonObject = new JSONObject(jsonString);
            boolean hasAdjustAngleParam = jsonObject.has(Definition.JSON_NAVI_ISADJUST_ANGLE);
            boolean adjustAngle = hasAdjustAngleParam && jsonObject.optBoolean(Definition.JSON_NAVI_ISADJUST_ANGLE);
            boolean globalAdjustAngle = RobotSettingApi.getInstance().getRobotInt(ROBOT_SETTINGS_ISADJUSTANGLE) == 1; // carry机型用这个全局参数控制，其余不用
            String destinationName = jsonObject.optString(Definition.JSON_NAVI_DESTINATION_NAME);
            if (TextUtils.isEmpty(destinationName)) {
                return adjustAngle;
            }
            PlaceInfo placeInfo = NavigationDataManager.getInstance().getPlaceByName(destinationName);
            Log.d(TAG, "placeInfo:" + placeInfo);
            if (placeInfo == null) {
                return false;
            }
            if (ProductInfo.isCarryProduct()) { // carry机型无方向停靠逻辑：优先判断全局无方向字段是否开启，且只针对普通点位生效
                if (globalAdjustAngle &&  placeInfo.getTypeId() == 0) {
                    return placeInfo.getNoDirectionalParking(); // 普通点位还需要根据noDirectionalParking字段判断,地图工具中创建点位设置
                } else {
                    return adjustAngle; // 否则就直接用上层传入的值adjustAngle
                }
            }
            return adjustAngle;
        } catch (JSONException e) {
            Log.e(TAG, "getIsAdjustAngle error: " + e.getMessage());
            return false;
        }
    }


    private boolean getIsReversePoseTheta(String jsonString) {
        Log.e(TAG, "getIsReversePoseTheta: jsonString=" + jsonString);
        if (TextUtils.isEmpty(jsonString)) {
            return false;
        }
        boolean isReversePoseTheta = false;
        try {
            JSONObject jsonObject = new JSONObject(jsonString);
            isReversePoseTheta = jsonObject.has(JSON_NAVI_IS_REVERSE_POSE_THETA)
                    ? jsonObject.getBoolean(Definition.JSON_NAVI_IS_REVERSE_POSE_THETA) : false;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return isReversePoseTheta;
    }

    private int getTaskPriority(String jsonString) {
        if (TextUtils.isEmpty(jsonString)) {
            return 0;
        }
        int priority = 0;
        try {
            JSONObject jsonObject = new JSONObject(jsonString);
            priority = jsonObject.has(Definition.JSON_NAVI_TASK_PRIORITY)
                    ? jsonObject.getInt(Definition.JSON_NAVI_TASK_PRIORITY) : 0;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return priority;
    }

    private boolean getIsKeepMove(String jsonString) {
        if (TextUtils.isEmpty(jsonString)) {
            return false;
        }
        boolean keepMove = false;
        try {
            JSONObject jsonObject = new JSONObject(jsonString);
            keepMove = jsonObject.has(Definition.JSON_NAVI_IS_KEEP_MOVE)
                    ? jsonObject.getBoolean(Definition.JSON_NAVI_IS_KEEP_MOVE) : false;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return keepMove;
    }

    private double getNavigationDestinationRange(String jsonString) {
        double keepMove = Def.ROBOT_NAVIGATION_DEFAULT_DESTINATION_RANGE;
        if (TextUtils.isEmpty(jsonString)) {
            return keepMove;
        }
        try {
            JSONObject jsonObject = new JSONObject(jsonString);
            keepMove = jsonObject.has(Definition.JSON_NAVI_DESTINATION_RANGE)
                    ? jsonObject.getDouble(Definition.JSON_NAVI_DESTINATION_RANGE) : Def.ROBOT_NAVIGATION_DEFAULT_DESTINATION_RANGE;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return keepMove;
    }

    private double getNavigationObsDistance(String jsonString) {
        double defaultObsDistance;
        if (RobotSettingApi.getInstance().hasRobotSetting("robot_setting_navi_block_obstacle_distance")) {
            defaultObsDistance = RobotSettingApi.getInstance().getRobotFloat("robot_setting_navi_block_obstacle_distance");
            Log.d(TAG, "getNavigationObsDistance: hasRobotSetting: defaultObsDistance=" + defaultObsDistance);
        } else {
            defaultObsDistance = Def.ROBOT_NAVIGATION_DEFAULT_OBS_DISTANCE;
        }

        double obsDistance = defaultObsDistance;

        if (TextUtils.isEmpty(jsonString)) {
            return obsDistance;
        }
        try {
            JSONObject jsonObject = new JSONObject(jsonString);
            obsDistance = jsonObject.has(Definition.JSON_NAVI_OBSTACLE_DISTANCE)
                    ? jsonObject.getDouble(Definition.JSON_NAVI_OBSTACLE_DISTANCE) : defaultObsDistance;
        } catch (JSONException e) {
            e.printStackTrace();
        }

        //传0时，底盘默认使用默认值
        if(obsDistance <= 0){
            obsDistance = defaultObsDistance;
        }

        return obsDistance;
    }

    private double getPosTolerance(String jsonString) {
        double tolerance = TargetPose.POS_TOLERANCE_DEFAULT;
        if (TextUtils.isEmpty(jsonString)) {
            return tolerance;
        }
        try {
            JSONObject jsonObject = new JSONObject(jsonString);
            tolerance = jsonObject.has(Definition.JSON_NAVI_POS_TOLERANCE)
                    ? jsonObject.getDouble(Definition.JSON_NAVI_POS_TOLERANCE) : TargetPose.POS_TOLERANCE_DEFAULT;
        } catch (JSONException e) {
            e.printStackTrace();
        }

        //传值<min时，使用默认值
        if (tolerance < TargetPose.POS_TOLERANCE_MIN) {
            tolerance = TargetPose.POS_TOLERANCE_DEFAULT;
        }

        return tolerance;
    }

    private double getAngleTolerance(String jsonString) {
        double tolerance = TargetPose.ANGLE_TOLERANCE_DEFAULT;
        if (TextUtils.isEmpty(jsonString)) {
            return tolerance;
        }
        try {
            JSONObject jsonObject = new JSONObject(jsonString);
            tolerance = jsonObject.has(Definition.JSON_NAVI_ANGLE_TOLERANCE)
                    ? jsonObject.getDouble(Definition.JSON_NAVI_ANGLE_TOLERANCE) : TargetPose.ANGLE_TOLERANCE_DEFAULT;
        } catch (JSONException e) {
            e.printStackTrace();
        }

        //传0时，使用默认值
        //传值<min时，使用默认值
        if (tolerance < TargetPose.ANGLE_TOLERANCE_MIN) {
            tolerance = TargetPose.ANGLE_TOLERANCE_DEFAULT;
        }

        return tolerance;
    }

    private int getRoadMode(String jsonString) {
        int defaultRoadMode = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTINGS_NAVIGATION_LINE_TRACKING);
        Log.d(TAG, "getRoadMode: defaultRoadMode=" + defaultRoadMode);
        int roadMode = defaultRoadMode;
        if (TextUtils.isEmpty(jsonString)) {
            return defaultRoadMode;
        }
        try {
            JSONObject jsonObject = new JSONObject(jsonString);
            roadMode = jsonObject.has(Definition.JSON_NAVI_ROAD_MODE) ?
                    jsonObject.optInt(Definition.JSON_NAVI_ROAD_MODE) : defaultRoadMode;
        } catch (JSONException e) {
            e.printStackTrace();
        }

        //传0时，使用默认值
        //传值<min时，使用默认值
        if (roadMode < TargetPose.ROAD_MODE_MAP_PATH || roadMode > TargetPose.ROAD_MODE_GRAPH_PATH) {
            roadMode = defaultRoadMode;
        }
        Log.d(TAG, "getRoadMode: roadMode=" + roadMode);
        return roadMode;
    }

    private boolean isChassisReady() {
        return chassisClient.isSocketConnected();
    }

    private void sendStatusReport(String type, String data) {
        RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, type, data);
    }

    private void sendExceptionReport(String type, String data) {
        RobotCore.sendExceptionReport(RobotOS.NAVIGATION_SERVICE, type, data);
    }

    private void sendAsyncResponse(String type, int result, String message) {
        RobotCore.sendAsyncResponse(type, result, message);
    }

    private void sendAsyncResponse(String type, String message) {
        RobotCore.sendAsyncResponse(type, Definition.RESULT_SUCCEED, message);
    }

    private void sendAsyncResponse(String type, int result, String message, String extraJson) {
        RobotCore.sendAsyncResponse(type, result, message, extraJson);
    }

    private void sendAsyncStatus(String type, String status) {
        RobotCore.sendAsyncStatus(type, status);
    }

    private void sendAsyncStatus(String type, String status, String extraJson) {
        RobotCore.sendAsyncStatus(type, status, extraJson);
    }

    @Override
    public boolean goCharge(final String cmdType, String cmdParam) {
        boolean isFrontCamera = false;
        try {
            JSONObject jsonObject = new JSONObject(cmdParam);
            isFrontCamera = jsonObject.getBoolean(JSON_NAVI_IS_FRONT_CHARGE);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        chassisClient.goCharge(isFrontCamera, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "goCharge resultCode = " + resultCode + ", result = " + result);
                if (resultCode <= 0) {
                    sendAsyncStatus(cmdType, (String) result);
                } else {
                    sendAsyncResponse(cmdType, (String) result);
                }

            }
        });
        return true;
    }

    @Override
    public boolean stopCharge(final String cmdType) {
        chassisClient.stopCharge(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "stopCharge resultCode = " + resultCode + ", result = " + result);
                sendAsyncResponse(cmdType, (String) result);
            }
        });
        return true;
    }

    @Override
    public boolean motionPid(String params) {
//        Log.d(TAG, "Command motionPid");
        try {
            JSONObject json = new JSONObject(params);
            double angle = json.getDouble(Definition.JSON_NAVI_ANGLE);
            double latency = json.optDouble(Definition.JSON_NAVI_LATENCY);
            boolean bodyFollow = json.optBoolean(Definition.JSON_NAVI_BODY_FOLLOW);
            Velocity velocity;
            if (bodyFollow) {
                velocity = chassisClient.getFollowVelocity(angle, latency);
            } else {
                velocity = chassisClient.getFollowVelocity(angle, latency);
            }
            if (velocity != null) {
                chassisClient.motionPid(velocity.getZ(), velocity.getX());
            } else {
                chassisClient.stopMove();
            }
            return true;
        } catch (JSONException e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean setCameraEnable(final String cmdType, String cmdParam) {
        try {
            JSONObject json = new JSONObject(cmdParam);
            int cameraType = json.optInt(Definition.JSON_NAVI_CAMERA_TYPE);
            boolean enable = json.optBoolean(Definition.JSON_NAVI_ENABLE_STATE);
            Log.d(TAG, "setCameraEnable cmdParam:" + cmdParam + " cameraType:" + cameraType + " enable:" + enable);
            chassisClient.setCameraEnable(cameraType, enable, new IChassisClient.ChassisResListener() {
                @Override
                public void onResponse(boolean status, int resultCode, Object result) {
                    Log.d(TAG, "setCameraEnable resultCode = " + resultCode + ", result = " + result);
                    if (status) {
                        sendAsyncResponse(cmdType, SUCCEED);
                    } else {
                        sendAsyncResponse(cmdType, FAILED);
                    }
                }
            });
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    /**
     * 下发多机参数配置信息
     */
    @Override
    public boolean setMultiRobotSettingConfigData(final String cmdType, final String cmdParam) {
        try {
            Log.d(TAG, "setLoraSettingConfigData cmdParam:" + cmdParam);
            final MultiRobotConfigBean configInfo = mGson.fromJson(cmdParam, MultiRobotConfigBean.class);
            chassisClient.setMultiRobotSettingConfigData(configInfo, new IChassisClient.ChassisResListener() {
                @Override
                public void onResponse(boolean status, int resultCode, Object result) {
                    Log.d(TAG, "setLoraSettingConfigData resultCode = " + resultCode + ", result = " + result);
                    JSONObject resultJson = new JSONObject();
                    String dataStr = mGson.toJson(result);
                    try {
                        resultJson.put("resultData", dataStr);
                        resultJson.put("result", status ? SUCCEED : FAILED);
                        sendAsyncResponse(cmdType, resultJson.toString());
                    } catch (JSONException e) {
                        e.printStackTrace();
                        sendAsyncResponse(cmdType, FAILED);
                    }
                }
            });
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    /**
     * 下发Lora多机数据
     */
    @Override
    public boolean sendMultiRobotMsgData(final String cmdType, String cmdParam) {
        try {
            JSONObject json = new JSONObject(cmdParam);
            boolean isTest = json.optBoolean(Definition.JSON_NAVI_TEST_MESSAGE, false);
            Log.d(TAG, "sendLoraMsgData cmdParam:" + cmdParam + " isTest:" + isTest);
            if (isTest) {
                chassisClient.sendLoraTestMsg(new IChassisClient.ChassisResListener() {
                    @Override
                    public void onResponse(boolean status, int resultCode, Object result) {
                        Log.d(TAG, "sendLoraTestMsg resultCode = " + resultCode + ", result = " + result);
                        if (status) {
                            sendAsyncResponse(cmdType, (String) result);
                        } else {
                            sendAsyncResponse(cmdType, FAILED);
                        }
                    }
                });
            } else {
                String loraData = json.optString(Definition.JSON_NAVI_LORA_DATA, "");
                chassisClient.sendLoraMsgData(loraData, new IChassisClient.ChassisResListener() {
                    @Override
                    public void onResponse(boolean status, int resultCode, Object result) {
                        Log.d(TAG, "sendLoraMsgData resultCode = " + resultCode + ", result = " + result);
                        if (status) {
                            sendAsyncResponse(cmdType, SUCCEED);
                        } else {
                            sendAsyncResponse(cmdType, FAILED);
                        }
                    }
                });
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        sendAsyncResponse(cmdType, FAILED);
        return true;
    }

    @Override
    public boolean setLoraTestMode(final String cmdType, String cmdParam) {
        try {
            JSONObject json = new JSONObject(cmdParam);
            boolean enable = json.optBoolean(Definition.JSON_NAVI_LORA_TEST_ENABLE, false);
            Log.d(TAG, "setLoraTestMode cmdParam:" + cmdParam + " enable:" + enable);
            chassisClient.setLoraTestMode(enable, new IChassisClient.ChassisResListener() {
                @Override
                public void onResponse(boolean status, int resultCode, Object result) {
                    Log.d(TAG, "setLoraTestMode resultCode = " + resultCode + ", result = " + result);
                    if (status) {
                        sendAsyncResponse(cmdType, SUCCEED);
                    } else {
                        sendAsyncResponse(cmdType, FAILED);
                    }
                }
            });
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        sendAsyncResponse(cmdType, FAILED);
        return true;
    }

    @Override
    public boolean resetLoraDefaultConfig(final String cmdType, String cmdParam) {
        try {
            Log.d(TAG, "resetLoraDefaultConfig");
            chassisClient.resetLoraDefaultConfig(new IChassisClient.ChassisResListener() {
                @Override
                public void onResponse(boolean status, int resultCode, Object result) {
                    Log.d(TAG, "resetLoraDefaultConfig resultCode = " + resultCode + ", result = " + result);
                    if (status) {
                        sendAsyncResponse(cmdType, SUCCEED);
                    } else {
                        sendAsyncResponse(cmdType, FAILED);
                    }
                }
            });
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        sendAsyncResponse(cmdType, FAILED);
        return true;
    }

    /**
     * 控制轮子状态
     */
    @Override
    public boolean setWheelControlMode(final String cmdType, String cmdParam) {
        try {
            JSONObject jsonObject = new JSONObject(cmdParam);
            int controlMode = jsonObject.optInt(Definition.JSON_NAVI_WHEEL_MODE, 0);
            boolean releaseMode = Definition.WheelControlMode.TYPE_RELEASE.getValue() == controlMode;
            chassisClient.setWheelControlMode(releaseMode, new IChassisClient.ChassisResListener() {
                @Override
                public void onResponse(boolean status, int resultCode, Object result) {
                    Log.d(TAG, "setWheelControlMode: status:" + status + " resultCode:" + resultCode);
                    if (status) {
                        sendAsyncResponse(cmdType, SUCCEED);
                    } else {
                        sendAsyncResponse(cmdType, FAILED);
                    }
                }
            });
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public boolean getNaviPathInfoToGoals(final String cmdType, String cmdParam) {

        if (!chassisClient.isPoseEstimate()) {
            Log.d(TAG, "getNaviPathInfoToGoals, not estimate return!!!");
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        final Type type = new TypeToken<List<Pose>>() {
        }.getType();
        List<Pose> points;
        try {
            points = mGson.fromJson(cmdParam, type);
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, "navi path info to goals param : " + cmdParam);
            sendAsyncResponse(cmdType, PARAMS_ERROR);
            return false;
        }

        List<NaviPathInfo> pathList = new ArrayList<>();

        for (Pose pose : points) {
            NaviPathInfo pathInfo = new NaviPathInfo();
            pathInfo.setStartPose(chassisClient.getCurrentPose());
            if (TextUtils.isEmpty(pose.getName())) {
                pathInfo.setState(NaviPathInfo.STATE_POSE_NOT_EXIT);
            } else {
                PlaceInfo placeInfo = NavigationDataManager.getInstance().getPlaceByName(pose.getName());
                if (placeInfo != null) {
                    pose.setX(placeInfo.getPointX());
                    pose.setY(placeInfo.getPointY());
                    pose.setTheta(placeInfo.getPointTheta());
                } else {
                    Log.d(TAG, "can't find pose:" + pose.getName());
                }
            }
            pathInfo.setEndPose(pose);
            pathList.add(pathInfo);
        }

        chassisClient.calcNaviPathInfo(pathList, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "get navi to goals path info response, status:" + status + " ,resultCode:" + resultCode
                        + " ,result:" + (result == null ? "null" : result.toString()));
                if (status) {
                    sendAsyncResponse(cmdType, mGson.toJson(result, type));
                } else {
                    sendAsyncResponse(cmdType, FAILED);
                }
            }
        });
        return true;
    }

    @Override
    public boolean getNaviPathInfo(final String cmdType, String cmdParam) {

        if (!chassisClient.isPoseEstimate()) {
            Log.d(TAG, "getNaviPathInfo, not estimate return!!!");
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }

        NaviPathInfo pathInfo = new NaviPathInfo();
        try {
            JSONObject object = new JSONObject(cmdParam);
            String startPoseJson = object.optString("start_pose");
            String endPoseJson = object.optString("end_pose");
            pathInfo.setStartPose(mGson.fromJson(startPoseJson, Pose.class));
            pathInfo.setEndPose(mGson.fromJson(endPoseJson, Pose.class));
        } catch (JSONException e) {
            e.printStackTrace();
            Log.d(TAG, "parse json error, return!!!");
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }

        List<NaviPathInfo> pathList = new ArrayList<>();
        pathList.add(pathInfo);
        chassisClient.calcNaviPathInfo(pathList, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "get path info response, status:" + status + " ,resultCode:" + resultCode
                        + " ,result:" + (result == null ? "null" : result.toString()));
                if (status) {
                    List<NaviPathInfo> list = (List<NaviPathInfo>) result;
                    NaviPathInfo info = list.get(0);
                    sendAsyncResponse(cmdType, mGson.toJson(info));
                } else {
                    sendAsyncResponse(cmdType, FAILED);
                }
            }
        });
        return true;
    }

    @Override
    public boolean getNaviPathDetail(final String cmdType, String cmdParam) {
        if (!chassisClient.isPoseEstimate()) {
            Log.d(TAG, "getNaviPathInfo, not estimate return!!!");
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }

        NaviPathInfo pathInfo = new NaviPathInfo();
        try {
            JSONObject object = new JSONObject(cmdParam);
            String startPoseJson = object.optString("start_pose");
            String endPoseJson = object.optString("end_pose");
            pathInfo.setStartPose(mGson.fromJson(startPoseJson, Pose.class));
            pathInfo.setEndPose(mGson.fromJson(endPoseJson, Pose.class));
        } catch (JSONException e) {
            e.printStackTrace();
            Log.d(TAG, "parse json error, return!!!");
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }

        List<NaviPathDetail> pathList = new ArrayList<>();
        NaviPathDetail detail = new NaviPathDetail();
        detail.setPathInfo(pathInfo);
        pathList.add(detail);
        chassisClient.calcNaviPathDetail(pathList, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "get path info response, status:" + status + " ,resultCode:" + resultCode
                        + " ,result:" + (result == null ? "null" : result.toString()));
                if (status) {
                    List<NaviPathDetail> list = (List<NaviPathDetail>) result;
                    NaviPathDetail info = list.get(0);
                    sendAsyncResponse(cmdType, mGson.toJson(info));
                } else {
                    sendAsyncResponse(cmdType, FAILED);
                }
            }
        });
        return true;
    }

    @Override
    public boolean getNaviGatePassingRoute(final String cmdType, String cmdParam) {
        GatePathPrepareBean prepareResult = prepareGatePathCalculation(cmdType, cmdParam);
        if (prepareResult == null) {
            return false;
        }
        Log.d(TAG, "getNaviGatePassingRoute prepareResult: " + GsonUtil.toJson(prepareResult));

        // 选择第一条闸机线
        GateLineUnit bestGateLine = prepareResult.getGateLines().get(0);
        double enterX = bestGateLine.getEnterNode().getX();
        double enterY = bestGateLine.getEnterNode().getY();
        double outerX = bestGateLine.getOuterNode().getX();
        double outerY = bestGateLine.getOuterNode().getY();

        chassisClient.calcNaviPathDetail(prepareResult.getPathDetailList(), new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "get path info response, status:" + status + " ,resultCode:" + resultCode
                        + " ,result:" + (result == null ? "null" : result.toString()));
                if (status) {
                    List<NaviPathDetail> list = (List<NaviPathDetail>) result;
                    List<Vector2d> pathPoints = list.get(0).getPathList();
                    List<GateLineUnit.CustomVector2d> passingRoute = findGatePassingPoints(pathPoints, enterX, enterY, outerX, outerY);
                    if (passingRoute.size() != 2) {
                        Log.e(TAG, "passingRoute err, size():" + passingRoute.size());
                    }
                    // 创建包含ID的闸机线通过信息
                    sendAsyncResponse(cmdType, mGson.toJson(passingRoute));
                } else {
                    sendAsyncResponse(cmdType, FAILED);
                }
            }
        });
        return true;
    }

    @Override
    public boolean enableReportLineData(String cmdType, String cmdParam) {
        boolean enableLineData = false;
        try {
            JSONObject json = new JSONObject(cmdParam);
            enableLineData = json.optBoolean(Definition.JSON_NAVI_ENALBE_LINE_DATA);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Log.d(TAG, "enableReportLineData cmdType : " + cmdType + ", cmdParam :" + cmdParam);
        chassisClient.enableReportLineData(enableLineData);
        return true;
    }

    @Override
    public boolean enableRgbdDepthImage(String cmdType, String cmdParam) {
        boolean enable = false;
        int deviceId = 0;
        try {
            JSONObject json = new JSONObject(cmdParam);
            enable = json.optBoolean(Definition.JSON_NAVI_ENALBE_DEPTH_IMAGE);
            deviceId = json.optInt(Definition.JSON_NAVI_DEPTH_DEVICE);

        } catch (Exception e) {
            e.printStackTrace();
        }
        Log.d(TAG, "enableRgbdDepthImage cmdType : " + cmdType + ", cmdParam :" + cmdParam);
        chassisClient.enableReportDepthImage(enable, deviceId);
        sendAsyncResponse(cmdType, SUCCEED);
        return true;
    }

    @Override
    public boolean enableTopIRImage(String cmdType, String cmdParam) {
        boolean enable = false;
        int deviceId = 0;
        try {
            JSONObject json = new JSONObject(cmdParam);
            enable = json.optBoolean(Definition.JSON_NAVI_ENALBE_IR_IMAGE);
            deviceId = json.optInt(Definition.JSON_NAVI_IR_DEVICE);
        }catch (Exception e){
            e.printStackTrace();
        }
        Log.d(TAG, "enableTopIRImage cmdType : "+ cmdType+", cmdParam :"+cmdParam);
        chassisClient.enableReportIRImage(enable, deviceId + UpdateRawImageProtoWrapper.ImageDataProto.FrameType.Mono_VALUE);
        sendAsyncResponse(cmdType, SUCCEED);
        return true;
    }

    @Override
    public boolean autoDrawRoad(String cmdType, String cmdParam) {
        Log.d(TAG, "autoDrawRoad: mapName=" + cmdParam + " cmdType=" + cmdType);
        chassisClient.autoDrawRoadGraph(cmdParam, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "autoDrawRoad:onResponse: " + status
                        + " resultCode:" + resultCode
                        + " result:" + result);
                if (resultCode == SUCCESS && result != null) {
                    String road = (String) result;
                    Log.d(TAG, "autoDrawRoad:onResponse: road=" + road);
                    sendAsyncResponse(cmdType, road);
                } else {
                    sendAsyncResponse(cmdType, Definition.RESULT_FAILED, Definition.FAILED);
                }
            }
        });
        return false;
    }

    @Override
    public boolean getNaviParams(String cmdType, String cmdParam) {
        chassisClient.getNaviParams(cmdType, cmdParam, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (resultCode == SUCCESS && result != null) {
                    String params = (String) result;
                    Log.d(TAG, "getNaviParams:onResponse: params=" + params);
                    sendAsyncResponse(cmdType, params);
                } else {
                    sendAsyncResponse(cmdType, Definition.RESULT_FAILED, Definition.FAILED);
                }
            }
        });
        return false;
    }

    @Override
    public boolean naviPause(String cmdType, String cmdParam) {
        try {
            JSONObject json = new JSONObject(cmdParam);
            boolean isPause = json.optBoolean(Definition.JSON_NAVI_PAUSE_NAVIGATION, false);

            chassisClient.naviPause(isPause, new IChassisClient.ChassisResListener() {
                @Override
                public void onResponse(boolean status, int resultCode, Object result) {
                    if (resultCode == SUCCESS) {
                        sendAsyncResponse(cmdType, Definition.RESULT_SUCCEED, Definition.SUCCEED);
                    } else {
                        sendAsyncResponse(cmdType, Definition.RESULT_FAILED, Definition.FAILED);
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        sendAsyncResponse(cmdType, Definition.RESULT_FAILED, Definition.FAILED);
        return true;
    }

    public boolean gotoAlign(final String cmdType, String cmdParam) {
        Log.d(TAG, "Command go align isReady : " + isChassisReady());
        if (!isChassisReady()) {
            sendAsyncResponse(cmdType, "not ready");
            return false;
        }

        Pose pose = stringToPose(cmdParam);
        if (pose == null) {
            sendAsyncResponse(cmdType, "not exits");
            return false;
        }

        chassisClient.gotoAlign(pose, new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "gotoAlign resultCode = " + resultCode + ", result = " + result);
                if (resultCode <= 0) {
                    sendAsyncStatus(cmdType, (String) result);
                } else {
                    sendAsyncResponse(cmdType, (String) result);
                }

            }
        });
        return true;
    }

    @Override
    public boolean cancelAlign(final String cmdType, String cmdParam) {
        chassisClient.cancelAlign(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "cancelAlign resultCode = " + resultCode + ", result = " + result);
                sendAsyncResponse(cmdType, (String) result);
            }
        });
        return true;
    }

    @Override
    public boolean startHumanFollowing(String cmdType, String cmdParam) {
        Log.d(TAG, "Command start human following isReady : " + isChassisReady());
        if (!isChassisReady()) {
            sendAsyncResponse(cmdType, -1, "not ready");
            return false;
        }
        try {
            JSONObject object = new JSONObject(cmdParam);
            String followId = object.optString(Definition.JSON_NAVI_FOLLOW_ID);
            int lostFindTimeout = object.optInt(Definition.JSON_NAVI_FOLLOW_LOST_FIND_TIMEOUT);
            chassisClient.startHumanFollowing(followId, lostFindTimeout, (status, resultCode, result) -> {
                Log.d(TAG, "startHumanFollowing resultCode = " + resultCode + ", result = " + result);
                if (!status || resultCode < 0) {
                    sendAsyncResponse(cmdType, resultCode, (String) result);
                } else {
                    if (result instanceof HumanFollowBean) {
                        sendAsyncStatus(cmdType, ((HumanFollowBean) result).getStatus(), ((HumanFollowBean) result).getData());
                    } else {
                        sendAsyncStatus(cmdType, (String) result);
                    }
                }
            });
        } catch (JSONException e) {
            e.printStackTrace();
            Log.d(TAG, "parse json error, return!!!");
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        return true;
    }

    @Override
    public boolean stopHumanFollowing(String cmdType, String cmdParam) {
        chassisClient.stopHumanFollowing((status, resultCode, result) -> {
            Log.d(TAG, "stopHumanFollowing resultCode = " + resultCode + ", result = " + result);
            sendAsyncResponse(cmdType, resultCode, (String) result);
        });
        return true;
    }

    @Override
    public boolean detectQrCodeByPic(String cmdType, String cmdParam) {
        chassisClient.detectQrCodeByPic(cmdParam, new IChassisClient.ChassisResListener() {

            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "detectQrCodeByPic resultCode = " + resultCode + ", result = " + result);
                if (status) {
                    sendAsyncResponse(cmdType, Definition.RESULT_SUCCEED, Definition.SUCCEED, (String) result);
                } else {
                    sendAsyncResponse(cmdType, Definition.RESULT_SUCCEED, Definition.FAILED, (String) result);
                }
            }
        });
        return true;
    }

    @Override
    public boolean setMultiRobotWriteExtraData(String cmdType, String cmdParam) {
        try {
            JSONObject object = new JSONObject(cmdParam);
            JSONArray array = object.optJSONArray(Definition.JSON_NAVI_DATA);
            byte[] data = new byte[array.length()];
            for (int i = 0; i < array.length(); i++) {
                data[i] = (byte) array.getInt(i);
            }
            double time = object.optDouble(Definition.JSON_NAVI_TIME);
            chassisClient.setMultiRobotWriteExtraData(data, time, new IChassisClient.ChassisResListener() {
                @Override
                public void onResponse(boolean status, int resultCode, Object result) {
                    Log.d(TAG, "setMultiRobotWriteExtraData resultCode = " + resultCode + ", result = " + result);
                    sendAsyncResponse(cmdType, resultCode, (String) result);
                }
            });
        } catch (JSONException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 获取多个闸机线通过路径
     * 返回实际路径上经过所有闸机线附近的点
     * @param cmdType 命令类型
     * @param cmdParam 命令参数
     * @return 操作是否成功
     */
    @Override
    public boolean getMultiNaviGatePassingRoute(final String cmdType, String cmdParam) {
        long startTimeMillis = System.currentTimeMillis();
        GatePathPrepareBean prepareResult = prepareGatePathCalculation(cmdType, cmdParam);
        if (prepareResult == null) {
            Log.d(TAG, "prepareGatePathCalculation result is null");
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        Log.d(TAG, "prepareResult: " + GsonUtil.toJson(prepareResult));

        chassisClient.calcNaviPathDetail(prepareResult.getPathDetailList(), new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "getMultiNaviGatePassingRoute path info response, status:" + status + " ,resultCode:" + resultCode
                        + " ,result:" + (result == null ? "null" : result.toString()));
                if (status) {
                    try {
                        List<NaviPathDetail> list = (List<NaviPathDetail>) result;
                        List<Vector2d> pathPoints = list.get(0).getPathList();
                        // 1. 使用新方法按路径顺序查找所有闸机通过点
                        List<GateLineUnit.GatePassingInfo> allGatePassingInfo =
                            findAllGatePassingPointsInOrder(pathPoints, prepareResult.getGateLines());
                        // 2. 获取当前地图下的所有闸机点位 typeId = 9 或者 typeId = 10
                        List<PlaceInfo> gatePlaceInfos = NavigationDataManager.getInstance()
                            .getPlacesByTypeArr(new int[]{Definition.GATE_ENTER_TYPE, Definition.GATE_OUTER_TYPE});
                        // 3. 计算闸机点位和闸机线两端点的距离差给巡线两端点位的点位角度和点位名称赋值
                        List<GatePairPose> gatePairPoses = calculateRealArrivedGatePoints(gatePlaceInfos, allGatePassingInfo);
                        String resultJson = mGson.toJson(gatePairPoses);
                        sendAsyncResponse(cmdType, resultJson);
                    } catch (Exception e) {
                        Log.e(TAG, "Error processing path result", e);
                        sendAsyncResponse(cmdType, FAILED);
                    }
                } else {
                    sendAsyncResponse(cmdType, FAILED);
                }
            }
        });
        Log.i(TAG, "getMultiNaviGatePassingRoute cost times: " + (System.currentTimeMillis() - startTimeMillis));
        return true;
    }

    /**
     * 准备导航路径计算所需的数据
     * @param cmdType 命令类型
     * @param cmdParam 命令参数
     * @return 包含闸机线数据和路径计算所需信息的对象，如果准备失败则返回null
     */
    private GatePathPrepareBean prepareGatePathCalculation(String cmdType, String cmdParam) {
        if (!chassisClient.isPoseEstimate()) {
            Log.d(TAG, "prepareGatePathCalculation, not estimate return!!!");
            sendAsyncResponse(cmdType, FAILED);
            return null;
        }

        // 获取闸机线数据 单闸机线在这里存储过, 现在多闸机线直接从json文件读取
        //String gateLineNodeJson = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_GATE_EDGE_PIXEL_NODE);
        String mapName = NavigationDataManager.getInstance().getMapName();
        GateLineNode.LineNode gateLineNode = NavigationDataManager.getInstance().getGateLinesFromFile(mapName);

        if (gateLineNode == null || gateLineNode.getGateLines() == null) {
            Log.d(TAG, "prepareGatePathCalculation err: empty data or parse failed");
            sendAsyncResponse(cmdType, FAILED);
            return null;
        }

        // 获取当前地图的闸机线数据
        List<GateLineUnit> gateLines = gateLineNode.getGateLines();
        if (gateLines.isEmpty()) {
            Log.d(TAG, "prepareGatePathCalculation err: no gate lines found");
            sendAsyncResponse(cmdType, FAILED);
            return null;
        } else {
            Log.d(TAG, "Found " + gateLines.size() + " gate lines for map: " + mapName);
        }

        Log.d(TAG, "prepareGatePathCalculation gateLines: " + mGson.toJson(gateLines));
        // 关联闸机线与闸机设备
        linkGateLinesToDevices(gateLines);

        // 获取当前位置和目标位置
        Pose currentPose = chassisClient.getCurrentPose();
        Pose targetPose;
        if (hasPlaceName(cmdParam)) {
            targetPose = stringToDestination(cmdParam);
        } else {
            targetPose = stringToPose(cmdParam);
        }

        // 准备路径计算请求
        NaviPathInfo pathInfo = new NaviPathInfo();
        pathInfo.setStartPose(currentPose);
        pathInfo.setEndPose(targetPose);

        List<NaviPathDetail> pathList = new ArrayList<>();
        NaviPathDetail detail = new NaviPathDetail();
        detail.setPathInfo(pathInfo);
        pathList.add(detail);

        return new GatePathPrepareBean(gateLines, pathList);
    }

    /**
     * 在路径上寻找与闸机线最接近的点对
     * @param pathPoints 导航路径点列表
     * @param enterX 闸机线入口点X坐标
     * @param enterY 闸机线入口点Y坐标
     * @param outerX 闸机线出口点X坐标
     * @param outerY 闸机线出口点Y坐标
     * @return 包含入口和出口附近点的列表，如果找不到则可能少于2个点
     */
    private List<GateLineUnit.CustomVector2d> findGatePassingPoints(List<Vector2d> pathPoints, double enterX, double enterY, double outerX, double outerY) {
        List<GateLineUnit.CustomVector2d> passingRoute = new ArrayList<>(2);
        boolean addedEnter = false;
        boolean addedOuter = false;
        double thresholdSquared = 0.01 * 0.01;

        for (Vector2d point : pathPoints) {
            double distanceEnterSquared = Math.pow(point.getX() - enterX, 2) + Math.pow(point.getY() - enterY, 2);
            double distanceOuterSquared = Math.pow(point.getX() - outerX, 2) + Math.pow(point.getY() - outerY, 2);

            if (distanceEnterSquared < thresholdSquared && !addedEnter) {
                passingRoute.add(new GateLineUnit.CustomVector2d(point.getX(), point.getY()));
                addedEnter = true;
            } else if (distanceOuterSquared < thresholdSquared && !addedOuter) {
                passingRoute.add(new GateLineUnit.CustomVector2d(point.getX(), point.getY()));
                addedOuter = true;
            }

            if (addedEnter && addedOuter) {
                break;
            }
        }

        return passingRoute;
    }


    /**
     * 关联闸机线与闸机设备
     * 查询闸机线ID对应的闸机设备信息，并将设备ID设置到闸机线对象中
     *
     * @param gateLines 闸机线列表
     */
    private void linkGateLinesToDevices(List<GateLineUnit> gateLines) {
        try {
            // 提取闸机线ID数组
            int[] gateLineIds = gateLines.stream().mapToInt(GateLineUnit::getGateLineId).toArray();
            Log.d(TAG, "gateLineIds: " + gateLineIds.length);
            // 批量查询闸机关联信息
            JSONObject returnData = NavigationDataManager.getInstance().findByLineIds(gateLineIds);
            Log.d(TAG, "linkGateLinesToDevices returnData: " + returnData);

            // 处理查询结果
            if (returnData != null && returnData.optBoolean("success", false)) {
                String dataStr = returnData.optString("data", "[]");
                if (!TextUtils.isEmpty(dataStr)) {
                    // 解析设备关联信息
                    Type gateRelationInfoType = new TypeToken<List<GateRelationInfo>>() {}.getType();
                    List<GateRelationInfo> relationInfos = GsonUtil.getGson().fromJson(dataStr, gateRelationInfoType);

                    if (relationInfos != null && !relationInfos.isEmpty()) {
                        // 构建高效的查找映射
                        Map<Integer, GateRelationInfo> relationMap = new HashMap<>();
                        for (GateRelationInfo relationInfo : relationInfos) {
                            relationMap.put(relationInfo.getGateLineId(), relationInfo);
                        }

                        // 创建新的列表，只包含已绑定设备的闸机线
                        Iterator<GateLineUnit> iterator = gateLines.iterator();
                        while (iterator.hasNext()) {
                            GateLineUnit gateLine = iterator.next();
                            GateRelationInfo gateRelationInfo = relationMap.get(gateLine.getGateLineId());
                            if (gateRelationInfo != null) {
                                gateLine.setGateId(gateRelationInfo.getGateId());
                            } else {
                                // 移除未绑定设备的闸机线
                                iterator.remove();
                            }
                        }
                        Log.d(TAG, "Successfully linked gate devices to " + gateLines.size() + " gate lines");
                    } else {
                        // 如果没有关联信息，清空所有闸机线
                        gateLines.clear();
                        Log.d(TAG, "No gate relations found, cleared all gate lines");
                    }
                }
            } else {
                String errorMsg = returnData != null ? returnData.optString("message", "Unknown error") : "Null response";
                Log.w(TAG, "Failed to get gate relations: " + errorMsg);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error while linking gate devices to lines", e);
        }
    }

    /**
     * 按照路径顺序查找经过的所有闸机点
     * @param pathPoints 导航路径点列表
     * @param gateLines 所有闸机线信息
     * @return 按路径顺序排列的闸机通过信息列表
     */
    private List<GateLineUnit.GatePassingInfo> findAllGatePassingPointsInOrder(List<Vector2d> pathPoints, List<GateLineUnit> gateLines) {
        // 存储每个闸机的入口和出口点在路径中的索引
        Map<String, Integer> gateEnterIndices = new HashMap<>();
        Map<String, Integer> gateOuterIndices = new HashMap<>();
        // 存储每个闸机的入口和出口点坐标
        Map<String, GateLineUnit.CustomVector2d> gateEnterPoints = new HashMap<>();
        Map<String, GateLineUnit.CustomVector2d> gateOuterPoints = new HashMap<>();
        // 存储闸机ID与闸机线对象的映射
        Map<String, GateLineUnit> gateLineMap = new HashMap<>();

        // 阈值距离的平方，用于判断点是否接近闸机线
        double thresholdSquared = 0.01 * 0.01;

        // 遍历路径上的每个点
        for (int i = 0; i < pathPoints.size(); i++) {
            Vector2d point = pathPoints.get(i);

            // 检查该点是否接近任何闸机线的入口或出口
            for (GateLineUnit gateLine : gateLines) {
                String gateId = gateLine.getGateId();
                gateLineMap.putIfAbsent(gateId, gateLine);

                // 如果该闸机的入口和出口点都已找到，则跳过
                if (gateEnterIndices.containsKey(gateId) && gateOuterIndices.containsKey(gateId)) {
                    continue;
                }

                double enterX = gateLine.getEnterNode().getX();
                double enterY = gateLine.getEnterNode().getY();
                double outerX = gateLine.getOuterNode().getX();
                double outerY = gateLine.getOuterNode().getY();

                // 计算当前点到入口和出口的距离平方
                double distanceEnterSquared = Math.pow(point.getX() - enterX, 2) + Math.pow(point.getY() - enterY, 2);
                double distanceOuterSquared = Math.pow(point.getX() - outerX, 2) + Math.pow(point.getY() - outerY, 2);

                // 检查是否接近入口点
                if (distanceEnterSquared < thresholdSquared && !gateEnterIndices.containsKey(gateId)) {
                    gateEnterIndices.put(gateId, i);
                    gateEnterPoints.put(gateId, new GateLineUnit.CustomVector2d(point.getX(), point.getY()));
                }
                // 检查是否接近出口点
                else if (distanceOuterSquared < thresholdSquared && !gateOuterIndices.containsKey(gateId)) {
                    gateOuterIndices.put(gateId, i);
                    gateOuterPoints.put(gateId, new GateLineUnit.CustomVector2d(point.getX(), point.getY()));
                }
            }
        }

        // 构建最终结果
        List<GateLineUnit.GatePassingInfo> result = new ArrayList<>();

        // 处理每个找到了入口和出口点的闸机
        for (String gateId : gateEnterIndices.keySet()) {
            if (gateOuterIndices.containsKey(gateId)) {
                int enterIndex = gateEnterIndices.get(gateId);
                int outerIndex = gateOuterIndices.get(gateId);
                GateLineUnit gateLine = gateLineMap.get(gateId);

                // 确定通过顺序和方向
                boolean isForward = enterIndex < outerIndex; // 如果入口点在路径中的索引小于出口点，则为正向通过
                int pathIndex = Math.min(enterIndex, outerIndex); // 使用较小的索引作为路径索引，用于排序

                // 创建通过点列表，按照路径顺序排列
                List<GateLineUnit.CustomVector2d> passingPoints = new ArrayList<>(2);
                if (isForward) {
                    passingPoints.add(gateEnterPoints.get(gateId));
                    passingPoints.add(gateOuterPoints.get(gateId));
                } else {
                    passingPoints.add(gateOuterPoints.get(gateId));
                    passingPoints.add(gateEnterPoints.get(gateId));
                }

                // 创建包含通过方向的闸机通过信息
                GateLineUnit.GatePassingInfo gateInfo = new GateLineUnit.GatePassingInfo(
                        gateLine.getGateLineId(), gateId, passingPoints, isForward, pathIndex);
                result.add(gateInfo);
            }
        }

        // 按路径索引排序
        result.sort(Comparator.comparingInt(GateLineUnit.GatePassingInfo::getPathIndex));

        return result;
    }

    private List<GatePairPose> calculateRealArrivedGatePoints(List<PlaceInfo> gatePointLists, List<GateLineUnit.GatePassingInfo> multiPoseList) {
        List<GatePairPose> gatePairPoseList = new ArrayList<>();
        if (gatePointLists != null && !gatePointLists.isEmpty()) {
            for (GateLineUnit.GatePassingInfo gatePassingInfo : multiPoseList) {
                // Create gate pair
                GatePairPose gatePair = new GatePairPose();
                gatePair.setGateIdentifier(gatePassingInfo.getPkGate());

                com.ainirobot.coreservice.client.actionbean.Pose firstGatePose = new com.ainirobot.coreservice.client.actionbean.Pose();
                firstGatePose.setX((float) gatePassingInfo.getPassingPoints().get(0).getX());
                firstGatePose.setY((float) gatePassingInfo.getPassingPoints().get(0).getY());

                com.ainirobot.coreservice.client.actionbean.Pose secondGatePose = new com.ainirobot.coreservice.client.actionbean.Pose();
                secondGatePose.setX((float) gatePassingInfo.getPassingPoints().get(1).getX());
                secondGatePose.setY((float) gatePassingInfo.getPassingPoints().get(1).getY());

                PlaceInfo closestToFirst = findClosestLocalPoint(firstGatePose, gatePointLists);
                PlaceInfo closestToSecond = findClosestLocalPoint(secondGatePose, gatePointLists);
                String mapLanguage = NavigationDataManager.getInstance().getCurrentMapLanguage();
                if (closestToFirst != null) {
                    firstGatePose.setTheta(closestToFirst.getPointTheta());
                    firstGatePose.setTypeId(closestToFirst.getTypeId());
                    firstGatePose.setName(closestToFirst.getPlaceName(mapLanguage));
                }
                if (closestToSecond != null) {
                    secondGatePose.setTheta(closestToSecond.getPointTheta());
                    secondGatePose.setTypeId(closestToSecond.getTypeId());
                    secondGatePose.setName(closestToSecond.getPlaceName(mapLanguage));
                }

                gatePair.setFirstPose(firstGatePose);
                gatePair.setSecondPose(secondGatePose);
                gatePairPoseList.add(gatePair);
            }
        }
        return gatePairPoseList;
    }

    private PlaceInfo findClosestLocalPoint(com.ainirobot.coreservice.client.actionbean.Pose gatePoint, List<PlaceInfo> localDataPoints) {
        PlaceInfo closestPoint = null;
        double minDistance = Double.MAX_VALUE;

        for (PlaceInfo localPoint : localDataPoints) {
            double distance = Math.sqrt(
                    Math.pow(gatePoint.getX() - localPoint.getPointX(), 2) +
                            Math.pow(gatePoint.getY() - localPoint.getPointY(), 2)
            );

            if (distance < minDistance) {
                minDistance = distance;
                closestPoint = localPoint;
            }
        }

        if (minDistance > 1.0) {
            return null;
        }

        return closestPoint;
    }

}