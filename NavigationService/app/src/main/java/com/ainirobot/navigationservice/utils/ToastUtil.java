package com.ainirobot.navigationservice.utils;


import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.widget.Toast;

public class ToastUtil {


    public static void showToast(final Context context, final String text) {

        if (isMainThread()) {
            Toast.makeText(context, text, Toast.LENGTH_SHORT).show();
        } else {
            Handler handler = new Handler(Looper.getMainLooper());
            handler.post(() -> Toast.makeText(context, text, Toast.LENGTH_LONG).show());
        }
    }

    private static boolean isMainThread() {
        return Looper.getMainLooper() == Looper.myLooper();
    }
}

