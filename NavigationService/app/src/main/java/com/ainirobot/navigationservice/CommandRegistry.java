package com.ainirobot.navigationservice;

import com.ainirobot.coreservice.bean.Command;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.navigationservice.Defs.Def;

import java.util.ArrayList;
import java.util.List;

public class CommandRegistry {

    /**
     * 加载地图超时时间
     */
    private static final long LOAD_MAP_TIME_OUT = 5 * Definition.MINUTE;

    //TODO 加载地图超时时间调试
    private static boolean mUseVisionMapTime = true;

    private static final List<Command> sTK1Commands;

    static {
        sTK1Commands = new ArrayList<Command>() {
            {
                add(new Command(Definition.CMD_NAVI_GET_LOCATION, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_LOCATION_HIGH_FREQUENCY, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_LOCATION, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_REMOVE_MAP, 30 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_IS_IN_NAVIGATION, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GO_LOCATION));
                add(new Command(Definition.CMD_NAVI_STOP_NAVIGATION, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_STOP_MOVE, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_CRUISELAYOUT_START, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_CRUISELAYOUT_STOP, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_CRUISE_START));
                add(new Command(Definition.CMD_NAVI_CRUISE_STOP, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_RELOCATION, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_MOVE_DIRECTION));
                add(new Command(Definition.CMD_NAVI_ROTATE_IN_PLACE));
                add(new Command(Definition.CMD_NAVI_IS_IN_LOCATION, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_MOVE_DISTANCE_ANGLE));
                add(new Command(Definition.CMD_NAVI_START_CREATING_MAP, 30 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_STOP_CREATING_MAP, 60 * 60 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_STOP_EXPANSION_MAP, 60 * 60 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_POSE_LOCATION, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_POSE_ESTIMATE, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SWITCH_MAP, LOAD_MAP_TIME_OUT));
                add(new Command(Definition.CMD_NAVI_GET_PLACE_NAME, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_PLACE_LIST, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_INTERNATIONAL_PLACE_LIST, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_INTERNATIONAL_PLACE_LIST_FOR_REPORT, 10 * Definition.SECOND));
                add(new Command("cmd_navi_get_international_place_list_inter", 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_PLACE_LIST_WITH_NAME, 6 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_PLACELIST_WITH_NAMELIST, 6 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GO_POSITION));
                add(new Command(Definition.CMD_NAVI_GO_POSITION_BY_TYPE));
                add(new Command(Definition.CMD_NAVI_GET_VERSION, 15 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_START_UPDATE));
                add(new Command(Definition.CMD_NAVI_GET_UPDATE_PARAMS, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_MOVE_DIRECTION_ANGLE));
                add(new Command(Definition.CMD_NAVI_MOVE_DIRECTION_ANGLE_SOFT));
                add(new Command(Definition.CMD_NAVI_IS_ESTIMATE, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SAVE_ESTIMATE));
                add(new Command(Definition.CMD_NAVI_GO_DEFAULT_THETA, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_FULL_CHECK_STATUS, 30 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_SENSOR_STATUS, 40 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_MAP_NAME, 3 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_POSITION, 3 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SWITCH_AUTO_CHARGE_MODE));
                add(new Command(Definition.CMD_NAVI_SWITCH_MANUAL_MODE));
                add(new Command(Definition.CMD_NAVI_SET_MAP_INFO, 120 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_LOCATE_VISION, 3 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_RESET_ESTIMATE, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_CONFIG, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_CONFIG, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_SERIAL_NUMBER, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_PATROL_LIST, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_PATROL_LIST, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_RESUME_SPECIAL_PLACE_THETA, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_REFRESH_MD5, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_LOAD_CURRENT_MAP, mUseVisionMapTime ? LOAD_MAP_TIME_OUT : 20 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_FIXED_ESTIMATE, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_PARSE_PLACE_LIST, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SAVE_ROAD_DATA, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SAVE_GATE_DATA, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_AUTO_DRAW_ROAD, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_CRUISE_ROUTE, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_CRUISE_ROUTE, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_CLEAR_CUR_NAVI_MAP, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_EDIT_PLACE, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_PLACELIST_BY_MAPNAME, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_HAS_PLACE_IN_MAPNAME, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_PLACELIST_BY_TYPE, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_PLACELIST_BY_MAPNAME_MINI, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_ERROR_LOG, 300 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_PACK_LOG_FILE, 300 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_LOG_FILE, 300 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_CHECK_CUR_NAVI_MAP, 30 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_SHOT_LOG, 60 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_TIME_OUT_REPORT));
                add(new Command(Definition.CMD_NAVI_TIME_OUT_MSG_DELETE));
                add(new Command(Definition.CMD_NAVI_GET_LOG_BY_ID, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_UPDATE_LOG_STATUS_BY_ID, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_RENAME_MAP, 15 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_CLEAR_CRUISE_ROUTE, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_RADAR_STATUS, 35 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_QUERY_RADAR_STATUS, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_CHECK_POSE_POSITION, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_TURN_BY_NAVIGATION));
                add(new Command(Definition.CMD_NAVI_CHECK_OBSTACLE, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_HAS_OBSTACLE_IN_AREA, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_RECOVERY, 60 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_ADD_MAPPING_POSE, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_DELETE_MAPPING_POSE, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_MAP_STATUS, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_FORCE_ESTIMATE, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_RENAME_MAPPING_POSE, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_MOVE_DISTANCE_ANGLE_WITH_OBSTACLES, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_MIN_OBSTACLES_DISTANCE, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_RESET_MIN_OBSTACLES_DISTANCE, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_MOVE_DIRECTION_ANGLE_OBSTACLES, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_VISION_CHARGE_START, 180 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_VISION_CHARGE_STOP, 60 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_UPDATE_PLACE_LIST, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SAVE_PLACE_LIST, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_START_EXTEND_MAP, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_CANCEL_CREATE_MAP, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_RELOCATION, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_MOVE_ANGLE_PID));
                add(new Command(Definition.CMD_NAVI_GET_MAP_INFO, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_MAP_UUID, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_MAP_SYNC_STATE, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_MAP_FINISH_STATE, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_MAP_UPDATE_TIME, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_CAMERA_STATE, 3 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_MAP_FORBID_LINE, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_ADD_MAP_INFO, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_PARSE_MAP_DATA, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_MULTI_ROBOT_CONFIG, 15 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SEND_MULTI_ROBOT_MESSAGE, 15 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_MULTI_ROBOT_CONFIG, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_MULTI_ROBOT_TEST_ENABLE, 3 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_RESET_MULTI_ROBOT_CONFIG, 3 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_WHEEL_CONTROL_MODE, 3 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_NAVI_PATH_INFO, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_NAVI_PATH_INFO_TO_GOALS, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_NAVI_PATH_DETAIL, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_NAVI_GATE_PASSING_ROUTE, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_ADDITIONAL_DEVICES, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_MAPPING_INFO, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_MAPPING_PLACE, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_ENABLE_REPORT_LINE_DATA, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_ENABLE_DEPTH_IMAGE, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_ENABLE_IR_IMAGE, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_START_DATA_SET_RECORD, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_STOP_DATA_SET_RECORD, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_UPLOAD_NAVI_DATA_SET, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_WRITE_MULTI_ROBOT_EXTRA_DATA, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_QUERY_MULTI_FLOOR_CONFIG, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_INSERT_MULTI_FLOOR_CONFIG, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_UPDATE_MULTI_FLOOR_CONFIG, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_REMOVE_MULTI_FLOOR_CONFIG, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_QUERY_CHARGE_AREA_CONFIG, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_INSERT_CHARGE_AREA_CONFIG, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_UPDATE_CHARGE_AREA_CONFIG, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_REMOVE_CHARGE_AREA_CONFIG, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_MULTI_FLOOR_CONFIG_AND_POSE, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_MULTI_FLOOR_CONFIG_AND_COMMON_POSE, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_MAP_HAS_VISION, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_SET_EXTRA_FILE_DATA, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_EXTRA_FILE_DATA, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_HAS_EXTRA_FILE, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_ZIP_EXTRA_FILE, 60 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_UNZIP_EXTRA_FILE, 60 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_ZIP_MAP_FILE, 60 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_UNZIP_MAP_FILE, 60 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_COPY_IMPORT_MAP_FILE, 60 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_LOCAL_MAP_INFO_LIST, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_NAVI_ANGLE_SPEED, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_MAP_INFO_BY_SD_MAP_NAMES, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_CURRENT_MAP_NAME, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_MOTION_DISTANCE, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_PAUSE_NAVIGATION, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_MOTION_DISTANCE, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_ALIGN_START, 180 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_ALIGN_CANCEL, 60 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_OTA_DOWNGRADE, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_START_HUMAN_FOLLOW));
                add(new Command(Definition.CMD_NAVI_STOP_HUMAN_FOLLOW, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_DETECT_QRCODE_BY_PIC, Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_POSITION_WITHOUT_ESTIMATE, 3 * Definition.SECOND));
                //闸机关系维护
                add(new Command(Definition.CMD_NAVI_FIND_BY_GATE_IDS, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_FIND_BY_LINE_IDS, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_BATCH_INSERT_OR_UPDATE_GATE, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_DELETE_BY_LINE_IDS, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_DELETE_BY_GATE_IDS, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_FIND_GATE_RELATION, 5 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_GET_NAVI_MULT_GATE_PASSING_ROUTE, 10 * Definition.SECOND));
                add(new Command(Definition.CMD_NAVI_DELETE_EXCEPT_LINE_IDS, 5 * Definition.SECOND));
            }
        };
    }

    public static List<Command> getTK1Commands() {
        return sTK1Commands;
    }

}
