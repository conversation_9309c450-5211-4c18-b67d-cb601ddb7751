package com.ainirobot.navigationservice.business.rpc;

import java.math.BigDecimal;

/**
 * speed bean
 *
 * @version V1.0.0
 * @date 2019/11/15 15:00
 */
public class SpeedBean {

    private BigDecimal angularSpeed = BigDecimal.ZERO;

    private BigDecimal linearSpeed = BigDecimal.ZERO;

    public SpeedBean() {
    }

    public SpeedBean(BigDecimal linearSpeed, BigDecimal angularSpeed) {
        this.linearSpeed = linearSpeed;
        this.angularSpeed = angularSpeed;
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof SpeedBean)) {
            return false;
        }
        SpeedBean target = ((SpeedBean) obj);
        return target.linearSpeed.compareTo(this.linearSpeed) == 0
                && target.angularSpeed.compareTo(this.angularSpeed) == 0;
    }

    public BigDecimal getAngularSpeed() {
        return this.angularSpeed;
    }

    public BigDecimal getLinearSpeed() {
        return this.linearSpeed;
    }

    public void setAngularSpeed(BigDecimal angularSpeed) {
        this.angularSpeed = angularSpeed;
    }

    public void setLinearSpeed(BigDecimal linearSpeed) {
        this.linearSpeed = linearSpeed;
    }

    @Override
    public String toString() {
        return "SpeedBean{" +
                "angularSpeed=" + angularSpeed +
                ", linearSpeed=" + linearSpeed +
                '}';
    }
}
