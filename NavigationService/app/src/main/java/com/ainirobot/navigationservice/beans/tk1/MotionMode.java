package com.ainirobot.navigationservice.beans.tk1;

public enum MotionMode {
    AUTO_MOVING(0),
    MANUAL_CONTROL(1),
    UNLOCK_WHEEL(2),
    AUTO_CHARGE(3),
    MOTION_MODE_SLEEP(4);

    private final int value;

    MotionMode(int value) {
        this.value = value;
    }

    public final int getValue() {
        return value;
    }

    public static MotionMode from(Object mode) {
        if (mode == null || !(mode instanceof Integer)) {
            return null;
        }
        switch ((int) mode) {
            case 0:
                return AUTO_MOVING;
            case 1:
                return MANUAL_CONTROL;
            case 2:
                return UNLOCK_WHEEL;
            default:
                return null;
        }
    }
}
