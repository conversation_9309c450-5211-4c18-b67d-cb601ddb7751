package com.ainirobot.navigationservice.business.rpc.algorithm;

import com.ainirobot.navigationservice.business.rpc.SpeedBean;

/**
 * stop motion Algorithm
 *
 * @version V1.0.0
 * @date 2019/11/15 15:00
 */
public class SingleLSStop extends MotionAlgorithm {

    public SingleLSStop() {
        super(null);
    }

    public SingleLSStop(SpeedBean target) {
        super(target);
    }

    @Override
    public void motion() {
        motion(0.0F, 0.0F);
    }
}
