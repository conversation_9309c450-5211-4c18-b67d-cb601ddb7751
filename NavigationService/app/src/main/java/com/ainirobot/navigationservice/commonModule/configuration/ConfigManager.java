package com.ainirobot.navigationservice.commonModule.configuration;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.CALIBRATION_READY;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.CAN_CONTROL_READY;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.CHECK_FAIL;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.CHECK_SUC;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.GYRO_READY;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.HARD_DISK_SPACE_ENOUGH;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.INFRARED;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.INFRARED_CAMERA_READY;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.INSPECTING_EXEC;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.LASER;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.LASER_AVAILABLE;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.NEED_CHECK;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.PING_NAV;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.RESET_CAMERA;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.RGBD;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.SELF_CHECK_RES;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.SERVICE_OK;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.SOCKET;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.SPEED_METER;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.STEP_PNIG;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.STEP_SELF_RES;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.STEP_SENSOR_CHECK;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.STEP_SERVICE_OK;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.STEP_SOCKET;
import static com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig.TIME_OUT_LEN;

import android.content.Context;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.StringRes;

import com.ainirobot.coreservice.IInspectCallBack;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.actionbean.ServiceInspectResultBean;
import com.ainirobot.coreservice.client.actionbean.ServiceInspectResultBean.FailBean;
import com.ainirobot.coreservice.client.subtype.DeviceSubType;
import com.ainirobot.coreservice.config.ServiceConfig;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.coreservice.utils.DelayTimer;
import com.ainirobot.navigationservice.Defs.Def;
import com.ainirobot.navigationservice.R;
import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.tk1.SensorStatus;
import com.ainirobot.navigationservice.beans.waiter.SubDeviceBean;
import com.ainirobot.navigationservice.chassisAbility.ChassisManager;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.commonModule.configuration.beans.DeviceInfo;
import com.ainirobot.navigationservice.commonModule.configuration.beans.ChassisHWConfig;
import com.ainirobot.navigationservice.commonModule.configuration.beans.InspectConfig;
import com.ainirobot.navigationservice.commonModule.configuration.beans.PublicConfig;
import com.ainirobot.navigationservice.db.NavigationDataManager;
import com.ainirobot.navigationservice.roversdkhelper.MapFileHelper;
import com.ainirobot.navigationservice.utils.GsonUtil;
import com.ainirobot.navigationservice.utils.NavigationConfig;
import com.ainirobot.navigationservice.utils.NetUtils;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import ninjia.android.proto.DeviceParamsProtoWrapper;

/**
 * <AUTHOR>
 * @date 20191011
 */

public class ConfigManager {
    private final static String TAG = TAGPRE + ConfigManager.class.getSimpleName();
    private Context mContext;

    private int mPingNavigationCount;
    private static final int PING_TOTAL_COUNT_NAVI = 20;
    private int DELAY_TIME = 30000;
    private ScheduledExecutorService executorService;
    private ChassisHWConfig chassisConfig;
    private InspectConfig inspectConfig;
    private PublicConfig publicConfig;
    private boolean enableNavigation;
    private Object tag = new Object();
    public volatile boolean alreadyInspect = false;
    public static List<Integer> sSupportVisionTypes = new ArrayList<>();
    public static List<Integer> sSupportTargetTypes = new ArrayList<>();
    /**
     * 当前支持的语言
     */
    private String[] supportLang;

    private static ConfigManager mInstance;

    private static int sDeviceTypeNumber;
    private static final DeviceInfo sDeviceInfo = MapFileHelper.getDeviceInfo();
    private static final SubDeviceBean sSubDeviceBean = MapFileHelper.getSubDeviceInfo();

    private static DeviceParamsProtoWrapper.DeviceParamsProto deviceParamsProto = null;

    /**
     * 多机设备类型
     */
    public enum MultiRobotModule {
        Lora, Esp32
    }

    private ConfigManager() {
        executorService = Executors.newSingleThreadScheduledExecutor();
    }

    public static synchronized ConfigManager getInstance() {
        if (mInstance == null) {
            mInstance = new ConfigManager();
        }
        return mInstance;
    }

    public void init(ServiceConfig config, Context mContext) {
        this.mContext = mContext;
//        supportLang = mContext.getResources().getStringArray(R.array.lang);

        if (config != null) {
            chassisConfig = GsonUtil.fromJson(config.getConfig(), ChassisHWConfig.class);
            inspectConfig = GsonUtil.fromJson(config.getInspection().toString(), InspectConfig.class);
            publicConfig = new Gson().fromJson(config.getPublicConfig(), PublicConfig.class);
            initMapSupportType();
            enableNavigation = config.isEnable();
            Log.d(TAG, "enableNavigation = " + enableNavigation + ", ChassisHWConfig = " + chassisConfig.toString()
                    + ", inspectConfig = " + inspectConfig.toString()
                    + ", publicConfig = " + publicConfig.toString());
        }

        if (chassisConfig == null || inspectConfig == null) {
            throw new RuntimeException("core navigation config is null");
        }
    }

    public String getDeviceType() {
        return chassisConfig.getDeviceType();
    }

    public synchronized int getSubDeviceTypeNumber() {
        if (sDeviceTypeNumber == RoverConfig.DEVICE_CUSTOM) {
            sDeviceTypeNumber = getRealDeviceTypeNumber();
        }
        return sDeviceTypeNumber;
    }

    public int getDeviceTypeNumber() {
        return chassisConfig.getDeviceTypeNumber();
    }

    private int getRealDeviceTypeNumber() {
        boolean useSubDevice = chassisConfig.isUseSubDeviceTypeNum();
        int deviceNumber;
        Log.d(TAG, "getDeviceTypeNumber: useSubDevice=" + useSubDevice);
        if (useSubDevice) {
            if ((deviceNumber = getRealDeviceTypeByDescribeFilter(chassisConfig.getDeviceTypeNumber())) != 0) {
                return deviceNumber;
            } else if ((deviceNumber = getRealDeviceTypeByFilter(chassisConfig.getDeviceTypeNumber())) != 0) {
                return deviceNumber;
            }
        }
        return chassisConfig.getDeviceTypeNumber();
    }

    private int getRealDeviceTypeByDescribeFilter(int type) {
        if (sSubDeviceBean == null || sDeviceInfo == null) {
            return RoverConfig.DEVICE_CUSTOM;
        }

        switch (type) {
            case RoverConfig.DEVICE_WAITER:
                if (DeviceInfo.MajorDepthType.ASJ_XB100.equals(sDeviceInfo.getMajorDepth())) {
                    if (sSubDeviceBean.getLidarFov() > 0 && sSubDeviceBean.getHeightBody() > 0) {
                        return RoverConfig.DEVICE_WAITER_SA_H_LW_X100;
                    }
                    if (sSubDeviceBean.getLidarFov() > 0) {
                        return RoverConfig.DEVICE_WAITER_SA_LW_X100;
                    }
                    if (sSubDeviceBean.getHeightBody() > 0) {
                        return RoverConfig.DEVICE_WAITER_SA_H_X100;
                    }
                    if (sSubDeviceBean.getDampener() > 0) {
                        return RoverConfig.DEVICE_WAITER_SA_X100;
                    }
                    return RoverConfig.DEVICE_WAITER_X100;
                } else if (DeviceInfo.MajorDepthType.YXSK_WF.equals(sDeviceInfo.getMajorDepth())) {
                    //这里的顺序不要调整，做的是优先级过滤
                    if (sSubDeviceBean.getLidarFov() > 0) {
                        return RoverConfig.DEVICE_WAITER_SA_LW_WF;
                    }
                    if (sSubDeviceBean.getDampener() > 0) {
                        return RoverConfig.DEVICE_WAITER_SA_WF;
                    }
                    return RoverConfig.DEVICE_WAITER_WF;
                } else if (DeviceInfo.BackCameraType.HP_6340_0c45.equals(sDeviceInfo.getBackCamera())) {
                    return RoverConfig.DEVICE_WAITER_DISINFECTION;
                }
                break;
            case RoverConfig.DEVICE_X86_MINI2:
                if (DeviceInfo.MajorDepthType.YXSK_WF.equals(sDeviceInfo.getMajorDepth())) {
                    if (DeviceInfo.FrontCameraType.HP_6341_0c45.equals(sDeviceInfo.getFrontCamera())) {
                        return RoverConfig.DEVICE_MINI2_WF_SSMONO;
                    } else {
                        return RoverConfig.DEVICE_MINI2_WF;
                    }
                } else if (DeviceInfo.FrontCameraType.HP_6341_0c45.equals(sDeviceInfo.getFrontCamera())) {
                    return RoverConfig.DEVICE_MINI2_SSMONO;
                }
                break;
            case RoverConfig.DEVICE_MESSIA_PLUS:
                if (DeviceInfo.MajorDepthType.YXSK_WF.equals(sDeviceInfo.getMajorDepth())) {
                    return RoverConfig.DEVICE_MESSIA_PLUS_WF;
                }
            case RoverConfig.DEVICE_WAITER_PRO:
                if (DeviceInfo.TopCameraType.HP_636d_636c.equals(sDeviceInfo.getTopCamera())) {
                    return RoverConfig.DEVICE_WAITER_PRO_TOP_MONO;
                } else if (sSubDeviceBean.getAutoDoor() > 0) {
                    return RoverConfig.DEVICE_WAITER_PRO_2XB40_X100_ELEC_GATE;
                } else if (DeviceInfo.MajorDepthType.ASJ_XB40.equals(sDeviceInfo.getMajorDepth())
                        && DeviceInfo.DownDepthType.ASJ_XB100.equals(sDeviceInfo.getDownDepth())) {
                    return RoverConfig.DEVICE_WAITER_PRO_2XB40_X100;
                }
        }

        return RoverConfig.DEVICE_CUSTOM;
    }

    private int getRealDeviceTypeByFilter(int type) {
        if (sSubDeviceBean == null) {
            return RoverConfig.DEVICE_CUSTOM;
        }

        switch (type) {
            case RoverConfig.DEVICE_WAITER:
                if (RoverConfig.DEPTH_TYPE_X100.equalsIgnoreCase(sSubDeviceBean.getDepthType())) {
                    //这里的顺序不要调整，做的是优先级过滤
                    if (sSubDeviceBean.getLidarFov() > 0 && sSubDeviceBean.getHeightBody() > 0) {
                        return RoverConfig.DEVICE_WAITER_SA_H_LW_X100;
                    }
                    if (sSubDeviceBean.getLidarFov() > 0) {
                        return RoverConfig.DEVICE_WAITER_SA_LW_X100;
                    }
                    if (sSubDeviceBean.getHeightBody() > 0) {
                        return RoverConfig.DEVICE_WAITER_SA_H_X100;
                    }
                    if (sSubDeviceBean.getDampener() > 0) {
                        return RoverConfig.DEVICE_WAITER_SA_X100;
                    }
                    return RoverConfig.DEVICE_WAITER_X100;
                }
                if (sSubDeviceBean.getRgbdWf() > 0) {
                    //这里的顺序不要调整，做的是优先级过滤
                    if (sSubDeviceBean.getLidarFov() > 0) {
                        return RoverConfig.DEVICE_WAITER_SA_LW_WF;
                    }
                    if (sSubDeviceBean.getDampener() > 0) {
                        return RoverConfig.DEVICE_WAITER_SA_WF;
                    }
                    return RoverConfig.DEVICE_WAITER_WF;
                }
                if (sSubDeviceBean.getRgbdFm1() > 0) {
                    return RoverConfig.DEVICE_WAITER_SA_FM1;
                }
                if (sSubDeviceBean.getChargeIr() > 0) {
                    return RoverConfig.DEVICE_WAITER_DISINFECTION;
                }
                //这里的顺序不要调整，做的是优先级过滤
                if (sSubDeviceBean.getLidarFov() > 0 && sSubDeviceBean.getHeightBody() > 0) {
                    return RoverConfig.DEVICE_WAITER_SA_H_LW;
                }
                if (sSubDeviceBean.getLidarFov() > 0) {
                    return RoverConfig.DEVICE_WAITER_SA_LW;
                }
                if (sSubDeviceBean.getHeightBody() > 0) {
                    return RoverConfig.DEVICE_WAITER_SA_H;
                }
                if (sSubDeviceBean.getDampener() > 0) {
                    return RoverConfig.DEVICE_WAITER_SA;
                }
                break;
            case RoverConfig.DEVICE_X86_MINI2:
                if (sSubDeviceBean.getRgbdWf() > 0 && sSubDeviceBean.getSsMono() > 0) {
                    return RoverConfig.DEVICE_MINI2_WF_SSMONO;
                } else if (sSubDeviceBean.getRgbdWf() > 0) {
                    return RoverConfig.DEVICE_MINI2_WF;
                } else if (sSubDeviceBean.getTopIr() > 0 && sSubDeviceBean.getLidarEai() > 0) {
                    return RoverConfig.DEVICE_MINI3_EAI;
                } else if (sSubDeviceBean.getTopIr() > 0) {
                    return RoverConfig.DEVICE_MINI3;
                } else if (sSubDeviceBean.getLidarEai() > 0) {
                    return RoverConfig.DEVICE_MINI2_EAI;
                }
                break;
            case RoverConfig.DEVICE_MESSIA_PLUS:
                if (sSubDeviceBean.getRgbdWf() > 0) {
                    return RoverConfig.DEVICE_MESSIA_PLUS_WF;
                }
                break;
            case RoverConfig.DEVICE_WAITER_PRO:
                if (sSubDeviceBean.getTopMono() > 0 && RoverConfig.DEPTH_TYPE_2XB40_X100.equalsIgnoreCase(sSubDeviceBean.getDepthType())) {
                    return RoverConfig.DEVICE_WAITER_PRO_2XB40_X100_TOP_MONO;
                } else if (sSubDeviceBean.getTopMono() > 0) {
                    return RoverConfig.DEVICE_WAITER_PRO_TOP_MONO;
                } else if (sSubDeviceBean.getAutoDoor() > 0) {
                    return RoverConfig.DEVICE_WAITER_PRO_2XB40_X100_ELEC_GATE;
                } else if (RoverConfig.DEPTH_TYPE_3X100.equalsIgnoreCase(sSubDeviceBean.getDepthType())) {
                    return RoverConfig.DEVICE_WAITER_PRO_D3;
                } else if (RoverConfig.DEPTH_TYPE_2XB40_X100.equalsIgnoreCase(sSubDeviceBean.getDepthType())) {
                    return RoverConfig.DEVICE_WAITER_PRO_2XB40_X100;
                }
                break;
            case RoverConfig.DEVICE_MEISSA2:
                // 目前没有子型号
                break;
            default:
                break;
        }
        return RoverConfig.DEVICE_CUSTOM;
    }

    private interface Device {
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getTopCamera();

        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getFrontCamera();

        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getBackCamera();

        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getMajorDepth();

        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getDownDepth();

        public DeviceParamsProtoWrapper.DeviceParamsLidarTypeProto.Type getLidar();

        public DeviceParamsProtoWrapper.DeviceParamsEspTypeProto.Type getEspType();
    }

    private class MiniDeviceImpl implements Device {
        private final int deviceType;

        public MiniDeviceImpl(int deviceType) {
            this.deviceType = deviceType;
        }

        public DeviceParamsProtoWrapper.Mini2ParamsProto.StructureType getStructureType() {
            if (sDeviceInfo != null && sDeviceInfo.hasStructureType()) {
                DeviceInfo.StructureType type = sDeviceInfo.getStructure();
                switch (type) {
                    case Mini2:
                        return DeviceParamsProtoWrapper.Mini2ParamsProto.StructureType.kMini2;
                    case Mini2_SSMono:
                        return DeviceParamsProtoWrapper.Mini2ParamsProto.StructureType.kMini2_SSMono;
                    case Mini2_TopMono:
                        return DeviceParamsProtoWrapper.Mini2ParamsProto.StructureType.kMini2_TopMono;
                }
            }
            if (sDeviceInfo != null && sDeviceInfo.isNewCameraInfo()) {
                if (DeviceInfo.FrontCameraType.HP_6341_0c45.equals(sDeviceInfo.getFrontCamera())) {
                    return DeviceParamsProtoWrapper.Mini2ParamsProto.StructureType.kMini2_SSMono;
                }
            } else if (sSubDeviceBean != null) {
                if (sSubDeviceBean.getSsMono() > 0) {
                    return DeviceParamsProtoWrapper.Mini2ParamsProto.StructureType.kMini2_SSMono;
                }
            }
            return DeviceParamsProtoWrapper.Mini2ParamsProto.StructureType.kMini2;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getTopCamera() {
            if (sDeviceInfo != null && sDeviceInfo.isNewCameraInfo()) {
                return generateTopCamera(sDeviceInfo.getTopCamera());
            } else if (sSubDeviceBean != null) {
                if (sSubDeviceBean.getTopIr() > 0) {
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHP_6369;
                }
            }
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kNone;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getFrontCamera() {
            if (sDeviceInfo != null && sDeviceInfo.isNewCameraInfo()) {
                return generateFrontCamera(sDeviceInfo.getFrontCamera());
            } else if (sSubDeviceBean != null) {
                if (sSubDeviceBean.getSsMono() > 0) {
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHP_6341;
                }
            }
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kCMJ_2076;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getBackCamera() {
            if (sDeviceInfo != null && sDeviceInfo.isNewCameraInfo()) {
                return generateBackCamera(sDeviceInfo.getBackCamera());
            } else if (sSubDeviceBean != null) {
                return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHP_6340;
            }
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHP_6340;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getMajorDepth() {
            if (sDeviceInfo != null && sDeviceInfo.isNewCameraInfo()) {
                return generateMajorDepth(sDeviceInfo.getMajorDepth());
            } else if (sSubDeviceBean != null) {
                if (sSubDeviceBean.getRgbdWf() > 0) {
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kWolf;
                }
                if (sSubDeviceBean.getRgbdD430() <= 0) {
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kNone;
                }
            }
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kRS_D430;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getDownDepth() {
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kNone;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsLidarTypeProto.Type getLidar() {
            return DeviceParamsProtoWrapper.DeviceParamsLidarTypeProto.Type.kUnknown;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsEspTypeProto.Type getEspType() {
            return DeviceParamsProtoWrapper.DeviceParamsEspTypeProto.Type.kUART;
        }
    }

    private class SaiphDeviceImpl implements Device {
        private final int deviceType;

        public SaiphDeviceImpl(int deviceType) {
            this.deviceType = deviceType;
        }

        public DeviceParamsProtoWrapper.WaiterParamsProto.StructureType getStructureType() {
            if (sDeviceInfo != null && sDeviceInfo.hasStructureType()) {
                DeviceInfo.StructureType type = sDeviceInfo.getStructure();
                switch (type) {
                    case Waiter:
                        return DeviceParamsProtoWrapper.WaiterParamsProto.StructureType.kWaiter;
                    case Waiter_SA:
                        return DeviceParamsProtoWrapper.WaiterParamsProto.StructureType.kWaiter_SA;
                    case Waiter_SA_H:
                        return DeviceParamsProtoWrapper.WaiterParamsProto.StructureType.kWaiter_SA_H;
                    case Waiter_SA_H_LW:
                        return DeviceParamsProtoWrapper.WaiterParamsProto.StructureType.kWaiter_SA_H_LW;
                    case Waiter_SA_LW:
                        return DeviceParamsProtoWrapper.WaiterParamsProto.StructureType.kWaiter_SA_LW;
                }
            }
            if (sSubDeviceBean == null) {
                return DeviceParamsProtoWrapper.WaiterParamsProto.StructureType.kWaiter;
            }
            if (sSubDeviceBean.getLidarFov() > 0 && sSubDeviceBean.getHeightBody() > 0) {
                return DeviceParamsProtoWrapper.WaiterParamsProto.StructureType.kWaiter_SA_H_LW;
            }
            if (sSubDeviceBean.getLidarFov() > 0) {
                return DeviceParamsProtoWrapper.WaiterParamsProto.StructureType.kWaiter_SA_LW;
            }
            if (sSubDeviceBean.getHeightBody() > 0) {
                return DeviceParamsProtoWrapper.WaiterParamsProto.StructureType.kWaiter_SA_H;
            }
            if (sSubDeviceBean.getDampener() > 0) {
                return DeviceParamsProtoWrapper.WaiterParamsProto.StructureType.kWaiter_SA;
            }
            return DeviceParamsProtoWrapper.WaiterParamsProto.StructureType.kWaiter;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getTopCamera() {
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kUnknown;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getFrontCamera() {
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kNone;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getBackCamera() {
            if (sDeviceInfo != null && sDeviceInfo.isNewCameraInfo()) {
                return generateBackCamera(sDeviceInfo.getBackCamera());
            } else if (sSubDeviceBean != null) {
                if (sSubDeviceBean.getChargeIr() > 0) {
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHP_6340;
                }
            }
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kNone;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getMajorDepth() {
            if (sDeviceInfo != null && sDeviceInfo.isNewCameraInfo()) {
                return generateMajorDepth(sDeviceInfo.getMajorDepth());
            } else if (sSubDeviceBean != null) {
                if (sSubDeviceBean.getRgbdWf() > 0) {
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kWolf;
                }
                if (sSubDeviceBean.getRgbdFm1() > 0) {
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kFM;
                }
            }
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kRS_D430;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getDownDepth() {
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kNone;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsLidarTypeProto.Type getLidar() {
            return DeviceParamsProtoWrapper.DeviceParamsLidarTypeProto.Type.kUnknown;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsEspTypeProto.Type getEspType() {
            if (this.deviceType == RoverConfig.DEVICE_MESSIA_PLUS) {
                return DeviceParamsProtoWrapper.DeviceParamsEspTypeProto.Type.kUART;
            } else {
                return DeviceParamsProtoWrapper.DeviceParamsEspTypeProto.Type.kUSB;
            }
        }
    }

    private class SaiphProDeviceImpl implements Device {
        private final int deviceType;

        public SaiphProDeviceImpl(int deviceType) {
            this.deviceType = deviceType;
        }

        public DeviceParamsProtoWrapper.WaiterProParamsProto.StructureType getStructureType() {
            if (sDeviceInfo != null && sDeviceInfo.hasStructureType()) {
                DeviceInfo.StructureType type = sDeviceInfo.getStructure();
                switch (type) {
                    case WaiterPro:
                        return DeviceParamsProtoWrapper.WaiterProParamsProto.StructureType.kWaiterPro;
                    case Meissa2:
                        return DeviceParamsProtoWrapper.WaiterProParamsProto.StructureType.kMeissa2;
                    case WaiterPro_EGate:
                        return DeviceParamsProtoWrapper.WaiterProParamsProto.StructureType.kEGate;
                    case WaiterPro_TopMono:
                        return DeviceParamsProtoWrapper.WaiterProParamsProto.StructureType.kWaiterPro_TopMono;
                    case WaiterPro_Carry:
                        return DeviceParamsProtoWrapper.WaiterProParamsProto.StructureType.kWaiterPro_Carry;
                    case WaiterPro_Carry_HS:
                        return DeviceParamsProtoWrapper.WaiterProParamsProto.StructureType.kWaiterPro_Carry_HS;
                }
            }
            if (this.deviceType == RoverConfig.DEVICE_MEISSA2) {
                return DeviceParamsProtoWrapper.WaiterProParamsProto.StructureType.kMeissa2;
            } else if (sDeviceInfo != null && sDeviceInfo.isNewCameraInfo()) {
                if (DeviceInfo.TopCameraType.HP_636d_636c.equals(sDeviceInfo.getTopCamera())) {
                    return DeviceParamsProtoWrapper.WaiterProParamsProto.StructureType.kWaiterPro_TopMono;
                }
            } else if (sSubDeviceBean != null) {
                if (sSubDeviceBean.getAutoDoor() > 0) {
                    return DeviceParamsProtoWrapper.WaiterProParamsProto.StructureType.kEGate;
                }
            }
            return DeviceParamsProtoWrapper.WaiterProParamsProto.StructureType.kWaiterPro;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getTopCamera() {
            if (sDeviceInfo != null && sDeviceInfo.isNewCameraInfo()) {
                return generateTopCamera(sDeviceInfo.getTopCamera());
            } else if (sSubDeviceBean != null) {
                if (sSubDeviceBean.getTopMono() > 0) {
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHP_636d;
                }
            }
            if (this.deviceType == RoverConfig.DEVICE_MEISSA2) {
                return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kNone;
            } else {
                return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHP_6369;
            }
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getFrontCamera() {
            if (sDeviceInfo != null && sDeviceInfo.isNewCameraInfo()) {
                return generateFrontCamera(sDeviceInfo.getFrontCamera());
            } else if (sSubDeviceBean != null) {
                if (sSubDeviceBean.getSsMono() > 0) {//SSMono待确认 waiter_pro_2xb40_x100_top_mono_params->top_camera
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHP_6341;
                }
            }
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kCMJ_2076;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getBackCamera() {
            if (sDeviceInfo != null && sDeviceInfo.isNewCameraInfo()) {
                return generateBackCamera(sDeviceInfo.getBackCamera());
            } else if (sSubDeviceBean != null) {
                return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHP_6340;
            }
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHP_6340;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getMajorDepth() {
            if (sDeviceInfo != null && sDeviceInfo.isNewCameraInfo()) {
                return generateMajorDepth(sDeviceInfo.getMajorDepth());
            } else if (sSubDeviceBean != null) {
                //3x100 DEVICE_WAITER_PRO_D3??
                return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kASJ_XB40;
            }
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kASJ_XB40;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getDownDepth() {
            if (sDeviceInfo != null && sDeviceInfo.isNewCameraInfo()) {
                return generateDownDepth(sDeviceInfo.getDownDepth());
            } else if (sSubDeviceBean != null) {
                //3x100 DEVICE_WAITER_PRO_D3??
                if ("2XB40_X100".equalsIgnoreCase(sSubDeviceBean.getDepthType())) {
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kASJ_X100;
                }
            }
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHST_007;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsLidarTypeProto.Type getLidar() {
            return DeviceParamsProtoWrapper.DeviceParamsLidarTypeProto.Type.kUnknown;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsEspTypeProto.Type getEspType() {
            return DeviceParamsProtoWrapper.DeviceParamsEspTypeProto.Type.kUART;
        }
    }

    private class SlimDeviceImpl implements Device {
        private final int deviceType;

        public SlimDeviceImpl(int deviceType) {
            this.deviceType = deviceType;
        }

        public DeviceParamsProtoWrapper.SlimParamsProto.StructureType getStructureType() {
            if (sDeviceInfo != null && sDeviceInfo.hasStructureType()) {
                DeviceInfo.StructureType type = sDeviceInfo.getStructure();
                switch (type) {
                    case Slim:
                        return DeviceParamsProtoWrapper.SlimParamsProto.StructureType.kSlim;
                    case Slim_EGate:
                        return DeviceParamsProtoWrapper.SlimParamsProto.StructureType.kSlim_Door;
                }
            }
            return DeviceParamsProtoWrapper.SlimParamsProto.StructureType.kSlim;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getTopCamera() {
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHP_636d;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getFrontCamera() {
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHY_0b15;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getBackCamera() {
            if (sDeviceInfo != null && sDeviceInfo.isNewCameraInfo()) {
                return generateBackCamera(sDeviceInfo.getBackCamera());
            } else {
                return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kNone;
            }
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getMajorDepth() {
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kWolf;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type getDownDepth() {
            return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kNone;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsLidarTypeProto.Type getLidar() {
            return DeviceParamsProtoWrapper.DeviceParamsLidarTypeProto.Type.kLH_E300;
        }

        @Override
        public DeviceParamsProtoWrapper.DeviceParamsEspTypeProto.Type getEspType() {
            return DeviceParamsProtoWrapper.DeviceParamsEspTypeProto.Type.kUART;
        }
    }

    private DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type
    generateTopCamera(DeviceInfo.TopCameraType type) {
        if (type != null) {
            switch (type) {
                case CX_6369_0c45:
                case HP_6369_0c45:
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHP_6369;
                case HP_636d_636c:
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHP_636d;
            }
        }
        return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kNone;
    }

    private DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type
    generateFrontCamera(DeviceInfo.FrontCameraType type) {
        if (type != null) {
            switch (type) {
                case HP_6341_0c45:
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHP_6341;
                case KMJ_2076_0edc:
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kCMJ_2076;
            }
        }
        return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kNone;
    }

    private DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type
    generateBackCamera(DeviceInfo.BackCameraType type) {
        if (type != null) {
            switch (type) {
                case HP_6340_0c45:
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHP_6340;
            }
        }
        return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kNone;
    }

    private DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type
    generateMajorDepth(DeviceInfo.MajorDepthType type) {
        if (type != null) {
            switch (type) {
                case INTEL_RS430:
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kRS_D430;
                case YXSK_WF:
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kWolf;
                case ASJ_XB40:
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kASJ_XB40;
                case ASJ_XB100:
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kASJ_X100;
            }
        }
        return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kNone;
    }

    private DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type
    generateDownDepth(DeviceInfo.DownDepthType type) {
        if (type != null) {
            switch (type) {
                case SY_HST007:
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kHST_007;
                case ASJ_XB100:
                    return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kASJ_X100;
            }
        }
        return DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kNone;
    }

    private DeviceParamsProtoWrapper.DeviceParamsLidarTypeProto.Type
    generateLidar() {
        return DeviceParamsProtoWrapper.DeviceParamsLidarTypeProto.Type.kUnknown;
    }

    public synchronized DeviceParamsProtoWrapper.DeviceParamsProto getDeviceParamsProto() {
        if (deviceParamsProto != null) {
            return deviceParamsProto;
        }
        boolean useSubDevice = chassisConfig.isUseSubDeviceTypeNum();
        int deviceNumber = chassisConfig.getDeviceTypeNumber();
        Log.d(TAG, "getDeviceTypeNumber: useSubDevice=" + useSubDevice + ", deviceNumber:" + deviceNumber);
        switch (deviceNumber) {
            case RoverConfig.DEVICE_X86_MINI2:
                MiniDeviceImpl miniDevice = new MiniDeviceImpl(deviceNumber);
                DeviceParamsProtoWrapper.Mini2ParamsProto mini2ParamsProto =
                        DeviceParamsProtoWrapper.Mini2ParamsProto.newBuilder()
                                .setStructure(miniDevice.getStructureType())
                                .setTopCamera(miniDevice.getTopCamera())
                                .setFrontCamera(miniDevice.getFrontCamera())
                                .setBackCamera(miniDevice.getBackCamera())
                                .setDepth(miniDevice.getMajorDepth())
                                .setLidar(miniDevice.getLidar())
                                .build();
                deviceParamsProto = DeviceParamsProtoWrapper.DeviceParamsProto.newBuilder()
                        .setType(DeviceParamsProtoWrapper.DeviceParamsProto.Type.kMini2)
                        .setParams(mini2ParamsProto.toByteString())
                        .build();
                break;
            case RoverConfig.DEVICE_WAITER:
            case RoverConfig.DEVICE_MESSIA_PLUS:
                SaiphDeviceImpl saiphDevice = new SaiphDeviceImpl(deviceNumber);
                DeviceParamsProtoWrapper.WaiterParamsProto waiterParamsProto =
                        DeviceParamsProtoWrapper.WaiterParamsProto.newBuilder()
                                .setStructure(saiphDevice.getStructureType())
                                .setTopCamera(saiphDevice.getTopCamera())
                                .setFrontCamera(saiphDevice.getFrontCamera())
                                .setBackCamera(saiphDevice.getBackCamera())
                                .setDepth(saiphDevice.getMajorDepth())
                                .setLidar(saiphDevice.getLidar())
                                .setEspType(saiphDevice.getEspType())
                                .build();
                deviceParamsProto = DeviceParamsProtoWrapper.DeviceParamsProto.newBuilder()
                        .setType(DeviceParamsProtoWrapper.DeviceParamsProto.Type.kWaiter)
                        .setParams(waiterParamsProto.toByteString())
                        .build();
                break;
            case RoverConfig.DEVICE_WAITER_PRO:
            case RoverConfig.DEVICE_MEISSA2:
                SaiphProDeviceImpl saiphProDevice = new SaiphProDeviceImpl(deviceNumber);
                DeviceParamsProtoWrapper.WaiterProParamsProto waiterProParamsProto =
                        DeviceParamsProtoWrapper.WaiterProParamsProto.newBuilder()
                                .setStructure(saiphProDevice.getStructureType())
                                .setTopCamera(saiphProDevice.getTopCamera())
                                .setFrontCamera(saiphProDevice.getFrontCamera())
                                .setBackCamera(saiphProDevice.getBackCamera())
                                .setMajorDepth(saiphProDevice.getMajorDepth())
                                .setDownDepth(saiphProDevice.getDownDepth())
                                .setLidar(saiphProDevice.getLidar())
                                .build();
                deviceParamsProto = DeviceParamsProtoWrapper.DeviceParamsProto.newBuilder()
                        .setType(DeviceParamsProtoWrapper.DeviceParamsProto.Type.kWaiterPro)
                        .setParams(waiterProParamsProto.toByteString())
                        .build();
                break;
            case RoverConfig.DEVICE_SLIM:
                SlimDeviceImpl slimDevice = new SlimDeviceImpl(deviceNumber);
                DeviceParamsProtoWrapper.SlimParamsProto slimParamsProto =
                        DeviceParamsProtoWrapper.SlimParamsProto.newBuilder()
                                .setStructure(slimDevice.getStructureType())
                                .setTopCamera(slimDevice.getTopCamera())
                                .setFrontCamera(slimDevice.getFrontCamera())
                                .setBackCamera(slimDevice.getBackCamera())
                                .setMajorDepth(slimDevice.getMajorDepth())
                                .setLidar(slimDevice.getLidar())
                                .build();
                deviceParamsProto = DeviceParamsProtoWrapper.DeviceParamsProto.newBuilder()
                        .setType(DeviceParamsProtoWrapper.DeviceParamsProto.Type.kSlim)
                        .setParams(slimParamsProto.toByteString())
                        .build();
                break;
            default:
                Log.e(TAG, "not support base type:" + deviceNumber);
                return null;
        }
        return deviceParamsProto;
    }

    /**
     * 获取多机硬件类型
     *
     * @return
     */
    public MultiRobotModule getMultiRobotModuleName() {
        MultiRobotModule module = MultiRobotModule.Lora; //默认Lora
        if (sSubDeviceBean != null) {
            if (sSubDeviceBean.getLora() == 1) {
                module = MultiRobotModule.Lora;
                return module;
            }
            if (sSubDeviceBean.getEsp32() == 1) {
                module = MultiRobotModule.Esp32;
                return module;
            }
        }
        return module;
    }

    public boolean hasMultiRobotModule() {
        int esp32 = sSubDeviceBean != null ? sSubDeviceBean.getEsp32() : -1;
        int lora = sSubDeviceBean != null ? sSubDeviceBean.getLora() : -1;
        boolean hasTopIr = sSubDeviceBean != null && sSubDeviceBean.getTopIr() > 0;//不需要使用device.properties中的配置，只针对mini topIR版本有用

        int deviceTN = ConfigManager.getInstance().getDeviceTypeNumber();
        if (deviceTN == RoverConfig.DEVICE_WAITER) {
            if (lora == 0 || esp32 == 0) {
                return false;
            }
        }
        if (deviceTN == RoverConfig.DEVICE_X86_MINI2) {
            return esp32 > 0 || hasTopIr;
        }
        return true;
    }

    public boolean isChassisConfigReady() {
        return chassisConfig != null;
    }

    public String getDeviceIP() {
        String deviceIP = chassisConfig.getDeviceIP();
        Log.d(TAG, "get deviceIP:" + deviceIP);
        if (TextUtils.isEmpty(deviceIP)) {
            return NavigationConfig.getNavIp();
        }
        return chassisConfig.getDeviceIP();
    }

    private boolean needPingIp() {
        return true;
    }

    private boolean needCheckSocket() {
        return true;
    }

    private boolean needCheckService() {
        return true;
    }

    private boolean needCheckSelfCheckRes() {
        return true;
    }

    private boolean needCheckResetCamera() {
        return inspectConfig.isResetCamera();
    }

    private boolean needCheckRGBD() {
        return inspectConfig.isRgbd();
    }

    private boolean needCheckLaser() {
        return inspectConfig.isLaser();
    }

    private boolean needCheckInfrared() {
        return inspectConfig.isInfrared();
    }

    private boolean needCheckSpeedMeter() {
        return inspectConfig.isSpeedometer();
    }

    private boolean needCheckLaserAvailable() {
        return inspectConfig.isLaserAvailable();
    }

    private boolean needCheckCalibrationReady() {
        return inspectConfig.isCalibrationReady();
    }

    private boolean needCheckHardDisk() {
        return inspectConfig.isCanControlReady();
    }

    private boolean needCheckCanControl() {
        return inspectConfig.isCanControlReady();
    }

    private boolean needCheckGyroReady() {
        return inspectConfig.isGyroReady();
    }

    private boolean needCheckInfraredCameraReady() {
        return inspectConfig.isInfraredCameraReady() && !ProductInfo.isSaiphMall();
    }

    private boolean needCheckSensor() {
        return needCheckResetCamera() || needCheckRGBD() || needCheckLaser() || needCheckInfrared()
                || needCheckSpeedMeter() || needCheckLaserAvailable() || needCheckCalibrationReady()
                || needCheckHardDisk() || needCheckCanControl() || needCheckGyroReady()
                || needCheckInfraredCameraReady();
    }

    DelayTimer timer;
    private volatile boolean alreadyCallBack = false;
    private volatile boolean isInspecting = false;

    public boolean isInspecting() {
        return isInspecting;
    }

    public void startInspect(final IInspectCallBack callBack) {
        Log.d(TAG, "NaviInspect:startInspect: " + Log.getStackTraceString(new Throwable("startInspect")));
        if (isInspecting) {
            try {
                callBack.onInspectFinish(false, INSPECTING_EXEC);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
            return;
        }
        isInspecting = true;
        final ServiceInspectResultBean rsltBean = new ServiceInspectResultBean();
        rsltBean.setPass(new ArrayList<ServiceInspectResultBean.PassBean>());
        rsltBean.setFail(new ArrayList<ServiceInspectResultBean.FailBean>());
        rsltBean.setStep(STEP_PNIG);
        rsltBean.setResult(CHECK_SUC);
        long period = 1 * 1000;
        naviInspect(period, rsltBean, callBack);
    }

    private void naviInspect(long period, final ServiceInspectResultBean rsltBean, final IInspectCallBack callBack) {
        Log.d(TAG, "NaviInspect:naviInspect: check period :" + period);
        DelayTask.cancel(tag);
        DelayTask.submit(tag, new Runnable() {
            @Override
            public void run() {
                if (ChassisManager.getInstance().getChassisClient().isServiceReady()
                        && ChassisManager.getInstance().getChassisClient().isChassisReady()
                        && !alreadyInspect) {
                    realInspect(rsltBean, callBack);
                    DelayTask.cancel(tag);
                } else {
                    Log.e(TAG, "NaviInspect:naviInspect: alreadyInspect:" + alreadyInspect
                            + ", isChassisReady:" + ChassisManager.getInstance().getChassisClient().isChassisReady()
                            + ", isServiceReady:" + ChassisManager.getInstance().getChassisClient().isServiceReady());
                }
            }
        }, 0, period);
    }

    private void realInspect(final ServiceInspectResultBean rsltBean, final IInspectCallBack callBack) {
        Log.d(TAG, "NaviInspect:realInspect:");
        alreadyInspect = true;
        if (ChassisManager.getInstance().getOtaClient().isOtaConnected()) {
            rsltBean.setOtaResult(true);
        } else {
            //本次修改为临时修改代码，主要是兼容豹小秘1.5暂时不支持ota自检
            String deviceType = getDeviceType();
            switch (deviceType) {
                case Def.CLIENT_X86:
                case Def.CLIENT_WAITER:
                    rsltBean.setOtaResult(true);
                    break;
                case Def.CLIENT_TK1:
                default:
                    rsltBean.setOtaResult(false);
                    break;
            }
        }

        if (!checkPingNaviIp(rsltBean)) {
            rsltBean.setResult(CHECK_FAIL);
            Log.d(TAG, "NaviInspect:realInspect: ping fail = " + rsltBean.toString());
            try {
                callBack.onInspectFinish(false, GsonUtil.toJson(rsltBean));
                isInspecting = false;
                alreadyInspect = false;
            } catch (RemoteException e) {
                e.printStackTrace();
            }
            return;
        }
        Log.d(TAG, "NaviInspect:realInspect: ping suc = " + rsltBean.toString());

        if (!checkSocketAndService(rsltBean)) {
            rsltBean.setResult(CHECK_FAIL);
            Log.d(TAG, "NaviInspect:realInspect: socket and service fail = " + rsltBean.toString());
            try {
                callBack.onInspectFinish(false, GsonUtil.toJson(rsltBean));
                isInspecting = false;
                alreadyInspect = false;
            } catch (RemoteException e) {
                e.printStackTrace();
            }
            return;
        }

        Log.d(TAG, "NaviInspect:realInspect: socket and service ok = " + rsltBean.toString());
        alreadyCallBack = false;
        Log.d(TAG, "NaviInspect:realInspect: time out timer start");
        timer = new DelayTimer(TIME_OUT_LEN, new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "NaviInspect:realInspect:DelayTimer: alreadyCallBack=" + alreadyCallBack);
                if (alreadyCallBack) {
                    Log.d(TAG, "timer destroy");
                    timer.destroy();
                    return;
                }
                alreadyCallBack = true;
                rsltBean.setResult(CHECK_FAIL);
                rsltBean.setStep(STEP_SELF_RES);
                addSelfCheckResFail(rsltBean, R.string.inspect_tk1_get_sensor_status_fail);
                //                            addSensorFail(rsltBean, SELF_CHECK_RES_TIMEOUT);

                Log.d(TAG, "NaviInspect:realInspect:DelayTimer: self check response timeout = " + rsltBean.toString());
                try {
                    callBack.onInspectFinish(false, GsonUtil.toJson(rsltBean));
                    isInspecting = false;
                    alreadyInspect = false;
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
                timer.destroy();
            }
        });
        timer.start();

        ChassisManager.getInstance().getChassisClient().getSensorStatus(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                Log.d(TAG, "NaviInspect:realInspect:getSensorStatus: status=" + status
                        + " resultCode=" + resultCode + " result=" + GsonUtil.toJson(result));
                Log.d(TAG, "NaviInspect:realInspect:getSensorStatus: alreadyCallBack=" + alreadyCallBack);
                if (alreadyCallBack) {
                    return;
                }
                alreadyCallBack = true;
                timer.destroy();
                handleNaviStatus(rsltBean, GsonUtil.toJson(result));
                rsltBean.setStep(STEP_SENSOR_CHECK);
                Log.d(TAG, "NaviInspect:realInspect:getSensorStatus: self check response suc = " + rsltBean.toString());
                try {
                    boolean isSuccess = rsltBean.getResult() == CHECK_SUC;
                    callBack.onInspectFinish(isSuccess, GsonUtil.toJson(rsltBean));
                    isInspecting = false;
                    alreadyInspect = false;
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void handleNaviStatus(ServiceInspectResultBean resultBean, String result) {

        Log.d(TAG, "Navi_IS handleNaviStatus result ：resultBean " + resultBean.toString() + " result" + result);
        if (TextUtils.isEmpty(result)) {
            Log.d(TAG, "Navi_IS handleNaviStatus result ：NULL");
            if (needCheckSelfCheckRes()) {
                resultBean.setResult(CHECK_FAIL);
                addSelfCheckResFail(resultBean, R.string.inspect_tk1_get_sensor_status_fail);
            }
            resultBean.setStep(STEP_SELF_RES);
            return;
        }
        if (result.equals("failed") || result.equals("null")) {
            Log.d(TAG, "Navi_IS handleNaviStatus result ：failed");
            if (needCheckSelfCheckRes()) {
                resultBean.setResult(CHECK_FAIL);
                addSelfCheckResFail(resultBean, R.string.inspect_tk1_get_sensor_status_fail);
            }
            resultBean.setStep(STEP_SELF_RES);
            return;
        }

        Log.d(TAG, "Navi_IS handleNaviSta" +
                "tus result ：" + result);
        SensorStatus sensor = GsonUtil.fromJson(result, SensorStatus.class);
        if (null != sensor) {
            if (sensor.isFishEyeReady()) {
                resultBean.getPass().add(new ServiceInspectResultBean.PassBean(RESET_CAMERA));
            } else {
                addSensorFailBean(resultBean, RESET_CAMERA, R.string.inspect_reset_camera, needCheckResetCamera());
            }

            if (sensor.isRgbdReady()) {
                resultBean.getPass().add(new ServiceInspectResultBean.PassBean(RGBD));
            } else {
                String roverConfig = NavigationDataManager.getInstance().getRoverConfig();
                addSensorFailBean(resultBean, RGBD, R.string.inspect_rgbd, needCheckRGBD());
            }

            if (sensor.isLaserReady()) {
                resultBean.getPass().add(new ServiceInspectResultBean.PassBean(LASER));
            } else {
                addSensorFailBean(resultBean, LASER, R.string.inspect_laser, needCheckLaser());
            }

            if (sensor.isIrReady()) {
                resultBean.getPass().add(new ServiceInspectResultBean.PassBean(INFRARED));
            } else {
                addSensorFailBean(resultBean, INFRARED, R.string.inspect_infrared, needCheckInfrared());
            }

            if (sensor.isOdomReady()) {
                resultBean.getPass().add(new ServiceInspectResultBean.PassBean(SPEED_METER));
            } else {
                addSensorFailBean(resultBean, SPEED_METER, R.string.inspect_speedometer, needCheckSpeedMeter());
            }

            if (sensor.isLaserAvailable()) {
                resultBean.getPass().add(new ServiceInspectResultBean.PassBean(LASER_AVAILABLE));
            } else {
                addSensorFailBean(resultBean, LASER_AVAILABLE, R.string.inspect_laser_available, needCheckLaserAvailable());
            }

            if (sensor.isIscalibrationready()) {
                resultBean.getPass().add(new ServiceInspectResultBean.PassBean(CALIBRATION_READY));
            } else {
                addSensorFailBean(resultBean, CALIBRATION_READY, R.string.inspect_calibration_ready, needCheckCalibrationReady());
            }

            if (sensor.isIsharddiskspaceok()) {
                resultBean.getPass().add(new ServiceInspectResultBean.PassBean(HARD_DISK_SPACE_ENOUGH));
            } else {
                addSensorFailBean(resultBean, HARD_DISK_SPACE_ENOUGH, R.string.inspect_hard_disk, needCheckHardDisk());
            }

            if (sensor.isCanControlReady()) {
                resultBean.getPass().add(new ServiceInspectResultBean.PassBean(CAN_CONTROL_READY));
            } else {
                addSensorFailBean(resultBean, CAN_CONTROL_READY, R.string.inspect_can_control, needCheckCanControl());
            }

            if (sensor.isGyroReady()) {
                resultBean.getPass().add(new ServiceInspectResultBean.PassBean(GYRO_READY));
            } else {
                addSensorFailBean(resultBean, GYRO_READY, R.string.inspect_gyro_ready, needCheckGyroReady());
            }

            if (sensor.isTopIrImageReady()) {
                resultBean.getPass().add(new ServiceInspectResultBean.PassBean(INFRARED_CAMERA_READY));
            } else {
                addSensorFailBean(resultBean, INFRARED_CAMERA_READY, R.string.inspect_ir_camera_ready, needCheckInfraredCameraReady());
            }
        }

    }

    private void addSensorFailBean(ServiceInspectResultBean resultBean, String sensorName, @StringRes int failDes, boolean needCheck) {
        //ServiceInspectResultBean.FailBean failBean = new ServiceInspectResultBean.FailBean(sensorName, CHECK_FAIL, failDes + mContext.getString(R.string.inspect_error));

        FailBean failBean = new FailBean(sensorName, CHECK_FAIL, mContext.getString(failDes) + mContext.getString(R.string.inspect_error));
//        for (String lang : supportLang) {
//            String text = ResUtils.getString(mContext, lang, failDes);
//            String error = ResUtils.getString(mContext, lang, R.string.inspect_error);
//            failBean.addErrorMsg(lang, text + error);
//        }

        if (needCheck) {
            failBean.setIgnore(false);
            resultBean.setResult(CHECK_FAIL);
        } else {
            failBean.setIgnore(true);
        }
        resultBean.getFail().add(failBean);
    }

    private boolean checkSocketAndService(ServiceInspectResultBean resultBean) {
        if (!needCheckSocket() && !needCheckService()) {
            return true;
        }
        //要查一起查，要不就不查，暂时不对这里进行进一步细化判断
        if (ChassisManager.getInstance().getChassisClient().isSocketConnected()) { // 暂时不考虑ServiceReady，Socket连接好了，超时30s保证TK1能够得到正确的传感器信息，即可
            resultBean.getPass().add(new ServiceInspectResultBean.PassBean(SOCKET));
            resultBean.getPass().add(new ServiceInspectResultBean.PassBean(SERVICE_OK));
            return true;
        } else if (!ChassisManager.getInstance().getChassisClient().isSocketConnected()) {
            addSocketFail(resultBean, R.string.inspect_tk1_error);
//            addServiceReadyFail(resultBean, mContext.getString(R.string.inspect_tk1_service_start_fail));
            resultBean.setStep(STEP_SOCKET);
//            addSelfCheckResFail(resultBean, mContext.getString(R.string.inspect_tk1_get_sensor_status_fail));
//            addSensorFail(resultBean, SOCKET_FAIL_REASON);
            return false;
        } else if (!ChassisManager.getInstance().getChassisClient().isServiceReady()) {
            resultBean.getPass().add(new ServiceInspectResultBean.PassBean(SOCKET));
            addServiceReadyFail(resultBean, R.string.inspect_tk1_service_start_fail);
            resultBean.setStep(STEP_SERVICE_OK);
//            addSelfCheckResFail(resultBean, mContext.getString(R.string.inspect_tk1_get_sensor_status_fail));
//            addSensorFail(resultBean, SERVICE_FAIL_REASON);
            return false;
        } else {
            addSocketFail(resultBean, R.string.inspect_tk1_error);
            addServiceReadyFail(resultBean, R.string.inspect_tk1_service_start_fail);
            resultBean.setStep(STEP_SOCKET);
//            addSelfCheckResFail(resultBean, mContext.getString(R.string.inspect_tk1_get_sensor_status_fail));
//            addSensorFail(resultBean, SOCKET_UNAVAILABLE);
            return false;
        }
    }

    private boolean checkPingNaviIp(ServiceInspectResultBean resultBean) {
        mPingNavigationCount = 0;
        if (!needPingIp()) {
            return true;
        }
        if (pingNavigation(getDeviceIP())) {
            resultBean.getPass().add(new ServiceInspectResultBean.PassBean(PING_NAV));
            return true;
        } else {
            resultBean.setStep(STEP_PNIG);

            addFailBean(resultBean, PING_NAV, CHECK_FAIL, R.string.inspect_tk1_error);
//            addSocketFail(resultBean, mContext.getString(R.string.inspect_tk1_error));
//            addServiceReadyFail(resultBean, mContext.getString(R.string.inspect_tk1_service_start_fail));
//            addSelfCheckResFail(resultBean, mContext.getString(R.string.inspect_tk1_get_sensor_status_fail));
//            addSensorFail(resultBean, mContext.getString(R.string.inspect_tk1_error));
            return false;
        }
    }

    private void addSocketFail(ServiceInspectResultBean resultBean, @StringRes int strId) {
        if (needCheckSocket()) {
            addFailBean(resultBean, SOCKET, NEED_CHECK, strId);
//            for (String lang : supportLang) {
//                String text = ResUtils.getString(mContext, lang, strId);
//                failBean.addErrorMsg(lang, text);
//            }
//
//            resultBean.getFail().add(failBean);
        }
    }

    private void addServiceReadyFail(ServiceInspectResultBean resultBean, @StringRes int strId) {
        if (needCheckService()) {
            addFailBean(resultBean, SERVICE_OK, NEED_CHECK, strId);
//            for (String lang : supportLang) {
//                String text = ResUtils.getString(mContext, lang, strId);
//                failBean.addErrorMsg(lang, text);
//            }
//
//            resultBean.getFail().add(failBean);
        }

    }

    private void addSelfCheckResFail(ServiceInspectResultBean resultBean, @StringRes int strId) {
        if (needCheckSelfCheckRes()) {
            addFailBean(resultBean, SELF_CHECK_RES, NEED_CHECK, strId);
//            for (String lang : supportLang) {
//                String text = ResUtils.getString(mContext, lang, strId);
//                failBean.addErrorMsg(lang, text);
//            }
//            resultBean.getFail().add(failBean);
        }
    }

    private void addSensorFail(ServiceInspectResultBean resultBean, String reason) {
        if (needCheckSensor()) {
            resultBean.getFail().add(new ServiceInspectResultBean.FailBean(RESET_CAMERA, NEED_CHECK, reason));
            resultBean.getFail().add(new ServiceInspectResultBean.FailBean(RGBD, NEED_CHECK, reason));
            resultBean.getFail().add(new ServiceInspectResultBean.FailBean(LASER, NEED_CHECK, reason));
            resultBean.getFail().add(new ServiceInspectResultBean.FailBean(INFRARED, NEED_CHECK, reason));
            resultBean.getFail().add(new ServiceInspectResultBean.FailBean(SPEED_METER, NEED_CHECK, reason));
            resultBean.getFail().add(new ServiceInspectResultBean.FailBean(LASER_AVAILABLE, NEED_CHECK, reason));
            resultBean.getFail().add(new ServiceInspectResultBean.FailBean(CALIBRATION_READY, NEED_CHECK, reason));
            resultBean.getFail().add(new ServiceInspectResultBean.FailBean(HARD_DISK_SPACE_ENOUGH, NEED_CHECK, reason));
            resultBean.getFail().add(new ServiceInspectResultBean.FailBean(CAN_CONTROL_READY, NEED_CHECK, reason));
        }
    }

    private void addFailBean(ServiceInspectResultBean resultBean, String name, int code, @StringRes int strId) {
        FailBean failBean = new FailBean(name, code, mContext.getString(strId));
//        for (String lang : supportLang) {
//            String text = ResUtils.getString(mContext, lang, strId);
//            failBean.addErrorMsg(lang, text);
//        }
        resultBean.getFail().add(failBean);
    }

    private boolean pingNavigation(String ip) {
        mPingNavigationCount++;
        if (NetUtils.ping(ip)) {
            return true;
        } else {
            if (mPingNavigationCount >= PING_TOTAL_COUNT_NAVI) {
                return false;
            } else {
                return pingNavigation(ip);
            }
        }
    }

    private void initMapSupportType() {
        Log.d(TAG, "initMapSupportType: ");
        //主机型配置解析
        if (publicConfig != null && publicConfig.getMapCompatibilityConfig() != null) {
            List<Integer> visionTypes = publicConfig.getMapCompatibilityConfig().getVisionTypes();
            List<Integer> targetTypes = publicConfig.getMapCompatibilityConfig().getTargetTypes();
            sSupportVisionTypes.addAll(visionTypes);
            sSupportTargetTypes.addAll(targetTypes);
        }

        //子类型修改配置
        boolean hasTopIr = DeviceSubType.getInstance().hasTopIr();
        boolean hasTopMono = DeviceSubType.getInstance().hasTopMono();
        Log.d(TAG, "initMapSupportType: hasTopMono=" + hasTopMono + " hasTopIr=" + hasTopIr);
        if (ProductInfo.isMiniProduct()) {
            if (hasTopMono || hasTopIr) {
                //mini默认config配置只支持视觉，这里根据子类型把target支持加上
                Log.d(TAG, "initMapSupportType: Mini tipIr/topMono model，support target type!");
                sSupportTargetTypes.addAll(Arrays.asList(
                        Definition.TargetType.TYPE_TARGET_NORMAL.getValue(),
                        Definition.TargetType.TYPE_TARGET_QL.getValue(),
                        Definition.TargetType.TYPE_TARGET_PD.getValue()));
            }
        }
        if(ProductInfo.isMeissa2()){
            if (hasTopMono) {
                //小秘2，默认config配置只支持视觉，这里根据子类型把target支持加上
                Log.d(TAG, "initMapSupportType: Meissa2 topMono model，support target type!");
                sSupportTargetTypes.addAll(Arrays.asList(
                        Definition.TargetType.TYPE_TARGET_NORMAL.getValue(),
                        Definition.TargetType.TYPE_TARGET_QL.getValue(),
                        Definition.TargetType.TYPE_TARGET_PD.getValue()));
            }
        }
        Log.d(TAG, "initMapSupportType: sSupportVisionTypes=" + sSupportVisionTypes +
                " sSupportTargetTypes=" + sSupportTargetTypes);
    }

}
