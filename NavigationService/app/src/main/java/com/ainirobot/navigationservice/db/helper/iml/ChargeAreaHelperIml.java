package com.ainirobot.navigationservice.db.helper.iml;

import com.ainirobot.navigationservice.db.entity.ChargeArea;

import java.util.List;

public interface ChargeAreaHelperIml extends BaseHelper<ChargeArea> {
    ChargeArea getChargeAreaConfigByAreaId(int areaId);

    void initChargeAreaData(List<ChargeArea> infoList);

    List<ChargeArea> getChargeAreaConfig();

    boolean updateChargeArea(List<ChargeArea> infoList);

    boolean updateChargeArea(ChargeArea areaInfo);

    boolean deleteChargeArea(ChargeArea info);
}
