package com.ainirobot.navigationservice.db.helper.iml;

import com.ainirobot.navigationservice.db.entity.MultiFloorInfo;

import java.util.List;

public interface MultiFloorInfoHelperIml extends BaseHelper<MultiFloorInfo> {
    MultiFloorInfo getMultiFloorConfigByFloorId(long floorId);

    void initMultiFloorInfoData(List<MultiFloorInfo> mapInfoList);

    List<MultiFloorInfo> getMultiFloorConfig();

    boolean updateMultiFloorInfo(List<MultiFloorInfo> infoList);

    boolean updateMultiFloorInfo(MultiFloorInfo floorInfo);

    boolean deleteMultiFloorInfo(MultiFloorInfo info);
}
