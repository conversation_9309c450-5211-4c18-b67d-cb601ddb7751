package com.ainirobot.navigationservice.beans.standard;

import java.util.ArrayList;

public class MapInfo {
    MapIdBean idInfo;
    ArrayList<LandMarkBean> landMarkList;
    MapContentBean mapContent;

    @Override
    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append("MapIdBean = ");
        sb.append(idInfo.toString());
        sb.append("\n landMarkList = ");
        for (LandMarkBean item : landMarkList) {
            sb.append(item.toString());
        }
        sb.append(", \nMapContentBean = ");
        sb.append(mapContent.toString());
        return sb.toString();
    }

    public MapInfo(MapIdBean idInfo, ArrayList<LandMarkBean> landMarkList, MapContentBean mapContent) {
        this.idInfo = idInfo;
        this.landMarkList = landMarkList;
        this.mapContent = mapContent;
    }

    public MapIdBean getIdInfo() {
        return idInfo;
    }

    public void setIdInfo(MapIdBean idInfo) {
        this.idInfo = idInfo;
    }

    public ArrayList<LandMarkBean> getLandMarkList() {
        return landMarkList;
    }

    public void setLandMarkList(ArrayList<LandMarkBean> landMarkList) {
        this.landMarkList = landMarkList;
    }

    public MapContentBean getMapContent() {
        return mapContent;
    }

    public void setMapContent(MapContentBean mapContent) {
        this.mapContent = mapContent;
    }
}
