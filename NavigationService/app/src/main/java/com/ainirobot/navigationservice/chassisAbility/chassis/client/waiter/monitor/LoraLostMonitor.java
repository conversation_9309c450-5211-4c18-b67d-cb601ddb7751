package com.ainirobot.navigationservice.chassisAbility.chassis.client.waiter.monitor;

import android.content.Context;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.SystemClock;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.navigationservice.ApplicationWrapper;
import com.ainirobot.navigationservice.db.NavigationDataManager;
import com.ainirobot.navigationservice.beans.waiter.BasePoseBean;
import com.ainirobot.navigationservice.beans.waiter.MultiRobotConfigBean;
import com.ainirobot.navigationservice.beans.waiter.MultiRobotStatus;
import com.ainirobot.navigationservice.chassisAbility.ChassisManager;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.chassisAbility.wifinan.RobotAwareManager;
import com.ainirobot.navigationservice.utils.GsonUtil;
import com.ainirobot.navigationservice.utils.MD5;

import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.LinkedBlockingDeque;


/**
 * 多机数据通过lora以WifiNAN的方式传输，此类用于Lora数据发送、接受、判断lora链接是否正常
 * <p>
 * 其中 更新的时间间隔的阈值时间 可通过 no_lora_update_interval SettingsGlobal设置
 * WifiNAN通过Pose计算的距离的阈值 可通过 wifinan_limited_distance SettingsGlobal设置
 */
public class LoraLostMonitor {

    private static final String TAG = LoraLostMonitor.class.getSimpleName();
    private static final int DEFAULT_LORA_ID = -1;
    private static final float DEFAULT_LIMITED_DISTANCE = 5.0f;
    private static final long NO_LORA_UPDATE_INTERVAL = 10 * Definition.SECOND;

    private volatile BasePoseBean basePose = null;
    private volatile int mSelfLoraId = DEFAULT_LORA_ID;
    private volatile double mLimitedDistance = DEFAULT_LIMITED_DISTANCE;

    private CopyOnWriteArrayList<MultiRobotStatus> cacheList = new CopyOnWriteArrayList<>();
    private LinkedBlockingDeque<WifiNanTransBean> receivedQueue = new LinkedBlockingDeque<>();
    private double mMultiDataTime;//（单位：毫秒）
    private byte[] mMultiDataBytes;

    private LoraStatusErrorListener mLoraStatusListener = null;

    private static final int STATUS_WORKING = 0X1;
    private static final int STATUS_PAUSE = 0X2;

    private volatile int mStatus = STATUS_WORKING;
    private volatile boolean isDuplicateData = false;
    private volatile boolean mHasLoraData = false;
    private IChassisClient chassisClient = null;
    private Object TAG1 = new Object();
    private Object TAG2 = new Object();

    public interface LoraStatusErrorListener {
        void onStatueError(String distance);
    }

    public LoraLostMonitor() {
        init(Definition.MINUTE);
    }

    /**
     *
     * @param delayTime　初始化时延迟时间为1Min，　后续动态切换时延迟时间提高为5秒
     */
    private void init(long delayTime) {
        initWifiAwareService();
//        initSocketServer(); // 暂时不需要直接和LXC 系统进行通信,不做初始化
        delaySendWifiNanMessage(delayTime);
        monitorLoraBroken(delayTime);
        packetStatistics();
    }

    private void initWifiAwareService() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            RobotAwareManager.getInstance().init();
            RobotAwareManager.getInstance().registMessageListener(mSessionListener);
        }
    }

    public void setChassisClient(IChassisClient client){
        this.chassisClient = client;
    }

    public synchronized void updateLaraEnableStatus(boolean enable) {
        Log.d(TAG, "updateLaraEnableStatus enable = " + enable);
        if (enable) {
            mStatus = STATUS_WORKING;
            init(5* Definition.SECOND);
            return;
        }
        mStatus = STATUS_PAUSE;
        closeWifiNAN();
    }

    private void closeWifiNAN() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (mMultiDataBytes == null) {
                mMultiDataBytes = new byte[1];
            }
            Arrays.fill(mMultiDataBytes, (byte) 0x00);
            RobotAwareManager.getInstance().updatePublishConfig(mMultiDataBytes);
            RobotAwareManager.getInstance().destoryAll();
        }
    }

    private RobotAwareManager.SessionMessageListener mSessionListener = new RobotAwareManager.SessionMessageListener() {
        @Override
        public void onDiscoverDevice(byte[] message) {
            if (mStatus == STATUS_PAUSE) {
                return;
            }
            try {
                mAwareRecAll++;
                Log.d(TAG, "onDiscoverDevice message: length=" + message.length
                        + " content=" + Arrays.toString(message));
                //2021-12-21 fix by YC:Json解析改为字节流解析，增加首字节校验判断
                if (message != null || message.length > 0 && message[0] == WifiNanTransBean.STREAM_HEAD) {
                    WifiNanTransBean transBean = new WifiNanTransBean().streamToBean(message);
                    if (transBean == null || transBean.getMultiBytesCounts() == 0) {
                        Log.e(TAG, "onDiscoverDevice invalid data ");
                        return;
                    }
                    receivedQueue.offer(transBean);
                    setNaviMultiBytes(transBean);
                }
            } catch (Throwable e) {
                e.printStackTrace();
                Log.e(TAG, "onDiscoverDevice:e: " + e.getMessage());
            }
        }
    };

    public void setOnLoraStatusError(LoraStatusErrorListener listener) {
        this.mLoraStatusListener = listener;
    }

    /**
     * 延时构建和发送WifiNAN数据包
     * @param delayTime
     */
    private void delaySendWifiNanMessage(long delayTime) {
        DelayTask.cancel(TAG);
        DelayTask.submit(TAG, new Runnable() {
            @Override
            public void run() {
                if (mStatus == STATUS_PAUSE) {
                    Log.d(TAG, "STATUS_PAUSE, Not send WifiNan message.");
                    return;
                }

                if (chassisClient == null || !chassisClient.isPoseEstimate()) {
                    Log.d(TAG, "Pose isn't Estimate , Not send WifiNan message.");
                    return;
                }

                WifiNanTransBean nanTransBean = buildWifiNanTransBean();
                //2021-12-21 fix by YC:传输数据由Json格式转换为字节流格式
                publishMessage(nanTransBean.beanToStream());
            }
        }, delayTime, (Definition.SECOND / 2));
    }

    private WifiNanTransBean buildWifiNanTransBean() {
        if (mSelfLoraId == DEFAULT_LORA_ID) {
            MultiRobotConfigBean configBean = GsonUtil.fromJson(NavigationDataManager.getInstance().getMultiRobotConfig(),
                    MultiRobotConfigBean.class);
            if (configBean != null) {
                mSelfLoraId = configBean.getLoraId();
            }
        }
        Log.d(TAG, "build wifinan self loraId : " + mSelfLoraId);
        String mapName = NavigationDataManager.getInstance().getMapName();
        long elapsedTime = SystemClock.elapsedRealtime();
        WifiNanTransBean wifiNanTransBean = new WifiNanTransBean(mSelfLoraId, elapsedTime, basePose, mapName);
        //2021-12-21 fix by YC: 增加多机补偿数据
        if (mMultiDataBytes != null && mMultiDataBytes.length > 0) {
            long realtime = SystemClock.elapsedRealtime();
            Log.d(TAG, "SystemClock.elapsedRealtime : " + realtime + " mMultiDataTime="
                    + mMultiDataTime + " diff=" + (realtime - mMultiDataTime));
            wifiNanTransBean.setMultiBytesTimeDiff((long) (realtime - mMultiDataTime));
            wifiNanTransBean.setMultiBytesCounts(mMultiDataBytes.length);
            wifiNanTransBean.setMultiBytes(mMultiDataBytes);
        }

        return wifiNanTransBean;
    }

    /**
     * 本机数据外发
     */
    public void publishMessage(byte[] message) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (mStatus == STATUS_PAUSE){
                Arrays.fill(mMultiDataBytes, (byte) 0x00);
                RobotAwareManager.getInstance().updatePublishConfig(mMultiDataBytes);
            }else {
                RobotAwareManager.getInstance().updatePublishConfig(message);
            }
        }
    }

    public void updateBasePose(BasePoseBean poseBean) {
        this.basePose = poseBean;
    }

    /**
     * 注意: 即使当前机器的lora模块数据线拔掉，该接口Event.LORA_MULTI_ROBOT_STATUS 依然会上报事件
     * pose=BiPoseBean{x=6.508195498820448, y=1.179442057870612, theta=1.224073645112228},
     * goal=BiPoseBean{x=0.0, y=0.0, theta=0.0}, id=2, priority=0, mapMatch=true, time=1018917, status=2, curRobot=true, errorStatus=3}
     *
     * @param dataList
     */
    public void updateLoraStatus(ArrayList<MultiRobotStatus> dataList) {
        // dataList中保证包含了场景中所有的lora机器
        cacheList.clear();
        cacheList.addAll(dataList);
    }

    /**
     * 初始化后，延时监控，判断当前机器人lora是否异常
     * @param delayTime
     */
    private void monitorLoraBroken(long delayTime) {
        DelayTask.cancel(TAG1);
        DelayTask.submit(TAG1,new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "monitor cacheList : " + cacheList.toString()
                        + ", isDuplicateData:" + isDuplicateData);
                if (!isMyLoraBroken()) {
                    isDuplicateData = false;
                    Log.d(TAG, "monitor Lora not broken");
                    if (!mHasLoraData) {
                        mHasLoraData = true;
                        forceOpenWifi();
                    }
                    return;
                }
                mHasLoraData = false;
                if (isDuplicateData) {
                    Log.d(TAG, "Duplicate report");
                    return;
                }
                //开始计算，计算当前机器人位置和 收到的对应的机器人之间的距离
                String mapName = NavigationDataManager.getInstance().getMapName();
                String md5Prefix = MD5.createMd5(mapName).substring(0, 4);
                Log.d(TAG, "monitor mapName=" + mapName + " md5Prefix=" + md5Prefix);
                WifiNanTransBean wifiNanTransBean = receivedQueue.peekLast();
                mLimitedDistance = Settings.Global.getFloat(ApplicationWrapper.getContext().getContentResolver(),
                        "wifinan_limited_distance", DEFAULT_LIMITED_DISTANCE);

                Log.d(TAG, "monitor mLimitedDistance : " + mLimitedDistance);

                //2021-12-21 fix by YC: 使用地图名校验改为使用地图名称md5前四位
                if (wifiNanTransBean != null && md5Prefix.equals(wifiNanTransBean.getMd5Prefix())) {
                    double distance = getDistance(basePose, wifiNanTransBean.getPoseBean());
                    Log.d(TAG, "monitor distance : " + distance);

                    if (distance < mLimitedDistance) {
                        //当前本机位置 距离发送wifinan的机器 小于阈值, 说明本机lora坏掉
                        if (mLoraStatusListener != null) {
                            NumberFormat nf = NumberFormat.getInstance();
                            nf.setGroupingUsed(false);
                            String format = nf.format(distance);
                            Log.d(TAG, "distance so near:" + format);
                            isDuplicateData = true;
                            mLoraStatusListener.onStatueError(format);
                        }

                    }
                }

                clearReceivedQueue();
            }
        }, delayTime , 5 * Definition.SECOND);
    }

    private void clearReceivedQueue() {
        receivedQueue.clear();
    }


    /**
     * 检查 cacheList 中是否包含了,最新的所有Lora 数据id.
     *
     * @return
     */
    private boolean isContainsAllLoraId(int loraId) {
        for (int i = 0; i < cacheList.size(); i++) {
            if (cacheList.get(i).getId() == loraId) {
                return true;
            }
        }
        return false;
    }

    private boolean isMyLoraBroken() {
        for (MultiRobotStatus robotStatus : cacheList) {

            if (robotStatus.getId() == mSelfLoraId) {
                continue;
            }
            boolean multiLoraUpdate = isMultiLoraUpdate(robotStatus);
            if (multiLoraUpdate) {
                Log.d(TAG, "updated Id : " + robotStatus.getId());
                return false;
            }
        }
        return true;
    }

    /**
     * 当前lora消息长时间 是否更新?
     * 更新的时间间隔小于或等于 NO_LORA_UPDATE_INTERVAL ，则表示在持续更新.
     * 更新的时间间隔大于 NO_LORA_UPDATE_INTERVAL ，则表示Lora丢失.
     */
    private boolean isMultiLoraUpdate(MultiRobotStatus curStatus) {
        long realtime = SystemClock.elapsedRealtime();
        Log.d(TAG, "SystemClock.elapsedRealtime : " + realtime);
        long timeDiff = Math.abs(realtime - curStatus.getTime());
        long interval = Settings.Global.getLong(ApplicationWrapper.getContext().getContentResolver(),
                "no_lora_update_interval", NO_LORA_UPDATE_INTERVAL);
        Log.d(TAG, "no_lora_update_interval : " + interval);
        return timeDiff <= interval;
    }

    private double getDistance(BasePoseBean p1, BasePoseBean p2) {
        if (p1 == null || p2 == null) {
            return Double.MAX_VALUE;
        }
        double x1 = p1.getX();
        double y1 = p1.getY();
        double x2 = p2.getX();
        double y2 = p2.getY();
        return Math.sqrt(Math.pow((x1 - x2), 2) + Math.pow((y1 - y2), 2));
    }

    /**
     * 补偿多机数据给导航：多机数据字节流+时间差
     */
    private void setNaviMultiBytes(WifiNanTransBean transBean) {
        String mapName = NavigationDataManager.getInstance().getMapName();
        Log.d(TAG, "monitor mapName=" + mapName);
        if(TextUtils.isEmpty(mapName)){
            return;
        }
        String md5Prefix = MD5.createMd5(mapName).substring(0, 4);
        Log.d(TAG, "monitor mapName=" + mapName + " md5Prefix=" + md5Prefix);
        if (transBean == null || !md5Prefix.equals(transBean.getMd5Prefix())) {
            return; //非当前地图不处理
        }
        if (transBean.getMultiBytes() != null &&
                transBean.getMultiBytes().length > 0) {
            Log.d(TAG, "setNaviMultiBytes:");
//            ChassisManager.getInstance().getChassisClient().
//                    setMultiRobotWriteExtraData(transBean.getMultiBytes(),
//                            (SystemClock.elapsedRealtime() - transBean.getMultiBytesTimeDiff()) / 1000.0, null);

            //旧接口给回充等待区使用了，使用底盘新增接口做补偿数据传输
            ChassisManager.getInstance().getChassisClient().
                    setMultiRobotWriteExternalData(transBean.getMultiBytes(),
                            (SystemClock.elapsedRealtime() - transBean.getMultiBytesTimeDiff()) / 1000.0, null);
        }
        if (mRecData.containsKey(transBean.getLoraId())) {
            mRecData.put(transBean.getLoraId(), (mRecData.get(transBean.getLoraId()) + 1));
        } else {
            mRecData.put(transBean.getLoraId(), 1);
        }
    }

    private static final int INTERVAL = 1000 * 60;//单次统计时间
    private static final int SEND_COUNTS_PER_MIN = 60 * 2;//单机一分钟发送包的次数
    private volatile int mAwareRecAll = 0; //所有接受包
    private volatile HashMap<Integer, Integer> mRecData = new HashMap<>();//当前地图机器收包计数，key:loraId,value:counts

    //丢包统计
    private void packetStatistics() {
        Log.d(TAG + ":PS", "packetStatistics: Wifi-Nan 收包率统计");
        DelayTask.cancel(TAG2);
        DelayTask.submit(TAG2,new Runnable() {
            @Override
            public void run() {
                Log.d(TAG + ":PS", String.format("%d 秒内实际收包总数：%d", INTERVAL / 1000, mAwareRecAll));
                int curMapCounts = 0;
                try {
                    for (Integer loraId : mRecData.keySet()) {
                        int counts = mRecData.get(loraId);
                        curMapCounts += counts;
                        Log.d(TAG + ":PS", String.format("LoraId: %d , 实际收包数：%d, 收包率：%.2f%%",
                                loraId,
                                counts,
                                (counts / (SEND_COUNTS_PER_MIN * 1.0)) * 100));
                    }
                    Log.d(TAG + ":PS", String.format("该多机群组有另外 %d 台机器，应收包总数：%d，实际收包总数：%d，收包率：%.2f%%，群组内包数占比：%.2f%%",
                            mRecData.size(),
                            mRecData.size() * SEND_COUNTS_PER_MIN,
                            curMapCounts,
                            (curMapCounts / ((mRecData.size() * SEND_COUNTS_PER_MIN) * 1.0) * 100),
                            (curMapCounts / (mAwareRecAll * 1.0) * 100)
                    ));
                } catch (Exception e) {
                    e.printStackTrace();
                    Log.d(TAG + ":PS", " e:" + e.getMessage());
                } finally {
                    mAwareRecAll = 0;
                    mRecData.clear();
                }
            }
        }, INTERVAL, INTERVAL);
    }

    public void updateMultiByteData(double time, byte[] data) {
        if (mStatus == STATUS_PAUSE ){
            Log.d(TAG, "STATUS_PAUSE , Not update MultiByteData ");
            return;
        }
        this.mMultiDataTime = time * 1000.0;
        this.mMultiDataBytes = data;
    }

    private void forceOpenWifi() {
        Log.d(TAG, "forceOpenWifi:");
        WifiManager wifiManager = (WifiManager) ApplicationWrapper.getContext().
                getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        if (wifiManager != null && !wifiManager.isWifiEnabled()) {
            Log.d(TAG, "forceOpenWifi: Set wifi enable!");
            wifiManager.setWifiEnabled(true);
        }
    }

}
