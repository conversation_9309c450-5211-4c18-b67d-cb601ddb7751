/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.tk1;
import java.io.ByteArrayOutputStream;
import java.io.DataOutput;
import java.io.IOException;

/**
 * Parse the map data from the navigation.
 */
public class Map extends Message {

    private int width;
    private int height;
    private double resolution;
    private double originalX;
    private double originalY;
    private byte[] data;

    public Map(int type) {
        super(type);
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public double getResolution() {
        return resolution;
    }

    public void setResolution(double resolution) {
        this.resolution = resolution;
    }

    public double getOriginalX() {
        return originalX;
    }

    public void setOriginalX(double originalX) {
        this.originalX = originalX;
    }

    public double getOriginalY() {
        return originalY;
    }

    public void setOriginalY(double originalY) {
        this.originalY = originalY;
    }

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

    @Override
    public void writeTo(DataOutput out) throws IOException {
        out.writeInt(width);
        out.writeInt(height);
        out.writeDouble(resolution);
        out.writeDouble(originalX);
        out.writeDouble(originalY);
        out.writeLong(getLength());
        out.write(data);
    }

    @Override
    public void readData(ReverseInputStream in) throws IOException {
        width = in.readInt();
        height = in.readInt();
        resolution = in.readDouble();
        originalX = in.readDouble();
        originalY = in.readDouble();
        int size = width * height;
        //data = new byte[size];
        long destLength = in.readLong();

//        Log.d("Navigation", "Map message receive width : " + width
//                + "  height : " + height
//                + "  resolution : " + resolution
//                + "  originalX : " + originalX
//                + "  originalY : " + originalY
//                + "  destLength : " + destLength);


        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int len;
        byte[] buffer = new byte[4096];
        while (destLength > 0) {
            len = buffer.length;
            if (destLength < len) {
                len = (int) destLength;
            }
            len = in.read(buffer,0,len);
            if (len != -1) {
                out.write(buffer,0,len);
                destLength -= len;
            }
        }
        data = out.toByteArray();

//        Log.d("Navigation", "Map message receive data length : " + data.length);
    }

    @Override
    public long getLength() {
        if (data != null){
            return data.length;
        }
        return 0;
    }
}
