package com.ainirobot.navigationservice.beans.tk1;

import static com.ainirobot.navigationservice.beans.tk1.WorkMode.*;

public class WorkStateMode {

    public static final int CHASSIS_UNKOWN = -1;
    /**
     * The chassis in the switch, not change work mode
     */
    public static final int CHASSIS_SWITCHING = 0;

    /**
     * The chassis in the ready, allows to change work mode
     */
    public static final int CHASSIS_READY = 1;

    /**
     * The chassis in the crashed, not change work mode
     */
    public static final int CHASSIS_CRASHED = 2;


    /**
     * Refer to the enumeration class（WorkMode）
     */
    private int modeValue;

    /**
     * New in version 4.8 field, On behalf of the state of the chassis
     */
    private int stateValue;

    public WorkStateMode(int modeValue, int stateValue) {
        this.modeValue = modeValue;
        this.stateValue = stateValue;
    }

    public static WorkStateMode parseFrom(Object result) {
        if (!(result instanceof WorkStateMode)) {
            return null;
        }

        return (WorkStateMode) result;
    }

    public WorkMode generateWorkModeFromValue() {
        switch (modeValue) {
            case 0:
                return NAVIGATION;
            case 1:
                return CREATING_MAP;
            case 2:
                return SELF_CHECKING;
            case 3:
                return FREE;
            default:
                return null;
        }
    }

    public int getModeValue() {
        return modeValue;
    }

    public void setModeValue(int modeValue) {
        this.modeValue = modeValue;
    }

    public int getStateValue() {
        return stateValue;
    }

    public void setStateValue(int stateValue) {
        this.stateValue = stateValue;
    }
}
