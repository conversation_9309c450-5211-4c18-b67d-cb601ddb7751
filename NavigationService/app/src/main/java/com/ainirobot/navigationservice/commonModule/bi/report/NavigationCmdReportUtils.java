package com.ainirobot.navigationservice.commonModule.bi.report;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.navigationservice.beans.tk1.Event;
import com.ainirobot.navigationservice.commonModule.logs.beans.LogCacheBean;

import java.util.ArrayList;
import java.util.List;

/**
 * report navigation cmd utils class
 *
 * @version V1.0.0
 * @date 2019/4/1 21:56
 */
public class NavigationCmdReportUtils {

    private static final String TAG = "Navigation:CmdReport";
    private static final String DISCONNECT_EVENT = "event";
    public static final String CHARGE_PILE = "charge_pile";
    public static final String VISION = "vision";
    private static final String MATCH_ERR = "match_err";
    private static final String CHECK_ERR = "check_err";

    private static String cmd = "";
    private static List<String> list = new ArrayList<>();
    private static List<Integer> pushEvent = new ArrayList<>();

    static {
        list.add(Definition.CMD_NAVI_MOVE_DISTANCE_ANGLE);
        list.add(Definition.CMD_NAVI_MOVE_DIRECTION_ANGLE);
        list.add(Definition.CMD_NAVI_STOP_MOVE);
        list.add(Definition.CMD_NAVI_GET_LOCATION);
        list.add(Definition.CMD_NAVI_GET_LOCATION_HIGH_FREQUENCY);
        list.add(Definition.CMD_NAVI_IS_IN_LOCATION);
        list.add(Definition.CMD_NAVI_GET_PLACE_NAME);
        list.add(Definition.CMD_NAVI_GET_PLACE_LIST);
        list.add(Definition.CMD_NAVI_GET_PLACE_LIST_WITH_NAME);
        list.add(Definition.CMD_NAVI_GET_PLACELIST_WITH_NAMELIST);
        list.add(Definition.CMD_NAVI_IS_ESTIMATE);

        pushEvent.add(Event.LOCAL_GOAL_INVAILD);
//        pushEvent.add(Event.PATH_FAILED);
        pushEvent.add(Event.LOCAL_PATH_FAILED);
//        pushEvent.add(Event.GOAL_INVALID);
//        pushEvent.add(Event.GOAL_IS_DANGEROUS);
//        pushEvent.add(Event.ROBOT_IS_OUT);
        pushEvent.add(Event.DETECT_PEOPLE);
    }

    public static void setCmd(String cmd) {
        NavigationCmdReportUtils.cmd = cmd;
    }

    private static boolean isReportCmd(String cmd) {
        return cmd != null && !list.contains(cmd);
    }

    private static boolean isReportEvent(int cmd) {
        return !pushEvent.contains(cmd);
    }

    public static void pushNavigationReport(int statusCode, String msg) {
        if (!isReportEvent(statusCode)) {
            return;
        }
        reportError(NavigationApiReport.TYPE_EVENT
                , null
                , NavigationApiReport.ACTION_TYPE_EVENT
                , String.valueOf(statusCode)
                , msg);
    }

    public static void eventErrorNavigationReport(LogCacheBean bean, String errorId) {
        if (bean == null) {
            Log.w(TAG, "eventErrorNavigationReport: bean=null");
            return;
        }
        reportError(NavigationApiReport.TYPE_EVENT
                , null
                , NavigationApiReport.ACTION_TYPE_EVENT
                , null
                , bean.getType()
                , errorId);
    }

    public static void disconnectNavigationReport(String msg) {
        if (TextUtils.isEmpty(msg)) {
            Log.e(TAG, "disconnectNavigationReport: msg is empty");
            return;
        }
        String reportMsg = DISCONNECT_EVENT.equals(msg)
                ? NavigationApiReport.MSG_EVENT_DISCONNECT
                : NavigationApiReport.MSG_CMD_DISCONNECT;

        reportError(NavigationApiReport.TYPE_CONNECT_STATE
                , NavigationApiReport.REQUEST_SOCKET_CHASSIS
                , NavigationApiReport.ACTION_TYPE_EVENT
                , null
                , reportMsg);
    }

    public static void lostEventNavigationReport(boolean isOutOfMap, String msg) {
        String statusCode = isOutOfMap ? NavigationApiReport.STATUS_CODE_LOST_OUT_MAP
                : NavigationApiReport.STATUS_CODE_LOST;
        reportError(NavigationApiReport.TYPE_EVENT
                , null
                , NavigationApiReport.ACTION_TYPE_EVENT
                , statusCode
                , msg);
    }

    public static void relocationFunctionReport(boolean isSuccess, String type, String reason) {
        String actionType = "";
        int statusCode = isSuccess ? NavigationApiReport.STATUS_CODE_RELOCATION_SUCCESS
                : NavigationApiReport.STATUS_CODE_RELOCATION_FAIL;
        String msg = getFailMsg(isSuccess,reason);

        switch (type) {
            case CHARGE_PILE:
                actionType = NavigationApiReport.ACTION_TYPE_RELOCATION_CHARGE_PILE;
                break;
            case VISION:
                actionType = NavigationApiReport.ACTION_TYPE_RELOCATION_VISION;
                break;
            default:
        }
        reportError(NavigationApiReport.TYPE_FUNCTION
                , NavigationApiReport.REQUEST_RELOCATION
                , actionType
                , statusCode
                , msg);
    }

    public static void reportDetectShakeFromTk1() {
        NaviDetectShakeReport report = new NaviDetectShakeReport();
        report.report();
    }
    private static String getFailMsg(boolean isSuccess, String reason) {
        if (TextUtils.isEmpty(reason)) {
            return isSuccess ? NavigationApiReport.MSG_RELOCATION_SUCCESS
                    : NavigationApiReport.MSG_RELOCATION_FAIL;
        } else {
            if (reason.equals(MATCH_ERR)) {
                return NavigationApiReport.REASON_MAP_ERROR;
            } else if (reason.equals(CHECK_ERR)) {
                return NavigationApiReport.REASON_CHECK_ERROR;
            }
        }
        return NavigationApiReport.REASON_OTHERS;
    }


    private static void reportError(String type
            , String request
            , String actionType
            , Object statusCode
            , String msg
            , String errorId) {
        NavigationApiReport navigationApiReport = new NavigationApiReport();
        navigationApiReport.addType(type)
                .addRequest(request)
                .addActionType(actionType)
                .addStatusCode(statusCode)
                .addMsg(msg)
                .addErrorId(errorId)
                .report();
    }

    private static void reportError(String type
            , String request
            , String actionType
            , Object statusCode
            , String msg) {
        reportError(type, request, actionType, statusCode, msg, null);
    }

}
