package com.ainirobot.navigationservice.chassisAbility.ota.client.waiter;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.navigationservice.beans.tk1.SystemData;
import com.ainirobot.navigationservice.chassisAbility.ChassisManager;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.chassisAbility.ota.client.IOtaClient;
import com.ainirobot.navigationservice.chassisAbility.ota.client.bean.BoardVersionBean;
import com.google.gson.Gson;

import java.util.ArrayList;

import ninjia.android.roversdk.Result;

public class OtaClientWaiterImpl implements IOtaClient {

    private static final String TAG = OtaClientWaiterImpl.class.getSimpleName();
    private IChassisClient mChassisClient;
    private Gson mGson = new Gson();
    private final String customVersion = "[{\"board\":\"tk1\",\"version\":\"20190907v94\"},{\"board\":\"motor_left\",\"version\":\"20190918v1.05.08\"},{\"board\":\"motor_right\",\"version\":\"20190918v1.05.08\"}]";

    @Override
    public void init(Context mContext) {
        mChassisClient = ChassisManager.getInstance().getChassisClient();
    }

    @Override
    public void getUpdateParams(OtaResListener listener) {
        listener.onResult("{\"command\":\"getVersion\",\"params\":[{\"board\":\"tk1\",\"version\":\"20190907v94\"},{\"board\":\"motor_left\",\"version\":\"20190919v1.05.08\"},{\"board\":\"motor_right\",\"version\":\"20190919v1.05.08\"}]}");
    }

    @Override
    public void getVersion(final OtaResListener listener) {
        if (mChassisClient != null && mChassisClient.isChassisReady()) {
            mChassisClient.getSystemInformation(new IChassisClient.ChassisResListener() {
                @Override
                public void onResponse(boolean status, int resultCode, Object result) {
                    Log.d(TAG, "getSystemInformation status:" + status + " resultCode:" + resultCode
                            + " result:" + result);
                    if (resultCode == Result.CODE_SUCCESS
                            && result != null && (result instanceof SystemData)){
                        SystemData dataInfo = (SystemData)result;
                        String versionInfo = creatChassisVersionInfo(dataInfo.getApkVersion());
                        listener.onResult(versionInfo);
                    }else {
                        listener.onResult(customVersion);
                    }
                }
            });
            return;
        }
        listener.onResult(customVersion);
    }

    @Override
    public boolean isOtaConnected() {
        return false;
    }

    @Override
    public void startUpdate(String otaInfo, String[] packagePath, OtaResListener listener) {

    }

    private String creatChassisVersionInfo(String chassisVersion){
        try {
            if (TextUtils.isEmpty(chassisVersion)){
                return customVersion;
            }
            BoardVersionBean chassisInfo = new BoardVersionBean("tk1", chassisVersion);
            BoardVersionBean motorLeft = new BoardVersionBean("motor_left", "20190919v1.05.08");
            BoardVersionBean motorRight = new BoardVersionBean("motor_right", "20190919v1.05.08");
            ArrayList<BoardVersionBean> versionList = new ArrayList<>();
            versionList.add(chassisInfo);
            versionList.add(motorLeft);
            versionList.add(motorRight);
            return mGson.toJson(versionList);
        }catch (Exception e){
            e.printStackTrace();
        }
        return customVersion;
    }

}
