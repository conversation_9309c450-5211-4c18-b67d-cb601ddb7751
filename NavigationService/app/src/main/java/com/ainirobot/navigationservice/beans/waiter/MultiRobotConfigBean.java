package com.ainirobot.navigationservice.beans.waiter;

public class MultiRobotConfigBean {

    /**
     * 多机是否打开，只有打开多机和lora的情况下才能下发多机配置。此值对应proto中的loraEnable
     */
    private boolean enable;

    /**
     * 当前机器人的编号，编号越大多机优先级越高，对应proto中的loraSetSeq
     */
    private int loraId;
    /**
     * 多机环境总机器人数量，对应proto中的loraSetTotal
     */
    private int totalDeviceCnt;
    /**
     * 多机消息发送时间间隔，
     */
    private int msgInterval;
    /**
     * 信道
     */
    private int channel;
    /**
     * 发射功率
     */
    private int rfPower;

    /**
     * 状态标识位，表示多机的配置结果，如果配置异常则机器多机不可用
     * 0表示配置正常 1表示配置异常
     */
    private int errorStatus = 0;

    /**
     * 多机类型
     * 0:未知 1:Lora 2:esp32
     */
    private int rf_type;

    public MultiRobotConfigBean(){
    }

    public MultiRobotConfigBean(boolean enable, int loraId, int totalDeviceCnt, int msgInterval,
                                int channel, int rfPower, int errorStatus, int rf_type) {
        this.enable = enable;
        this.loraId = loraId;
        this.totalDeviceCnt = totalDeviceCnt;
        this.msgInterval = msgInterval;
        this.channel = channel;
        this.rfPower = rfPower;
        this.errorStatus = errorStatus;
        this.rf_type = rf_type;
    }

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public int getLoraId() {
        return loraId;
    }

    public void setLoraId(int loraId) {
        this.loraId = loraId;
    }

    public int getTotalDeviceCnt() {
        return totalDeviceCnt;
    }

    public void setTotalDeviceCnt(int totalDeviceCnt) {
        this.totalDeviceCnt = totalDeviceCnt;
    }

    public int getMsgInterval() {
        return msgInterval;
    }

    public void setMsgInterval(int msgInterval) {
        this.msgInterval = msgInterval;
    }

    public int getChannel() {
        return channel;
    }

    public void setChannel(int channel) {
        this.channel = channel;
    }

    public int getErrorStatus() {
        return errorStatus;
    }

    public void setErrorStatus(int errorStatus) {
        this.errorStatus = errorStatus;
    }

    public int getRfPower() {
        return rfPower;
    }

    public void setRfPower(int rfPower) {
        this.rfPower = rfPower;
    }

    public int getRf_type() {
        return rf_type;
    }

    public void setRf_type(int rf_type) {
        this.rf_type = rf_type;
    }

    @Override
    public String toString() {
        return "LoraConfigBean{" +
                "enable=" + enable +
                ", loraId=" + loraId +
                ", totalDeviceCnt=" + totalDeviceCnt +
                ", msgInterval=" + msgInterval +
                ", channel=" + channel +
                ", rfPower=" + rfPower +
                ", errorStatus=" + errorStatus +
                ", rf_type=" + rf_type +
                '}';
    }

    /**
     * 只判断部分Config是否有更新，并不判断全部，使用时需注意
     *
     */
    public boolean equalsPartialConfig(Object obj) {
        if (obj == null || !(obj instanceof MultiRobotConfigBean)) {
            return false;
        }

        MultiRobotConfigBean configBean = (MultiRobotConfigBean) obj;
        return this.loraId == configBean.loraId
                && this.totalDeviceCnt == configBean.totalDeviceCnt
                && this.channel == configBean.channel
                && this.msgInterval == configBean.msgInterval
                && this.rfPower == configBean.rfPower
                && this.rf_type == configBean.rf_type;
    }
}
