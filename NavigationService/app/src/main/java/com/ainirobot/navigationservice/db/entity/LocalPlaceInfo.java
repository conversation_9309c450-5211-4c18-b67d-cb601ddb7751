package com.ainirobot.navigationservice.db.entity;

import com.ainirobot.coreservice.client.Definition;

import io.objectbox.annotation.Entity;
import io.objectbox.annotation.Id;
import io.objectbox.annotation.Index;
import io.objectbox.annotation.Transient;

/**
 * 支持非点图分离模式下的多特殊点位，这里存储的都是特殊点优先级为0的点位
 * 非点图分离模式下：获取特殊点、修改特殊点、删除特殊点等操作LocalPlaceInfo的信息优先级高于PlaceInfo中的
 */
@Entity
public class LocalPlaceInfo {
    @Id
    public long id;
    @Index
    private String placeId="";
    /**
     * 特殊点位类型
     */
    private int typeId;
    @Transient
    private int priority;
    private String mapName="";
    private String placeName="";
    private int placeStatus;
    private float pointTheta;
    private float pointX;
    private float pointY;
    private String alias="";
    private boolean ignoreDistance = false;

    private boolean noDirectionalParking = false;

    private int safeDistance = Definition.POSE_SAFE_DISTANCE_DEFAULT;

    public void setPlaceId(String placeId) {
        this.placeId = placeId;
    }

    public String getPlaceId() {
        return placeId;
    }

    public int getTypeId() {
        return typeId;
    }

    public void setTypeId(int typeId) {
        this.typeId = typeId;
    }

    public void setMapName(String mapName) {
        this.mapName = mapName;
    }

    public String getMapName() {
        return mapName;
    }

    public String getPlaceName() {
        return placeName;
    }

    public void setPlaceName(String placeName) {
        this.placeName = placeName;
    }

    public int getPlaceStatus() {
        return placeStatus;
    }

    public void setPlaceStatus(int placeStatus) {
        this.placeStatus = placeStatus;
    }

    public float getPointTheta() {
        return pointTheta;
    }

    public void setPointTheta(float pointTheta) {
        this.pointTheta = pointTheta;
    }

    public float getPointX() {
        return pointX;
    }

    public void setPointX(float pointX) {
        this.pointX = pointX;
    }

    public float getPointY() {
        return pointY;
    }

    public void setPointY(float pointY) {
        this.pointY = pointY;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public int getSafeDistance() {
        return safeDistance;
    }

    public void setSafeDistance(int safeDistance) {
        this.safeDistance = safeDistance;
    }

    public boolean getIgnoreDistance() {
        return ignoreDistance;
    }

    public void setIgnoreDistance(boolean ignoreDistance) {
        this.ignoreDistance = ignoreDistance;
    }

    public boolean getNoDirectionalParking() {
        return noDirectionalParking;
    }

    public void setNoDirectionalParking(boolean noDirectionalParking) {
        this.noDirectionalParking = noDirectionalParking;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    @Override
    public String toString() {
        return "LocalPlaceInfo{" +
                "id=" + id +
                ", placeId='" + placeId + '\'' +
                ", typeId=" + typeId +
                ", priority=" + priority +
                ", mapName='" + mapName + '\'' +
                ", placeName='" + placeName + '\'' +
                ", placeStatus=" + placeStatus +
                ", pointTheta=" + pointTheta +
                ", pointX=" + pointX +
                ", pointY=" + pointY +
                ", alias='" + alias + '\'' +
                ", ignoreDistance=" + ignoreDistance +
                ", safeDistance=" + safeDistance +
                '}';
    }
}
