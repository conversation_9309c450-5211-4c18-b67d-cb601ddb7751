package com.ainirobot.navigationservice;

import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import com.ainirobot.navigationservice.chassisimpl.bean.LandMarkBean;
import com.ainirobot.navigationservice.chassisimpl.bean.MapIdBean;
import com.ainirobot.navigationservice.chassisimpl.bean.MapInfo;
import com.ainirobot.navigationservice.chassisimpl.bean.NaviSpeedParam;
import com.ainirobot.navigationservice.chassisimpl.bean.PoseInfo;
import com.ainirobot.navigationservice.chassisimpl.bean.SensorStatus;
import com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.CmdStandardApi;
import com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.CmdStandardApiStandardImpl;
import com.ainirobot.navigationservice.chassisimpl.connector.ConnectApi;
import com.ainirobot.navigationservice.chassisimpl.connector.connectApiStandard.ConnectApiStandardImpl;
import com.ainirobot.navigationservice.chassisimpl.utils.ListLandMar;
import com.ainirobot.navigationservice.chassisimpl.utils.ListMapId;
import com.ainirobot.navigationservice.chassisimpl.utils.Parameter;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static com.ainirobot.navigationservice.chassisimpl.Def.EXECUTION;
import static com.ainirobot.navigationservice.chassisimpl.Def.TAGPREFIX;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.EventStandardDef.EVENT_POSE_UPDATE;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.EventStandardDef.EVENT_REALTIME_MAP_DATA;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.EventStandardDef.EVENT_REMOTE_CONTROL_STATE;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_ADD_LANDMARK;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_CHARGE_PILE_DOCKING;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_CHARGE_PILE_RECOGNIZE;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_CHARGE_PILE_SEARCH;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_DELETE_LANDMARK;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_DELETE_MAP;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_FORCE_FORWARD_MOVE;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_FORCE_ROTATION_MOVE;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_GET_CUR_MAP_INFO;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_GET_LANDMARK_LIST;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_GET_LOCALIZATION_STATE;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_GET_MAP_INFO;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_GET_MAP_LIST;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_GET_REALTIME_MAP;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_GET_SENSOR_STATUS;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_MODIFY_LANDMARK_INFO;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_SAVE_MAPPING_PATH;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_SAVE_MAPPING_POSE;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_SET_CONTROL_MODE;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_SET_FIX_POINT_MODE;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_SET_MANUAL_MODE;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_SET_NAVI_GOAL_POINT;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_SET_NAVI_SPEED_PARAM;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_SET_RELOCALIZATION;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_START_CREATE_MAP;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_STOP_CREATE_MAP;
import static com.ainirobot.navigationservice.chassisimpl.cmdParser.cmdParserStandard.ReqCmdStandardDef.REQ_SWITCH_MAP;

public class TestYueFan extends Activity implements View.OnClickListener {
    private final static String TAG = TAGPREFIX + TestYueFan.class.getSimpleName();

    private final static String GO_ON_PATROL = "go_on_patrol";
    private final static String GO_CHARGE = "go_charge";
    private final static String CHARGE_POINT = "chargePose";


    private EditText param1;
    private EditText param2;
    private EditText param3;
    private EditText param4;
    private EditText param5;
    private EditText param6;
    private EditText param7;
    private EditText param8;

    private Spinner spinner;

    private ImageView mapImage;

    private TextView textView;
    private TextView pose_v;

    private Button btnSend;
    private CmdStandardApi cmdStandardApi;
    private ConnectApi connectApi;


    private List<String> commands;

    private String cmdChoose;
    private Parameter mParameter;
    private HashMap<String, String> mapIdHash = new HashMap<>();
    private HashMap<String, PoseInfo> hashMapPose = new HashMap<>();
    private ArrayList<MapIdBean> arrayMapId = new ArrayList<>();
    private ArrayList<LandMarkBean> landMarkBeans = new ArrayList<>();
    private ArrayList<PoseInfo> patrolList = new ArrayList<>();
    private int curPartrolPoseIndex;

    private Handler uiHandler = new Handler();
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_test_yue_fan);
        commands = Arrays.asList(getResources().getStringArray(R.array.request_cmd));

        param1 = (EditText) findViewById(R.id.param1);
        param2 = (EditText) findViewById(R.id.param2);
        param3 = (EditText) findViewById(R.id.param3);
        param4 = (EditText) findViewById(R.id.param4);
        param5 = (EditText) findViewById(R.id.param5);
        param6 = (EditText) findViewById(R.id.param6);
        param7 = (EditText) findViewById(R.id.param7);
        param8 = (EditText) findViewById(R.id.param8);

        spinner = (Spinner) findViewById(R.id.choose_cmd);
        spinner.setOnItemSelectedListener(listener);

        textView = (TextView) findViewById(R.id.textView);
        pose_v = (TextView) findViewById(R.id.pose_view);

        btnSend = (Button) findViewById(R.id.button);
        mapImage = (ImageView) findViewById(R.id.imageView);
        btnSend.setOnClickListener(this);
        mParameter = new Parameter(TestYueFan.this);

        inflateMapIdHash();
        initCmdApi();

        registerPoseListener();
        registerMapListener();
    }

    private void registerMapListener() {
        cmdStandardApi.registerEventListener(EVENT_REALTIME_MAP_DATA, new CmdStandardApi.EventListener() {
            @Override
            public void onEvent(int type, String msg, Object param) {
                Log.d(TAG, "realtime map data in");
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            FileInputStream fis = new FileInputStream("/sdcard/map.png");
                            Bitmap bitmap = BitmapFactory.decodeStream(fis);
                            mapImage.setImageBitmap(bitmap);
                        } catch (FileNotFoundException e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        });
    }

    private void registerPoseListener() {
        cmdStandardApi.registerEventListener(EVENT_POSE_UPDATE, new CmdStandardApi.EventListener() {
            @Override
            public void onEvent(int type, String msg, final Object param) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        pose_v.setText(((PoseInfo) param).toString());
                    }
                });
            }
        });
    }

    private void inflateMapIdHash() {
        try {
            if (mParameter != null) {
                ArrayList<MapIdBean> arrayList = mParameter.getMapIdList().getListMapId();
                for (MapIdBean idBean : arrayList) {
                    Log.d(TAG, "name = " + idBean.getName() + ", id = " + idBean.getId());
                    mapIdHash.put(idBean.getName(), idBean.getId());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.button:
                if (cmdChoose.equals(GO_ON_PATROL)) {
                    if (isPatroling) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (cmdChoose.equals(GO_ON_PATROL)) {
                                    btnSend.setText("开始导航");
                                }
                            }
                        });
                        cmdStandardApi.cancelNaviGoPlace(naviStopListener);
                    } else {
                        inflatePatrolList();
                        naviLogic();
                    }
                } else if (cmdChoose.equals(GO_CHARGE)) {
                    chargeLogic();
                } else {
                    processSendCmd();
                }
                break;
        }
    }

    private void chargeLogic() {
        if (hashMapPose.get(CHARGE_POINT) == null) {
            Toast.makeText(this, "充电点为空", Toast.LENGTH_SHORT).show();
            return;
        }
        cmdStandardApi.exeNaviGoPlace(hashMapPose.get(CHARGE_POINT), new CmdStandardApi.ResponseListener() {
            @Override
            public void onError(int resultCode, String msg) {
                showRespose(generateFailMsg(cmdChoose, resultCode, msg));
            }

            @Override
            public void onResult(int resultCode, String msg, Object param) {
                showRespose(generateSucMsg(cmdChoose, resultCode, msg));
                dockingChargePile();
            }

            @Override
            public void onStatusUpdate(int stage, Object param) {
                showRespose(generateStageMsg(cmdChoose, stage));
            }
        });
    }

    private void dockingChargePile() {
        cmdStandardApi.exeChargePileDocking(new CmdStandardApi.ResponseListener() {
            @Override
            public void onError(int resultCode, String msg) {
                showRespose(generateFailMsg(cmdChoose, resultCode, msg));
            }

            @Override
            public void onResult(int resultCode, String msg, Object param) {
                showRespose(generateSucMsg(cmdChoose, resultCode, msg));
            }

            @Override
            public void onStatusUpdate(int stage, Object param) {
                showRespose(generateStageMsg(cmdChoose, stage));
            }
        });
    }

    private CmdStandardApi.ResponseListener naviStopListener = new CmdStandardApi.ResponseListener() {
        @Override
        public void onError(int resultCode, String msg) {
            showRespose(generateFailMsg(cmdChoose, resultCode, msg));
            isPatroling = false;
        }

        @Override
        public void onResult(int resultCode, String msg, Object param) {
            showRespose(generateSucMsg(cmdChoose, resultCode, msg));
            isPatroling = false;
        }

        @Override
        public void onStatusUpdate(int stage, Object param) {
            showRespose(generateStageMsg(cmdChoose, stage));
            isPatroling = false;
        }
    };

    private void processSendCmd() {

        try {
            cmdWithStageNoParam(cmdChoose);
            cmdWithMap(cmdChoose);
        } catch (Exception e) {
            Toast.makeText(this, "参数输入有误", Toast.LENGTH_SHORT).show();
            e.printStackTrace();
        }

    }

    private boolean isPatroling = false;
    private void naviLogic() {
        if (patrolList.size() == 0){
            Toast.makeText(this, "巡逻点为空", Toast.LENGTH_SHORT).show();
            return;
        }
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (cmdChoose.equals(GO_ON_PATROL)) {
                    btnSend.setText("停止导航");
                }
            }
        });

        cmdStandardApi.exeNaviGoPlace(patrolList.get(curPartrolPoseIndex), new CmdStandardApi.ResponseListener() {
            @Override
            public void onResult(int resultCode, String msg, Object param) {
                Log.d(TAG, "navi goal result curPatrolIndex = " + curPartrolPoseIndex);
                curPartrolPoseIndex = (curPartrolPoseIndex + 1) % patrolList.size();
                isPatroling = true;
                naviLogic();
            }

            @Override
            public void onStatusUpdate(int stage, Object param) {
                Log.d(TAG, "on status update, stage = " + stage);
                showRespose(generateStageMsg(cmdChoose, stage));
                isPatroling = true;
            }

            @Override
            public void onError(int resultCode, String msg) {
                isPatroling = false;
                showRespose(generateFailMsg(cmdChoose, resultCode, msg));
            }
        });
    }

    private void inflatePatrolList() {
        patrolList.clear();
        PoseInfo pose1 = getPose(param1.getText().toString());
        PoseInfo pose2 = getPose(param2.getText().toString());
        PoseInfo pose3 = getPose(param3.getText().toString());
        PoseInfo pose4 = getPose(param4.getText().toString());
        if (pose1 != null) {
            patrolList.add(pose1);
        }
        if (pose2 != null) {
            patrolList.add(pose2);
        }
        if (pose3 != null) {
            patrolList.add(pose3);
        }
        if (pose4 != null) {
            patrolList.add(pose4);
        }
        curPartrolPoseIndex = 0;
        Log.d(TAG, "patrol List = " + patrolList.toString());
    }

    private PoseInfo getPose(String name) {
        if (!TextUtils.isEmpty(name)) {
            return hashMapPose.get(name);
        }
        return null;
    }

    long preTime = System.currentTimeMillis();
    private void cmdWithMap(final String cmdChoose) throws Exception {
        switch (cmdChoose) {
            case REQ_DELETE_MAP:
                cmdStandardApi.deleteMap((param1.getText().toString() != null ?
                        param1.getText().toString() : ""), listenerWithStageNoParam);
                break;
            case REQ_START_CREATE_MAP:
                cmdStandardApi.startCreateMap((param1.getText().toString() != null ?
                        param1.getText().toString() : ""), 1, (!TextUtils.isEmpty(param1.getText().toString()) ?
                        param1.getText().toString() : ""), listenerWithStageNoParam);
                break;
            case REQ_DELETE_LANDMARK:
                cmdStandardApi.deleteLandMark((param1.getText().toString() != null ?
                        param1.getText().toString() : ""),(!TextUtils.isEmpty(param2.getText().toString()) ?
                        param2.getText().toString() : "0"), listenerWithStageNoParam);
                break;
            case REQ_SWITCH_MAP:
                String switchName = (param1.getText().toString() != null ?
                        param1.getText().toString() : "");
                Log.d(TAG, "name = " + switchName + ", id = " + mapIdHash.get(switchName));
                cmdStandardApi.switchMap(mapIdHash.get(switchName) != null ? mapIdHash.get(switchName) : "", listenerWithStageNoParam);
                ListLandMar listLandMar = mParameter.getListLandMark(switchName);
                hashMapPose.clear();
                for (LandMarkBean bean : listLandMar.getLandMarkBeanArrayList()) {
                    Log.d(TAG, "pose name = " + bean.getName() +
                            ", poseInfo = " + bean.getPoseInfo().toString());
                    hashMapPose.put(bean.getName(), bean.getPoseInfo());
                }
                break;
            case REQ_GET_MAP_INFO:
                cmdStandardApi.getMapInfo((param1.getText().toString() != null ?
                        param1.getText().toString() : ""), new CmdStandardApi.ResponseListener() {
                    @Override
                    public void onError(int resultCode, String msg) {
                        showRespose(generateFailMsg(cmdChoose, resultCode, msg));
                    }

                    @Override
                    public void onResult(int resultCode, String msg, Object param) {
                        StringBuffer stringBuffer = new StringBuffer(generateSucMsg(cmdChoose, resultCode, msg));
                        MapInfo info = (MapInfo) param;
                        stringBuffer.append(info.toString());
                        showRespose(stringBuffer.toString());
                    }
                });
                break;
            case REQ_GET_LANDMARK_LIST:
                cmdStandardApi.getLandMarkList((param1.getText().toString() != null ?
                        param1.getText().toString() : ""), new CmdStandardApi.ResponseListener() {
                    @Override
                    public void onError(int resultCode, String msg) {
                        showRespose(generateFailMsg(cmdChoose, resultCode, msg));
                    }

                    @Override
                    public void onResult(int resultCode, String msg, Object param) {
                        StringBuffer stringBuffer = new StringBuffer(generateSucMsg(cmdChoose, resultCode, msg));
                        ArrayList<LandMarkBean> info = (ArrayList<LandMarkBean>) param;
                        stringBuffer.append("地点列表 = ");
                        for (LandMarkBean item : info) {
                            stringBuffer.append(item.toString());
                        }
                        showRespose(stringBuffer.toString());
                    }
                });
                break;
            case REQ_MODIFY_LANDMARK_INFO:
                cmdStandardApi.modifyLandmarkInfo((param1.getText().toString() != null ?
                        param1.getText().toString() : ""), (param2.getText().toString() != null ?
                        param2.getText().toString() : ""), new LandMarkBean((!TextUtils.isEmpty(param3.getText().toString()) ?
                        param3.getText().toString() : ""), (!TextUtils.isEmpty(param4.getText().toString()) ?
                        param4.getText().toString() : ""), new PoseInfo(Double.parseDouble(!TextUtils.isEmpty(param5.getText().toString()) ?
                        param5.getText().toString() : "0"), Double.parseDouble(!TextUtils.isEmpty(param6.getText().toString()) ?
                        param6.getText().toString() : "0"), Double.parseDouble(!TextUtils.isEmpty(param7.getText().toString()) ?
                        param7.getText().toString() : "0"))), new CmdStandardApi.ResponseListener() {
                    @Override
                    public void onError(int resultCode, String msg) {
                        showRespose(generateFailMsg(cmdChoose, resultCode, msg));
                    }

                    @Override
                    public void onResult(int resultCode, String msg, Object param) {
                        StringBuffer stringBuffer = new StringBuffer(generateSucMsg(cmdChoose, resultCode, msg));
                        ArrayList<LandMarkBean> info = (ArrayList<LandMarkBean>) param;
                        stringBuffer.append("地点列表 = ");
                        for (LandMarkBean item : info) {
                            stringBuffer.append(item.toString());
                        }
                        showRespose(stringBuffer.toString());
                    }
                });
                break;
            case REQ_ADD_LANDMARK:
                String name = (param1.getText().toString() != null ?
                        param1.getText().toString() : "");
                Log.d(TAG, "mapName = " + name + ", id = " + mapIdHash.get(name));
                cmdStandardApi.addNewLandMark(mapIdHash.get(name), new LandMarkBean((!TextUtils.isEmpty(param2.getText().toString()) ?
                        param2.getText().toString() : ""), (!TextUtils.isEmpty(param3.getText().toString()) ?
                        param3.getText().toString() : ""), new PoseInfo(Double.parseDouble(!TextUtils.isEmpty(param4.getText().toString()) ?
                        param4.getText().toString() : "0"), Double.parseDouble(!TextUtils.isEmpty(param5.getText().toString()) ?
                        param5.getText().toString() : "0"), Double.parseDouble(!TextUtils.isEmpty(param6.getText().toString()) ?
                        param6.getText().toString() : "0"))), new CmdStandardApi.ResponseListener() {
                    @Override
                    public void onError(int resultCode, String msg) {
                        showRespose(generateFailMsg(cmdChoose, resultCode, msg));
                    }

                    @Override
                    public void onResult(int resultCode, String msg, Object param) {
                        StringBuffer stringBuffer = new StringBuffer(generateSucMsg(cmdChoose, resultCode, msg));
                        ArrayList<LandMarkBean> info = (ArrayList<LandMarkBean>) param;
                        stringBuffer.append("地点列表 = ");
                        for (LandMarkBean item : info) {
                            stringBuffer.append(item.toString());
                        }
                        showRespose(stringBuffer.toString());
                    }
                });
                break;
            case REQ_GET_CUR_MAP_INFO:
                cmdStandardApi.getCurMapInfo(new CmdStandardApi.ResponseListener() {
                    @Override
                    public void onError(int resultCode, String msg) {
                        showRespose(generateFailMsg(cmdChoose, resultCode, msg));
                    }

                    @Override
                    public void onResult(int resultCode, String msg, Object param) {
                        StringBuffer stringBuffer = new StringBuffer(generateSucMsg(cmdChoose, resultCode, msg));
                        MapInfo info = (MapInfo) param;
                        stringBuffer.append(info.toString());
                        showRespose(stringBuffer.toString());
                        hashMapPose.clear();
                        for (LandMarkBean bean : info.getLandMarkList())  {
                            hashMapPose.put(bean.getName(), bean.getPoseInfo());
                        }

                        for (String key : hashMapPose.keySet()) {
                            Log.e(TAG, "pose list key = " + key +
                                    ", poseInfo = " + hashMapPose.get(key).toString());
                        }
                    }
                });
            case REQ_GET_REALTIME_MAP:
                //TODO can not show byte[]
                break;
            case REQ_GET_MAP_LIST:
                cmdStandardApi.getMapList(new CmdStandardApi.ResponseListener() {
                    @Override
                    public void onError(int resultCode, String msg) {
                        showRespose(generateFailMsg(cmdChoose, resultCode, msg));
                    }

                    @Override
                    public void onResult(int resultCode, String msg, Object param) {
                        StringBuffer stringBuffer = new StringBuffer(generateSucMsg(cmdChoose, resultCode, msg));
                        ArrayList<MapIdBean> info = (ArrayList<MapIdBean>) param;
                        stringBuffer.append("地图列表 = ");
                        for (MapIdBean item : info) {
                            stringBuffer.append(item.toString());
                        }
                        showRespose(stringBuffer.toString());
                    }
                });
                break;
            case REQ_STOP_CREATE_MAP:
                cmdStandardApi.stopCreateMap(true, new CmdStandardApi.ResponseListener(){
                    @Override
                    public void onError(int resultCode, String msg) {
                        showRespose(generateFailMsg(cmdChoose, resultCode, msg));
                    }

                    @Override
                    public void onResult(int resultCode, String msg, Object param) {
                        StringBuffer stringBuffer = new StringBuffer(generateSucMsg(cmdChoose, resultCode, msg));
                        MapInfo info = (MapInfo) param;
                        stringBuffer.append(info.toString());
                        Log.d(TAG, "mapInfo = " + info);
                        ListMapId listMapId = mParameter.getMapIdList();
                        if (listMapId != null && listMapId.getListMapId().size() != 0) {
                            arrayMapId = listMapId.getListMapId();
                        }
                        arrayMapId.add(info.getIdInfo());
                        mParameter.setListMapId(new ListMapId(arrayMapId));

                        mParameter.setListLandMark(info.getIdInfo().getName(),
                                new ListLandMar(info.getLandMarkList()));
                        inflateMapIdHash();
                        showRespose(stringBuffer.toString());
                    }
                });
//                cmdStandardApi.unRegisterEventListener(EventDef.EVENT_REALTIME_MAP_DATA);
                break;
                default:
                    break;
        }
    }

    CmdStandardApi.ResponseListener listenerWithStageNoParam = new CmdStandardApi.ResponseListener() {
        @Override
        public void onError(int resultCode, String msg) {
            showRespose(generateFailMsg(cmdChoose, resultCode, msg));
        }

        @Override
        public void onResult(int resultCode, String msg, Object param) {
            showRespose(generateSucMsg(cmdChoose, resultCode, msg));
        }

        @Override
        public void onStatusUpdate(int stage, Object param) {
            showRespose(generateStageMsg(cmdChoose, stage));
        }
    };


    private void cmdWithStageNoParam(final String cmdChoose) throws Exception {
        int type = 1;
        try {
            type = Integer.parseInt(!TextUtils.isEmpty(param1.getText().toString()) ?
                    param1.getText().toString() : "1");
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        switch (cmdChoose) {
            case REQ_SET_MANUAL_MODE:
                if (type == EXECUTION) {
                    cmdStandardApi.enterManualMode(listenerWithStageNoParam);
                } else {
                    cmdStandardApi.exitManualMode(listenerWithStageNoParam);
                }
                break;
            case REQ_SET_FIX_POINT_MODE:
                if (type == EXECUTION) {
                    cmdStandardApi.enterFixPointMode(listenerWithStageNoParam);
                } else {
                    cmdStandardApi.exitFixPointMode(listenerWithStageNoParam);
                }
                break;
            case REQ_SET_CONTROL_MODE:
                if (type == EXECUTION) {
                    cmdStandardApi.enterRemoteControlMode(Double.parseDouble(!TextUtils.isEmpty(param2.getText().toString()) ?
                            param2.getText().toString() : "0"), Double.parseDouble(!TextUtils.isEmpty(param3.getText().toString()) ?
                            param3.getText().toString() : "0"), listenerWithStageNoParam);
                    cmdStandardApi.registerEventListener(EVENT_REMOTE_CONTROL_STATE, new CmdStandardApi.EventListener() {
                        @Override
                        public void onEvent(final int type, String msg, Object param) {
                            if (System.currentTimeMillis() - preTime > 6000) {
                                preTime = System.currentTimeMillis();
                                runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        Toast.makeText(TestYueFan.this, "控制状态 type" +
                                                " = " + type, Toast.LENGTH_SHORT).show();
                                    }
                                });
                            }
                        }
                    });
                } else {
                    cmdStandardApi.exitRemoteControlMode(listenerWithStageNoParam);
                    cmdStandardApi.unRegisterEventListener(EVENT_REMOTE_CONTROL_STATE);
                }
                break;
            case REQ_CHARGE_PILE_DOCKING:
                if (type == EXECUTION) {
                    cmdStandardApi.exeChargePileDocking(listenerWithStageNoParam);
                } else {
                    cmdStandardApi.cancelChargePileDocking(listenerWithStageNoParam);
                }
                break;
            case REQ_FORCE_FORWARD_MOVE:
                if (type == EXECUTION) {
                    cmdStandardApi.exeForceForwardMove(Double.parseDouble(!TextUtils.isEmpty(param2.getText().toString()) ?
                            param2.getText().toString() : "0"), Double.parseDouble(!TextUtils.isEmpty(param3.getText().toString()) ?
                            param3.getText().toString() : "0") ,listenerWithStageNoParam);
                } else {
                    cmdStandardApi.cancelForceForwardMove(listenerWithStageNoParam);
                }
                break;
            case REQ_FORCE_ROTATION_MOVE:
                if (type == EXECUTION) {
                    cmdStandardApi.exeForceRotationMove(Double.parseDouble(!TextUtils.isEmpty(param2.getText().toString()) ?
                            param2.getText().toString() : "0"), Double.parseDouble(!TextUtils.isEmpty(param3.getText().toString()) ?
                            param3.getText().toString() : "0"),listenerWithStageNoParam);
                } else {
                    cmdStandardApi.cancelForceRotationMove(listenerWithStageNoParam);
                }
                break;
            case REQ_SET_NAVI_GOAL_POINT:
                if (type == EXECUTION) {
                    PoseInfo poseInfo = hashMapPose.get((!TextUtils.isEmpty(param2.getText().toString()) ?
                            param2.getText().toString() : "defaut"));
                    if (poseInfo == null) {
                        Toast.makeText(this, "点位为空", Toast.LENGTH_SHORT).show();
                        return;
                    }
                    Log.d(TAG, "go pose = " + poseInfo.toString());
                    cmdStandardApi.exeNaviGoPlace(poseInfo ,listenerWithStageNoParam);
                } else {
                    cmdStandardApi.cancelNaviGoPlace(listenerWithStageNoParam);
                }
                break;
            case REQ_SET_RELOCALIZATION:
                if (type == EXECUTION) {
                    PoseInfo poseInfo = hashMapPose.get((!TextUtils.isEmpty(param2.getText().toString()) ?
                            param2.getText().toString() : "defaut"));
                    if (poseInfo == null) {
                        Toast.makeText(this, "点位为空", Toast.LENGTH_SHORT).show();
                        return;
                    }
                    Log.d(TAG, "set relocalization pose = " + poseInfo.toString());
                    cmdStandardApi.setRelocalization(poseInfo, new CmdStandardApi.ResponseListener() {
                        @Override
                        public void onError(int resultCode, String msg) {
                            showRespose(generateFailMsg(cmdChoose, resultCode, msg));
                        }

                        @Override
                        public void onResult(int resultCode, String msg, Object param) {
                            StringBuffer str = new StringBuffer(generateSucMsg(cmdChoose, resultCode, msg));
                            str.append("定位状态 = " + (boolean) param);
                            showRespose(str.toString());
                        }

                        @Override
                        public void onStatusUpdate(int stage, Object param) {
                            showRespose(generateStageMsg(cmdChoose, stage));
                        }
                    });
                }
                break;

            case REQ_GET_LOCALIZATION_STATE:
                cmdStandardApi.getRelocalizationState(new CmdStandardApi.ResponseListener() {
                    @Override
                    public void onError(int resultCode, String msg) {
                        showRespose(generateFailMsg(cmdChoose, resultCode, msg));
                    }

                    @Override
                    public void onResult(int resultCode, String msg, Object param) {
                        StringBuffer str = new StringBuffer(generateSucMsg(cmdChoose, resultCode, msg));
                        str.append("定位状态 = " + (boolean) param);
                        showRespose(str.toString());
                    }

                    @Override
                    public void onStatusUpdate(int stage, Object param) {
                        showRespose(generateStageMsg(cmdChoose, stage));
                    }
                });
                break;
            case REQ_SET_NAVI_SPEED_PARAM:
                //底盘加速度参数不能设置为0，-1表示维持上一次参数
                cmdStandardApi.setNaviSpeedParam(new NaviSpeedParam(Double.parseDouble(!TextUtils.isEmpty(param1.getText().toString()) ?
                        param1.getText().toString() : "0"), Double.parseDouble(!TextUtils.isEmpty(param2.getText().toString()) ?
                        param2.getText().toString() : "0"), Double.parseDouble(!TextUtils.isEmpty(param3.getText().toString()) ?
                        param3.getText().toString() : "-1"), Double.parseDouble(!TextUtils.isEmpty(param4.getText().toString()) ?
                        param4.getText().toString() : "-1")), listenerWithStageNoParam);
                break;
            case REQ_SAVE_MAPPING_PATH:
                /*if (type == EXECUTION) {
                    cmdStandardApi.saveMappingPath((!TextUtils.isEmpty(param2.getText().toString()) ?
                            param2.getText).toString() : "0"), listenerWithStageNoParam);
                }*/
                break;
            case REQ_CHARGE_PILE_RECOGNIZE:
                if (type == EXECUTION) {
                    cmdStandardApi.exeChargePileRecognize(new CmdStandardApi.ResponseListener() {
                        @Override
                        public void onError(int resultCode, String msg) {
                            showRespose(generateFailMsg(cmdChoose, resultCode, msg));
                        }

                        @Override
                        public void onResult(int resultCode, String msg, Object param) {
                            StringBuffer str = new StringBuffer(generateSucMsg(cmdChoose, resultCode, msg));
                            str.append("编号 = " + (String) param);
                            showRespose(str.toString());
                        }

                        @Override
                        public void onStatusUpdate(int stage, Object param) {
                            showRespose(generateStageMsg(cmdChoose, stage));
                        }
                    });
                } else {
                    cmdStandardApi.cancelChargePileRecognize(listenerWithStageNoParam);
                }
                break;
            case REQ_CHARGE_PILE_SEARCH:
                if (type == EXECUTION) {
                    cmdStandardApi.exeChargePileSearch(new CmdStandardApi.ResponseListener() {
                        @Override
                        public void onError(int resultCode, String msg) {
                            showRespose(generateFailMsg(cmdChoose, resultCode, msg));
                        }

                        @Override
                        public void onResult(int resultCode, String msg, Object param) {
                            StringBuffer str = new StringBuffer(generateSucMsg(cmdChoose, resultCode, msg));
                            PoseInfo pose = (PoseInfo) param;
                            str.append("坐标 = " + pose.toString());
                            showRespose(str.toString());
                        }

                        @Override
                        public void onStatusUpdate(int stage, Object param) {
                            showRespose(generateStageMsg(cmdChoose, stage));
                        }
                    });
                } else {
                    cmdStandardApi.cancelChargePileSearch(listenerWithStageNoParam);
                }
                break;
            case REQ_SAVE_MAPPING_POSE:
                if (type == EXECUTION) {
                    cmdStandardApi.saveMappingPose(Integer.parseInt(!TextUtils.isEmpty(param2.getText().toString()) ?
                            param2.getText().toString() : "0"), (!TextUtils.isEmpty(param3.getText().toString()) ?
                            param3.getText().toString() : ""), new CmdStandardApi.ResponseListener() {
                        @Override
                        public void onError(int resultCode, String msg) {
                            showRespose(generateFailMsg(cmdChoose, resultCode, msg));
                        }

                        @Override
                        public void onResult(int resultCode, String msg, Object param) {
                            StringBuffer str = new StringBuffer(generateSucMsg(cmdChoose, resultCode, msg));
                            Integer index = (Integer) param;
                            str.append("索引 = " + index.intValue());
                            showRespose(str.toString());
                        }

                        @Override
                        public void onStatusUpdate(int stage, Object param) {
                            showRespose(generateStageMsg(cmdChoose, stage));
                        }
                    });
                } else {
                    cmdStandardApi.cancelSaveMappingPose(Integer.parseInt(!TextUtils.isEmpty(param2.getText().toString()) ?
                            param2.getText().toString() : "0"), (!TextUtils.isEmpty(param3.getText().toString()) ?
                            param3.getText().toString() : ""), listenerWithStageNoParam);
                }
                break;
            case REQ_GET_SENSOR_STATUS:
                if (type == EXECUTION) {
                    cmdStandardApi.getSensorStatus(new CmdStandardApi.ResponseListener() {
                        @Override
                        public void onError(int resultCode, String msg) {
                            showRespose(generateFailMsg(cmdChoose, resultCode, msg));
                        }

                        @Override
                        public void onResult(int resultCode, String msg, Object param) {
                            StringBuffer str = new StringBuffer(generateSucMsg(cmdChoose, resultCode, msg));
                            SensorStatus sensorStatus = (SensorStatus) param;
                            str.append("传感器状态 = " + sensorStatus.toString());
                            showRespose(str.toString());
                        }

                        @Override
                        public void onStatusUpdate(int stage, Object param) {
                            showRespose(generateStageMsg(cmdChoose, stage));
                        }
                    });
                } else {
                    cmdStandardApi.cancelGetSensorStatus(listenerWithStageNoParam);
                }
                break;
                default:
                    break;
        }
    }



    private String generateFailMsg(String cmdChoose, int resultCode, String msg) {
        StringBuffer sb = new StringBuffer();
        sb.append("execute " + cmdChoose + " fail: ");
        sb.append("resultCode = " + resultCode);
        sb.append("msg = " + msg);
        return sb.toString();
    }

    private String generateSucMsg(String cmdChoose, int resultCode, String msg) {
        StringBuffer sb = new StringBuffer();
        sb.append("execute " + cmdChoose + " success: ");
        sb.append("resultCode = " + resultCode);
        sb.append("msg = " + msg);
        return sb.toString();
    }

    private String generateStageMsg(String cmdChoose, int stage) {
        StringBuffer sb = new StringBuffer();
        sb.append("execute " + cmdChoose + " 进度: ");
        sb.append("stage = " + stage);
        return sb.toString();
    }


    private void showRespose(final String msg) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                textView.setText(msg);
            }
        });
    }

    private AdapterView.OnItemSelectedListener listener = new AdapterView.OnItemSelectedListener() {
        @Override
        public synchronized void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
            Log.d(TAG, "choose command = " + commands.get(position));
            Toast.makeText(TestYueFan.this, commands.get(position), Toast.LENGTH_SHORT).show();
            cmdChoose = commands.get(position);
            inVisibleAllParam();
            switch (cmdChoose) {
                case REQ_SET_MANUAL_MODE:
                    param1.setVisibility(View.VISIBLE);
                    param1.setHint("1开启/0关闭");
                    break;
                case REQ_SET_FIX_POINT_MODE:
                    param1.setVisibility(View.VISIBLE);
                    break;
                case REQ_SET_CONTROL_MODE:
                    param1.setVisibility(View.VISIBLE);
                    param2.setVisibility(View.VISIBLE);//vx
                    param3.setVisibility(View.VISIBLE);//vTheta

                    break;
                case REQ_CHARGE_PILE_DOCKING:
                    param1.setVisibility(View.VISIBLE);
                    break;
                case REQ_FORCE_FORWARD_MOVE:
                    param1.setVisibility(View.VISIBLE);
                    param2.setVisibility(View.VISIBLE);
                    param3.setVisibility(View.VISIBLE);
                    break;
                case REQ_FORCE_ROTATION_MOVE:
                    param1.setVisibility(View.VISIBLE);
                    param2.setVisibility(View.VISIBLE);
                    param3.setVisibility(View.VISIBLE);
                    break;
                case REQ_SET_NAVI_GOAL_POINT:
                    param1.setVisibility(View.VISIBLE);
                    param1.setHint("1开始/0取消");
                    param2.setVisibility(View.VISIBLE);
                    param2.setHint("地点名称");
                    showRespose(getAllPoseName());
                    break;
                case REQ_SAVE_MAPPING_PATH:
                    param1.setVisibility(View.VISIBLE);
                    param2.setVisibility(View.VISIBLE);
                    mapImage.setVisibility(View.VISIBLE);
                    break;
                case REQ_CHARGE_PILE_RECOGNIZE:
                    param1.setVisibility(View.VISIBLE);
                    break;
                case REQ_CHARGE_PILE_SEARCH:
                    param1.setVisibility(View.VISIBLE);
                    break;
                case REQ_SAVE_MAPPING_POSE:
//                    param1.setVisibility(View.VISIBLE);
                    param2.setVisibility(View.VISIBLE);
                    param2.setHint("地点索引0,1,2...");
                    param3.setVisibility(View.VISIBLE);
                    param3.setHint("地点名称");
                    mapImage.setVisibility(View.VISIBLE);
                    break;
                case REQ_GET_SENSOR_STATUS:
                    param1.setVisibility(View.VISIBLE);
                    break;
                case REQ_DELETE_MAP:
                    param1.setVisibility(View.VISIBLE);
                    break;
                case REQ_START_CREATE_MAP:
                    param1.setVisibility(View.VISIBLE);
                    param1.setHint("输入地图名");
                    mapImage.setVisibility(View.VISIBLE);
                    break;
                case REQ_DELETE_LANDMARK:
                    param1.setVisibility(View.VISIBLE);
                    param2.setVisibility(View.VISIBLE);
                    break;
                case REQ_SWITCH_MAP:
                    param1.setVisibility(View.VISIBLE);
                    param1.setHint("地图名称");
                    break;
                case REQ_GET_MAP_INFO:
                    param1.setVisibility(View.VISIBLE);
                    break;
                case REQ_GET_LANDMARK_LIST:
                    param1.setVisibility(View.VISIBLE);
                    break;
                case REQ_MODIFY_LANDMARK_INFO:
                    param1.setVisibility(View.VISIBLE);
                    param2.setVisibility(View.VISIBLE);
                    param3.setVisibility(View.VISIBLE);
                    param4.setVisibility(View.VISIBLE);
                    param5.setVisibility(View.VISIBLE);
                    param6.setVisibility(View.VISIBLE);
                    param7.setVisibility(View.VISIBLE);
                    break;
                case REQ_ADD_LANDMARK:
                    param1.setVisibility(View.VISIBLE);
                    param2.setVisibility(View.VISIBLE);
                    param3.setVisibility(View.VISIBLE);
                    param4.setVisibility(View.VISIBLE);
                    param5.setVisibility(View.VISIBLE);
                    param6.setVisibility(View.VISIBLE);
                    break;
                case REQ_GET_CUR_MAP_INFO:
                    break;
                case REQ_GET_REALTIME_MAP:
                    //TODO can not show byte[]
                    break;
                case REQ_GET_MAP_LIST:
                    break;
                case REQ_STOP_CREATE_MAP:
                    mapImage.setVisibility(View.VISIBLE);
                    break;
                case REQ_SET_RELOCALIZATION:
//                    param1.setVisibility(View.VISIBLE);
                    param2.setVisibility(View.VISIBLE);
                    param2.setHint("点位名称");
                    showRespose(getAllPoseName());
                    break;
                case REQ_GET_LOCALIZATION_STATE:
                    break;
                case REQ_SET_NAVI_SPEED_PARAM:
                    param1.setVisibility(View.VISIBLE);
                    param1.setHint("线速度");
                    param2.setVisibility(View.VISIBLE);
                    param2.setHint("角速度");
                    param3.setVisibility(View.VISIBLE);
                    param3.setHint("线加速度");
                    param4.setVisibility(View.VISIBLE);
                    param4.setHint("角加速度");
                    break;
                case GO_ON_PATROL:
                    param1.setVisibility(View.VISIBLE);
                    param1.setHint("第1个导航点");
                    param2.setVisibility(View.VISIBLE);
                    param2.setHint("第2个导航点");
                    param3.setVisibility(View.VISIBLE);
                    param3.setHint("第3个导航点");
                    param4.setVisibility(View.VISIBLE);
                    param4.setHint("第4个导航点");
                    btnSend.setText("开始导航");
                    showRespose(getAllPoseName());
                    break;
                case GO_CHARGE:
                    btnSend.setText("去充电");
                    showRespose(getAllPoseName());
                default:
                    break;
            }
        }

        @Override
        public void onNothingSelected(AdapterView<?> parent) {

        }
    };

    private String getAllPoseName() {
        StringBuffer sb = new StringBuffer(30);
        sb.append("当前点位列表\n");
        for (String key : hashMapPose.keySet()) {
            sb.append(key);
            sb.append("\n");
        }
        return sb.toString();
    }


    private void inVisibleAllParam() {
        param1.setVisibility(View.GONE);
        param1.setText("");
        param1.setHint("");
        param2.setVisibility(View.GONE);
        param2.setText("");
        param2.setHint("");
        param3.setVisibility(View.GONE);
        param3.setText("");
        param3.setHint("");
        param4.setVisibility(View.GONE);
        param4.setText("");
        param4.setHint("");
        param5.setVisibility(View.GONE);
        param5.setText("");
        param5.setHint("");
        param6.setVisibility(View.GONE);
        param6.setText("");
        param6.setHint("");
        param7.setVisibility(View.GONE);
        param7.setText("");
        param7.setHint("");
        param8.setVisibility(View.GONE);
        param8.setText("");
        param8.setHint("");
        mapImage.setVisibility(View.GONE);
        btnSend.setText("发送");
    }



    private void initCmdApi() {
        cmdStandardApi = new CmdStandardApiStandardImpl();
        connectApi = new ConnectApiStandardImpl();
        cmdStandardApi.injectConnector(connectApi);
    }



}
