package com.ainirobot.navigationservice.chassisAbility;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.hardware.RobotCore;

/**
 * 处理导航传感器异常和正常的交互逻辑
 * 异常后5秒，上报给CoreService.
 */
public class SensorExceptionManager {

    private static final String TAG = SensorExceptionManager.class.getSimpleName();
    private final Handler mHandler;
    private static final long ERROR_DURING_TIME = 30 * Definition.SECOND;
    private static final long SLIGHT_TIME = 5 * Definition.SECOND;
    private static final int MSG_SENSOR_NORMAL = 1;
    private static final int MSG_SENSOR_ERROR = 2;
    private static final int MSG_SENSOR_SLIGHT_ERROR = 3;
    private static final int MSG_POSE_TIMEOUT_ERROR = 4;
    private static final int MSG_POSE_TIMEOUT_RECOVERY = 5;
    private static final int MSG_CHECK_SENSOR_NORMAL = 6;
    private static final int MSG_CHECK_POSE_NORMAL = 7;
    private static final int MSG_CHECK_SENSOR_TIMEOUT = 8;

    private volatile boolean isSensorNormal = true;
    private volatile boolean isReceivePoseNormal = true;
    private long errorTime;
    private long correctTime;
    private long sendSlightTime;

    private SensorExceptionManager() {
        HandlerThread workThread = new HandlerThread("WorkThread");
        workThread.start();
        mHandler = new WorkHandler(workThread.getLooper());
    }

    private static class SingletonHolder {
        private static final SensorExceptionManager mInstance = new SensorExceptionManager();
    }

    public static SensorExceptionManager getInstance() {
        return SingletonHolder.mInstance;
    }

    private final class WorkHandler extends Handler {

        public WorkHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case MSG_SENSOR_NORMAL:
                case MSG_POSE_TIMEOUT_RECOVERY:
                    Log.d(TAG, "MSG_SENSOR_NORMAL or MSG_POSE_TIMEOUT_RECOVERY");
                    RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, Definition.STATUS_NAVI_SENSOR_EXCEPTION,
                            Definition.NAVI_SENSOR_STATE_NORMAL);
                    break;
                case MSG_SENSOR_ERROR:
                    Log.d(TAG, "MSG_SENSOR_ERROR");
                    isSensorNormal = false;
                    RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, Definition.STATUS_NAVI_SENSOR_EXCEPTION,
                            Definition.NAVI_SENSOR_STATE_ERROR);
                    break;
                case MSG_POSE_TIMEOUT_ERROR:
                    Log.d(TAG, "MSG_POSE_TIMEOUT_ERROR");
                    isReceivePoseNormal = false;
                    RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, Definition.STATUS_NAVI_SENSOR_EXCEPTION,
                            Definition.NAVI_SENSOR_STATE_ERROR);
                    break;
                case MSG_SENSOR_SLIGHT_ERROR:
                    Log.d(TAG, "MSG_SENSOR_SLIGHT_ERROR");
                    RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, Definition.STATUS_NAVI_SENSOR_EXCEPTION,
                            Definition.NAVI_SENSOR_STATE_SLIGHT);
                    break;
                case MSG_CHECK_SENSOR_NORMAL:
                    Log.d(TAG, "MSG_CHECK_SENSOR_NORMAL isSensorNormal:" + isSensorNormal);
                    removeSensorMessages();
                    
                    if (!isSensorNormal) {
                        isSensorNormal = true;
                        correctTime = SystemClock.elapsedRealtime();
                        long time = correctTime - errorTime;
                        if (errorTime > 0 && time < ERROR_DURING_TIME) {
                            long delay = ERROR_DURING_TIME - time;
                            Log.d(TAG, "sendNormal delay:" + delay);
                            mHandler.sendEmptyMessageDelayed(MSG_SENSOR_NORMAL, delay);
                        } else {
                            Log.d(TAG, "sendNormal delay:0");
                            mHandler.sendEmptyMessage(MSG_SENSOR_NORMAL);
                        }
                    }
                    break;
                case MSG_CHECK_POSE_NORMAL:
                    Log.d(TAG, "MSG_CHECK_POSE_NORMAL isReceivePoseNormal:" + isReceivePoseNormal);
                    if (mHandler.hasMessages(MSG_POSE_TIMEOUT_ERROR)) {
                        mHandler.removeMessages(MSG_POSE_TIMEOUT_ERROR);
                    }
                    if (!isReceivePoseNormal) {
                        isReceivePoseNormal = true;
                        mHandler.sendEmptyMessage(MSG_POSE_TIMEOUT_RECOVERY);
                    }
                    break;
                case MSG_CHECK_SENSOR_TIMEOUT:
                    errorTime = SystemClock.elapsedRealtime();
                    if (errorTime - correctTime >= ERROR_DURING_TIME
                        || errorTime - sendSlightTime >= ERROR_DURING_TIME) {
                        Log.d(TAG, "MSG_CHECK_SENSOR_TIMEOUT errorTime:" + errorTime + " correctTime:" + correctTime + " sendSlightTime:" + sendSlightTime);
                        sendSlightTime = SystemClock.elapsedRealtime();
                        sendSlight();
                    }
                    break;
            }
        }
    }

    /**
     * 底盘持续上报, 约100毫秒上报１帧.
     */
    private void receivePoseNormal() {
        mHandler.sendEmptyMessage(MSG_CHECK_POSE_NORMAL);
    }

    /**
     * 底盘持续上报, 约100毫秒上报１帧.
     * 接收位姿超时异常时,
     */
    public void receivePoseTimeout() {
        mHandler.removeMessages(MSG_POSE_TIMEOUT_ERROR);
        mHandler.sendEmptyMessageDelayed(MSG_POSE_TIMEOUT_ERROR, 5 * Definition.SECOND);
    }

    /**
     * 底盘持续上报, 约100毫秒上报１帧.
     */
    public void sensorNormal() {
        receiveMapNormal();
        receivePoseNormal();
    }

    private void receiveMapNormal() {
        mHandler.sendEmptyMessage(MSG_CHECK_SENSOR_NORMAL);
    }

    /**
     * 底盘持续上报, 约100毫秒上报１帧.
     */
    public void sensorReceiveMapTimeout() {
        mHandler.sendEmptyMessage(MSG_CHECK_SENSOR_TIMEOUT);
    }

    /**
     * 5秒后上报, 轻微异常
     */
    private void sendSlight() {
        mHandler.removeMessages(MSG_SENSOR_NORMAL);
        mHandler.removeMessages(MSG_SENSOR_SLIGHT_ERROR);
        mHandler.sendEmptyMessageDelayed(MSG_SENSOR_SLIGHT_ERROR, SLIGHT_TIME);
        sendDelayError();
    }

    private void removeSlightMsg() {
        mHandler.removeMessages(MSG_SENSOR_SLIGHT_ERROR);
    }

    /**
     * 30秒后上报,Sensor错误结果.
     */
    private void sendDelayError() {
        mHandler.removeMessages(MSG_SENSOR_NORMAL);
        mHandler.removeMessages(MSG_SENSOR_ERROR);
        mHandler.sendEmptyMessageDelayed(MSG_SENSOR_ERROR, ERROR_DURING_TIME);
    }

    private void removeSensorMessages() {
        if (mHandler.hasMessages(MSG_SENSOR_ERROR)) {
            mHandler.removeMessages(MSG_SENSOR_ERROR);
        }
        if (mHandler.hasMessages(MSG_SENSOR_SLIGHT_ERROR)) {
            mHandler.removeMessages(MSG_SENSOR_SLIGHT_ERROR);
        }
        if (mHandler.hasMessages(MSG_SENSOR_NORMAL)) {
            mHandler.removeMessages(MSG_SENSOR_NORMAL);
        }
    }
}
