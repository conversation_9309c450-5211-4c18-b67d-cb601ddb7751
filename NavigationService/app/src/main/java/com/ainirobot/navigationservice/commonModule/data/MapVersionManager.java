package com.ainirobot.navigationservice.commonModule.data;

import android.util.Log;

/**
 * 地图版本管理
 *
 * <p>版本管理支持判断逻辑分两级：（SOP约束机器OTA升级）
 * 第一级判断，地图版本号是否支持，代表地图结构/内容的变化；
 * 第二级判断，视觉类型 & 标识码类型 & XXX 组合，代表同一版本号中不同机型的区分；
 *
 * <p> 招财豹 Pro 8.2 版本引入地图版本，默认版本号 0，后续做地图版本管理使用。
 * 本地序列化：
 * 服务端接口：
 *
 * <p> 招财豹 Pro-8.3，修改版本号为 1，并作为 Pro-8.4 合入主线后的统一版本。
 * 版本更新重点：支持视觉建图，地图类型支持组合类型；{@link com.ainirobot.navigationservice.roversdkhelper.maptype.MapTypeHelper}
 *
 * <p> 地图版本兼容原则和底盘算法保持一致：单向向前兼容，即新版本算法兼容旧地图，旧版本算法不一定兼容新地图。
 */
public class MapVersionManager {
    private static final String TAG = "MapVersionManager";

    public static final int MAP_VERSION = 2;

    public static MapVersionManager getInstance() {
        return SingleTone.INSTANCE;
    }

    private static class SingleTone {
        private static final MapVersionManager INSTANCE = new MapVersionManager();
    }

    private MapVersionManager() {
    }

    /**
     * 如果下载的地图版本号小于当前机器支持的版本号，则把版本号升级为当前机器支持的版本号
     */
    public static int updateDownloadMapVersion(int downloadMapVersion) {
        Log.d(TAG, "updateDownloadMapVersion: downloadMapVersion=" + downloadMapVersion +
                " Current MAP_VERSION=" + MAP_VERSION);
        return downloadMapVersion < MAP_VERSION ? MAP_VERSION : downloadMapVersion;
    }

}
