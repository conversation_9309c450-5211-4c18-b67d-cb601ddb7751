package com.ainirobot.navigationservice.commonModule.configuration.beans;

import com.google.gson.annotations.SerializedName;

public class InspectConfig {
    public static final int CHECK_SUC = 1;
    public static final int CHECK_FAIL = 0;
    public static final int NEED_CHECK = -1;
    public static final int SOCKET_SUC = 2;

    public static final int STEP_PNIG = 1;
    public static final int STEP_SOCKET = 2;
    public static final int STEP_SERVICE_OK = 3;
    public static final int STEP_SELF_RES = 4;
    public static final int STEP_SENSOR_CHECK = 5;

    public static final long TIME_OUT_LEN = 40*1000;

    public static final String INSPECTING_EXEC = "inspect executing";
    public static final String PING_NAV = "pingNav";
    public static final String SOCKET = "socket";
    public static final String SOCKET_UNAVAILABLE = "socket_unavailable";
    public static final String SOCKET_FAIL_REASON = "socket_disconnect";
    public static final String SERVICE_OK = "serviceOk";
    public static final String SERVICE_FAIL_REASON = "service not ready";
    public static final String SELF_CHECK_RES = "selfCheckResponse";
    public static final String SELF_CHECK_RES_TIMEOUT = "self check response timeout";
    public static final String SELF_CHECK_RES_NULL = "self check response is NULL";
    public static final String RESET_CAMERA = "resetCamera";
    public static final String RGBD = "rgbd";
    public static final String LASER = "laser";
    public static final String INFRARED = "infrared";
    public static final String SPEED_METER = "speedometer";
    public static final String LASER_AVAILABLE = "laserAvailable";
    public static final String CALIBRATION_READY = "calibrationReady";
    public static final String HARD_DISK_SPACE_ENOUGH = "hardDiskSpaceEnough";
    public static final String CAN_CONTROL_READY = "canControlReady";
    public static final String INFRARED_CAMERA_READY = "infraredCameraReady";
    public static final String GYRO_READY = "gyroReady";

    public static final String OTA_CONNECT = "otasocket";

    @SerializedName("ping网络")
    private boolean pingNav;

    @SerializedName("socket连接")
    private boolean socket;

    @SerializedName("底盘服务")
    private boolean serviceOk;

    @SerializedName("自检指令响应")
    private boolean selfCheckResponse;

    @SerializedName("重定位摄像头")
    private boolean resetCamera;

    @SerializedName("RGBD")
    private boolean rgbd;

    @SerializedName("激光雷达")
    private boolean laser;

    @SerializedName("红外")
    private boolean infrared;

    @SerializedName("里程计")
    private boolean speedometer;

    @SerializedName("激光数据")
    private boolean laserAvailable;

    @SerializedName("校准文件")
    private boolean calibrationReady;

    @SerializedName("硬盘空间")
    private boolean hardDiskSpaceEnough;

    @SerializedName("can连接")
    private boolean canControlReady;

    @SerializedName("红外摄像头")
    private boolean infraredCameraReady;

    @SerializedName("陀螺仪")
    private boolean gyroReady;

    public boolean isPingNav() {
        return pingNav;
    }

    public void setPingNav(boolean pingNav) {
        this.pingNav = pingNav;
    }

    public boolean isSocket() {
        return socket;
    }

    public void setSocket(boolean socket) {
        this.socket = socket;
    }

    public boolean isServiceOk() {
        return serviceOk;
    }

    public void setServiceOk(boolean serviceOk) {
        this.serviceOk = serviceOk;
    }

    public boolean isSelfCheckResponse() {
        return selfCheckResponse;
    }

    public void setSelfCheckResponse(boolean selfCheckResponse) {
        this.selfCheckResponse = selfCheckResponse;
    }

    public boolean isResetCamera() {
        return resetCamera;
    }

    public void setResetCamera(boolean resetCamera) {
        this.resetCamera = resetCamera;
    }

    public boolean isRgbd() {
        return rgbd;
    }

    public void setRgbd(boolean rgbd) {
        this.rgbd = rgbd;
    }

    public boolean isLaser() {
        return laser;
    }

    public void setLaser(boolean laser) {
        this.laser = laser;
    }

    public boolean isInfrared() {
        return infrared;
    }

    public void setInfrared(boolean infrared) {
        this.infrared = infrared;
    }

    public boolean isSpeedometer() {
        return speedometer;
    }

    public void setSpeedometer(boolean speedometer) {
        this.speedometer = speedometer;
    }

    public boolean isLaserAvailable() {
        return laserAvailable;
    }

    public void setLaserAvailable(boolean laserAvailable) {
        this.laserAvailable = laserAvailable;
    }

    public boolean isCalibrationReady() {
        return calibrationReady;
    }

    public void setCalibrationReady(boolean calibrationReady) {
        this.calibrationReady = calibrationReady;
    }

    public boolean isHardDiskSpaceEnough() {
        return hardDiskSpaceEnough;
    }

    public void setHardDiskSpaceEnough(boolean hardDiskSpaceEnough) {
        this.hardDiskSpaceEnough = hardDiskSpaceEnough;
    }

    public boolean isCanControlReady() {
        return canControlReady;
    }

    public void setCanControlReady(boolean canControlReady) {
        this.canControlReady = canControlReady;
    }

    public boolean isInfraredCameraReady() {
        return infraredCameraReady;
    }

    public void setInfraredCameraReady(boolean infraredCameraReady) {
        this.infraredCameraReady = infraredCameraReady;
    }

    public boolean isGyroReady() {
        return gyroReady;
    }

    public void setGyroReady(boolean gyroReady) {
        this.gyroReady = gyroReady;
    }

    @Override
    public String toString() {
        return "InspectConfig{" +
                "pingNav=" + pingNav +
                ", socket=" + socket +
                ", serviceOk=" + serviceOk +
                ", selfCheckResponse=" + selfCheckResponse +
                ", resetCamera=" + resetCamera +
                ", rgbd=" + rgbd +
                ", laser=" + laser +
                ", infrared=" + infrared +
                ", speedometer=" + speedometer +
                ", laserAvailable=" + laserAvailable +
                ", calibrationReady=" + calibrationReady +
                ", hardDiskSpaceEnough=" + hardDiskSpaceEnough +
                ", canControlReady=" + canControlReady +
                ", infraredCameraReady=" + infraredCameraReady +
                ", gyroReady=" + gyroReady +
                '}';
    }
}
