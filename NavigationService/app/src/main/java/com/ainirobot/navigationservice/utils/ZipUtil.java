/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.navigationservice.utils;

import android.os.Build;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.navigationservice.roversdkhelper.mappackage.MapPkgHelper;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.zip.GZIPOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipException;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

public class ZipUtil {
    private static final String TAG = "ZipUtil";

    // 1M Byte
    private static final int ZIP_BUFF_SIZE = 1024 * 1024;

    public static void zipFileOrDirectory(ZipOutputStream out,
                                          File fileOrDirectory, String curPath) throws IOException {
        FileInputStream in = null;
        try {
            if (!fileOrDirectory.isDirectory()) {
                byte[] buffer = new byte[4096];
                int bytes_read;
                in = new FileInputStream(fileOrDirectory);
                ZipEntry entry = new ZipEntry(curPath
                        + fileOrDirectory.getName());
                out.putNextEntry(entry);
                while ((bytes_read = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytes_read);
                }
                out.closeEntry();
            } else {
                File[] entries = fileOrDirectory.listFiles();
                for (int i = 0; i < entries.length; i++) {
                    zipFileOrDirectory(out, entries[i], curPath
                            + fileOrDirectory.getName() + File.separator);
                }
            }
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException ex) {
                    ex.printStackTrace();
                }
            }
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    private static void unZipFile(File zipFile, String folderPath)
            throws ZipException, IOException {
        File desDir = new File(folderPath);
        if (!desDir.exists()) {
            desDir.mkdirs();
        }
        ZipFile zf = null;
        try {
            zf = new ZipFile(zipFile, Charset.forName("GBK"));
            for (Enumeration<?> entries = zf.entries(); entries.hasMoreElements(); ) {
                InputStream in = null;
                OutputStream out = null;
                try {
                    ZipEntry entry = ((ZipEntry) entries.nextElement());
                    in = zf.getInputStream(entry);
                    String str = folderPath + File.separator + entry.getName();
                    Log.d(TAG, "unZipFile: entry name: " + str);
                    File desFile = new File(str);
                    if (!desFile.exists()) {
                        File fileParentDir = desFile.getParentFile();
                        if (!fileParentDir.exists()) {
                            fileParentDir.mkdirs();
                        }
                        desFile.createNewFile();
                    }
                    out = new FileOutputStream(desFile);
                    byte buffer[] = new byte[ZIP_BUFF_SIZE];
                    int realLength;
                    while ((realLength = in.read(buffer)) > 0) {
                        out.write(buffer, 0, realLength);
                    }
                } finally {
                    IOUtils.close(in);
                    IOUtils.close(out);
                }
            }
        } finally {
            IOUtils.close(zf);
        }
    }

    /**
     * @param zipFile       压缩包路径
     * @param folderPath    解压后的文件存储路径
     * @param matchFileName 需要找寻的压缩文件名，如果需要全部解压传null或“”
     * @throws ZipException
     * @throws IOException
     */
    public static void upZipFile(File zipFile, String folderPath, String matchFileName)
            throws ZipException, IOException {
        File desDir = new File(folderPath);
        if (!desDir.exists()) {
            desDir.mkdirs();
        }
        ZipFile zf = null;
        try {
            zf = new ZipFile(zipFile);
            for (Enumeration<?> entries = zf.entries(); entries.hasMoreElements(); ) {
                InputStream in = null;
                OutputStream out = null;
                try {
                    ZipEntry entry = ((ZipEntry) entries.nextElement());
                    //判断如果当前要解压的文件名和需要解压的文件名不一致，则继续查询
                    if (!TextUtils.isEmpty(matchFileName)) {
                        if (entry == null || !matchFileName.equals(entry.getName())) {
                            continue;
                        }
                    }
                    in = zf.getInputStream(entry);
                    String str = folderPath + File.separator + entry.getName();
                    Log.d("MapUtils", str);
                    //str = new String(str.getBytes("8859_1"), "GB2312");
                    Log.d("MapUtils", "**** " + new String(str.getBytes("8859_1"), "GB2312"));
                    File desFile = new File(str);
                    if (!desFile.exists()) {
                        File fileParentDir = desFile.getParentFile();
                        if (!fileParentDir.exists()) {
                            fileParentDir.mkdirs();
                        }
                        desFile.createNewFile();
                    }
                    out = new FileOutputStream(desFile);
                    byte buffer[] = new byte[ZIP_BUFF_SIZE];
                    int realLength;
                    while ((realLength = in.read(buffer)) > 0) {
                        out.write(buffer, 0, realLength);
                    }
                } finally {
                    IOUtils.close(in);
                    IOUtils.close(out);
                }
            }
        } finally {
            IOUtils.close(zf);
        }
    }

    public static byte[] zip(byte[] data) {
        byte[] b = null;
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ZipOutputStream zip = new ZipOutputStream(bos);
            ZipEntry entry = new ZipEntry("zip");
            entry.setSize(data.length);
            zip.putNextEntry(entry);
            zip.write(data);
            zip.closeEntry();
            zip.close();
            b = bos.toByteArray();
            bos.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return b;
    }

    public static byte[] unZip(byte[] data) {
        byte[] b = null;
        try {
            ByteArrayInputStream bis = new ByteArrayInputStream(data);
            ZipInputStream zip = new ZipInputStream(bis);
            while (zip.getNextEntry() != null) {
                byte[] buf = new byte[1024];
                int num = -1;
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                while ((num = zip.read(buf, 0, buf.length)) != -1) {
                    baos.write(buf, 0, num);
                }
                b = baos.toByteArray();
                baos.flush();
                baos.close();
            }
            zip.close();
            bis.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return b;
    }

    /**
     * 压缩多个文件到一个zip包
     *
     * @param filePaths     需要压缩的文件列表
     * @param targetZipPath 目标zip包路径
     * @throws IOException
     */
    public static boolean zipFiles(List<String> filePaths, String targetZipPath, String targetZipDir) {
        Log.d(TAG, "zipFiles: filePaths=" + filePaths + " targetZipPath=" + targetZipPath);
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(new FileOutputStream(targetZipPath))) {
            for (String filePath : filePaths) {
                File file = new File(filePath);
                if (!file.exists()){
                    Log.d(TAG, "zipFiles: file not exist , path is " + filePath);
                    continue;
                }
                try (FileInputStream fileInputStream = new FileInputStream(file)) {
                    String name = file.getName();
                    if (!TextUtils.isEmpty(targetZipDir)) {
                        name = targetZipDir + File.separator + name;
                    }
                    ZipEntry zipEntry = new ZipEntry(name);
                    zipOutputStream.putNextEntry(zipEntry);
                    byte[] bytes = new byte[1024];
                    int length;
                    while ((length = fileInputStream.read(bytes)) >= 0) {
                        zipOutputStream.write(bytes, 0, length);
                    }
                }
            }
        } catch (IOException e) {
            Log.d(TAG, "zipFiles: IOException:" + e.getMessage());
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * DeCompress the ZIP to the path
     *
     * @param zipFilePath name of ZIP, zip pkg, absolute dir name
     * @param outPathDir  path to be unZIP， unzip all file to this directory
     * @throws Exception
     */
    public static boolean unZipFolder(String zipFilePath, String outPathDir) {
        Log.d(TAG, "unZipFolder: zipFilePath=" + zipFilePath + " outPathDir=" + outPathDir);
        ZipInputStream inZip = null;
        ZipEntry zipEntry;
        String szName = "";
        try {
            inZip = new ZipInputStream(new FileInputStream(zipFilePath));
            while ((zipEntry = inZip.getNextEntry()) != null) {
                szName = zipEntry.getName();
                if (zipEntry.isDirectory()) {
                    // get the folder name of the widget
                    szName = szName.substring(0, szName.length() - 1);
                    File folder = new File(outPathDir + File.separator + szName);
                    folder.mkdirs();
                } else {
                    String targetFilePath = outPathDir + File.separator + szName;
                    File file = new File(targetFilePath);
                    Log.d(TAG, "unZipFolder: targetFilePath=" + targetFilePath);
                    if (!file.exists()) {
                        File fileParentDir = file.getParentFile();
                        if (!fileParentDir.exists()) {
                            fileParentDir.mkdirs();
                        }
                        file.createNewFile();
                    }

                    FileOutputStream out = null;
                    try {
                        out = new FileOutputStream(file);
                        int len;
                        byte[] buffer = new byte[1024];
                        // read (len) bytes into buffer
                        while ((len = inZip.read(buffer)) != -1) {
                            // write (len) byte from buffer at the position 0
                            out.write(buffer, 0, len);
                            out.flush();
                        }
                    } finally {
                        IOUtils.close(out);
                    }
                }
            }
        } catch (Exception e) {
            Log.d(TAG, "unZipFolder: Exception:" + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            IOUtils.close(inZip);
        }
        Log.d(TAG, "unZipFolder: Success!");
        return true;
    }

    public static void zipFiles(List<File> files, String targetZipPath, MapPkgHelper.Callback callback) {
        ThreadUtils.getIoService().submit(() -> {
            try {
                try (ZipOutputStream zipOutputStream = new ZipOutputStream(new FileOutputStream(targetZipPath))) {
                    for (File file : files) {
                        try (FileInputStream fileInputStream = new FileInputStream(file)) {
                            ZipEntry zipEntry = new ZipEntry(file.getName());
                            zipOutputStream.putNextEntry(zipEntry);
                            byte[] bytes = new byte[1024];
                            int length;
                            while ((length = fileInputStream.read(bytes)) >= 0) {
                                zipOutputStream.write(bytes, 0, length);
                            }
                        }
                    }
                }
                callback.onFinished(true, targetZipPath, "");
            } catch (IOException e) {
                e.printStackTrace();
                callback.onFinished(false, targetZipPath, e.getMessage());
            }
        });
    }

    public static void unZipFile(String zipFilePath, String folderPath, MapPkgHelper.Callback callback) {
        ThreadUtils.getIoService().submit(() -> {
            try {
                unZipFile(new File(zipFilePath), folderPath);
                if (null != callback) callback.onFinished(true, zipFilePath, "");
            } catch (Exception e) {
                e.printStackTrace();
                if (null != callback) callback.onFinished(false, zipFilePath, e.getMessage());
            }
        });
    }
}
