package com.ainirobot.navigationservice.chassisAbility.ota.connector.tk1.bean;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

public class OtaResMessage {
    private final static String TAG = TAGPRE + OtaResMessage.class.getSimpleName();
    public final static int ERROR_RES = 0;
    public final static int SUC_RES = 1;
    private int type;
    private String msg;

    public OtaResMessage(int type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

}

