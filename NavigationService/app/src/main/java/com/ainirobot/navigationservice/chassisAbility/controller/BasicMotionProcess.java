package com.ainirobot.navigationservice.chassisAbility.controller;

import static com.ainirobot.navigationservice.Defs.Def.ResultCode.FAIL_NO_REASON;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.SUCCESS;

import android.annotation.SuppressLint;
import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.navigationservice.beans.tk1.RobotPose;
import com.ainirobot.navigationservice.beans.tk1.Velocity;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.x86.WheelControlX86;
import com.ainirobot.navigationservice.utils.LogUtils;

import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Created by Orion on 2020/6/23.
 * 基础运动过程控制器：
 * 1.保证给定距离和角度的运动精度
 * 2.运动结束后反馈成功状态
 *
 * <p>
 * 精度
 * 1.静止状态开始运动，运动中触发同向运动
 * 直线运动误差<5cm
 * 角速度误差<5度
 * 2.运动中触发逆向运动
 * 直线运动误差5-10cm
 * 角速度误差5-10度
 *
 * <p>
 * 误差因素：
 * 1.odom上报延时(100ms)，最大速度越大误差越大；--不处理
 * 2.实际刹车距离与计算值偏差（5-12cm）,轮毂释放后惯性运动；--不处理
 * 3.里程计本身偏差；--不处理
 * 4.成功判断条件：移动距离>=开始刹车距离。
 * 优化方式：
 * 4.1 提高判断频率，判断移动距离频率提高到50hz；
 * 4.2 以最后一个小于目标距离的值为成功判断条件；这里减小的差量和1、2因素中增大的差量起一定抵消作用；
 */
public class BasicMotionProcess {
    private static final String TAG = "BasicMotionProcess";

    //mini2 轮距常量 0.29m
    private static final double MINI2_WHEEL_BASE = 0.29;
    //小豹轮距常量
    private static final double XIAOMI_WHEEL_BASE = 0.37;

    private IChassisClient mChassis;
    private volatile IChassisClient.ChassisResListener mChassisListener;
    private ScheduledExecutorService mExecutor = Executors.newScheduledThreadPool(1);
    private Future mFuture;
    private Future mFutureTimer;
    private volatile BasicMotionMode mMode = BasicMotionMode.LINEAR;
    private double mLastLeftAcc;
    private double mLastRightAcc;
    private volatile double mMoveOffset; //直线差量
    private double mTotalDistance;
    private volatile double mLastMove;
    private volatile double mAngleOffset; //角度差量
    private volatile double mLastThetaAcc;
    private volatile double mTargetDistance; //目标距离
    private volatile double mTargetSpeed; //目标速度
    private volatile double mStartDecDistance; //开始刹车距离
    private volatile double mDecTime; //开始刹车到速度降为0需要的时间
    private volatile State mMotionState = State.IDLE;
    private static double mWheelBase = XIAOMI_WHEEL_BASE;

    //运动开始时间
    private long mStartTime = 0;
    //里程计异常，机器人未移动最大超时时间，每5秒检测一次里程计是否在运动
    private static final long NOT_MOVE_MAX_TIME = 1000 * 5;
    private volatile double mTempMoveOffset; //用于检测的直线差量
    private volatile double mTempAngleOffset; //用于检测的角度差量

    enum State {
        IDLE,
        MOTIONING,
        INERTIAL_MOTION
    }

    enum BasicMotionMode {
        LINEAR,
        ANGULAR
    }

    static class Singleton {
        public static final BasicMotionProcess instance = new BasicMotionProcess();
    }

    public static BasicMotionProcess getInstance() {
        return Singleton.instance;
    }

    public void init(IChassisClient client) {
        this.mChassis = client;
        if (ProductInfo.isMiniProduct()) {
            mWheelBase = MINI2_WHEEL_BASE;
        }
    }

    private BasicMotionProcess() {

    }

    public void startLinearTask(double distance, double speed,
                                IChassisClient.ChassisResListener listener) {
        Log.i(TAG, "startLinearTask : distance=" + distance + ", speed=" + speed);
        mMode = BasicMotionMode.LINEAR;

        startMotionTask(distance, speed, listener);
    }

    public void startAngularTask(double radians, double speed,
                                 IChassisClient.ChassisResListener listener) {
        Log.i(TAG, "startAngularTask : radians=" + radians + "(" + Math.toDegrees(radians) + ")"
                + ", speed=" + speed);
        mMode = BasicMotionMode.ANGULAR;

        startMotionTask(radians, speed, listener);
    }

    private synchronized void startMotionTask(double distance, double speed,
                                              IChassisClient.ChassisResListener listener) {
        Log.i(TAG, "startMotionTask : mMode=" + mMode + ", distance=" + distance
                + ", speed=" + speed);
//        if (mChassisListener != null) {
//            mChassisListener.onResponse(false, Def.ResultCode.BASIC_MOTION_INTERRUPTED,
//                    "Interrupted");
//        }
        mChassisListener = listener;

        mMotionState = State.MOTIONING;
        mMoveOffset = 0;
        mAngleOffset = 0;
        mTempMoveOffset = 0;
        mTempAngleOffset = 0;
        mStartTime = System.currentTimeMillis();
        calculateStartDecDistance(distance, speed);
        startMotionProcessMonitor();
    }

    /**
     * 运动过程监听
     */
    @SuppressLint("DiscouragedApi")
    private synchronized void startMotionProcessMonitor() {
        Log.i(TAG, "startMotionProcessMonitor:mStartDecDistance=" + mStartDecDistance
                + ", mDecTime=" + mDecTime);
        cancelMotionProcessMonitor();
        cancelDecTimer();
        mFuture = mExecutor.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
//                Log.i(TAG, "startMotionTimer run : mMoveOffset=" + mMoveOffset);
                double distanceOffset = (mMode == BasicMotionMode.LINEAR
                        ? mMoveOffset + mLastMove
                        : mAngleOffset + mLastThetaAcc);
                if (distanceOffset >= mStartDecDistance) {
                    Log.i(TAG, "Start dec : distanceOffset=" + distanceOffset
                            + ", mStartDecDistance=" + mStartDecDistance + ", mDecTime=" + mDecTime);
                    cancelMotionProcessMonitor();
                    mChassis.motion(0, 0, 0, true);
                    startDecTimer();
                    return;
                }
                //每隔5秒检测一次里程计是否在运动，
                if (System.currentTimeMillis() - mStartTime > NOT_MOVE_MAX_TIME) {
                    //如果未移动则认为里程计异常,直接结束运动
                    if ((mMode == BasicMotionMode.LINEAR && mTempMoveOffset == 0) || (mMode == BasicMotionMode.ANGULAR && mTempAngleOffset == 0)) {
                        Log.e(TAG, "Not move max time, odom error");
                        cancelMotionProcessMonitor();
                        mChassis.motion(0, 0, 0, true);
                        motionFailed();
                    } else {
                        //重置检测值，重新计时
                        mTempMoveOffset = 0;
                        mTempAngleOffset = 0;
                        mStartTime = System.currentTimeMillis();
                    }
                }
            }
        }, 0, 20, TimeUnit.MILLISECONDS);
    }

    private synchronized void cancelMotionProcessMonitor() {
        if (mFuture == null) {
            return;
        }
        if (!mFuture.isCancelled() && !mFuture.isDone()) {
            mFuture.cancel(true);
            mFuture = null;
        }
    }

    /**
     * 等待减速完成
     */
    private void startDecTimer() {
        Log.i(TAG, "startDecTimer mDecTime=" + mDecTime);
        mMotionState = State.INERTIAL_MOTION;
        cancelDecTimer();
        if (mDecTime > 0) {
            mFutureTimer = mExecutor.schedule(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "startDecTimer run : mDecTime=" + mDecTime
                            + ", mMotionState=" + mMotionState);
                    if (mMotionState == State.INERTIAL_MOTION) {
                        motionSuccess();
                    }
                }
            }, (long) mDecTime, TimeUnit.MILLISECONDS);
        } else {
            motionSuccess();
        }
    }

    private void cancelDecTimer() {
        if (mFutureTimer == null) {
            return;
        }
        if (!mFutureTimer.isCancelled() && !mFutureTimer.isDone()) {
            mFutureTimer.cancel(true);
            mFutureTimer = null;
        }
    }

    private void motionSuccess() {
        Log.i(TAG, "motionSuccess");
        mMotionState = State.IDLE;
        if (mChassisListener != null) {
            mChassisListener.onResponse(true, SUCCESS, "Motion success");
            mChassisListener = null;
        }
        mChassis.updateMotionAvoidState(false);
    }

    private void motionFailed() {
        Log.i(TAG, "motionFailed");
        mMotionState = State.IDLE;
        if (mChassisListener != null) {
            mChassisListener.onResponse(false, FAIL_NO_REASON, "Motion failed");
            mChassisListener = null;
        }
        mChassis.updateMotionAvoidState(false);
    }

    /**
     * 计算开始刹车的节点，机器所走过的距离
     *
     * @param distance 运动距离
     * @param speed    最大速度
     */
    private void calculateStartDecDistance(double distance, double speed) {
        mTargetDistance = distance;
        mTargetSpeed = Math.abs(speed);
        double signTarget = Math.signum(speed);

        double currentSpeed = 0;
        double acc = 0;
        double dec = 0;
        boolean isCurrentStill = true;
        Velocity velocity = mChassis.getRealtimeVelocity();
        Log.i(TAG, "Start Dec Distance : Realtime velocity=" + (velocity != null ? velocity.toString() : "null"));
        if (velocity == null) {
            velocity = new Velocity(0, 0);
        }
        if (mMode == BasicMotionMode.LINEAR) {
            isCurrentStill = WheelControlX86.getInstance().isLinearStill(velocity);
            currentSpeed = velocity.getX();
            acc = getLinearAcc();
            dec = getLinearDec();
        } else if (mMode == BasicMotionMode.ANGULAR) {
            isCurrentStill = WheelControlX86.getInstance().isAngularStill(velocity);
            currentSpeed = velocity.getZ();
            acc = getAngularAcc();
            dec = getAngularDec();
        }
        double signCurrent = Math.signum(currentSpeed);

        boolean isInverseMotion = (signTarget * signCurrent == -1) && !isCurrentStill; //是否逆向运动
        double accDistance; //目标速度加速距离
        double decDistance; //目标速度减速距离
        double currentSpeedDecDistance; //当前速度减速距离
        if (isInverseMotion) {
            currentSpeedDecDistance = Math.pow(currentSpeed, 2) / (2 * dec);
            currentSpeed = 0;
            accDistance = Math.pow(mTargetSpeed, 2) / (2 * acc);
            decDistance = Math.pow(mTargetSpeed, 2) / (2 * dec);
            mTargetDistance += currentSpeedDecDistance;
        } else {
            accDistance = (Math.pow(mTargetSpeed, 2) - Math.pow(currentSpeed, 2)) / (2 * acc);
            decDistance = Math.pow(mTargetSpeed, 2) / (2 * dec);
            currentSpeedDecDistance = Math.pow(currentSpeed, 2) / (2 * dec);
        }
        Log.i(TAG, "Start Dec Distance : isInverseMotion=" + isInverseMotion
                + ", isCurrentStill=" + isCurrentStill + ", mTargetDistance=" + mTargetDistance
                + ", mTargetSpeed=" + mTargetSpeed + ", currentSpeed=" + currentSpeed
                + ", acc=" + acc + ", dec=" + dec);

        if (mTargetDistance >= (accDistance + decDistance)) {
            mStartDecDistance = mTargetDistance - decDistance;
            mDecTime = (mTargetSpeed / dec) * 1000;
        } else if (mTargetDistance <= currentSpeedDecDistance && !isInverseMotion) {
            mStartDecDistance = 0;
            mDecTime = (currentSpeed / dec) * 1000;
        } else {
//            mTargetDistance = (Math.pow(vMaxReal, 2) - Math.pow(currentSpeed, 2)) / 2 * acc + Math.pow(vMaxReal, 2) / 2 * dec; //加速度公式，推导出下面公式计算实际可达到的最大速度
            double vMaxReal = Math.sqrt((2 * acc * dec * mTargetDistance + Math.pow(currentSpeed, 2) * dec) / (acc + dec));
            double decDistanceReal = Math.pow(vMaxReal, 2) / (2 * dec);
            Log.i(TAG, "Start Dec Distance : vMaxReal=" + vMaxReal + ", decDistanceReal=" + decDistanceReal);
            mStartDecDistance = mTargetDistance - decDistanceReal;
            mDecTime = (vMaxReal / dec) * 1000;
        }

        if (isInverseMotion) {
            mStartDecDistance += currentSpeedDecDistance; //逆向运动补偿当前速度减速过程距离
        }

        Log.i(TAG, "Start Dec Distance : mStartDecDistance=" + mStartDecDistance
                + ", accDistance=" + accDistance + ", decDistance=" + decDistance
                + ", currentSpeedDecDistance=" + currentSpeedDecDistance
                + ", mDecTime=" + mDecTime);
    }

    /**
     * 实时计算直线和角度差量
     *
     * @param move     两次上报间隔的直线差量，轮毂空转时move值有变化
     * @param leftAcc  左轮累计量，轮毂空转时leftAcc值有变化
     * @param rightAcc 邮轮累计量，轮毂空转时rightAcc值有变化
     */
    public void handleOdomData(double move, double leftAcc, double rightAcc) {
        LogUtils.printLog(LogUtils.TYPE_ODOM_LINEAR, TAG, "handleOdomData : move=" + move
                + " leftAcc=" + leftAcc + " rightAcc=" + rightAcc, 1000);
        mLastMove = Math.abs(move);
        mMoveOffset += Math.abs(move);
        mTempMoveOffset += Math.abs(move);
        mTotalDistance += Math.abs(move);

        double leftD = leftAcc - mLastLeftAcc;
        double rightD = rightAcc - mLastRightAcc;
        RobotPose poseD = getTargetPose(leftD, rightD);
        LogUtils.printLog(LogUtils.TYPE_ODOM_ANGULAR, TAG, "handleOdomData : "
                        + "move=" + move + " leftAcc=" + leftAcc + " rightAcc=" + rightAcc
                        + " leftD=" + leftD + " rightD=" + rightD + " poseD=" + poseD.toString()
                , 1000);
        mLastThetaAcc = Math.abs(poseD.getTheta());
        mAngleOffset += Math.abs(poseD.getTheta());
        mTempAngleOffset += Math.abs(poseD.getTheta());
        mLastLeftAcc = leftAcc;
        mLastRightAcc = rightAcc;
    }

    /**
     * 计算差量pose，得到差量theta
     *
     * @param leftOffset
     * @param rightOffset
     * @return
     */
    private RobotPose getTargetPose(double leftOffset, double rightOffset) {
        double l = leftOffset;//左轮差量
        double r = rightOffset;//右轮差量
        RobotPose poseD;
        double t = mWheelBase; //轮距常量
        double p = r + l;
        double m = r - l;
        if (Math.abs(m) < 1e-6) {
            poseD = new RobotPose(l, 0, 0);
        } else {
            double theta = m / t;
            double s = Math.sin(theta);
            double c = Math.cos(theta);

            double radius = p / 2.0 / theta;
            double x = radius * s;
            double y = radius * (1 - c);

            poseD = new RobotPose(x, y, theta);
        }
        return poseD;
    }

    public synchronized void stopMotionTask(boolean status, int resultCode, Object result) {
        Log.i(TAG, "stopMotionTask : status=" + status + ", resultCode=" + resultCode
                + ", result=" + result + ", mMotionState=" + mMotionState);
        if (mMotionState == State.IDLE) {
            Log.e(TAG, "stopMotionTask : Is not running error");
            return;
        }

        mMotionState = State.IDLE;
        cancelMotionProcessMonitor();
        cancelDecTimer();
        if (mChassisListener != null) {
            mChassisListener.onResponse(status, resultCode, result);
        }
        mChassisListener = null;
    }

    public double getTotalDistance() {
        return mTotalDistance;
    }

    //线-加速度
    private double getLinearAcc() {
        return WheelControlX86.A_LINEAR_ACC;
    }

    //线-减速度
    private double getLinearDec() {
        return WheelControlX86.A_LINEAR_DEC;
    }

    //角-加速度
    private double getAngularAcc() {
        return WheelControlX86.A_ANGULAR_ACC;
    }

    //角-减速度
    private double getAngularDec() {
        return WheelControlX86.A_ANGULAR_DEC;
    }
}
