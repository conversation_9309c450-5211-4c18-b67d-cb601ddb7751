/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.tk1;


import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.navigationservice.ApplicationWrapper;

public class RoverConfig {

    public static final int NORMAL = 0;
    public static final int RISKY = 1;
    public static final int UNSAFE = 2;
    public static final int UNRECOGNIZED = -1;

    public static final int DEVICE_CUSTOM = 0;
    public static final int DEVICE_KTV = 1;
    public static final int DEVICE_MESSIA_LH = 2;
    public static final int DEVICE_MESSIA = 4;
    public static final int DEVICE_MESSIA_MIDEA = 5;
    public static final int DEVICE_X86 = 6;
    public static final int DEVICE_X86_MINI = 7;
    public static final int DEVICE_X86_ = 8;
    public static final int DEVICE_MESSIA_S = 9;
    public static final int DEVICE_X86_MINI2 = 10;
    public static final int DEVICE_KTV2 = 11;
    public static final int DEVICE_WAITER = 12;
    public static final int DEVICE_MINI2_XIAOQIAO = 13;
    public static final int DEVICE_MINI2_EAI = 14;
    public static final int DEVICE_WAITER_SA = 15;
    public static final int DEVICE_MINI2_SHANG_CHANG = 16;
    public static final int DEVICE_MINI3 = 17;
    public static final int DEVICE_MINI3_EAI = 18;
    public static final int DEVICE_WAITER_SA_H = 19; // 招财，减震加高
    public static final int DEVICE_WAITER_PRO = 20; // 招财Pro
    public static final int DEVICE_WAITER_DISINFECTION = 21; // 招财消毒版本
    public static final int DEVICE_WAITER_SA_H_LW = 22; // 招财，减震加高，雷达210开口
    public static final int DEVICE_WAITER_SA_FM1 = 23; // 招财，减震加宽，肇观RGBD
    public static final int DEVICE_WAITER_WF = 24; // 招财，瞰瞰RGBD
    public static final int DEVICE_WAITER_SA_WF = 25; // 招财，减震，瞰瞰RGBD
    public static final int DEVICE_WAITER_SA_LW_WF = 26; // 招财，减震，瞰瞰RGBD，210°雷达开口
    public static final int DEVICE_MESSIA_PLUS = 27; // 豹小秘Plus，Inter D430
    public static final int DEVICE_MESSIA_PLUS_WF = 28; // 豹小秘Plus，瞰瞰RGBD
    public static final int DEVICE_WAITER_SA_LW = 29; // 招财，减震，雷达210开口
    public static final int DEVICE_WAITER_PRO_D3 = 30; // 招财Pro，X100 RGBD
    public static final int DEVICE_WAITER_X100 = 31; // 招财，X100
    public static final int DEVICE_WAITER_SA_X100 = 32; // 招财，减震，X100
    public static final int DEVICE_WAITER_SA_H_X100 = 33; // 招财，减震加高，X100
    public static final int DEVICE_WAITER_SA_H_LW_X100 = 34; // 招财，减震加高，X100，210°雷达开口
    public static final int DEVICE_WAITER_SA_LW_X100 = 35; // 招财，减震，X100，210°雷达开口
    public static final int DEVICE_WAITER_PRO_2XB40_X100 = 36; // 招财Pro，2XB40上视，1X100下视
    public static final int DEVICE_MEISSA2 = 37; // 豹小秘2，无TOP，有悬挂（不写参数），XB40X100
    public static final int DEVICE_WAITER_PRO_TOP_MONO = 38; // 导航使用，暂时无引用
    public static final int DEVICE_WAITER_PRO_2XB40_X100_ELEC_GATE = 39; // 招财Pro，AutoDoor
    public static final int DEVICE_MINI2_SIMULATOR = 40; // Simulator
    //导航Wrapper中重复定义，都是38即可
    public static final int DEVICE_WAITER_PRO_2XB40_X100_TOP_MONO = 41; // 招财Pro，TOPMono
    public static final int DEVICE_MINI2_WF = 42; // Mini，瞰瞰
    public static final int DEVICE_MINI2_WF_SSMONO = 43; // Mini，瞰瞰，SSMono
    public static final int DEVICE_MINI2_SSMONO = 44; // Mini，SSMono

    public static final int DEVICE_SLIM = 50; // LuckZip

    public static final int SCENES_DEFAULT = 0;

    // 默认距离
    public static final double LOST_MAX_DISTANCE_HIGH = 10.0f;

    public static final String DEPTH_TYPE_3X100 = "3X100";
    public static final String DEPTH_TYPE_X100 = "X100";
    public static final String DEPTH_TYPE_2XB40_X100 = "2XB40_X100";

    private String rosIp;
    private boolean enableCamera;
    private int deviceType;
    private int scenesType;
    private boolean enableFishEye;
    private boolean enableIR;
    private boolean enableSonar;
    private boolean openMaxLostDistance;
    private double avoidStopDetectionDistance;
    //视觉、对应使用的是mono摄像头
    private boolean enableVision;
    private String serverIp;
    private boolean enableKeepRight;
    private boolean enableMultiRobot;
    private int multiRobotServerPort;
    private long multiRobotClientId;

    private boolean mapWithRecord;
    private boolean recordMono;
    private boolean recordRgbd;
    private int lethalRadius;
    //相遇防碰撞
    private boolean enableCollisionAvoid;
    //单通防卡死
    private boolean enableStuckAvoid;
    //机器人Rgbd避障
    private boolean enableRgbdAvoidObs;
    //深度相机高精度模式（优化小物体避障）
    private boolean enableRgbdHighAccuracy;
    //是否使用Target定位
    private boolean enableTargetLocate;
    //是否使用巡线功能
    private boolean enableLineTracking;
    //防跌落
    private boolean enableCheckCliff;
    //地图障碍物覆盖
    private boolean enableConveringMapObstacles;
    //巡线路线宽度限制
    private boolean enablePathWidthLimit;

    //定位丢失后,盲走的最大距离的档位, 三个档位,取值Definition.LOST_MAX_DISTANCE.RANGE_HIGH 　
    private float positionLostMaxRange;

    //调整rgbd障碍物高度阈值偏差
    private double rgbd_obs_height_threshold_offset;

    //多机障碍物点功能，用于引导路径绕开障碍物
    private boolean enableGraphLeadingPlannerObsPoints = true;

    //enableGraphLeadingPlannerObsPoints的二级菜单——路径规划等待时长
    private float graphLeadingPlannerWaitTime;
    private float graphLeadingPlannerWaitTimeScale;

    //局部地图人腿检测等级，默认为0 [0： 为无膨胀, 1：仅非致命膨胀, 2：致命与非致命组合膨胀]
    private int localMapLegsInflationLevel;

    //是否增加机器人自转速度
    private boolean increaseRotationSelfSpeed;


    //是否无方向停靠
    private boolean isAdjustAngle;

    //是否启用RGBD上视开关
    private boolean slamEnableVox3DMapUp = true;
    //是否启用RGBD下视开关
    private boolean slamEnableVox3DMapDown = true;


    //是否启动靠近巡线功能
    private boolean enableCloseToRoadLine = true;
    //动态障碍物避障策略，默认关闭
    public boolean enableDynamicAvoidanceStrategy = false;

    //避免反序列化默认值失效
    public RoverConfig() {
    }

    public double getRgbd_obs_height_threshold_offset() {
        return rgbd_obs_height_threshold_offset;
    }

    public void setRgbd_obs_height_threshold_offset(double rgbd_obs_height_threshold_offset) {
        this.rgbd_obs_height_threshold_offset = rgbd_obs_height_threshold_offset;
    }

    public boolean isMapWithRecord() {
        return mapWithRecord;
    }

    public void setMapWithRecord(boolean mapWithRecord) {
        this.mapWithRecord = mapWithRecord;
    }

    public boolean isRecordMono() {
        return recordMono;
    }

    public void setRecordMono(boolean recordMono) {
        this.recordMono = recordMono;
    }

    public boolean isRecordRgbd() {
        return recordRgbd;
    }

    public void setRecordRgbd(boolean recordRgbd) {
        this.recordRgbd = recordRgbd;
    }

    public int getLethalRadius() {
        return lethalRadius;
    }

    public void setLethalRadius(int lethalRadius) {
        this.lethalRadius = lethalRadius;
    }

    public RoverConfig(String rosIp) {
        this.rosIp = rosIp;
    }

    public String getRosIp() {
        return rosIp;
    }

    public boolean isEnableCamera() {
        return enableCamera;
    }

    public int getDeviceType() {
        return deviceType;
    }

    public int getScenesType() {
        return scenesType;
    }

    public void setEnableCamera(boolean enableCamera) {
        this.enableCamera = enableCamera;
    }

    public void setDeviceType(int deviceType) {
        this.deviceType = deviceType;
    }

    public void setScenesType(int scenesType) {
        this.scenesType = scenesType;
    }

    public boolean isEnableFishEye() {
        return enableFishEye;
    }

    public void setEnableFishEye(boolean enableFishEye) {
        this.enableFishEye = enableFishEye;
    }
    public boolean isEnableRgbdHighAccuracy() {
        return enableRgbdHighAccuracy;
    }

    public void setEnableRgbdHighAccuracy(boolean enableRgbdHighAccuracy) {
        this.enableRgbdHighAccuracy = enableRgbdHighAccuracy;
    }

    public boolean isEnableIR() {
        return enableIR;
    }

    public void setEnableIR(boolean enableIR) {
        this.enableIR = enableIR;
    }

    public boolean isEnableSonar() {
        return enableSonar;
    }

    public void setEnableSonar(boolean enableSonar) {
        this.enableSonar = enableSonar;
    }

    public boolean isOpenMaxLostDistance() {
        return openMaxLostDistance;
    }

    public void setOpenMaxLostDistance(boolean openMaxLostDistance) {
        this.openMaxLostDistance = openMaxLostDistance;
    }

    public void setAvoidStopDetectionDistance(double avoidStopDetectionDistance) {
        this.avoidStopDetectionDistance = avoidStopDetectionDistance;
    }

    public double getAvoidStopDetectionDistance() {
        return avoidStopDetectionDistance;
    }

    public void setEnableVision(boolean enableVision) {
        this.enableVision = enableVision;
    }

    public boolean isEnableVision() {
        return enableVision;
    }

    public String getServerIp() {
        return serverIp;
    }

    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }

    public boolean isEnableKeepRight() {
        return enableKeepRight;
    }

    public void setEnableKeepRight(boolean enableKeepRight) {
        this.enableKeepRight = enableKeepRight;
    }

    public boolean isEnableMultiRobot() {
        return enableMultiRobot;
    }

    public void setEnableMultiRobot(boolean enableMultiRobot) {
        this.enableMultiRobot = enableMultiRobot;
    }

    public int getMultiRobotServerPort() {
        return multiRobotServerPort;
    }

    public void setMultiRobotServerPort(int multiRobotServerPort) {
        this.multiRobotServerPort = multiRobotServerPort;
    }

    public long getMultiRobotClientId() {
        return multiRobotClientId;
    }

    public void setMultiRobotClientId(long multiRobotClientId) {
        this.multiRobotClientId = multiRobotClientId;
    }

    public boolean isEnableCollisionAvoid() {
        return enableCollisionAvoid;
    }

    public void setEnableCollisionAvoid(boolean enableCollisionAvoid) {
        this.enableCollisionAvoid = enableCollisionAvoid;
    }

    public boolean isEnableStuckAvoid() {
        return enableStuckAvoid;
    }

    public void setEnableStuckAvoid(boolean enableStuckAvoid) {
        this.enableStuckAvoid = enableStuckAvoid;
    }

    public boolean isEnableRgbdAvoidObs() {
        //接口仅做旧版本RoverConfig转换用，写给导航默认为true
        return enableRgbdAvoidObs;
    }

    public void setEnableRgbdAvoidObs(boolean enableRgbdAvoidObs) {
        this.enableRgbdAvoidObs = enableRgbdAvoidObs;
    }

    public boolean isEnableTargetLocate() {
        return enableTargetLocate;
    }

    public void setEnableTargetLocate(boolean enableTargetLocate) {
        this.enableTargetLocate = enableTargetLocate;
    }

    public boolean isEnableLineTracking() {
        return enableLineTracking;
    }

    public void setEnableLineTracking(boolean enableLineTracking) {
        this.enableLineTracking = enableLineTracking;
        try {
            RobotSettings.setRobotInt(ApplicationWrapper.getContext(),
                    Definition.ROBOT_SETTINGS_NAVIGATION_LINE_TRACKING,
                    enableLineTracking ? 1 : 0);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isEnableCheckCliff() {
        return enableCheckCliff;
    }

    public void setEnableCheckCliff(boolean enableCheckCliff) {
        this.enableCheckCliff = enableCheckCliff;
    }

    public boolean isEnableConveringMapObstacles() {
        return enableConveringMapObstacles;
    }

    public void setEnableConveringMapObstacles(boolean enableConveringMapObstacles) {
        this.enableConveringMapObstacles = enableConveringMapObstacles;
    }

    public float getPositionLostMaxRange() {
        return positionLostMaxRange;
    }

    public void setPositionLostMaxRange(float positionLostMaxRange) {
        this.positionLostMaxRange = positionLostMaxRange;
    }

    public boolean isEnableGraphLeadingPlannerObsPoints() {
        return enableGraphLeadingPlannerObsPoints;
    }

    public void setEnableGraphLeadingPlannerObsPoints(boolean enable) {
        this.enableGraphLeadingPlannerObsPoints = enable;
    }

    public float getGraphLeadingPlannerWaitTime() {
        if (!enableGraphLeadingPlannerObsPoints) {
            return 0.0f;
        }
        if (Math.abs(graphLeadingPlannerWaitTime) < 0.001f) {
            return 1.0f;
        }
        return graphLeadingPlannerWaitTime;
    }

    public void setGraphLeadingPlannerWaitTime(float waitTime) {
        this.graphLeadingPlannerWaitTime = waitTime;
    }

    public float getGraphLeadingPlannerWaitTimeScale() {
        if (!enableGraphLeadingPlannerObsPoints) {
            return 0.0f;
        }
        return graphLeadingPlannerWaitTimeScale;
    }

    public void setGraphLeadingPlannerWaitTimeScale(float waitTimeScale) {
        this.graphLeadingPlannerWaitTimeScale = waitTimeScale;
    }

    public int getLocalMapLegsInflationLevel() {
        return localMapLegsInflationLevel;
    }

    public void setLocalMapLegsInflationLevel(int level) {
        this.localMapLegsInflationLevel = level;
    }

    public boolean isEnableIncreaseRotationSelfSpeed() {
        return increaseRotationSelfSpeed;
    }

    public void setEnableIncreaseRotationSelfSpeed(boolean enable) {
        this.increaseRotationSelfSpeed = enable;
    }

    public boolean getIsAdjustAngle() {
        return isAdjustAngle;
    }

    public void setISAdjustAngle(boolean enable) {
        this.isAdjustAngle = enable;
    }

    public boolean isSlamEnableVox3DMapUp() {
        return slamEnableVox3DMapUp;
    }

    public void setSlamEnableVox3DMapUp(boolean slamEnableVox3DMapUp) {
        this.slamEnableVox3DMapUp = slamEnableVox3DMapUp;
    }

    public boolean isSlamEnableVox3DMapDown() {
        return slamEnableVox3DMapDown;
    }

    public void setSlamEnableVox3DMapDown(boolean slamEnableVox3DMapDown) {
        this.slamEnableVox3DMapDown = slamEnableVox3DMapDown;
    }

    public boolean isNaviEnableCloseToRoadLine() {
        return enableCloseToRoadLine;
    }

    public void setNaviEnableCloseToRoadLine(boolean enableCloseToRoadLine) {
        this.enableCloseToRoadLine = enableCloseToRoadLine;
    }

    public boolean isEnableDynamicAvoidanceStrategy() {
        return enableDynamicAvoidanceStrategy;
    }

    public void setEnableDynamicAvoidanceStrategy(boolean enable) {
        this.enableDynamicAvoidanceStrategy = enable;
    }
    @Override
    public String toString() {
        return "RoverConfig{" +
                "rosIp='" + rosIp + '\'' +
                ", enableCamera=" + enableCamera +
                ", deviceType=" + deviceType +
                ", scenesType=" + scenesType +
                ", enableFishEye=" + enableFishEye +
                ", enableRgbdHighAccuracy=" + enableRgbdHighAccuracy +
                ", enableIR=" + enableIR + '\'' +
                ", enableSonar=" + enableSonar +
                ", openMaxLostDistance=" + openMaxLostDistance +
                ", avoidStopDetectionDistance=" + avoidStopDetectionDistance +
                ", enableVision=" + enableVision +
                ", serverIp='" + serverIp + '\'' +
                ", enableKeepRight=" + enableKeepRight +
                ", enableMultiRobot=" + enableMultiRobot +
                ", multiRobotServerPort=" + multiRobotServerPort +
                ", multiRobotClientId=" + multiRobotClientId +
                ", mapWithRecord=" + mapWithRecord +
                ", recordMono=" + recordMono + '\'' +
                ", recordRgbd=" + recordRgbd +
                ", lethalRadius=" + lethalRadius +
                ", enableCollisionAvoid=" + enableCollisionAvoid +
                ", enableStuckAvoid=" + enableStuckAvoid +
                ", enableRgbdAvoidObs=" + enableRgbdAvoidObs +
                ", enableTargetLocate=" + enableTargetLocate +
                ", enableLineTracking=" + enableLineTracking + '\'' +
                ", enableCheckCliff=" + enableCheckCliff +
                ", enableConveringMapObstacles=" + enableConveringMapObstacles +
                ", enablePathWidthLimit=" + enablePathWidthLimit +
                ", positionLostMaxRange=" + positionLostMaxRange +
                ", enableGraphLeadingPlannerObsPoints=" + enableGraphLeadingPlannerObsPoints +
                ", localMapLegsInflationLevel=" + localMapLegsInflationLevel +
                ", increaseRotationSelfSpeed=" + increaseRotationSelfSpeed +
                ", isAdjustAngle=" + isAdjustAngle +
                ", graphLeadingPlannerWaitTime=" + graphLeadingPlannerWaitTime +
                ", graphLeadingPlannerWaitTimeScale=" + graphLeadingPlannerWaitTimeScale +
                ", slamEnableVox3DMapUp=" + slamEnableVox3DMapUp +
                ", slamEnableVox3DMapDown=" + slamEnableVox3DMapDown +
                ", enableCloseToRoadLine=" + enableCloseToRoadLine +
                '}';
    }
}
