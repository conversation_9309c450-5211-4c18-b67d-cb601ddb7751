package com.ainirobot.navigationservice.chassisAbility.controller;

import static com.ainirobot.navigationservice.Defs.Def.ResultCode.FAIL_NO_REASON;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.SUCCESS;

import android.util.Log;

import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.beans.tk1.Velocity;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.x86.WheelControlX86;

import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 基于相对坐标系的运动控制
 */
public class BasicMotionProcessNew {
    private static final String TAG = "BasicMotionProcessNew";

    //线-加速度
    private static final double A_LINEAR_ACC = WheelControlX86.A_LINEAR_ACC;
    //线-减速度
    private static final double A_LINEAR_DEC = WheelControlX86.A_LINEAR_DEC;
    //角-加速度
    private static final double A_ANGULAR_ACC = WheelControlX86.A_ANGULAR_ACC;
    //角-减速度
    private static final double A_ANGULAR_DEC = WheelControlX86.A_ANGULAR_DEC;

    private IChassisClient mChassis;
    private volatile IChassisClient.ChassisResListener mChassisListener;
    private ScheduledExecutorService mExecutor = Executors.newScheduledThreadPool(1);
    private Future mFuture;
    private Future mFutureTimer;
    private volatile BasicMotionMode mMode = BasicMotionMode.LINEAR;

    /**
     * 开始运动时的位姿，用于计算运动过程中的位姿差量，以及判断是否到达目标点
     */
    private final AtomicReference<Pose> mStartRealPose;
    private volatile double mMoveOffset; //直线差量
    private volatile double mAngleOffset; //角度差量

    private volatile double mTargetDistance; //目标距离
    private volatile double mTargetSpeed; //目标速度
    private volatile double mStartDecDistance; //开始刹车距离
    private volatile double mDecTime; //开始刹车到速度降为0需要的时间
    private volatile State mMotionState = State.IDLE;

    /**
     * 过程中速度，大角度大速度时，需要适当提前降速
     * 初始值等于目标速度
     */
    private volatile double mProcessSpeed;

    enum State {
        IDLE,
        MOTIONING,
        INERTIAL_MOTION
    }

    enum BasicMotionMode {
        LINEAR,
        ANGULAR
    }

    static class Singleton {
        public static final BasicMotionProcessNew instance = new BasicMotionProcessNew();
    }

    public static BasicMotionProcessNew getInstance() {
        return Singleton.instance;
    }

    public void init(IChassisClient client) {
        this.mChassis = client;
    }

    private BasicMotionProcessNew() {
        mStartRealPose = new AtomicReference<>();
    }

    public void startLinearTask(double distance, double speed,
                                IChassisClient.ChassisResListener listener) {
        Log.i(TAG, "startLinearTask : distance=" + distance + ", speed=" + speed);
        mMode = BasicMotionMode.LINEAR;

        startMotionTask(distance, speed, listener);
    }

    public void startAngularTask(double radians, double speed,
                                 IChassisClient.ChassisResListener listener) {
        Log.i(TAG, "startAngularTask : radians=" + radians + "(" + Math.toDegrees(radians) + ")"
                + ", speed=" + speed);
        mMode = BasicMotionMode.ANGULAR;

        startMotionTask(radians, speed, listener);
    }

    private synchronized void startMotionTask(double distance, double speed,
                                              IChassisClient.ChassisResListener listener) {
        Log.i(TAG, "startMotionTask : -------->Start<--------");
        Log.i(TAG, "startMotionTask : mMode=" + mMode + ", distance=" + distance
                + ", speed=" + speed);

        Pose startRealtimePose = mChassis.getRealtimePose();
        if(startRealtimePose == null) {
            Log.e(TAG, "startMotionTask : startRealtimePose is null");
            if(listener != null) {
                listener.onResponse(false, FAIL_NO_REASON, "Start pose is null");
            }
            return;
        }
        Log.i(TAG, "startMotionTask : startRealtimePose=" + startRealtimePose.toString());

        mChassisListener = listener;

        mMotionState = State.MOTIONING;
        mMoveOffset = 0;
        mAngleOffset = 0;
        mStartRealPose.set(startRealtimePose);
        mProcessSpeed = speed;
        calculateStartDecDistance(distance, speed);
        startMotionProcessMonitor();
    }

    /**
     * 运动过程监听
     */
    private synchronized void startMotionProcessMonitor() {
        Log.i(TAG, "startMotionProcessMonitor:mStartDecDistance=" + mStartDecDistance
                + ", mDecTime=" + mDecTime);
        cancelMotionProcessMonitor();
        cancelDecTimer();
        mFuture = mExecutor.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                double distanceOffset = (mMode == BasicMotionMode.LINEAR
                        ? mMoveOffset
                        : mAngleOffset);
                if (distanceOffset >= mStartDecDistance) {
                    Log.i(TAG, "Start dec : distanceOffset=" + distanceOffset
                            + ", mStartDecDistance=" + mStartDecDistance + ", mDecTime=" + mDecTime);
                    cancelMotionProcessMonitor();
                    mChassis.motion(0, 0,0, false);//不减速，直接停止
                    startDecTimer();
                } else {
                    //目标角度大于90度/s时
                    //目标速度大于50度/s，最后360度速度降为50
                    //目标速度大于30度/s，最后180度速度降为30
                    //目标速度大于20度/s，最后90度速度降为20
                    if(mMode == BasicMotionMode.ANGULAR) {
                        //剩余距离
                        double distance = mTargetDistance - distanceOffset;
                        if (mTargetSpeed > Math.toRadians(50) && (distance <= Math.toRadians(360) && distance > Math.toRadians(180))) {
                            //过程速度等于初始速度，降速到50
                            if(mProcessSpeed == mTargetSpeed) {
                                Log.i(TAG, "startMotionProcessMonitor : mTargetSpeed=" + mTargetSpeed
                                        + ", distance=" + distance + ".Set speed to 50");
                                mChassis.motion(Math.toRadians(50), 0, 0, true);
                                mProcessSpeed = Math.toRadians(50);
                            }
                        } else if(mTargetSpeed > Math.toRadians(30) && (distance <= Math.toRadians(180) && distance > Math.toRadians(90))) {
                            if(mProcessSpeed == Math.toRadians(50)) {
                                Log.i(TAG, "startMotionProcessMonitor : mTargetSpeed=" + mTargetSpeed
                                        + ", distance=" + distance + ".Set speed to 30");
                                mChassis.motion(Math.toRadians(30), 0, 0, true);
                                mProcessSpeed = Math.toRadians(30);
                            }
                        }
                    }
                }
            }
        }, 0, 20, TimeUnit.MILLISECONDS);
    }

    private synchronized void cancelMotionProcessMonitor() {
        if (mFuture == null) {
            return;
        }
        if (!mFuture.isCancelled() && !mFuture.isDone()) {
            mFuture.cancel(true);
            mFuture = null;
        }
    }

    /**
     * 等待减速完成
     */
    private void startDecTimer() {
        Log.i(TAG, "startDecTimer mDecTime=" + mDecTime);
        mMotionState = State.INERTIAL_MOTION;
        cancelDecTimer();
        if (mDecTime > 0) {
            mFutureTimer = mExecutor.schedule(new Runnable() {
                @Override
                public void run() {
                    Log.i(TAG, "startDecTimer run : mDecTime=" + mDecTime
                            + ", mMotionState=" + mMotionState);
                    if (mMotionState == State.INERTIAL_MOTION) {
                        motionSuccess();
                    }
                }
            }, (long) mDecTime, TimeUnit.MILLISECONDS);
        } else {
            motionSuccess();
        }
    }

    private void cancelDecTimer() {
        if (mFutureTimer == null) {
            return;
        }
        if (!mFutureTimer.isCancelled() && !mFutureTimer.isDone()) {
            mFutureTimer.cancel(true);
            mFutureTimer = null;
        }
    }

    /**
     * 计算开始刹车的节点，机器所走过的距离
     *
     * @param distance 运动距离
     * @param speed    最大速度
     */
    private void calculateStartDecDistance(double distance, double speed) {
        mTargetDistance = distance;
        mTargetSpeed = Math.abs(speed);

        //不减速，直接停止：减速距离等于目标距离减去3度/5cm，减速时间默认延迟200ms
        if (mMode == BasicMotionMode.LINEAR) {
            mStartDecDistance = mTargetDistance - 0.05;
        } else if (mMode == BasicMotionMode.ANGULAR) {
            //速度小于等于20度/s，减速距离等于目标距离减去3度；速度大于20度/s，减速距离等于目标距离减去5度；
            //底盘坐标上报频率为5HZ，速度越快偏差越大，所以减速距离需要适当增加
            if(mTargetSpeed <= Math.toRadians(20))
                mStartDecDistance = mTargetDistance - Math.toRadians(3);
            else if(mTargetSpeed > Math.toRadians(20))
                mStartDecDistance = mTargetDistance - Math.toRadians(5);
        }
        mDecTime = 200;
        Log.i(TAG, "Start Dec Distance : mStartDecDistance=" + mStartDecDistance
                + ", mDecTime=" + mDecTime);
    }

    /**
     * 实时计算直线和角度差量
     *
     * @param pose 实时位姿
     */
    public void handleRealtimePose(Pose pose) {
        if(mMotionState == State.IDLE) {
            return;
        }
        if (pose == null) {
            return;
        }
        Pose startPose = mStartRealPose.get();
        if (startPose == null) {
            return;
        }
//        LogUtils.printLog(LogUtils.TYPE_ODOM_LINEAR,
//                TAG,
//                "handleRealtimePose," +
//                        "当前Pose: x=" + pose.getX() + " y=" + pose.getY() + " t=" + pose.getTheta() +
//                        "起始Pose: x=" + startPose.getX() + " y=" + startPose.getY() + " t=" + startPose.getTheta(),
//                1000);
        Log.i(TAG, "handleRealtimePose" +
                ", 起始Pose: x=" + startPose.getX() + " y=" + startPose.getY() + " t=" + startPose.getTheta() +
                ", 当前Pose: x=" + pose.getX() + " y=" + pose.getY() + " t=" + pose.getTheta());

        //计算直线差量

        //计算角度差量
        double angleDifference = calculateAngleDifference(startPose.getTheta(), pose.getTheta());
        //角度差量累计，取差量值的绝对值，因为角度差值可能为负值（左转差量为正值，右转差量为负值）
        mAngleOffset += Math.abs(angleDifference);
        //更新起始位姿
        mStartRealPose.set(pose);
        Log.i(TAG, "handleRealtimePose : mAngleOffset=" + mAngleOffset + ", angleDifference=" + angleDifference);
    }

    /**
     * 计算角度差值
     *
     * @param t1 起始角度
     * @param t2 结束角度
     * @return 角度差值
     */
    public static double calculateAngleDifference(double t1, double t2) {
        // 计算初始差值
        double deltaTheta = t2 - t1;

        // 将差值规范化到 [-π, π) 范围内
        while (deltaTheta >= Math.PI) {
            deltaTheta -= 2 * Math.PI;
        }
        while (deltaTheta < -Math.PI) {
            deltaTheta += 2 * Math.PI;
        }

        return deltaTheta;
    }

    private void motionSuccess() {
        Log.i(TAG, "motionSuccess :");

        //打印结束时的位姿，差量值，速度，对比是否到达目标点
        Pose endPose = mChassis.getRealtimePose();
        Log.i(TAG, "motionSuccess : End pose=" + (endPose != null ? endPose.toString() : "null"));
        Pose startPose = mStartRealPose.get();
        if (startPose != null && endPose != null) {
            double moveOffset = Math.sqrt(Math.pow(endPose.getX() - startPose.getX(), 2)
                    + Math.pow(endPose.getY() - startPose.getY(), 2));
            double angleOffset = endPose.getTheta() - startPose.getTheta();
            Log.i(TAG, "motionSuccess : moveOffset=" + moveOffset + ", angleOffset=" + angleOffset);
        }
        Velocity velocity = mChassis.getRealtimeVelocity();
        boolean isLinearStill = WheelControlX86.getInstance().isLinearStill(velocity);
        boolean isAngularStill = WheelControlX86.getInstance().isAngularStill(velocity);
        Log.i(TAG, "motionSuccess : Realtime velocity=" + (velocity != null ? velocity.toString() : "null") +
                ", isLinearStill=" + isLinearStill + ", isAngularStill=" + isAngularStill);

        mMotionState = State.IDLE;
        if (mChassisListener != null) {
            mChassisListener.onResponse(true, SUCCESS, "Motion success");
            mChassisListener = null;
        }
        mChassis.updateMotionAvoidState(false);
        Log.i(TAG, "motionSuccess : -------->End<--------");
    }

    public synchronized void stopMotionTask(boolean status, int resultCode, Object result) {
        Log.i(TAG, "stopMotionTask : status=" + status + ", resultCode=" + resultCode
                + ", result=" + result + ", mMotionState=" + mMotionState);
        if (mMotionState == State.IDLE) {
            Log.e(TAG, "stopMotionTask : Is not running error");
            return;
        }

        cancelDecTimer();
        cancelMotionProcessMonitor();
        mMotionState = State.IDLE;
        if (mChassisListener != null) {
            mChassisListener.onResponse(status, resultCode, result);
            mChassisListener = null;
        }
        Log.i(TAG, "motionSuccess : -------->End<--------");
    }

}
