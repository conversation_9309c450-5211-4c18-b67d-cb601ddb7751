/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file elinearcept in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either elinearpress or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.tk1;

public class NavAcceleration {

    /**
     * 线加速度
     */
    private double linearAcc;
    /**
     * 角加速度
     */
    private double angularAcc;

    public NavAcceleration() {

    }

    public NavAcceleration(double linearAcc, double angularAcc) {
        this.linearAcc = linearAcc;
        this.angularAcc = angularAcc;
    }

    public double getLinearAcc() {
        return linearAcc;
    }

    public void setLinearAcc(double linearAcc) {
        this.linearAcc = linearAcc;
    }

    public double getAngularAcc() {
        return angularAcc;
    }

    public void setAngularAcc(double angularAcc) {
        this.angularAcc = angularAcc;
    }

    @Override
    public String toString() {
        return "NavAcceleration{" +
                "linearAcc=" + linearAcc +
                ", angularAcc=" + angularAcc +
                '}';
    }
}
