package com.ainirobot.navigationservice.utils;

import android.util.Log;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

public class NetUtils {
    private final static String TAG = TAGPRE + NetUtils.class.getSimpleName();
    private static final String PING = "ping -c 3 -w 10 %s";
    public static boolean ping(String address) {
        long startTime = System.currentTimeMillis();

        address = address.replace("http://", "");
        String command = String.format(PING, address);
        Log.d(TAG, command);
        String result = null;
        result = exec(command);
        Log.e(TAG, "" + result + "  time=" + (System.currentTimeMillis() - startTime));
        return result != null;
    }

    private static String exec(String command){

        String result;
        BufferedReader reader = null;
        try {
            Process process = Runtime.getRuntime().exec(command);
            reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream()));

            int status = process.waitFor();
            if (status != 0) {
                reader.close();
                return null;
            }

            String line;
            StringBuilder output = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                if (output.length() > 0) {
                    output.append("\n");
                }
                output.append(line);
            }
            result = output.toString();
        }
        catch (IOException e) {
            e.printStackTrace();
            result = null;
        }
        catch (InterruptedException e) {
            e.printStackTrace();
            result = null;
        }
        finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return result;

    }
}
