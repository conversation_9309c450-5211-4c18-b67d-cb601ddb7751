/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.tk1;

import com.ainirobot.coreservice.client.Definition;

import java.io.DataOutput;
import java.io.IOException;

import ninjia.android.proto.ChassisPacketProtoWrapper;
import ninjia.android.proto.CommonProtoWrapper;
import ninjia.android.proto.UpdateGlobalMapProtoWrapper;

/**
 * This expresses a position and orientation on a 2D manifold.
 */
public class Pose extends Message {

    /**
     * 正常区域，可以到
     */
    public static final int MAP_STATUS_NORMAL_AREA = ChassisPacketProtoWrapper.NaviPoseProto.PoseType.FREE_VALUE;
    /**
     * 有障碍物但机器可以到达，但设点时不可设置，因为现在点位距离障碍物的距离可以设置，需要根据设置的距离做判断
     */
    public static final int MAP_STATUS_OBSTACLE_AREA = ChassisPacketProtoWrapper.NaviPoseProto.PoseType.OBSTACLE_VALUE;

    /**
     * 地图外，不可以到
     */
    public static final int MAP_STATUS_OUTSIDE_AREA = ChassisPacketProtoWrapper.NaviPoseProto.PoseType.OUTSIDE_VALUE;
    /**
     * 点位被障碍物包围，不可以到达
     * 设点时，可以MAP_STATUS_OBSTACLE_AREA统一提示，都是距离障碍物过近
     */
    public static final int MAP_STATUS_SURROUNDED_AREA = ChassisPacketProtoWrapper.NaviPoseProto.PoseType.NO_ESCAPE_VALUE;

    private float x, y, theta;
    private String id;
    private String name;
    private int postype;
    private int status;
    /**
     * 是否忽略障碍物，（现在没用了，不用考虑）
     * 忽略停靠点位附近的RGBD障碍物,专门用于想要塞进某些停靠位置使用的
     */
    private boolean ignoreDistance = false;

    private boolean noDirectionalParking = false;

    /**
     * 距离障碍物的距离，单位是cm
     */
    private int safeDistance = Definition.POSE_SAFE_DISTANCE_DEFAULT;
    private double obsDistance = 0.1; // 当前机器与最近障碍物的实时距离,默认0.1m
    private int typeId; // 特殊点位类型
    private int priority; // 特殊点位对应优先级

    public Pose() {
        this(Message.MSG_TYPE_GOAL);
    }

    public Pose(int type, float x, float y, float theta) {
        super(type);
        this.x = x;
        this.y = y;
        this.theta = theta;
    }

    public Pose(float x, float y, float theta) {
        this(Message.MSG_TYPE_GOAL, x, y, theta);
    }

    public Pose(float x, float y, float theta, int typeId, int priority) {
        this(Message.MSG_TYPE_GOAL, x, y, theta, typeId, priority);
    }

    public Pose(final float x, final float y, final float theta,
                final int status) {
        this(Message.MSG_TYPE_GOAL, x, y, theta,status);
    }

    public Pose(final float x, final float y, final float theta,
                final int status, final boolean ignoreDistance,final boolean noDirectionalParking, final int safeDistance) {
        this(Message.MSG_TYPE_GOAL, x, y, theta,status, ignoreDistance,noDirectionalParking, safeDistance);
    }

    public Pose(final int messageType, final float x, final float y, final float theta,
                final int status) {
        super(messageType);
        this.x = x;
        this.y = y;
        this.theta = theta;
        this.status = status;
    }

    public Pose(final int messageType, final float x, final float y, final float theta,
                final int status, final boolean ignoreDistance,final boolean noDirectionalParking, final int safeDistance) {
        super(messageType);
        this.x = x;
        this.y = y;
        this.theta = theta;
        this.status = status;
        this.noDirectionalParking=noDirectionalParking;
        this.ignoreDistance = ignoreDistance;
        this.safeDistance = safeDistance;
    }

    public Pose(int type, float x, float y, float theta, int typeId, int priority) {
        super(type);
        this.x = x;
        this.y = y;
        this.theta = theta;
        this.typeId = typeId;
        this.priority = priority;
    }

    public Pose(int type) {
        super(type);
    }

    public float getX() {
        return x;
    }

    public float getY() {
        return y;
    }

    public void setTheta(float theta) {
        this.theta = theta;
    }

    public float getTheta() {
        return theta;
    }

    public int getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getPostype() {
        return postype;
    }

    public void setPostype(int postype) {
        this.postype = postype;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setX(float x) {
        this.x = x;
    }

    public void setY(float y) {
        this.y = y;
    }

    public boolean getIgnoreDistance() {
        return ignoreDistance;
    }

    public void setIgnoreDistance(boolean ignoreDistance) {
        this.ignoreDistance = ignoreDistance;
    }

    public boolean getNoDirectionalParking() {
        return noDirectionalParking;
    }

    public void setNoDirectionalParking(boolean noDirectionalParking) {
        this.noDirectionalParking = noDirectionalParking;
    }

    public int getSafeDistance() {
        return safeDistance;
    }

    public void setSafeDistance(int safeDistance) {
        this.safeDistance = safeDistance;
    }

    public double getObsDistance() {
        return obsDistance;
    }

    public void setObsDistance(double obsDistance) {
        this.obsDistance = obsDistance;
    }

    public void setTypeId(int typeId) {
        this.typeId = typeId;
    }

    public int getTypeId() {
        return typeId;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public int getPriority() {
        return priority;
    }

    @Override
    public void readData(ReverseInputStream in) throws IOException {
        x = in.readFloat();
        y = in.readFloat();
        theta = in.readFloat();
        status = in.readInt();
        obsDistance = in.readDouble();
    }

    @Override
    public void writeTo(DataOutput out) throws IOException {
        out.writeFloat(x);
        out.writeFloat(y);
        out.writeFloat(theta);
        out.writeInt(status);
        out.writeDouble(obsDistance);
    }

    @Override
    public String toString() {
        return "x=" + x + "  y=" + y + " theta=" + theta + " name=" + name+" posetype=" + postype
                + "  status=" + status + "  time=" + getTime() + " messageType=" + getMessageType()
                + "  ignoreDistance=" + ignoreDistance + "  safeDistance=" + safeDistance
                + "  poseObsDistance="+ obsDistance + " typeId=" + typeId + " priority=" + priority;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null || !(obj instanceof Pose)) {
            return false;
        }

        Pose pose = (Pose) obj;
        return this.x == pose.getX() && this.y == pose.getY() && this.theta == pose.getTheta() && this.status == pose.getStatus();
    }

    public double getDistance(Pose pose) {
        if (pose == null) {
            return Double.MAX_VALUE;
        }
        double destX = this.getX();
        double destY = this.getY();
        double x = pose.getX();
        double y = pose.getY();
        return Math.sqrt(Math.pow((x - destX), 2) + Math.pow((y - destY), 2));
    }
}
