package com.ainirobot.navigationservice.Defs;

public class NavigationStatus {

    public static final int BASE_NAVIGATION_STATUS = 20010000;
    public static final int STATUS_NAVI_EVENT_NORMAL = BASE_NAVIGATION_STATUS +1;
    public static final int STATUS_NAVI_EVENT_START = BASE_NAVIGATION_STATUS +2;
    public static final int STATUS_NAVI_EVENT_GOAL_INVALID = BASE_NAVIGATION_STATUS +3;
    public static final int STATUS_NAVI_EVENT_GLOBAL_PATH_FAILED = BASE_NAVIGATION_STATUS +4;
    public static final int STATUS_NAVI_EVENT_LOCAL_GOAL_INVAILD = BASE_NAVIGATION_STATUS +5;
    public static final int STATUS_NAVI_EVENT_LOCAL_PATH_FAILED = BASE_NAVIGATION_STATUS +6;
    public static final int STATUS_NAVI_EVENT_ESTIMATE_LOST = BASE_NAVIGATION_STATUS +7;
    public static final int STATUS_NAVI_EVENT_ESTIMATE_RECOVERY = BASE_NAVIGATION_STATUS +8;
    public static final int STATUS_NAVI_EVENT_PATH_SUCCESS = BASE_NAVIGATION_STATUS +9;
    public static final int STATUS_NAVI_EVENT_OBSTACLES_AVOID = BASE_NAVIGATION_STATUS +10;
    public static final int STATUS_NAVI_EVENT_GOAL_IS_DANGEROUS = BASE_NAVIGATION_STATUS +11;
    public static final int STATUS_NAVI_EVENT_ROBOT_IS_OUT = BASE_NAVIGATION_STATUS +12;
    public static final int STATUS_NAVI_EVENT_GRAPH_SEARCHER_FAILED_ERROR = BASE_NAVIGATION_STATUS +13;
    /**
     * 没有绘制巡线
     */
    public static final int STATUS_NAVI_EVENT_GRAPH_ROAD_INVALID_ERROR = BASE_NAVIGATION_STATUS +14;
    public static final int STATUS_NAVI_EVENT_ROBOT_IS_OUT_OF_ROAD_ERROR = BASE_NAVIGATION_STATUS +15;
    public static final int STATUS_NAVI_EVENT_GOAL_IS_OUT_OF_ROAD_ERROR = BASE_NAVIGATION_STATUS +16;
    public static final int STATUS_NAVI_EVENT_MULTI_ROBOT_AVOID_WAITING = BASE_NAVIGATION_STATUS +17;
    public static final int STATUS_NAVI_EVENT_MULTI_ROBOT_AVOID_WAITING_END = BASE_NAVIGATION_STATUS +18;
    public static final int STATUS_NAVI_EVENT_GO_STRAIGHT = BASE_NAVIGATION_STATUS +19;
    public static final int STATUS_NAVI_EVENT_TURN_LEFT = BASE_NAVIGATION_STATUS +20;
    public static final int STATUS_NAVI_EVENT_TURN_RIGHT = BASE_NAVIGATION_STATUS +21;
    public static final int STATUS_NAVI_EVENT_PATH_WAITING = BASE_NAVIGATION_STATUS +22;
    /**
     * 机器人长时间无位移状态上报
     */
    public static final int STATUS_NAVI_NOT_MOVING_LONG_TIME = BASE_NAVIGATION_STATUS +23;
    /**
     * 轮子打滑
     */
    public static final int STATUS_NAVI_EVENT_WHEEL_SLIP = BASE_NAVIGATION_STATUS +24;
    /**
     * 多机打开时，巡线未打开
     */
    public static final int STATUS_NAVI_LINE_TRACKING_CLOSED_IN_MULTI_MODE = BASE_NAVIGATION_STATUS +25;
    /**
     * 当地图设定的允许机器人最大直径低于当前机器人本地设置的直径时
     */
    public static final int STATUS_NAVI_GRAPH_ROAD_PARAM_ERROR = BASE_NAVIGATION_STATUS +26;

}
