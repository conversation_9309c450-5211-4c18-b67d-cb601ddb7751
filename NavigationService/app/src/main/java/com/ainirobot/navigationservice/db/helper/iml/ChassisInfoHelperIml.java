package com.ainirobot.navigationservice.db.helper.iml;

import android.content.Context;

import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.waiter.MultiRobotConfigBean;
import com.ainirobot.navigationservice.db.entity.ChassisInfo;

import java.util.List;

public interface ChassisInfoHelperIml extends BaseHelper<ChassisInfo> {
    String getRoverConfig();

    String getIpNavigation();

    String getMultiRobotConfig();

    String getIpSdkRos();

    void initChassisInfoData(Context context, List<ChassisInfo> chassisInfos);

    boolean updateIpNavigation(String ipNavigation);

    boolean updateIpSdkRos(String ip);

    boolean updateMultiRobotConfig(RoverConfig roverConfig);

    boolean updateMultiRobotConfig(MultiRobotConfigBean multiRobotConfigBean);
}
