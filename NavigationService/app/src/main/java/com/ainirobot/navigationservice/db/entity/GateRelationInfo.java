package com.ainirobot.navigationservice.db.entity;

import io.objectbox.annotation.Entity;
import io.objectbox.annotation.Id;

/**
 * @version V11.2
 * @date 2025/3/12 11:20
 */
@Entity
public class GateRelationInfo {

    @Id
    public long id;

    /**
     * 闸机id
     */
    private String gateId;

    /**
     * 闸机线id
     */
    private int gateLineId;


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getGateId() {
        return gateId;
    }

    public void setGateId(String gateId) {
        this.gateId = gateId;
    }

    public int getGateLineId() {
        return gateLineId;
    }

    public void setGateLineId(int gateLineId) {
        this.gateLineId = gateLineId;
    }

    @Override
    public String toString() {
        return "GateRelationInfo{" + "id='" + id + "gateId='" + gateId + ", gateLineId='" + gateLineId +'}';
    }
}
