package com.ainirobot.navigationservice.chassisAbility.controller;

import android.util.Log;

import com.ainirobot.navigationservice.beans.tk1.Laser;
import com.ainirobot.navigationservice.beans.tk1.Velocity;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.utils.LogUtils;
import com.ainirobot.navigationservice.utils.RLog;

import java.util.ArrayList;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Created by Orion on 2019/12/19.
 * 静态避停，用于人体跟随
 */
public class StaticStoppingPolicy extends BaseAvoidPolicy {
    private static final String TAG = "StaticStoppingPolicy";

    private volatile double mMinObstacleDistance = Double.MAX_VALUE;
    private AtomicReference<Laser> mDangerousLaser = new AtomicReference<>();
    private volatile double mSafeDistance = DEFAULT_SAFE_DISTANCE;
    private ScheduledExecutorService mExecutor = Executors.newScheduledThreadPool(1);
    private Future mFuture;
    private IChassisClient mChassis;

    /**
     * 障碍物水平偏移容错距离
     */
    private static final double SAFE_OFFSET = 0.03f;

    /**
     * 默认障碍物安全距离
     */
    private static final double DEFAULT_SAFE_DISTANCE = 0.6f;

    /**
     * 默认障碍物危险距离,mini底盘边缘障碍物上报距离大概为0.22
     */
    private static final double DEFAULT_DANGEROUS_DISTANCE = 0.35f;

    /**
     * 障碍物状态消除超时时间
     */
    private static final long OBSTACLES_CHECK_TIME = (long) (0.5 * 1000);

    public StaticStoppingPolicy(IChassisClient client) {
        super();
        init(client);
    }

    private void init(IChassisClient client) {
        this.mChassis = client;
        startStaticAvoidTask();
    }

    private void startStaticAvoidTask() {
        if (mFuture == null) {
            mFuture = mExecutor.scheduleAtFixedRate(new Runnable() {
                @Override
                public void run() {
                    try {
                        handleObstaclesInfo();
                    } catch (Exception e) {
                        Log.e(TAG, "startStaticAvoidTask Exception : " + e.getMessage());
                    }
                }
            }, 0, 100, TimeUnit.MILLISECONDS);
        }
    }

    public void handleObstaclesInfo() {
        ArrayList<Laser> data = (ArrayList<Laser>) mChassis.getLasersData();
        if (data == null || data.size() <= 0) {
            Log.i(TAG, "handleObstaclesInfo : Lasers data null Error");
            return;
        }
        Velocity velocity = mChassis.getRealtimeVelocity();
        if (velocity == null) {
            Log.i(TAG, "dynamicPathPlaning : Velocity null Error");
            return;
        }
        RLog.v(TAG, "handleObstaclesInfo : Lasers size=" + data.size());

        int score = obstacleScoreAdj(data, velocity.getX());

        LogUtils.printLog(LogUtils.TYPE_LASER, TAG,
                "handleObstaclesInfo : "
                        + ", Pre state=" + mHasObstacle
                        + ", mSafeDistance=" + mSafeDistance
                        + ", mMinObstacleDistance=" + mMinObstacleDistance
                        + ", score=" + score,
                1000);

        onScoreResult(score);
    }

    private int obstacleScoreAdj(ArrayList<Laser> lasers, double linearSpeed) {
        if (lasers == null || lasers.size() <= 0) {
            return OBSTACLES_SCORE_SAFE;
        }

        double minDistance = Double.MAX_VALUE;
        Laser dangerousLaser = null;
        for (Laser laser : lasers) {
            double laserX = Math.sin(Math.abs(laser.getAngle())) * laser.getDistance();
            if (laserX <= ROBOT_RADIUS + SAFE_OFFSET) {
                double laserY = Math.cos(Math.abs(laser.getAngle())) * laser.getDistance();
//                minDistance = Math.min(minDistance, laserY);
                if (laserY < minDistance) {
                    minDistance = laserY;
                    dangerousLaser = laser;
                }
            }
        }

        if ((linearSpeed < 0.8
                && minDistance <= Math.min(DEFAULT_DANGEROUS_DISTANCE_LOW_SPEED, mSafeDistance))
                || (linearSpeed >= 0.8
                && minDistance <= Math.min(DEFAULT_DANGEROUS_DISTANCE_FAST_SPEED, mSafeDistance))) {
            return OBSTACLES_SCORE_PERILOUS;
        } else if (minDistance <= mSafeDistance) {
            mMinObstacleDistance = minDistance;
            mDangerousLaser.set(dangerousLaser);
            return OBSTACLES_SCORE_DANGEROUS;
        }

        mMinObstacleDistance = Double.MAX_VALUE;
        mDangerousLaser.set(null);
        return OBSTACLES_SCORE_SAFE;
    }

    public boolean getState(double distance) {
        Log.d(TAG, "getAvoidState : distance=" + distance
                + ", mMinObstacleDistance=" + mMinObstacleDistance
                + ", mHasObstacle=" + mHasObstacle);
        if (distance < mMinObstacleDistance) {
            return false;
        }
        return mHasObstacle;
    }

    public double getMinObstacleDistance() {
        return mMinObstacleDistance;
    }

    public Laser getDangerousLaser() {
        return mDangerousLaser.get();
    }

    public double getSafeDistance() {
        return mSafeDistance;
    }

    public void setSafeDistance(double distance) {
        Log.d(TAG, "setSafeDistance : new distance=" + distance
                + ", old distance=" + mSafeDistance);
        this.mSafeDistance = distance;
    }

    public void resetSafeDistance() {
        Log.d(TAG, "resetSafeDistance : old distance=" + mSafeDistance);
        this.mSafeDistance = DEFAULT_SAFE_DISTANCE;
    }

    public static boolean hasObstacle(ArrayList<Laser> lasers, double radio, double startAngle, double endAngle, double distance) {
        if (lasers == null || lasers.size() <= 0) {
            Log.d(TAG, "hasObstacle: lasers null!");
            return false;
        }
        Log.d(TAG, "hasObstacle: radio=" + radio + " startAngle=" + startAngle +
                " endAngle=" + endAngle + " distance=" + distance + " lasersSize=" + lasers.size());
        double arcStartAngle = startAngle * Math.PI / 180;
        double arcEndAngle = endAngle * Math.PI / 180;
        double limitX = (radio + SAFE_OFFSET);
        double limitY = distance;
        Log.d(TAG, "hasObstacle: arcStartAngle=" + arcStartAngle +
                " arcEndAngle=" + arcEndAngle + " limitX=" + limitX + " limitY=" + limitY);
        for (Laser laser : lasers) {
            double aimAngle = laser.getAngle();
            if (!(aimAngle >= startAngle && aimAngle <= endAngle)) {
                continue;
            }
            double laserX = Math.sin(Math.abs(laser.getAngle())) * laser.getDistance();
            double laserY = Math.cos(Math.abs(laser.getAngle())) * laser.getDistance();
//            Log.d(TAG, "hasObstacle: laser=" + laser + " <" + laserX + ", " + laserY + ">");
            if (laserX <= limitX) {
                if (laserY < limitY) {
                    Log.d(TAG, "hasObstacle: Has obstacle: laser=" + laser + " <" + laserX + ", " + laserY + ">");
                    return true;
                }
            }
        }
        Log.d(TAG, "hasObstacle: No obstacle!");
        return false;
    }

}
