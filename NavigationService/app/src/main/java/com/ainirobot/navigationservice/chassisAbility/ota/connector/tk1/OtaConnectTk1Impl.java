package com.ainirobot.navigationservice.chassisAbility.ota.connector.tk1;

import com.ainirobot.navigationservice.chassisAbility.ota.connector.IOtaConnect;

public class OtaConnectTk1Impl implements IOtaConnect {
    private CnnResListener cnnResListener;
    private CnnEventListener cnnEventListener;
    private CnnOnConnectListener cnnListener;
    private ConnectServer mOtaCnnServer;

    public OtaConnectTk1Impl() {
        mOtaCnnServer = new ConnectServer(this);
    }

    @Override
    public void init() {
        if (mOtaCnnServer != null) {
            mOtaCnnServer.start();
        } else {
            throw new RuntimeException("OTA ConnectServer is null");
        }
    }

    @Override
    public boolean request(String message) {
        mOtaCnnServer.sendMessage(message);
        return true;
    }

    @Override
    public void registerResponseListener(CnnResListener listener) {
        cnnResListener = listener;
    }

    @Override
    public void registerConnectListener(CnnOnConnectListener listener) {
        cnnListener = listener;
    }

    @Override
    public void registerEventListener(CnnEventListener listener) {
        cnnEventListener = listener;
    }


    public void onResponse(String msg) {
        if (cnnResListener != null) {
            cnnResListener.onResponse(msg);
        }
    }

    public void onConnectIn() {
        cnnListener.onConnected();
    }

    public void onDisConnectIn() {
        cnnListener.onDisconnected();
    }
}
