/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.tk1;

public class Event extends BaseEvent{
    public static final int UNUSE_COMPACT = 0;
    public static final int GOAL_REACHED = 1;
    public static final int GOAL_INVALID = 2;
    public static final int PATH_FAILED = 3;
    public static final int ESTIMATE_SUCCESS = 4;
    public static final int ESTIMATE_FAILED = 5;
    public static final int LOCAL_GOAL_INVAILD = 6;
    public static final int LOCAL_PATH_FAILED = 7;
    public static final int ESTIMATE_LOST = 8;
    public static final int ESTIMATE_RECOVERY = 9;
    public static final int PATH_SUCCESS = 10;
    public static final int EXPECTED_TIME_FOR_VISION = 11;
    public static final int OBSTACLES_AVOID = 12;
    public static final int ERROR_LOG = 13;
    public static final int PACK_LOG_END = 14;
    public static final int TAKE_SNAPSHOT_END = 15;
    public static final int CREATE_MAP_SIZE = 16;
    public static final int PROCESS_CREATE_MAP = 17;
    public static final int GOAL_IS_DANGEROUS = 18;
    public static final int ROBOT_IS_OUT = 19;
    public static final int ROBOT_BEING_PUSHED = 20;
    public static final int DETECT_PEOPLE = 21;
    public static final int DETECT_SHAKE = 22; //过坎上报
    public static final int PREVENT_FALL = 23; //防跌落上报
    public static final int FORCE_SNAPSHOT = 24;
    public static final int GOTO_CHARGE_SUCCESS = 25;
    public static final int GOTO_CHARGE_FAILED  = 26;
    public static final int MULTI_ROBOT_ACTIVE = 27;
    public static final int MULTI_ROBOT_INACTIVE = 28;
    /**
     * 29~32这四种异常是针对豹跑堂提供的，其他机器暂不支持。
     */
    public static final int GRAPH_SEARCHER_FAILED_ERROR = 29;
    /**
     * 没有绘制巡线
     */
    public static final int GRAPH_ROAD_INVALID_ERROR = 30;
    public static final int ROBOT_IS_OUT_OF_ROAD_ERROR = 31;
    public static final int GOAL_IS_OUT_OF_ROAD_ERROR = 32;
    public static final int MULTI_ROBOT_AVOID_WAITING = 33;
    public static final int UPLOAD_FILE = 34;
    public static final int MULTI_ROBOT_MAP_NOT_MATCH = 35;
    public static final int MULTI_ROBOT_MAP_MATCH = 36;
    public static final int GOTO_CHARGE_STATUS = 37;
    public static final int MULTI_ROBOT_AVOID_WAITING_END = 38;
    public static final int REPORT_JSON = 39;
    /**
     * 机器人转弯行进信号
     */
    public static final int TURN_SIGNAL = 40;
    /**
     * 机器人Lora多机测试返回结果
     */
    public static final int LORA_TEST = 41;
    /**
     * Lora多机，机器人多机实时状态
     */
    public static final int LORA_MULTI_ROBOT_STATUS = 42;
    /**
     * 多机等待事件
     */
    public static final int PATH_WAITING = 43;
    /**
     * Lora数据上报，仅针对工厂测试场景使用
     */
    public static final int LORA_RECEIVE_DATA = 45;

    /**
     * 多机异常-地图不匹配
     * 这种情况下导航会失败
     */
    public static final int MULTI_ROBOTS_MAP_NOT_MATCH = 44;

    /**
     * 多机异常-lora断连
     * 这种情况下导航会失败
     */
    public static final int LORA_DISCONNECTED = 46;

    /**
     * 多机异常-lora配置异常
     * 这种情况下导航会失败
     */
    public static final int LORA_CONFIG_ERROR = 47;

    /**
     * 多机版本不匹配-导航版本不匹配
     * 这种情况下导航会失败
     */
    public static final int MULTI_ROBOTS_VERSION_NOT_MATCH = 48;

    /**
    *camera 错误码
    *
    */
    public static final int CAMERA_ERROR_CODE = 49;

    /**
     * 建图时视觉信息上报
     */
    public static final int NINJIA_SLAM_MAPPING_VISION_INFO = 50;

    /**
    *传感器数据错误
    */


    public static final int SENSORHUB_ERROR_CODE = 51;

    /**
     *轮子打滑异常
     */
    public static final int SLAM_SLIP_INFO = 52;

    /**
     * 导航多机字节流，用户 Wifi-NAN 补偿
     */
    public static final int MULTI_ROBOT_DATA = 53;

    /**
     * 接受位姿超时（轮式里程计异常）
     */
    public static final int NAVI_CALC_VEL_RECIEVE_POSE_TIMEOUT = 54;
    /**
     * 接受实时地图超时（激光雷达异常）
     */
    public static final int NAVI_CALC_VEL_RECIEVE_MAP_TIMEOUT = 55;
    /**
     * 上面的两种异常，一次上报仅有一种，两种不会同时上报．
     * 上面两种异常对应的正常状态
     */
    public static final int NAVI_CALC_VEL_SUCCESS_VALUE = 56;

    /**
     * 当后台设定的允许机器人最大直径低于当前机器人本地设置的半径*2时，导航会在巡线模式时上报这个错误，和巡线宽度无关
     * 目前只表示机器人的直径参数设置错误，后续可能会扩展
     */
    public static final int NAVI_GRAPH_ROAD_PARAM_ERROR = 57;

    public static final int NAVI_GOTO_ALIGN_SUCCESS = 58;

    public static final int NAVI_GOTO_ALIGN_FAILED = 59;

    public static final int HUMAN_FOLLOWING_INIT_SUCCESS = 60;

    public static final int HUMAN_FOLLOWING_TRACKING_LOST = 61;

    public static final int HUMAN_FOLLOWING_ID_CHANGE = 62;

    public static final int HUMAN_FOLLOWING_LONG_TIME_NO_TAG = 63;

    public static final int REMOTE_ERROR = -1001;
    public static final int REMOTE_RECOVER = -1002;

    private final String additional;
    private byte[] bytes = null;

    public Event(int code, String message, String additional) {
        super(code, message);
        this.additional = additional;
    }

    public Event(int code, String message, String additional, byte[] bytes) {
        this(code, message, additional);
        this.bytes = bytes;
    }

    public String getAdditional() {
        return additional;
    }

    public byte[] getBytesData() {
        return bytes;
    }
}
