package com.ainirobot.navigationservice.chassisAbility.ota.connector.tk1.bean;

import android.util.Log;

import com.ainirobot.navigationservice.utils.IOUtils;

import java.io.DataOutputStream;
import java.io.FileInputStream;
import java.io.IOException;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

public class OtaReqMessage {
    private final static String TAG = TAGPRE + OtaReqMessage.class.getSimpleName();
    public final static int STRING_MSG = 0;
    public final static int FILE_MSG = 1;
    /**
     *  0 means String msg
     *  1 means file msg
     *
     */
    private int type;

    private String msg;

    public OtaReqMessage(int type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public void write(DataOutputStream out) throws IOException {
        switch (this.type) {
            case STRING_MSG:
                writeStringMsg(out);
                break;
            case FILE_MSG:
                writeFileMsg(out);
                break;
                default:
                    break;
        }
        Log.d(TAG, "command send success");
    }

    private void writeStringMsg(DataOutputStream out) throws IOException {
        out.write(this.msg.getBytes());
        out.write("\n".getBytes());
    }
    
    
    private void writeFileMsg(DataOutputStream out) throws IOException {
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(this.msg);
            byte[] buffer = new byte[1024 * 4];
            int len;
            while ((len = fis.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
            out.flush();
        } finally {
            IOUtils.close(fis);
        }
    }

    @Override
    public String toString() {
        StringBuffer buffer = new StringBuffer();
        buffer.append("OTAMessage,");
        buffer.append("type = ");
        buffer.append(this.type);
        buffer.append("\n");
        buffer.append("msg = ");
        buffer.append(this.msg);
        return buffer.toString();
    }
}
