package com.ainirobot.navigationservice.db.sqlite.utils;

import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.commonModule.data.DataManager;
import com.ainirobot.navigationservice.utils.GsonUtil;
import com.ainirobot.navigationservice.utils.IOUtils;
import com.ainirobot.navigationservice.utils.NavigationConfig;
import com.google.gson.JsonSyntaxException;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

public class PropertiesHelper {
    private static final String TAG = PropertiesHelper.class.getSimpleName();
    private static final String TYPE_POSE = "pose_";
    private static final String MD5 = "MD5";
    private static final String HAS_VISION = "hasVision";
    private static final String ROVER_CONFIG = "rover_config";
    private static final String IP_NAVIGATION = "navigation";
    private static final String IP_ROS = "ros";
    private static final String IP_SDK_ROS = "sdk_ros";
    private static final String CONFIG_DIR = "/robot/config";
    private static final String CONFIG_FILE_COPY = "navicopy.properties";
    private static final String CONFIG_FILE = "navigation.properties";
    private static final String MAP_TOOL_CONFIG_FILE = "maptool.properties";

    public String getSpecialMapLanguageFromProperties(Properties properties, String mapName) {
        String mapLanguage = getFromProperties(properties, DataManager.getInstance().getTypeMapLanguage(mapName));
        if (TextUtils.isEmpty(mapLanguage)) {
            mapLanguage = Locale.SIMPLIFIED_CHINESE.toString();
        }
        Log.d(TAG, "get map language: mapName=" + mapName + " language=" + mapLanguage);
        return mapLanguage;
    }

    public boolean getForbidLineFlagFromProperties(Properties properties, String mapName) {
        String forbidLine = getFromProperties(properties, DataManager.getInstance().getTypeForbidLine(mapName));
        return Boolean.parseBoolean(forbidLine);
    }

    public String getHasVisionFromProperties(Properties properties, String mapName) {
        return getFromProperties(properties, HAS_VISION + "_" + mapName);
    }

    public String getMapMd5FromProperties(Properties properties, String mapName) {
        return getFromProperties(properties, MD5 + "_" + mapName);
    }

    public String getRoverConfigFromProperties(Properties properties) {
        return getFromProperties(properties, ROVER_CONFIG);
    }

    public String getNavIpFromProperties(Properties properties) {
        String navIp = getFromProperties(properties, NavigationConfig.getWlan0Ip() + "_" + IP_NAVIGATION);
        return TextUtils.isEmpty(navIp) ? NavigationConfig.DEFAULT_NAVIGATION_IP : navIp;
    }

    public String getRosIpFromProperties(Properties properties) {
        String rosIp = getFromProperties(properties, NavigationConfig.getWlan0Ip() + "_" + IP_ROS);
        return TextUtils.isEmpty(rosIp) ? NavigationConfig.DEFAULT_ROS_IP : rosIp;
    }

    public String getSdkRosIpFromProperties(Properties properties) {
        String sdkRosIp = getFromProperties(properties, NavigationConfig.getWlan0Ip() + "_" + IP_SDK_ROS);
        return TextUtils.isEmpty(sdkRosIp) ? NavigationConfig.DEFAULT_SDK_ROS_IP : sdkRosIp;
    }

    public String getFromProperties(Properties properties, String key) {
        if (properties == null || properties.size() <= 0) {
            return "";
        }
        return properties.getProperty(key);
    }

    /**
     * 获取properties中所有地点信息，用于转存到数据库
     */
    public List<Pose> getAllPlaceListFromProperties(Properties properties) {
        Enumeration<?> keys = properties.propertyNames();
        String type = TYPE_POSE;
        List<Pose> array = new ArrayList<>();
        while (keys.hasMoreElements()) {
            String key = (String) keys.nextElement();
            if (key.startsWith(type)) {
                String poseStr = properties.getProperty(key);
                try {
                    Pose curPose = GsonUtil.fromJson(poseStr, Pose.class);
                    if (curPose != null) {
                        String tmp = key.replace(type, "");
                        curPose.setName(tmp);
                        Log.i("TAG", "pose info:" + curPose);
                        array.add(curPose);
                    }
                } catch (JsonSyntaxException e) {
                    e.printStackTrace();
                }
            }
        }
        return array;
    }

    public Properties getNaviProperties() {
        return getProperties(CONFIG_FILE);
    }

    public Properties getMapToolProperties() {
        return getProperties(MAP_TOOL_CONFIG_FILE);
    }

    //保存properties
    public boolean saveNaviProperties(Properties properties) {
        File file = getConfigFile(CONFIG_FILE);
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(file);
            properties.store(fos, "config");
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        } finally {
            IOUtils.close(fos);
        }
        return true;
    }

    public void deleteAllPropertiesFile() {
        Log.d(TAG, "deleteAllPropertiesFile:");
        File root = Environment.getExternalStorageDirectory();
        File dir = new File(root, CONFIG_DIR);
        if (!dir.exists()) {
            return;
        }
        File naviProFile = new File(dir, CONFIG_FILE);
        if (naviProFile.exists()) {
            Log.d(TAG, "deleteAllPropertiesFile: delete " + CONFIG_FILE);
            naviProFile.delete();
        }
        File naviProCopyFile = new File(dir, CONFIG_FILE_COPY);
        if (naviProCopyFile.exists()) {
            Log.d(TAG, "deleteAllPropertiesFile: delete " + CONFIG_FILE_COPY);
            naviProCopyFile.delete();
        }
        File mapToolProCopyFile = new File(dir, MAP_TOOL_CONFIG_FILE);
        if (mapToolProCopyFile.exists()) {
            Log.d(TAG, "deleteAllPropertiesFile: delete " + MAP_TOOL_CONFIG_FILE);
            mapToolProCopyFile.delete();
        }
    }

    //拿properties
    private Properties getProperties(String fileName) {
        Properties properties = new Properties();
        File file = getConfigFile(fileName);
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            properties.load(fis);
        } catch (IOException e) {
            e.printStackTrace();
            return properties;
        } finally {
            IOUtils.close(fis);
        }
        Log.d(TAG, "getProperties: start --->>>" + fileName);
        printProperty(properties);
        Log.d(TAG, "getProperties: end <<<---" + fileName);
        return properties;
    }

    //获取config文件
    private File getConfigFile(String fileName) {
        File root = Environment.getExternalStorageDirectory();
        File dir = new File(root, CONFIG_DIR);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        File file = new File(dir, fileName);
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return file;
    }

    private void printProperty(Properties properties) {
        if (properties == null) {
            Log.d("printProperty ", "properties is null");
            return;
        }

        for (Map.Entry<Object, Object> objectObjectEntry : properties.entrySet()) {
            Object key1 = objectObjectEntry.getKey();
            Object value1 = objectObjectEntry.getValue();
            Log.d("printProperty ", "key1=" + key1 + " value1=" + value1);
        }
    }
}
