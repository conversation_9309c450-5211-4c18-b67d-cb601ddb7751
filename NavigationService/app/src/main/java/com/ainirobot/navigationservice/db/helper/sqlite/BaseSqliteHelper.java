package com.ainirobot.navigationservice.db.helper.sqlite;

import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.ainirobot.navigationservice.Defs.Def;
import com.ainirobot.navigationservice.db.sqlite.SqliteDbMigrate;

import java.util.List;
import java.util.Map;


public abstract class BaseSqliteHelper<T> {
    protected final String TAG;
    protected SqliteDbMigrate sqliteDbMigrate;
    protected Map<String, Integer> cursorIndexMap;
    protected String mTableName;

    public BaseSqliteHelper(SqliteDbMigrate sqliteDbMigrate, String tableName) {
        this.sqliteDbMigrate = sqliteDbMigrate;
        this.mTableName = tableName;
        TAG = Def.MAP_DB_PRE + "Sq_Help_" + this.getClass().getSimpleName();
    }

    protected Map<String, Integer> getCursorIndexMap(Cursor cursor) {
        if (cursorIndexMap == null) {
            cursorIndexMap = updateCursorIndexMap(cursor);
        }
        return cursorIndexMap;
    }

    public void deleteAllData() {
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        writeDb.delete(mTableName, null, null);
    }

    public List<T> getAllDbInfo() {
        return null;
    }

    protected abstract Map<String, Integer> updateCursorIndexMap(Cursor cursor);
}
