package com.ainirobot.navigationservice;


import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.os.RemoteCallbackList;
import androidx.annotation.Nullable;
import android.util.Log;

import com.ainirobot.navigationservice.business.rpc.JoyStickControlServer;

/**
 * 手柄控制机器人移动服务
 *
 * <AUTHOR> on 2018/8/29 20:14
 * @version V1.0.0
 */
public class JoyStickControlService extends Service {

    private static final String TAG = "JoyStickControlService";

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        Log.d(TAG, "onBind: JoyStickControlService");
        return new JoyStickControlServer();
    }

    @Override
    public boolean onUnbind(Intent intent) {
        return super.onUnbind(intent);
    }
}
