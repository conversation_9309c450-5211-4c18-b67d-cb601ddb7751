package com.ainirobot.navigationservice.commonModule.bi.report;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;
import com.ainirobot.navigationservice.ApplicationWrapper;

/**
 * navigation cmd report
 *
 * @version V1.0.0
 * @date 2019/4/1 20:29
 */
public class NavigationApiReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_error";
    private static final String SYSTEM = "system";
    private static final String APPNAME = "appname";
    private static final String TYPE = "type";
    private static final String REQUEST = "request";
    private static final String ACTION_TYPE = "action_type";
    private static final String TRACK_ID = "track_id";
    private static final String STATUS_CODE = "status_code";
    private static final String MSG = "msg";
    private static final String ERROR_ID = "error_id";
    private static final String CTIME = "ctime";
    private static final String SYSTEM_821 = "821";
    public static final String TYPE_API_INVOKE = "api_invoke";
    public static final String TYPE_EVENT = "event";
    public static final String TYPE_CONNECT_STATE = "connect_state";
    public static final String TYPE_FUNCTION = "function ";

    public static final String REQUEST_SOCKET_CHASSIS = "socket_chassis";
    public static final String REQUEST_RELOCATION = "relocation ";

    public static final String ACTION_TYPE_CALL = "call";
    public static final String ACTION_TYPE_RESPONSE = "response";
    public static final String ACTION_TYPE_TIMEOUT = "timeout";
    public static final String ACTION_TYPE_EVENT = "event";
    public static final String ACTION_TYPE_DISCONNECT = "disconnect";
    public static final String ACTION_TYPE_RELOCATION_CHARGE_PILE = "relocation_charging_pile";
    public static final String ACTION_TYPE_RELOCATION_VISION = "relocation_vision";

    public static final String MSG_CMD_DISCONNECT = "cmd_disconnect";
    public static final String MSG_EVENT_DISCONNECT = "event_disconnect";

    public static final String MSG_RELOCATION_SUCCESS = "relocation_success";
    public static final String MSG_RELOCATION_FAIL = "relocation_fail";

    public static final String REASON_MAP_ERROR = "relocation_fail_by_map";
    public static final String REASON_CHECK_ERROR = "relocation_fail_by_charger";
    public static final String REASON_OTHERS = "relocation_fail_others";

    public static final String STATUS_CODE_LOST_OUT_MAP = "8_lost_outmap";
    public static final String STATUS_CODE_LOST = "8_lost";

    public static final int STATUS_CODE_RELOCATION_SUCCESS = 0;
    public static final int STATUS_CODE_RELOCATION_FAIL = -1;


    public static volatile String trackId = "";

    public NavigationApiReport() {
        super(TABLE_NAME);
        initData();
    }

    private void initData() {
        addSystem("");
        addAppName("");
        addType("");
        addRequest("");
        addActionType("");
        addTrackId();
        addStatusCode("");
        addMsg("");
        addErrorId("");
    }

    private NavigationApiReport addSystem(String system) {
        addData(SYSTEM, system);
        return this;
    }

    private NavigationApiReport addAppName(String appName) {
        addData(APPNAME, appName);
        return this;
    }

    public NavigationApiReport addType(String type) {
        addData(TYPE, type);
        return this;
    }

    public NavigationApiReport addRequest(String request) {
        addData(REQUEST, request);
        return this;
    }

    public NavigationApiReport addActionType(String actionType) {
        addData(ACTION_TYPE, actionType);
        return this;
    }

    private NavigationApiReport addTrackId() {
        addData(TRACK_ID, trackId);
        return this;
    }

    public NavigationApiReport addStatusCode(Object statusCode) {
        addData(STATUS_CODE, statusCode);
        return this;
    }

    public NavigationApiReport addMsg(String msg) {
        addData(MSG, msg);
        return this;
    }

    public NavigationApiReport addErrorId(String errorId) {
        addData(ERROR_ID, errorId);
        return this;
    }

    @Override
    public void report() {
        addSystem(SYSTEM_821);
        String appName = ApplicationWrapper.getContext().getPackageName();
        appName = appName == null ? "" : appName;
        addAppName(appName);
        addTrackId();
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }
}
