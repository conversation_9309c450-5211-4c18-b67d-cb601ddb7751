package com.ainirobot.navigationservice.Defs;

public class NavigationError {

    public static final int BASE_NAVIGATION_ERROR = -20010000;
    /**
     * Lora配置异常，此状态在RobotPlatform的NavigationTransferComponent组件内上传
     */
    public static final int NAVI_ERROR_MULTIPLE_LORA_CONFIG_FAIL = BASE_NAVIGATION_ERROR - 1;
    /**
     * 多机地图不匹配，此状态在RobotPlatform的NavigationTransferComponent组件内上传
     */
    public static final int NAVI_ERROR_MULTIPLE_MAP_NOT_MATCH = BASE_NAVIGATION_ERROR - 2;
    /**
     * 多机Lora断连，此状态在RobotPlatform的NavigationTransferComponent组件内上传
     */
    public static final int NAVI_ERROR_MULTIPLE_LORA_DISCONNECT = BASE_NAVIGATION_ERROR - 3;

    /**
     * 多机版本不一致
     */
    public static final int NAVI_ERROR_MULTIPLE_VERSION_NOT_MATCH = BASE_NAVIGATION_ERROR - 4;

}
