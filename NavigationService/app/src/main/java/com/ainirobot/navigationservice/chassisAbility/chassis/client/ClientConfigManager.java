package com.ainirobot.navigationservice.chassisAbility.chassis.client;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.waiter.CameraBean;
import com.ainirobot.navigationservice.beans.waiter.MultiRobotConfigBean;
import com.ainirobot.navigationservice.commonModule.configuration.ConfigManager;
import com.ainirobot.navigationservice.db.NavigationDataManager;
import com.ainirobot.navigationservice.utils.GsonUtil;
import com.ainirobot.navigationservice.utils.MapUtils;
import com.google.protobuf.InvalidProtocolBufferException;

import org.json.JSONException;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.util.HashMap;

import ninjia.android.proto.CommonProtoWrapper;
import ninjia.android.proto.DeviceParamsProtoWrapper;
import ninjia.android.proto.RoverConfigProtoWrapper;
import ninjia.android.proto.RoverConfigProtoWrapper.RoverConfigProto;

/**
 * <AUTHOR>
 */
public class ClientConfigManager {

    private static final String TAG = ClientConfigManager.class.getSimpleName();
    private static ClientConfigManager mInstance;

    private static final float RANGE_VERY_HIGH = 20.0f;
    private static final float RANGE_EXTREMELY_HIGH = 50.0f;

    private ClientConfigManager() {

    }

    public static synchronized ClientConfigManager getInstance() {
        if (mInstance == null) {
            mInstance = new ClientConfigManager();
        }
        return mInstance;
    }

    /**
     * 生成RoverConfig或者从navigation.properties中读取RoverConfig信息
     * 注意：1.必须要等deviceType获取成功并存储后才能执行
     * 2.如果没有默认配置（如工厂新机器）则需要生成默认config
     * 3.必须要考虑config更新，RoverConfig更新的每一项，在不同产品线需要不同的配置
     */
    public void storeDifferentClientRoverConfig() {
        String config = NavigationDataManager.getInstance().getRoverConfig();
        int deviceTN = ConfigManager.getInstance().getSubDeviceTypeNumber();
        DeviceParamsProtoWrapper.DeviceParamsProto paramsProto = ConfigManager.getInstance().getDeviceParamsProto();

        RoverConfig roverConfig = null;
        if (!TextUtils.isEmpty(config)) {
            Log.d(TAG, "Load stored RoverConfig : " + config);
            roverConfig = loadLocalRoverConfig(config, deviceTN);
        }

        if (roverConfig == null) {
            Log.d(TAG, "Create default RoverConfig : " + config);
            roverConfig = generateDefaultRoverConfig(deviceTN, paramsProto);
        }

        Log.d(TAG, "Update rover config : " + roverConfig.toString());
        NavigationDataManager.getInstance().updateMultiRobotConfig(roverConfig);
    }

    public String getDefaultRoverConfig() {
        int deviceTN = ConfigManager.getInstance().getSubDeviceTypeNumber();
        DeviceParamsProtoWrapper.DeviceParamsProto paramsProto = ConfigManager.getInstance().getDeviceParamsProto();
        RoverConfig roverConfig = generateDefaultRoverConfig(deviceTN, paramsProto);
        Log.d(TAG, "getDefaultRoverConfig: roverConfig=" + roverConfig.toString());
        return GsonUtil.toJson(roverConfig);
    }

    /**
     * 根据不同的deviceType生成不同的RoverConfigProto配置。
     * roverconfig 必须存储并且存在，否则机器人底盘无法正常使用
     */
    public RoverConfigProto generateRoverConfigProtoFromConfigFile() {
        Log.d(TAG, "generateRoverConfigProtoFromConfigFile:Start");
        String roverConfig = NavigationDataManager.getInstance().getRoverConfig();
        if (TextUtils.isEmpty(roverConfig)) {
            throw new RuntimeException("no roverConfig in navigation.properties");
        }

        RoverConfig config = GsonUtil.fromJson(roverConfig, RoverConfig.class);

        boolean enableSingleTarget = RobotSettingApi.getInstance()
                .getRobotInt(Definition.ROBOT_SETTING_ENABLE_SINGLE_MARKER) == 1;
        boolean enableDynamicFilter = RobotSettingApi.getInstance()
                .getRobotInt(Definition.ROBOT_SETTING_ENABLE_DYNAMIC_MAP_FILTER) == 1;
        boolean enableGridFilterUseCostmapObs = true;

        float offset = RobotSettingApi.getInstance().hasRobotSetting("robot_setting_navi_temporary_obs_extra_raduis_offset") ?
                RobotSettingApi.getInstance().getRobotFloat("robot_setting_navi_temporary_obs_extra_raduis_offset") : 0.0f;
        if (Math.abs(offset) < 0.01f) {
            offset = Definition.DEFAULT_TEMPORARY_OBS_EXTRA_RADUIS_OFFSET;
        }
        double naviTemporaryObsExtraRaduisOffset = Double.parseDouble(String.valueOf(offset));

        //RGBD上视值naviTemporaryRgbdFilterLevel——》从Global取值
        String naviTemporaryRgbdFilterLevel = RobotSettingApi.getInstance()
                .getRobotString("robot_setting_navi_temporary_rgbd_filter_level");
        Log.d(TAG, " generateRoverConfigProtoFromConfigFile:: 上视RGBD naviTemporaryRgbdFilterLevel = " + naviTemporaryRgbdFilterLevel);
        if (config != null && !config.isEnableRgbdAvoidObs()) {
            naviTemporaryRgbdFilterLevel = "0";
        }
        if (TextUtils.isEmpty(naviTemporaryRgbdFilterLevel)) {
            naviTemporaryRgbdFilterLevel = Definition.DEFAULT_TEMPORARY_RGBD_FILTER_LEVEL + "";
        }
        naviTemporaryRgbdFilterLevel = MapUtils.getLevel(Integer.parseInt(naviTemporaryRgbdFilterLevel)) + "";

        //RGBD下视值rgbdDownVisionLevel——》从数据库取值
        int rgbdDownVisionLevel = RobotSettingApi.getInstance().getRobotInt("robot_setting_navi_temporary_rgbd_filter_down_vision_level");
        if (rgbdDownVisionLevel < 0) {
            rgbdDownVisionLevel = 3;
        }
        if (!config.isEnableRgbdAvoidObs()) {
            rgbdDownVisionLevel = 0;
        }
        rgbdDownVisionLevel = MapUtils.getLevel(rgbdDownVisionLevel);

        boolean naviTemporaryDisableDwaPlanner = "1".equals(RobotSettingApi.getInstance()
                .getRobotString("robot_setting_navi_temporary_disable_dwa_planner"));


        boolean disableMultiRobotAutoWidenPaths = "1".equals(RobotSettingApi.getInstance()
                .getRobotString("robot_setting_disable_multirobot_auto_widen_path"));

        float distance = RobotSettingApi.getInstance()
                .getRobotFloat("robot_setting_navi_block_obstacle_distance");
        if (Math.abs(distance) < 0.01f) {
            distance = 0;
        }
        //最大避障距离，当目标点被障碍物阻挡时，机器人距离目标点的距离大于该值时，机器人会停止移动
        double naviBlockObstacleDistance = Double.parseDouble(String.valueOf(distance));
        //华住写死3米，验证方案可行性
//        naviBlockObstacleDistance = 3.0;(合入主线后，华住通过config配置)

        //安全半径，额外膨胀的安全距离
        float lethalRadius = RobotSettingApi.getInstance()
                .getRobotFloat("robot_setting_navi_lethal_radius_offset");
        if (lethalRadius < -0.02f) {
            lethalRadius = 0;
        }
        if (lethalRadius > 0.1f) {
            lethalRadius = 0;
        }
        BigDecimal naviLethalRadiusOffset = BigDecimal.valueOf(Double.parseDouble(String.valueOf(lethalRadius)));

        int robotStructureMode = RobotSettingApi.getInstance()
                .getRobotInt("robot_setting_navi_robot_structure_mode");
        //V10.3新增机器人结构模式
        //结构模式会把机器人形状从圆形改为大小圆
        //只有Carry才可以设置该参数
        //无货架 = mode 0(默认)
        //有货架 = mode 1
        //自定义货架 = mode 0 + 物理半径 - 0.4 + offset
        //跟产品 和 韩国确认不用做历史数据兼容

        float factoryRobotRadiusF = RobotSettingApi.getInstance()
                .getRobotFloat("robot_setting_navi_factory_robot_radius");
        //机器物理半径，carry机器添加额外货架时需要调整该值,robot_setting_navi_factory_robot_radius
        //物理半径和安全半径共同决定naviLethalRadiusOffset参数值：
        // naviLethalRadiusOffset = （物理半径 - 基准半径值） + 安全半径
        //基准半径值目前固定为0.4m，其他平台为0，即只有carry会参与计算

        //基准值修改：0.4改为0.3，修改原因：底盘发现底层bug，0.4的值是错误的。

        BigDecimal factoryRobotRadius = null;
        if (ProductInfo.isCarryProduct()) {
            if (robotStructureMode == 2) {
                factoryRobotRadius = new BigDecimal(Float.toString(factoryRobotRadiusF));
                if (factoryRobotRadius.compareTo(BigDecimal.valueOf(0.3)) >= 0 && factoryRobotRadius.compareTo(BigDecimal.valueOf(0.7)) <= 0) {
                    naviLethalRadiusOffset = factoryRobotRadius.subtract(BigDecimal.valueOf(0.3)).add(naviLethalRadiusOffset);
                }
            }
        }

        Log.d(TAG, "generateRoverConfigProtoFromConfigFile: "
                + " lethalRadius = " + lethalRadius
                + " factoryRobotRadiusF = " + factoryRobotRadiusF
                + " factoryRobotRadius = " + factoryRobotRadius
                + " naviLethalRadiusOffset = " + naviLethalRadiusOffset.doubleValue());

        boolean naviUseOldMultiSafeChecker = !("" + Definition.DEFAULT_USE_NEW_MULTI_ROBOTS_SAFE_CHECKER).equals(
                RobotSettingApi.getInstance().getRobotString("robot_setting_navi_use_new_multi_robots_safe_checker"));

        String forceFollowRobot = RobotSettingApi.getInstance()
                .getRobotString("robot_setting_navi_enable_local_path_force_follow_robot");
        boolean naviEnableLocalPathForceFollowRobot = TextUtils.isEmpty(forceFollowRobot) || TextUtils.equals("0", forceFollowRobot);

        int naviReduceSpeedEncounterPeople;
        if (RobotSettingApi.getInstance()
                .hasRobotSetting("reduce_speed_encounter_people")) {
            naviReduceSpeedEncounterPeople = RobotSettingApi.getInstance()
                    .getRobotInt("reduce_speed_encounter_people");
            if (naviReduceSpeedEncounterPeople < 0 || naviReduceSpeedEncounterPeople > 2) {
                naviReduceSpeedEncounterPeople = Definition.DEFAULT_REDUCE_SPEED_ENCOUNTER_PEOPLE;
            }
        } else {
            naviReduceSpeedEncounterPeople = Definition.DEFAULT_REDUCE_SPEED_ENCOUNTER_PEOPLE;
        }

        //不需要使用device.properties中的配置，因为这个配置是针对单目视觉数据采集使能状态的。
        boolean hasMono = ProductInfo.isMeissa2() || ProductInfo.isSaiphPro() || ProductInfo.isAlnilamPro() || ProductInfo.isMiniProduct();
        boolean naviEnableMonoVisualDataCollection = hasMono && !ProductInfo.isOverSea();

        //知书定制版本临时实现方式，通过知书单独配置文件写死，后续是否要在MapTool中设置RoverConfig待定
        float autoChargeSpeedRatio = RobotSettingApi.getInstance()
                .hasRobotSetting("robot_setting_auto_charge_speed_ratio")
                ? RobotSettingApi.getInstance()
                .getRobotFloat("robot_setting_auto_charge_speed_ratio") : 1.0f;

        Log.i(TAG, "enable single target: " + enableSingleTarget
                + " enable dynamic filter:" + enableDynamicFilter
                + " enableGridFilterUseCostmapObs:" + enableGridFilterUseCostmapObs
                + " naviTemporaryObsExtraRaduisOffset:" + naviTemporaryObsExtraRaduisOffset
                + " naviTemporaryRgbdFilterLevel:" + naviTemporaryRgbdFilterLevel
                + " rgbdDownVisionLevel:" + rgbdDownVisionLevel
                + " naviTemporaryDisableDwaPlanner:" + naviTemporaryDisableDwaPlanner
                + " disableMultiRobotAutoWidenPaths:" + disableMultiRobotAutoWidenPaths
                + " naviBlockObstacleDistance:" + naviBlockObstacleDistance
                + " naviLethalRadiusOffset:" + naviLethalRadiusOffset
                + " naviUseOldMultiSafeChecker:" + naviUseOldMultiSafeChecker
                + " naviEnableLocalPathForceFollowRobot:" + naviEnableLocalPathForceFollowRobot
                + " naviReduceSpeedEncounterPeople:" + naviReduceSpeedEncounterPeople
                + " naviEnableMonoVisualDataCollection:" + naviEnableMonoVisualDataCollection
                + " generateDeviceProto:" + generateDeviceProto(config.getDeviceType())
                + " robotStructureMode:" + robotStructureMode
                + " autoChargeSpeedRatio:" + autoChargeSpeedRatio);

        Log.d(TAG, "generateRoverConfigProtoFromConfigFile: RoverConfig = " + config.toString());
        RoverConfigProto.Builder builder = RoverConfigProto.newBuilder()
                .setRosServerIp(config.getRosIp())
                .setDeviceType(generateDeviceProto(config.getDeviceType()))
                .setEnableDepthCameraHighAccuracy(config.isEnableRgbdHighAccuracy())
                .setEnableIr(config.isEnableIR())
                .setMaxLostDistance(generateMaxLostDistance(config.getPositionLostMaxRange()))
                .setEnableVision(config.isEnableVision())
                .setScenesType(RoverConfigProtoWrapper.ScenesTypeProto.SCENES_DEFAULT)
                //enableRgbdAvoidObs废弃，不能设置为false，有没有rgbd不影响
                .setEnableAvoidObs(true)
                .setEnableTargetLocate(config.isEnableTargetLocate())
                .setEnableLineTracking(config.isEnableLineTracking())
                .setEnableCheckCliff(config.isEnableCheckCliff())
                .setEnableMultiRobot(config.isEnableMultiRobot())
                .setEnableDynamicMapFilter(enableDynamicFilter)
                .setEnableSingleTargetLocation(enableSingleTarget)
                .setEnableGridFilterUseCostmapObs(enableGridFilterUseCostmapObs)
                .setRgbdObsHeightThresholdOffset(config.getRgbd_obs_height_threshold_offset())
                .setNaviTemporaryObsExtraRaduisOffset(naviTemporaryObsExtraRaduisOffset)
                .setPoints3DUpLevelValue(Integer.parseInt(naviTemporaryRgbdFilterLevel))
                .setPoints3DDownLevelValue(rgbdDownVisionLevel)
                .setDisableMultistageDwaPlanner(naviTemporaryDisableDwaPlanner)
                .setDisableMultirobotAutoWidenPath(disableMultiRobotAutoWidenPaths)
                .setNaviBlockObstacleDistance(naviBlockObstacleDistance)
                .setNaviLethalRadiusOffset(naviLethalRadiusOffset.doubleValue())
                .setNaviUseOldMultiRobotsSafeChecker(naviUseOldMultiSafeChecker)
                .setNaviEnableGraphLeadingPlannerObsPoints(config.isEnableGraphLeadingPlannerObsPoints())
                .setNaviLocalMapLegsInflationLevel(config.getLocalMapLegsInflationLevel())
                .setNaviIncreaseRotationSelfSpeed(config.isEnableIncreaseRotationSelfSpeed())
                .setNaviEnableLocalPathForceFollowRobot(naviEnableLocalPathForceFollowRobot)
                .setNaviRoadPointObsMinWaitTime(config.getGraphLeadingPlannerWaitTime())
                .setNaviRoadPointObsCostScale(config.getGraphLeadingPlannerWaitTimeScale())
                .setNaviLocalPathSpeedLimitForHumanLevel(naviReduceSpeedEncounterPeople)
                .setEnableMonoVisualDataCollection(naviEnableMonoVisualDataCollection)
                .setDeviceParams(ConfigManager.getInstance().getDeviceParamsProto())
                .setEnableParticleFilter(true)//导航粒子滤波算法开关
                .setParticleCsmMinRadius(0.3)//导航粒子滤波算法参数默认值，最小半径
                .setParticleCsmMaxRadius(1.0)//导航粒子滤波算法参数默认值，最大半径
                .setNaviEnableCloseToRoadLine(config.isNaviEnableCloseToRoadLine())
                .setAutoChargeSpeedRatio(autoChargeSpeedRatio)
                .setNaviDisableDynamicObsStop(!config.isEnableDynamicAvoidanceStrategy());

        //知书定制：新增导航额外半径offset值，这里给知书需要设置为-0.05。（主线不设置）
        if(RobotSettingApi.getInstance()
                .hasRobotSetting("robot_setting_navi_extra_radius_offset")){
            double naviExtraRadiusOffset = RobotSettingApi.getInstance()
                    .getRobotFloat("robot_setting_navi_extra_radius_offset");
            Log.d(TAG, "generateRoverConfigProtoFromConfigFile: naviExtraRadiusOffset = " + naviExtraRadiusOffset);
            builder.setNaviExtraRadiusOffset(naviExtraRadiusOffset);
        }

        if (ProductInfo.isCarryProduct()) {
            if (robotStructureMode == 2) {
                builder.setRobotStructureMode(0);
            } else {
                builder.setRobotStructureMode(robotStructureMode);
            }
        }
//        if (ProductInfo.isSaiphMall()) {
//            builder.setDeviceParams(generateDeviceParamsProto(config.getDeviceParams()));
//        }
        return builder.build();
    }

    private double generateMaxLostDistance(float distanceRange) {
        Log.d(TAG, "generate MaxLostDistance: " + distanceRange);
        double distance;
        if (checkDistanceRange(distanceRange)) {
            distance = distanceRange;
        } else {
            distance = RoverConfig.LOST_MAX_DISTANCE_HIGH;
        }
        Log.d(TAG, "generate final distance : " + distance);
        return distance;
    }

    private boolean checkDistanceRange(float distanceRange) {
        return distanceRange == Definition.LOST_MAX_DISTANCE.RANGE_SUPER_LOW.getValue()
                || distanceRange == Definition.LOST_MAX_DISTANCE.RANGE_LOW.getValue()
                || distanceRange == Definition.LOST_MAX_DISTANCE.RANGE_MIDDLE.getValue()
                || distanceRange == Definition.LOST_MAX_DISTANCE.RANGE_HIGH.getValue()
                || distanceRange == RANGE_VERY_HIGH
                || distanceRange == RANGE_EXTREMELY_HIGH;
    }

    private RoverConfigProtoWrapper.DeviceTypeProto generateDeviceProto(int deviceType) {
        RoverConfigProtoWrapper.DeviceTypeProto deviceTypeProto;
        switch (deviceType) {
            case RoverConfig.DEVICE_CUSTOM:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_CUSTOM;
                break;
            case RoverConfig.DEVICE_KTV:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_KTV;
                break;
            case RoverConfig.DEVICE_MESSIA_LH:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MESSIA_LH;
                break;
            case RoverConfig.DEVICE_X86_MINI:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MINI;
                break;
            case RoverConfig.DEVICE_X86_MINI2:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MINI2;
                break;
            case RoverConfig.DEVICE_KTV2:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_KTV2;
                break;
            case RoverConfig.DEVICE_WAITER:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER;
                break;
            case RoverConfig.DEVICE_MESSIA_S:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MESSIA_S;
                break;
            case RoverConfig.DEVICE_MINI2_XIAOQIAO:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MINI2_XIAOQIAO;
                break;
            case RoverConfig.DEVICE_WAITER_SA:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_SA;
                break;
            case RoverConfig.DEVICE_MINI2_EAI:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MINI2_EAI;
                break;
            case RoverConfig.DEVICE_MINI2_SHANG_CHANG:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MINI2_SHANG_CHANG;
                break;
            case RoverConfig.DEVICE_MINI3:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MINI3;
                break;
            case RoverConfig.DEVICE_MINI3_EAI:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MINI3_EAI;
                break;
            case RoverConfig.DEVICE_WAITER_SA_H:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_SA_H;
                break;
            case RoverConfig.DEVICE_WAITER_DISINFECTION:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_DISINFECTION;
                break;
            case RoverConfig.DEVICE_WAITER_SA_H_LW:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_SA_H_LW;
                break;
            case RoverConfig.DEVICE_WAITER_SA_FM1:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_SA_FM1;
                break;
            case RoverConfig.DEVICE_WAITER_PRO:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_PRO;
                break;
            case RoverConfig.DEVICE_WAITER_WF:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_WF;
                break;
            case RoverConfig.DEVICE_WAITER_SA_WF:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_SA_WF;
                break;
            case RoverConfig.DEVICE_WAITER_SA_LW_WF:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_SA_LW_WF;
                break;
            case RoverConfig.DEVICE_MESSIA_PLUS:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MESSIA_PLUS;
                break;
            case RoverConfig.DEVICE_MESSIA_PLUS_WF:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MESSIA_PLUS_WF;
                break;
            case RoverConfig.DEVICE_WAITER_SA_LW:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_SA_LW;
                break;
            case RoverConfig.DEVICE_WAITER_PRO_D3:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_PRO_D3;
                break;
            case RoverConfig.DEVICE_WAITER_X100:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_X100;
                break;
            case RoverConfig.DEVICE_WAITER_SA_X100:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_SA_X100;
                break;
            case RoverConfig.DEVICE_WAITER_SA_H_X100:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_SA_H_X100;
                break;
            case RoverConfig.DEVICE_WAITER_SA_H_LW_X100:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_SA_H_LW_X100;
                break;
            case RoverConfig.DEVICE_WAITER_SA_LW_X100:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_SA_LW_X100;
                break;
            case RoverConfig.DEVICE_WAITER_PRO_2XB40_X100:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_PRO_2XB40_X100;
                break;
            case RoverConfig.DEVICE_MEISSA2:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MEISSA2;
                break;
            case RoverConfig.DEVICE_WAITER_PRO_TOP_MONO:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_PRO_TOP_MONO;
                break;
            case RoverConfig.DEVICE_WAITER_PRO_2XB40_X100_ELEC_GATE:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_PRO_2XB40_X100_ELEC_GATE;
                break;
            case RoverConfig.DEVICE_MINI2_SIMULATOR:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MINI2_SIMULATOR;
                break;
            case RoverConfig.DEVICE_WAITER_PRO_2XB40_X100_TOP_MONO:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_WAITER_PRO_2XB40_X100_TOP_MONO;
                break;
            case RoverConfig.DEVICE_MINI2_WF:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MINI2_WF;
                break;
            case RoverConfig.DEVICE_MINI2_WF_SSMONO:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MINI2_WF_SSMONO;
                break;
            case RoverConfig.DEVICE_MINI2_SSMONO:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MINI2_SSMONO;
                break;
            default:
                deviceTypeProto = RoverConfigProtoWrapper.DeviceTypeProto.DEVICE_MESSIA_LH;
                break;
        }
        return deviceTypeProto;
    }

    private RoverConfig loadLocalRoverConfig(String config, int deviceTN) {
        RoverConfig roverConfig;
        try {
            JSONObject jsonObject = new JSONObject(config);
            boolean isHasCVConfig = jsonObject.has("enableCollisionAvoid");
            boolean isHasSAConfig = jsonObject.has("enableStuckAvoid");
            boolean isHasTLConfig = jsonObject.has("enableTargetLocate");
            boolean isHasLTConfig = jsonObject.has("enableLineTracking");
            boolean isHasCCConfig = jsonObject.has("enableCheckCliff");
            boolean isHasCMOConfig = jsonObject.has("enableConveringMapObstacles");
            boolean isHasPWLConfig = jsonObject.has("enablePathWidthLimit");
            boolean isHasLostMaxRange = jsonObject.has("positionLostMaxRange");
            boolean isHasRGBDUpConfig = jsonObject.has("slamEnableVox3DMapUp");
            boolean isHasRGBDDownConfig = jsonObject.has("slamEnableVox3DMapDown");

            Log.d(TAG, "storeRoverConfigIfNeed isHasCVConfig:" + isHasCVConfig
                    + " isHasSAConfig:" + isHasSAConfig
                    + " isHasTLConfig:" + isHasTLConfig
                    + " isHasLTConfig:" + isHasLTConfig
                    + " isHasCCConfig:" + isHasCCConfig
                    + " isHasCMOConfig:" + isHasCMOConfig
                    + " isHasPWLConfig:" + isHasPWLConfig
                    + " isHasLostMaxRange : " + isHasLostMaxRange);
            roverConfig = GsonUtil.fromJson(config, RoverConfig.class);
            if (roverConfig == null) {
                return null;
            }

            if (roverConfig.getDeviceType() != deviceTN) {
                Log.d(TAG, "Update rover config : new deviceTN = " + deviceTN);
                roverConfig.setDeviceType(deviceTN);
            }
            if (!isHasCVConfig) {
                updateDefaultCollisionAvoidConfig(roverConfig);
            }
            if (!isHasSAConfig) {
                updateDefaultStuckAvoidConfig(roverConfig);
            }
            if (!isHasTLConfig) {
                updateDefaultTargetLocateConfig(roverConfig);
            }
            if (!isHasLTConfig) {
                updateDefaultLineTrackingConfig(roverConfig);
            }
            if (!isHasCCConfig) {
                updateDefaultCheckCliffConfig(roverConfig);
            }
            if (!isHasCMOConfig) {
                updateDefaultConveringMapObstacles(roverConfig);
            }
            if (!isHasPWLConfig) {
                updateDefaultPathWidthLimitConfig(roverConfig);
            }
            if (!isHasLostMaxRange) {
                updateDefaultLostMaxRange(roverConfig);
            }
            if (!isHasRGBDUpConfig) {
                updateDefaultSlamEnableVox3DMapUp(roverConfig);
            }
            if (!isHasRGBDDownConfig) {
                updateDefaultSlamEnableVox3DMapDown(roverConfig);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            return null;
        }
        return roverConfig;
    }


    /**
     * 　检查机器中是否移除了　RGBD_D430 硬件
     *
     * @param roverConfig
     */
    @Deprecated
    private void checkRgbdDevice(RoverConfig roverConfig) {
        //该接口废弃，RGBD改为level方式
        //有没有深度，enableRgbdAvoidObs都要设置为true
    }

    /**
     * 纠正底盘的配置项，由于底盘的不兼容问题导致的上层配置兜底策略。纠正后的配置项需要同步存储
     * 该纠正函数目前针对送餐机器人生效，主要纠正rgbd和标准避障的配置
     *
     * @param roverConfig 本地存储的config信息
     */
    @Deprecated
    private void correctChassisConfig(RoverConfig roverConfig) {
        //该接口废弃，RGBD改为level方式
        //有没有深度，enableRgbdAvoidObs都要设置为true
    }

    /**
     * 更新bo后台相关下发参数配置
     */
    @Deprecated
    private void updateRemoteBoConfig(int deviceType, RoverConfig roverConfig) {
        //该接口废弃，RGBD改为level方式
    }

    /**
     * bo运营工具下发RGBD避障配置
     * 产品确认该配置仅仅针对招财豹生效 2021.1.13
     */
    @Deprecated
    private String updateRemoteRgbdAvoidObsConfig(int deviceType, RoverConfig roverConfig) {
        //该接口废弃，RGBD改为level方式
        return null;
    }

    /**
     * bo运营工具下发RGBD防跌落配置
     * 该配置如果是招财豹需忽略，产品确认不对招财豹生效 2021.1.13
     */
    @Deprecated
    private String updateRemoteRgbdConfig(int deviceType, RoverConfig roverConfig) {
        //该接口废弃，RGBD改为level方式
        return null;
    }

    @Deprecated
    private void reportRGBDStatus(RoverConfig roverConfig, String globalEnableRgbd) {
        //该接口废弃，RGBD改为level方式
    }

    /**
     * 上报RGBD算法防避障开关状态
     */
    @Deprecated
    private void reportRgbdAvoidObsStatus(RoverConfig roverConfig, String rgbdAvoidStatus) {
        //该接口废弃，RGBD改为level方式
    }

    /**
     * 更新相遇防碰撞配置，默认所有产品线关闭
     */
    private void updateDefaultCollisionAvoidConfig(RoverConfig roverConfig) {
        roverConfig.setEnableCollisionAvoid(false);
    }

    /**
     * 更新单通防卡死配置，默认所有产品线关闭
     */
    private void updateDefaultStuckAvoidConfig(RoverConfig roverConfig) {
        roverConfig.setEnableStuckAvoid(false);
    }

    /**
     * 更新Rgbd避障配置
     * 除豹小递（送餐）默认打开，其他产品线默认关闭
     */
    @Deprecated
    private void updateDefaultRgbdAvoidObsConfig(RoverConfig roverConfig) {
        //该接口废弃，RGBD改为level方式
    }

    /**
     * 更新Target定位配置
     * 除豹小递（送餐）默认打开，其他产品线默认关闭
     */
    private void updateDefaultTargetLocateConfig(RoverConfig roverConfig) {
        if (roverConfig.getDeviceType() == RoverConfig.DEVICE_WAITER) {
            roverConfig.setEnableTargetLocate(true);
        } else {
            roverConfig.setEnableTargetLocate(false);
        }
    }

    /**
     * 更新巡线配置
     * 除豹小递（送餐）默认打开，其他产品线默认关闭
     */
    private void updateDefaultLineTrackingConfig(RoverConfig roverConfig) {
        switch (roverConfig.getDeviceType()) {
            case RoverConfig.DEVICE_WAITER:
            case RoverConfig.DEVICE_WAITER_PRO:
            case RoverConfig.DEVICE_SLIM:
                roverConfig.setEnableLineTracking(true);
                break;
            case RoverConfig.DEVICE_X86_MINI2:
            case RoverConfig.DEVICE_MEISSA2:
            case RoverConfig.DEVICE_MESSIA_PLUS:
            default:
                roverConfig.setEnableLineTracking(false);
                break;
        }
    }

    /**
     * 更新防跌落配置
     * 除豹小递（送餐）默认打开，其他产品线默认关闭
     */
    private void updateDefaultCheckCliffConfig(RoverConfig roverConfig) {
        roverConfig.setEnableCheckCliff(false);
    }

    /**
     * mini更新导航定位丢失后, 盲走检测最大范围
     * 只有mini 提供外部设置最大距离参数, 其他产品线不设置，走roversdk底层的默认逻辑
     */
    private void updateDefaultLostMaxRange(RoverConfig roverConfig) {
        switch (roverConfig.getDeviceType()) {
            case RoverConfig.DEVICE_X86_MINI2:
                roverConfig.setPositionLostMaxRange(Definition.LOST_MAX_DISTANCE.RANGE_HIGH.getValue());
                break;
            default:
                break;
        }
    }

    /**
     * 更新RGBD上视开关，默认所有产品线开启
     */
    private void updateDefaultSlamEnableVox3DMapUp(RoverConfig roverConfig) {
        roverConfig.setSlamEnableVox3DMapUp(true);
    }


    /**
     * 更新RGBD下视开关，默认所有产品线开启
     */
    private void updateDefaultSlamEnableVox3DMapDown(RoverConfig roverConfig) {
        roverConfig.setSlamEnableVox3DMapDown(true);
    }

    /**
     * 更新地图障碍物覆盖配置
     */
    private void updateDefaultConveringMapObstacles(RoverConfig roverConfig) {
        roverConfig.setEnableConveringMapObstacles(false);
    }

    /**
     * 更新巡线路线宽度限制配置
     */
    @Deprecated
    private void updateDefaultPathWidthLimitConfig(RoverConfig roverConfig) {
        //该接口废弃，RGBD改为level方式
    }

    /**
     * 如果没有默认RoverConfig配置文件，则更新不同产品线差异性配置
     */
    private RoverConfig generateDefaultRoverConfig(int realDeviceType, DeviceParamsProtoWrapper.DeviceParamsProto paramsProto) {
        int enableDynamicAvoidance = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_DYNAMIC_AVOID);

        RoverConfig roverConfig = new RoverConfig("");
        roverConfig.setEnableCamera(true);
        roverConfig.setDeviceType(realDeviceType);
        roverConfig.setScenesType(RoverConfig.SCENES_DEFAULT);
        roverConfig.setEnableFishEye(false);
        roverConfig.setEnableRgbdAvoidObs(true);
        roverConfig.setEnableSonar(false);
        roverConfig.setEnableIR(false);
        roverConfig.setMapWithRecord(false);
        roverConfig.setRecordMono(true);
        roverConfig.setRecordRgbd(true);
        roverConfig.setLethalRadius(RoverConfig.NORMAL);
//        roverConfig.setOpenMaxLostDistance(false);
        roverConfig.setEnableVision(true);
        roverConfig.setEnableCollisionAvoid(false);
        roverConfig.setEnableStuckAvoid(false);
        roverConfig.setEnableConveringMapObstacles(false);
        roverConfig.setEnableKeepRight(false);
        roverConfig.setEnableMultiRobot(false);
        roverConfig.setMultiRobotServerPort(0);
        roverConfig.setMultiRobotClientId(0);
        roverConfig.setServerIp("");
        roverConfig.setEnableRgbdHighAccuracy(true);
        roverConfig.setEnableGraphLeadingPlannerObsPoints(true);
        roverConfig.setLocalMapLegsInflationLevel(2);
        roverConfig.setEnableIncreaseRotationSelfSpeed(true);
        roverConfig.setGraphLeadingPlannerWaitTime(2.0f);
        roverConfig.setGraphLeadingPlannerWaitTimeScale(1.0f);
        roverConfig.setSlamEnableVox3DMapUp(true);
        roverConfig.setSlamEnableVox3DMapDown(true);
        roverConfig.setNaviEnableCloseToRoadLine(true);
        roverConfig.setEnableDynamicAvoidanceStrategy(enableDynamicAvoidance != 0);

        switch (ConfigManager.getInstance().getDeviceTypeNumber()) {
            case RoverConfig.DEVICE_WAITER:
                try {
                    DeviceParamsProtoWrapper.WaiterParamsProto waiterParamsProto =
                            DeviceParamsProtoWrapper.WaiterParamsProto.parseFrom(paramsProto.getParams());
                    roverConfig.setEnableTargetLocate(waiterParamsProto.getTopCamera() !=
                            DeviceParamsProtoWrapper.DeviceParamsCameraTypeProto.Type.kNone);
                } catch (InvalidProtocolBufferException e) {
                    Log.e(TAG, "parse proto err:" + e.getMessage());
                    roverConfig.setEnableTargetLocate(true);
                }
                roverConfig.setEnableLineTracking(true);
                roverConfig.setEnableCheckCliff(false);
                break;
            case RoverConfig.DEVICE_SLIM:
            case RoverConfig.DEVICE_WAITER_PRO:
                roverConfig.setEnableTargetLocate(true);
                roverConfig.setEnableLineTracking(true);
                roverConfig.setEnableCheckCliff(false);
                try {
                    DeviceParamsProtoWrapper.WaiterParamsProto waiterParamsProto =
                            DeviceParamsProtoWrapper.WaiterParamsProto.parseFrom(paramsProto.getParams());
                    if (waiterParamsProto.getStructureValue() == DeviceParamsProtoWrapper.WaiterProParamsProto.StructureType.kWaiterPro_Carry.getNumber()
                            || waiterParamsProto.getStructureValue() == DeviceParamsProtoWrapper.WaiterProParamsProto.StructureType.kWaiterPro_Carry_HS.getNumber()) {
                        Log.d(TAG, "generateDefaultRoverConfig: carry默认关闭自转速度调节");
                        roverConfig.setEnableIncreaseRotationSelfSpeed(false);
                    }
                } catch (InvalidProtocolBufferException e) {
                    throw new RuntimeException(e);
                }
                break;
            case RoverConfig.DEVICE_X86_MINI2:
            case RoverConfig.DEVICE_MEISSA2:
            case RoverConfig.DEVICE_MESSIA_PLUS:
                roverConfig.setEnableTargetLocate(false);
                roverConfig.setEnableCheckCliff(false);
                roverConfig.setPositionLostMaxRange(Definition.LOST_MAX_DISTANCE.RANGE_HIGH.getValue());
                roverConfig.setEnableLineTracking(false);
                break;
            default:
                roverConfig.setEnableTargetLocate(false);
                roverConfig.setEnableLineTracking(false);
                roverConfig.setEnableCheckCliff(false);
                break;
        }
        return roverConfig;
    }

    /**
     * 根据不同的产品线初始化相机配置信息，由于不同的产品线机器人上的相机数量也不一样
     */
    public HashMap<CommonProtoWrapper.CameraTypeProto, CameraBean> generateDiffClientCameraBean() {
        int deviceType = ConfigManager.getInstance().getDeviceTypeNumber();
        HashMap<CommonProtoWrapper.CameraTypeProto, CameraBean> cameraMap = new HashMap<>();
        CameraBean monoBean;
        CameraBean depthBean;
        CameraBean topIrBean;
        CameraBean chargeBean;
        switch (deviceType) {
            case RoverConfig.DEVICE_WAITER:
            case RoverConfig.DEVICE_WAITER_PRO:
            case RoverConfig.DEVICE_MESSIA_PLUS:
                monoBean = new CameraBean(CommonProtoWrapper.CameraTypeProto.CAMERA_TYPE_MONO_VALUE,
                        false, false);
                depthBean = new CameraBean(CommonProtoWrapper.CameraTypeProto.CAMERA_TYPE_DEPTH_VALUE,
                        true, true);
                topIrBean = new CameraBean(CommonProtoWrapper.CameraTypeProto.CAMERA_TYPE_TOP_IR_VALUE,
                        true, true);
                chargeBean = new CameraBean(CommonProtoWrapper.CameraTypeProto.CAMERA_TYPE_CHARGE_IR_VALUE,
                        false, false);
                break;
            case RoverConfig.DEVICE_X86_MINI2:
            case RoverConfig.DEVICE_MEISSA2:
                monoBean = new CameraBean(CommonProtoWrapper.CameraTypeProto.CAMERA_TYPE_MONO_VALUE,
                        true, true);
                depthBean = new CameraBean(CommonProtoWrapper.CameraTypeProto.CAMERA_TYPE_DEPTH_VALUE,
                        true, true);
                topIrBean = new CameraBean(CommonProtoWrapper.CameraTypeProto.CAMERA_TYPE_TOP_IR_VALUE,
                        false, false);
                chargeBean = new CameraBean(CommonProtoWrapper.CameraTypeProto.CAMERA_TYPE_CHARGE_IR_VALUE,
                        true, true);
                break;
            default:
                monoBean = new CameraBean(CommonProtoWrapper.CameraTypeProto.CAMERA_TYPE_MONO_VALUE,
                        true, true);
                depthBean = new CameraBean(CommonProtoWrapper.CameraTypeProto.CAMERA_TYPE_DEPTH_VALUE,
                        true, true);
                topIrBean = new CameraBean(CommonProtoWrapper.CameraTypeProto.CAMERA_TYPE_TOP_IR_VALUE,
                        false, false);
                chargeBean = new CameraBean(CommonProtoWrapper.CameraTypeProto.CAMERA_TYPE_CHARGE_IR_VALUE,
                        false, false);
                break;
        }
        cameraMap.put(CommonProtoWrapper.CameraTypeProto.CAMERA_TYPE_MONO, monoBean);
        cameraMap.put(CommonProtoWrapper.CameraTypeProto.CAMERA_TYPE_DEPTH, depthBean);
        cameraMap.put(CommonProtoWrapper.CameraTypeProto.CAMERA_TYPE_TOP_IR, topIrBean);
        cameraMap.put(CommonProtoWrapper.CameraTypeProto.CAMERA_TYPE_CHARGE_IR, chargeBean);
        return cameraMap;
    }

    /**
     * 保存多机Config信息,目前只有招财豹需要，其他产品线没有多机设备
     */
    public void storeMultiRobotConfig() {
        if (!ConfigManager.getInstance().hasMultiRobotModule()) {
            return;
        }
        String config = NavigationDataManager.getInstance().getMultiRobotConfig();
        MultiRobotConfigBean loraConfig = null;
        if (!TextUtils.isEmpty(config)) {
            Log.d(TAG, "Load stored LoraConfig : " + config);
            loraConfig = loadLocalMultiRobotConfig(config);
        }

        if (loraConfig == null) {
            Log.d(TAG, "Create default LoraConfig : " + config);
            loraConfig = creatLocalDefaultMultiRobotConfig();
        }
        Log.d(TAG, "Update lora config : " + loraConfig.toString());
        NavigationDataManager.getInstance().updateMultiRobotConfig(loraConfig);
    }

    public MultiRobotConfigBean loadMultiRobotConfig() {
        if (!ConfigManager.getInstance().hasMultiRobotModule()) {
            return null;
        }
        try {
            String config = NavigationDataManager.getInstance().getMultiRobotConfig();
            return GsonUtil.fromJson(config, MultiRobotConfigBean.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private MultiRobotConfigBean creatLocalDefaultMultiRobotConfig() {
        MultiRobotConfigBean configBean = new MultiRobotConfigBean();
        ConfigManager.MultiRobotModule module = ConfigManager.getInstance().getMultiRobotModuleName();

        Log.d(TAG, "creatLocalDefaultMultiRobotConfig module: " + module);

        if (module == ConfigManager.MultiRobotModule.Lora) {
            configBean.setEnable(false);
            configBean.setLoraId(1);
            configBean.setChannel(0);
            configBean.setMsgInterval(80);
            configBean.setTotalDeviceCnt(1);
            configBean.setRfPower(4);
            configBean.setErrorStatus(0);
            configBean.setRf_type(1); //1代表lora，2代表esp32
        } else if (module == ConfigManager.MultiRobotModule.Esp32) {
            if (ProductInfo.isMiniProduct()) {
                configBean.setEnable(false);
                configBean.setLoraId(1);
                configBean.setChannel(1);
                configBean.setMsgInterval(200);
                configBean.setTotalDeviceCnt(1);
                configBean.setRfPower(30);
                configBean.setRf_type(2);
                configBean.setErrorStatus(0);
            } else {
                configBean.setEnable(false);
                configBean.setLoraId(1);
                configBean.setChannel(1);
                configBean.setMsgInterval(100);
                configBean.setTotalDeviceCnt(1);
                configBean.setRfPower(9);
                configBean.setErrorStatus(0);
                configBean.setRf_type(2); //1代表lora，2代表esp32
            }
        }
        return configBean;
    }

    private MultiRobotConfigBean loadLocalMultiRobotConfig(String config) {
        MultiRobotConfigBean localConfig = new MultiRobotConfigBean();
        ConfigManager.MultiRobotModule module = ConfigManager.getInstance().getMultiRobotModuleName();
        Log.d(TAG, "loadLocalMultiRobotConfig module: " + module);
        try {
            JSONObject jsonObject = new JSONObject(config);

            if (module == ConfigManager.MultiRobotModule.Lora) {
                localConfig.setEnable(jsonObject.optBoolean("enable", false));
                localConfig.setLoraId(jsonObject.optInt("loraId", 1));
                localConfig.setTotalDeviceCnt(jsonObject.optInt("totalDeviceCnt", 1));
                localConfig.setMsgInterval(jsonObject.optInt("msgInterval", 80));
                localConfig.setChannel(jsonObject.optInt("channel", 0));
                localConfig.setRfPower(jsonObject.optInt("rfPower", 4));
                localConfig.setErrorStatus(jsonObject.optInt("errorStatus", 0));
                localConfig.setRf_type(jsonObject.optInt("rf_type", 1));

                //功率升级兼容
                if (localConfig.getRfPower() > 4 || localConfig.getRfPower() < 1) {
                    localConfig.setRfPower(4);
                }

            } else if (module == ConfigManager.MultiRobotModule.Esp32) {
                if (ProductInfo.isMiniProduct()) {
                    localConfig.setEnable(jsonObject.optBoolean("enable", false));
                    localConfig.setLoraId(jsonObject.optInt("loraId", 1));
                    localConfig.setTotalDeviceCnt(jsonObject.optInt("totalDeviceCnt", 1));
                    localConfig.setMsgInterval(jsonObject.optInt("msgInterval", 200));
                    localConfig.setChannel(jsonObject.optInt("channel", 1));
                    localConfig.setRfPower(jsonObject.optInt("rfPower", 30));
                    localConfig.setRf_type(jsonObject.optInt("rfType", 2));
                    localConfig.setErrorStatus(jsonObject.optInt("errorStatus", 0));
                } else {
                    localConfig.setEnable(jsonObject.optBoolean("enable", false));
                    localConfig.setLoraId(jsonObject.optInt("loraId", 1));
                    localConfig.setTotalDeviceCnt(jsonObject.optInt("totalDeviceCnt", 1));
                    localConfig.setMsgInterval(jsonObject.optInt("msgInterval", 100));
                    localConfig.setChannel(jsonObject.optInt("channel", 0));
                    localConfig.setRfPower(jsonObject.optInt("rfPower", 9));
                    localConfig.setErrorStatus(jsonObject.optInt("errorStatus", 0));
                    localConfig.setRf_type(jsonObject.optInt("rf_type", 2));

                    //功率升级兼容
                    if (localConfig.getRfPower() > 9 || localConfig.getRfPower() < 1) {
                        localConfig.setRfPower(9);
                    }
                    //信道升级兼容
                    if (localConfig.getChannel() < 1 || localConfig.getChannel() > 13) {
                        localConfig.setChannel(1);
                    }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return localConfig;
    }


}
