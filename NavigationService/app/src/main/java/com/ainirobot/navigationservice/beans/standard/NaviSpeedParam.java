package com.ainirobot.navigationservice.beans.standard;

public class NaviSpeedParam {
    double vX;
    double vTheta;
    double aX;
    double aTheta;

    public NaviSpeedParam(double vX, double vTheta, double aX, double aTheta) {
        this.vX = vX;
        this.vTheta = vTheta;
        this.aX = aX;
        this.aTheta = aTheta;
    }

    public double getvX() {
        return vX;
    }

    public void setvX(double vX) {
        this.vX = vX;
    }

    public double getvTheta() {
        return vTheta;
    }

    public void setvTheta(double vTheta) {
        this.vTheta = vTheta;
    }

    public double getaX() {
        return aX;
    }

    public void setaX(double aX) {
        this.aX = aX;
    }

    public double getaTheta() {
        return aTheta;
    }

    public void setaTheta(double aTheta) {
        this.aTheta = aTheta;
    }
}
