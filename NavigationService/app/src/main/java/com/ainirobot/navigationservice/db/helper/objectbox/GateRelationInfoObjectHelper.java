package com.ainirobot.navigationservice.db.helper.objectbox;
import android.util.Log;
import com.ainirobot.navigationservice.db.entity.GateRelationInfo;
import com.ainirobot.navigationservice.db.helper.iml.GateRelationInfoHelperIml;
import com.ainirobot.navigationservice.db.entity.GateRelationInfo_;
import java.util.List;
import io.objectbox.BoxStore;
import io.objectbox.query.Query;
import io.objectbox.query.QueryBuilder;

/**
 * 数据访问实现类
 */
public class GateRelationInfoObjectHelper extends BaseObjectHelper<GateRelationInfo> implements GateRelationInfoHelperIml {
    private static final String TAG = GateRelationInfoObjectHelper.class.getSimpleName() + "_";

    public GateRelationInfoObjectHelper(BoxStore boxStore) {
        super(boxStore);
    }

    /**
     * 根据闸机ID数组批量查询闸机关系信息
     * @param validGateIds 闸机ID字符串数组
     * @return 符合条件的闸机关系信息列表，如果没有找到则返回空列表
     */
    @Override
    public List<GateRelationInfo> findByGateIds(List<String> validGateIds) {
        QueryBuilder<GateRelationInfo> builder = getBox().query();
        for (int i = 0; i < validGateIds.size(); i++) {
            if (i == 0) {
                builder.equal(GateRelationInfo_.gateId, validGateIds.get(i), QueryBuilder.StringOrder.CASE_SENSITIVE);
            } else {
                builder.or().equal(GateRelationInfo_.gateId, validGateIds.get(i), QueryBuilder.StringOrder.CASE_SENSITIVE);
            }
        }
        Query<GateRelationInfo> query = builder.build();
        List<GateRelationInfo> results = query.find();
        query.close();
        return  results;
    }

    @Override
    public List<GateRelationInfo> getAllGateRelationData() {
        Query<GateRelationInfo> gateRelationInfoQuery = getBox().query().build();
        List<GateRelationInfo> gateRelationInfoList = gateRelationInfoQuery.find();
        gateRelationInfoQuery.close();
        return gateRelationInfoList;
    }

    /**
     * 根据闸机线ID数组批量查询闸机关系信息
     * @param validGateLineIds 闸机线ID整数数组
     * @return 符合条件的闸机关系信息列表，如果没有找到则返回空列表
     */
    @Override
    public List<GateRelationInfo> findByLineIds(List<Integer> validGateLineIds) {
        QueryBuilder<GateRelationInfo> builder = getBox().query();
        // 使用条件组构建查询
        for (int i = 0; i < validGateLineIds.size(); i++) {
            if (i == 0) {
                builder.equal(GateRelationInfo_.gateLineId, validGateLineIds.get(i));
            } else {
                builder.or().equal(GateRelationInfo_.gateLineId, validGateLineIds.get(i));
            }
        }
        Query<GateRelationInfo> query = builder.build();
        List<GateRelationInfo> results = query.find();
        query.close();
        return  results;
    }

    /**
     * 批量插入或更新闸机关系信息
     * @param validInfos 闸机关系信息集合
     */
    @Override
    public void batchInsertOrUpdateGate(List<GateRelationInfo> validInfos) {
        getBox().put(validInfos);
    }


    /**
     * 根据闸机线ID数组批量删除闸机关系信息
     * @param validGateLineIds 闸机线ID整数数组
     * @return 成功删除的记录数量
     */
    @Override
    public long deleteByLineIds(List<Integer> validGateLineIds) {
        QueryBuilder<GateRelationInfo> builder = getBox().query();
        for (int i = 0; i < validGateLineIds.size(); i++) {
            if (i == 0) {
                builder.equal(GateRelationInfo_.gateLineId, validGateLineIds.get(i));
            } else {
                builder.or().equal(GateRelationInfo_.gateLineId, validGateLineIds.get(i));
            }
        }
        Query<GateRelationInfo> query = builder.build();
        long deletedCount = query.remove();
        Log.d(TAG, "deleteByLineIds: 成功删除" + deletedCount + "条记录");
        query.close();
        return  deletedCount;
    }

    /**
     * 根据闸机ID数组批量删除闸机关系信息
     * @param validGateIds 闸机ID字符串数组
     * @return 成功删除的记录数量
     */
    @Override
    public long deleteByGateIds(List<String> validGateIds) {
        QueryBuilder<GateRelationInfo> builder = getBox().query();
        for (int i = 0; i < validGateIds.size(); i++) {
            if (i == 0) {
                builder.equal(GateRelationInfo_.gateId, validGateIds.get(i), QueryBuilder.StringOrder.CASE_INSENSITIVE);
            } else {
                builder.or().equal(GateRelationInfo_.gateId, validGateIds.get(i), QueryBuilder.StringOrder.CASE_INSENSITIVE);
            }
        }
        Query<GateRelationInfo> query = builder.build();
        long deletedCount = query.remove();
        Log.d(TAG, "deleteByGateIds: 成功删除" + deletedCount + "条记录");
        query.close();
        return deletedCount;
    }

    /**
     * 删除除指定主键列表外的所有数据
     * @param validGateLineIds 需要保留的主键ID整数数组
     * @return 被删除的闸机关系信息列表
     */
    @Override
    public List<GateRelationInfo> deleteExceptLineIds(List<Integer> validGateLineIds) {
        QueryBuilder<GateRelationInfo> builder = getBox().query();

        // 构建NOT IN查询条件
        for (int i = 0; i < validGateLineIds.size(); i++) {
            if (i == 0) {
                builder.notEqual(GateRelationInfo_.gateLineId, validGateLineIds.get(i));
            } else {
                builder.and().notEqual(GateRelationInfo_.gateLineId, validGateLineIds.get(i));
            }
        }

        Query<GateRelationInfo> query = builder.build();
        List<GateRelationInfo> deletedItems = query.find();
        long deletedCount = query.remove();
        Log.d(TAG, "deleteExceptLineIds: 成功删除" + deletedCount + "条记录，保留了指定的" + validGateLineIds.size() + "个主键");
        query.close();
        return deletedItems;
    }

}