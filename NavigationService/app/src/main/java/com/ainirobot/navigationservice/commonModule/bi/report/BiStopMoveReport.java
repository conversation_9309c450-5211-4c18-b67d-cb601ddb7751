package com.ainirobot.navigationservice.commonModule.bi.report;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

/**
 * 机器人30秒无位移上报
 */
public class BiStopMoveReport extends BaseBiReport {

    private static final String TABLE_NAME = "base_robot_stop_moving";
    private static final String CTIME = "ctime";
    private static final String ERROR_START_TIME = "errorstarttime";
    private static final String START_POSE = "startpose";
    private static final String STOP_POSE = "stoppose";

    public BiStopMoveReport() {
        super(TABLE_NAME);
    }

    public BiStopMoveReport addErrorStartTime(long startTime) {
        addData(ERROR_START_TIME, startTime);
        return this;
    }

    public BiStopMoveReport addStartPose(String startPose) {
        addData(START_POSE, startPose);
        return this;
    }

    public BiStopMoveReport addStopPose(String stopPose) {
        addData(STOP_POSE, stopPose);
        return this;
    }

    public BiStopMoveReport addCTime(long cTime) {
        addData(CTIME, cTime);
        return this;
    }

    @Override
    public void report() {
        super.report();
    }


}
