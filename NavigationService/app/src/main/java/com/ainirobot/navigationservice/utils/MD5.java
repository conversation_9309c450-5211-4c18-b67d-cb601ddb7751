package com.ainirobot.navigationservice.utils;

import android.util.Log;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class MD5 {
    static String TAG = "MD5";

    public static String createSHA1(String str) {
        String result = null;
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-1");
            messageDigest.update(str.getBytes());
            byte[] data = messageDigest.digest();
            result = toHex(data);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } finally {
            return result;
        }
    }

    public static String createMd5(String str) {
        String result = null;
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(str.getBytes());
            byte[] by = messageDigest.digest();
            result = toHex(by);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } finally {
            return result;
        }
    }

    private static String createMd5FromFile(String filePath) {
        MessageDigest mMDigest;
        FileInputStream Input;
        File file = new File(filePath);
        byte buffer[] = new byte[1024];
        int len;
        if (!file.exists())
            return null;
        try {
            mMDigest = MessageDigest.getInstance("MD5");
            Input = new FileInputStream(file);
            while ((len = Input.read(buffer, 0, 1024)) != -1) {
                mMDigest.update(buffer, 0, len);
            }
            Input.close();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return null;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        byte[] digest = mMDigest.digest();
        String result = toHex(digest);

        return result;
    }

    /**
     * 将16位byte[] 转换为32位String; byte && 0xFF 转为hex
     *
     * @param buffer
     * @return
     */
    private static String toHex(byte buffer[]) {
        StringBuffer sb = new StringBuffer(buffer.length * 2);
        for (int i = 0; i < buffer.length; i++) {
            sb.append(Character.forDigit((buffer[i] & 0xF0) >> 4, 16));
            sb.append(Character.forDigit(buffer[i] & 0x0F, 16));
        }
        return sb.toString();
    }

    public static String getFileMd5(String filePath) {
        String md5Str = createMd5FromFile(filePath);
        Log.d(TAG, "getFileMd5: md5Str=" + md5Str);
        return md5Str;
    }

    public static String getFileMd5(File file) {
        if (!file.exists()) {
            Log.d(TAG, "getFileMd5: File not exists，return null!");
            return null;
        }
        return getFileMd5(file.getAbsolutePath());
    }

}
