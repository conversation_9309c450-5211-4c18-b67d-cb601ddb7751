package com.ainirobot.navigationservice.chassisAbility.ota.client.bean;

public class BoardVersionBean {

    public String board;
    public String version;

    BoardVersionBean(){

    }

    public BoardVersionBean(String boardName, String boardVersion){
        this.board = boardName;
        this.version = boardVersion;
    }

    public String getBoard() {
        return board;
    }

    public void setBoard(String board) {
        this.board = board;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @Override
    public String toString() {
        return "BoardVersionBean{" +
                "board='" + board + '\'' +
                ", version='" + version + '\'' +
                '}';
    }
}
