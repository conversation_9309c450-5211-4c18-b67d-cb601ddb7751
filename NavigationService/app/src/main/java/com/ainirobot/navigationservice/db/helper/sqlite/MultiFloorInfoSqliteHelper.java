package com.ainirobot.navigationservice.db.helper.sqlite;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ainirobot.navigationservice.db.entity.MultiFloorInfo;
import com.ainirobot.navigationservice.db.helper.iml.MultiFloorInfoHelperIml;
import com.ainirobot.navigationservice.db.sqlite.TableInfoDef;
import com.ainirobot.navigationservice.db.sqlite.SqliteDbMigrate;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MultiFloorInfoSqliteHelper extends BaseSqliteHelper<MultiFloorInfo> implements MultiFloorInfoHelperIml {

    public MultiFloorInfoSqliteHelper(SqliteDbMigrate sqliteDbMigrate) {
        super(sqliteDbMigrate, TableInfoDef.TABLE_NAME_MULTI_FLOOR_INFO);
    }

    @Override
    protected Map<String, Integer> updateCursorIndexMap(Cursor cursor) {
        return sqliteDbMigrate.getMultiFloorInfoIndex(cursor);
    }

    @Nullable
    @Override
    public MultiFloorInfo getMultiFloorConfigByFloorId(long floorId) {
        SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
        Cursor cursor = null;
        MultiFloorInfo multiFloorInfo = null;
        try {
            cursor = readDb.query(mTableName, null, TableInfoDef.COLUMN_MULTI_FLOOR_ID + " = ?", new String[]{String.valueOf(floorId)}, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                multiFloorInfo = sqliteDbMigrate.cursorToMultiFloorInfo(cursor, getCursorIndexMap(cursor), new TypeToken<List<String>>() {
                }.getType());
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return multiFloorInfo;
    }

    @Override
    public void initMultiFloorInfoData(List<MultiFloorInfo> multiFloorInfoList) {
        updateMultiFloorInfo(multiFloorInfoList);
    }

    @NonNull
    @Override
    public List<MultiFloorInfo> getMultiFloorConfig() {
        SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
        List<MultiFloorInfo> multiFloorInfoList = new ArrayList<>();
        try (Cursor cursor = readDb.query(mTableName, null, null, null, null, null, null)) {
            if (cursor != null) {
                Map<String, Integer> map = getCursorIndexMap(cursor);
                Type type = new TypeToken<List<String>>() {
                }.getType();
                while (cursor.moveToNext()) {
                    MultiFloorInfo multiFloorInfo = sqliteDbMigrate.cursorToMultiFloorInfo(cursor, map, type);
                    multiFloorInfoList.add(multiFloorInfo);
                }
            }
        }
        return multiFloorInfoList;
    }

    @Override
    public boolean updateMultiFloorInfo(List<MultiFloorInfo> infoList) {
        if (infoList == null || infoList.isEmpty()) {
            Log.d(TAG, "updateMultiFloorInfo: infoList is null or empty");
            return false;
        }
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        writeDb.beginTransaction();
        boolean allUpdated = true;
        try {
            for (MultiFloorInfo multiFloorInfo : infoList) {
                ContentValues values = sqliteDbMigrate.multiFloorInfoToContentValues(multiFloorInfo);
                // 尝试更新数据
                int rowsUpdated = writeDb.update(
                        mTableName,
                        values,
                        TableInfoDef.COLUMN_MULTI_FLOOR_ID + " = ?",
                        new String[]{String.valueOf(multiFloorInfo.getFloorId())}
                );
                // 如果更新失败（没有记录被更新），尝试插入新记录
                if (rowsUpdated <= 0) {
                    long rowId = writeDb.insert(
                            mTableName,
                            null,
                            values
                    );
                    if (rowId == -1) {
                        allUpdated = false;
                        Log.d(TAG, "updateMultiFloorInfo: insert failed for floorId = " + multiFloorInfo);
                    }
                }
            }
            // 如果全部更新或插入成功，提交事务
            if (allUpdated) {
                writeDb.setTransactionSuccessful();
            }
        } catch (Exception e) {
            Log.e(TAG, "updateMultiFloorInfo: exception occurred", e);
            allUpdated = false;
        } finally {
            writeDb.endTransaction();
        }
        return allUpdated;
    }

    @Override
    public boolean updateMultiFloorInfo(MultiFloorInfo floorInfo) {
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        ContentValues values = sqliteDbMigrate.multiFloorInfoToContentValues(floorInfo);
        // 尝试更新数据
        int rowsUpdated = writeDb.update(
                mTableName,
                values,
                TableInfoDef.COLUMN_MULTI_FLOOR_ID + " = ?",
                new String[]{String.valueOf(floorInfo.getFloorId())});
        // 如果更新失败（没有记录被更新），尝试插入新记录
        if (rowsUpdated <= 0) {
            long rowId = writeDb.insert(mTableName, null, values);
            Log.d(TAG, "updateMultiFloorInfo: insert rowId = " + rowId + " floorInfo = " + floorInfo);
            return rowId != -1;
        }
        Log.d(TAG, "updateMultiFloorInfo: update floorInfo = " + floorInfo);
        return true;
    }

    @Override
    public boolean deleteMultiFloorInfo(MultiFloorInfo info) {
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        int rowsDeleted = writeDb.delete(mTableName, TableInfoDef.COLUMN_MULTI_FLOOR_ID + " = ?", new String[]{String.valueOf(info.getFloorId())});
        return rowsDeleted > 0;
    }
}