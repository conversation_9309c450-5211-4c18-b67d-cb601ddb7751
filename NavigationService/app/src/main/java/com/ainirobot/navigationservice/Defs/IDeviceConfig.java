package com.ainirobot.navigationservice.Defs;

public interface IDeviceConfig {

    /**
     * 最小线加速度
     */
    double getMinLinearAcceleration();

    /**
     * 最大线加速度
     */
    double getMaxLinearAcceleration();

    /**
     * 默认线加速度
     */
    double getDefaultLinearAcceleration();

    /**
     * 最小角加速度
     */
    double getMinAngularAcceleration();

    /**
     * 最大角加速度
     */
    double getMaxAngularAcceleration();

    /**
     * 默认角加速度
     */
    double getDefaultAngularAcceleration(double navLinear);
}
