package com.ainirobot.navigationservice.db.sqlite;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.ainirobot.navigationservice.Defs.Def;

/**
 * 版本历史信息
 *
 * <p>
 * place_info 创建版本 1
 * 更新版本 2: 无表字段更新，增加地图主语言兼容逻辑
 * 兼容逻辑: 把点位名称最多的语言类型作为地图主语言存储到properties中
 * 更新版本 3: 无表字段更新,只修改了版本号兼容逻辑
 * 更新版本 4: 增加表字段：create_time、sync_state、map_id,增加trigger,添加外键
 * <p>
 * place_name 创建版本 1
 * <p>
 * map_info 创建版本 4，去掉properties方式存储地图和底盘相关数据
 * <p>
 * chassis_info 创建版本 4
 * <p>
 * mapping_info 创建版本 7
 * <p>
 * multi_floor_info 创建版本 9
 * <p>
 * extraInfo 创建版本 10
 * <p>
 * 更新版本 11：map_info 表增加地图版本号字段 map_version
 */
public class MapDbOpenHelper extends SQLiteOpenHelper {
    private static final String TAG = Def.MAP_DB_PRE + MapDbOpenHelper.class.getSimpleName();

    public static final String DB_NAME = "map.db";
    public static final String BACK_DB_NAME = "backup_map.db";
    private static final int DB_VERSION = 12;
    private final SqliteDbMigrate mSqliteDbMigrate;

    private static final String TABLE_NAME_TEMP_PLACE_INFO = TableInfoDef.TABLE_NAME_PLACE_INFO + "_backup";

    public MapDbOpenHelper(Context context, SqliteDbMigrate sqliteDbMigrate) {
        super(context, DB_NAME, null, DB_VERSION);
        mSqliteDbMigrate = sqliteDbMigrate;
        Log.i(TAG, "Construct method DB_VERSION=" + DB_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        Log.d(TAG, "onCreate DB_VERSION=" + DB_VERSION);
        createPlaceInfoTable(db);
        createPlaceNameTable(db);
        createMapInfoTable(db);
        createChassisInfoTable(db);
        mSqliteDbMigrate.initNewCreateDbData(db);
        createMappingInfoTable(db);
        createMultiFloorInfoTable(db);
        createExtraInfoTable(db);
        Log.d(TAG, "onCreate: Create done");
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.d(TAG, "onUpgrade: oldVersion=" + oldVersion + " newVersion=" + newVersion);
        if (oldVersion < 4) {
            renamePlaceInfoTable(db);
            createPlaceInfoTableUpgrade(db);
            createMapInfoTableUpgrade(db);
            createChassisInfoTableUpgrade(db);
            mSqliteDbMigrate.initUpgradeDbDataVersion4(db, oldVersion, newVersion);
            dropPlaceInfoBackupTable(db);
            Log.d(TAG, "onUpgrade: Version-4 Upgrade done");
        }
        if (oldVersion < 5) {
            //MapInfo添加target信息和两个扩展字段
            mapInfoDbAddTargetData(db);
            mSqliteDbMigrate.addMapTargetState(db);
            Log.d(TAG, "onUpgrade: Version-5 Upgrade done");
        }
        if (oldVersion < 6) {
            //ChassisInfo表添加Lora配置
            addLoraConfigToChassisInfoTable(db);
            Log.d(TAG, "onUpgrade: Version-6 Upgrade done");
        }
        if (oldVersion < 7) {
            //增加映射表，记录特殊点映射配置
            createMappingInfoTable(db);
            Log.d(TAG, "onUpgrade: Version-7 Upgrade done");
        }
        if (oldVersion < 8) {
            //PlaceInfo添加两个字段
            placeInfoDbAddTargetData(db);
            Log.d(TAG, "onUpgrade: Version-8 Upgrade done");
        }
        if (oldVersion < 9) {
            //增加多楼层地图配置表
            createMultiFloorInfoTable(db);
        }
        if (oldVersion < 10) {
            //增加额外文件信息表
            createExtraInfoTable(db);
        }
        if (oldVersion < 11) {
            //map_info 表增加 map_version 字段；
            addMapVersionToMapInfoTable(db);
            Log.d(TAG, "onUpgrade: Version-11 Upgrade done");
        }
        if (oldVersion < 12) {
            //map_info 表增加 map_transit_max_width 字段；
            addMapTransitMaxWidthToMapInfoTable(db);
            Log.d(TAG, "onUpgrade: Version-12 Upgrade done");
        }
    }

    @Override
    public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.d(TAG, "onDowngrade: oldVersion=" + oldVersion + " newVersion=" + newVersion);
    }

    private void createPlaceInfoTable(SQLiteDatabase db) {
        String createPlaceInfoSql = "create table if not exists " + TableInfoDef.TABLE_NAME_PLACE_INFO + "(" +
                TableInfoDef.COLUMN_PLACE_ID + " text not null primary key," +
                TableInfoDef.COLUMN_ICON_URL + " text," +
                TableInfoDef.COLUMN_PLACE_TYPE + " integer," +
                TableInfoDef.COLUMN_PLACE_STATUS + " integer," +
                TableInfoDef.COLUMN_POINT_THETA + " real," +
                TableInfoDef.COLUMN_POINT_X + " real," +
                TableInfoDef.COLUMN_POINT_Y + " real," +
                TableInfoDef.COLUMN_UPDATE_TIME + " TimeStamp not null default (datetime('now','localtime'))," +
                TableInfoDef.COLUMN_MAP_NAME + " text," +
                TableInfoDef.COLUMN_ALIAS + " text," +
                TableInfoDef.COLUMN_CREATE_TIME + " TimeStamp not null default (datetime('now','localtime'))," +
                TableInfoDef.COLUMN_SYNC_STATE + " integer," +
                TableInfoDef.COLUMN_MAP_ID + " text not null," +
                TableInfoDef.COLUMN_IGNORE_DISTANCE + " integer," +
                TableInfoDef.COLUMN_SAFE_DISTANCE + " integer," +
                " foreign key(" + TableInfoDef.COLUMN_MAP_ID + "," + TableInfoDef.COLUMN_MAP_NAME + ") " +
                "references " + TableInfoDef.TABLE_NAME_MAP_INFO + "(" + TableInfoDef.COLUMN_MAP_ID + "," +
                TableInfoDef.COLUMN_MAP_NAME + ")" + ")";
        Log.d(TAG, "createPlaceInfoTable:sql:" + createPlaceInfoSql);
        db.execSQL(createPlaceInfoSql);
        createPlaceInfoUpdateTimeTrigger(db);
        createPlaceInfoDeleteTrigger(db);
    }

    private void createPlaceNameTable(SQLiteDatabase db) {
        String createPlaceNameSql = "create table if not exists " + TableInfoDef.TABLE_NAME_PLACE_NAME + "(" +
                TableInfoDef.COLUMN_PLACE_ID + " text," +
                TableInfoDef.COLUMN_PLACE_NAME + " text," +
                TableInfoDef.COLUMN_LANGUAGE_TYPE + " text," +
                "primary key(" + TableInfoDef.COLUMN_PLACE_ID + "," + TableInfoDef.COLUMN_LANGUAGE_TYPE + ")," +
                "foreign key(" + TableInfoDef.COLUMN_PLACE_ID + ") " + "references " +
                TableInfoDef.TABLE_NAME_PLACE_INFO + "(" + TableInfoDef.COLUMN_PLACE_ID + "))";
        Log.d(TAG, "createPlaceNameTable:sql:" + createPlaceNameSql);
        db.execSQL(createPlaceNameSql);
    }

    private void createMapInfoTable(SQLiteDatabase db) {
        String createMapInfoTableSql = "create table if not exists " + TableInfoDef.TABLE_NAME_MAP_INFO + "(" +
                TableInfoDef.COLUMN_MAP_ID + " text not null," +
                TableInfoDef.COLUMN_MAP_UUID + " text," +
                TableInfoDef.COLUMN_MAP_NAME + " text," +
                TableInfoDef.COLUMN_MAP_TYPE + " integer," +
                TableInfoDef.COLUMN_USE_STATE + " integer," +
                TableInfoDef.COLUMN_MAP_PATH + " text," +
                TableInfoDef.COLUMN_MAP_MD5 + " text," +
                TableInfoDef.COLUMN_MAP_LANGUAGE + " text," +
                TableInfoDef.COLUMN_PATROL_ROUTE + " text," +
                TableInfoDef.COLUMN_POSE_ESTIMATE + " text," +
                TableInfoDef.COLUMN_NAVI_MAP_NAME + " text," +
                TableInfoDef.COLUMN_FORBID_LINE + " integer," +
                TableInfoDef.COLUMN_SYNC_STATE + " integer," +
                TableInfoDef.COLUMN_FINISH_STATE + " integer," +
                TableInfoDef.COLUMN_HAS_TARGET_DATA + " integer," +
                TableInfoDef.COLUMN_CREATE_TIME + " TimeStamp not null default (datetime('now','localtime'))," +
                TableInfoDef.COLUMN_UPDATE_TIME + " TimeStamp not null default (datetime('now','localtime'))," +
                TableInfoDef.COLUMN_MAP_VERSION + " integer," +
                TableInfoDef.COLUMN_MAP_TRANSIT_MAX_WIDTH + " double," +
                "primary key(" + TableInfoDef.COLUMN_MAP_ID + "," + TableInfoDef.COLUMN_MAP_NAME + "))";
        Log.d(TAG, "createMapInfoTable: create table sql:" + createMapInfoTableSql);
        db.execSQL(createMapInfoTableSql);
        createMapInfoTrigger(db);
    }

    private void createChassisInfoTable(SQLiteDatabase db) {
        String createChassisInfoSql = "create table if not exists " + TableInfoDef.TABLE_NAME_CHASSIS_INFO + "(" +
                "_id integer primary key autoincrement," +
                TableInfoDef.COLUMN_ROVER_CONFIG + " text," +
                TableInfoDef.COLUMN_IP_NAVIGATION + " text," +
                TableInfoDef.COLUMN_IP_ROS + " text," +
                TableInfoDef.COLUMN_IP_SDK_ROS + " text," +
                TableInfoDef.COLUMN_SERVER_IP + " text," +
                TableInfoDef.COLUMN_LORA_CONFIG + " text" + ")";
        Log.i(TAG, "createChassisInfoTable:sql:" + createChassisInfoSql);
        db.execSQL(createChassisInfoSql);
    }

    /**
     * map_info 表添加trigger:
     * 地图信息(forbid_line,finish_state)有更新操作时，自动跟新 update_time 字段；
     * 删除某一行地图信息，自动删除 place_info 中的关联行；
     */
    private void createMapInfoTrigger(SQLiteDatabase db) {
//        String mapInfoUpdateTimeTrigger = "create trigger map_info_update_time" +
//                " after update of forbid_line, finish_state on " + TableInfoDef.TABLE_NAME_MAP_INFO +
//                " begin " +
//                "update " + TableInfoDef.TABLE_NAME_MAP_INFO + " set " +
//                TableInfoDef.COLUMN_UPDATE_TIME + " = datetime('now','localtime') where " +
//                TableInfoDef.COLUMN_MAP_ID + "=new." + TableInfoDef.COLUMN_MAP_ID + ";" +
//                " end ";
//        Log.d(TAG, "createMapInfoTable: update trigger sql:" + mapInfoUpdateTimeTrigger);
//        db.execSQL(mapInfoUpdateTimeTrigger);
        String mapInfoDeleteTrigger = "create trigger map_info_delete " +
                "before delete on " + TableInfoDef.TABLE_NAME_MAP_INFO +
                " begin " +
                "delete from " + TableInfoDef.TABLE_NAME_PLACE_INFO + " where " +
                TableInfoDef.COLUMN_MAP_NAME + "=old." + TableInfoDef.COLUMN_MAP_NAME + ";" +
                "delete from " + TableInfoDef.TABLE_NAME_MAPPING_INFO + " where " +
                TableInfoDef.COLUMN_MAP_NAME + "=old." + TableInfoDef.COLUMN_MAP_NAME + ";" +
                " end ";
        String test = "create trigger map_info_delete before delete on map_info begin delete from place_info where map_name=new.map_name; end ";
        Log.d(TAG, "createMapInfoTable: delete trigger sql:" + mapInfoDeleteTrigger);
        db.execSQL(mapInfoDeleteTrigger);
    }

    /**
     * 版本4 增加Trigger: place_info 表有更新操作时，自动跟新 update_time 字段
     */
    private void createPlaceInfoUpdateTimeTrigger(SQLiteDatabase db) {
        String placeInfoUpdateTimeTrigger = "create trigger place_info_update_time after update on " +
                TableInfoDef.TABLE_NAME_PLACE_INFO + " begin " + "update " +
                TableInfoDef.TABLE_NAME_PLACE_INFO + " set " +
                TableInfoDef.COLUMN_UPDATE_TIME + " = datetime('now','localtime') where " +
                TableInfoDef.COLUMN_PLACE_ID + "=new." + TableInfoDef.COLUMN_PLACE_ID + ";" +
                " end ";
        Log.d(TAG, "createPlaceInfoUpdateTimeTrigger:sql:" + placeInfoUpdateTimeTrigger);
        db.execSQL(placeInfoUpdateTimeTrigger);
    }

    /**
     * 版本4 增加Trigger: 删除 place_info 中某一行数据时，自动删除 place_name 中的关联行
     */
    private void createPlaceInfoDeleteTrigger(SQLiteDatabase db) {
        String placeInfoDeleteTrigger = "create trigger place_info_delete_placeid " +
                "before delete on " + TableInfoDef.TABLE_NAME_PLACE_INFO +
                " begin " +
                "delete from " + TableInfoDef.TABLE_NAME_PLACE_NAME + " where " +
                TableInfoDef.COLUMN_PLACE_ID + "=old." + TableInfoDef.COLUMN_PLACE_ID + ";" +
                " end ";
        Log.d(TAG, "createPlaceInfoDeleteTrigger:sql:" + placeInfoDeleteTrigger);
        db.execSQL(placeInfoDeleteTrigger);
    }

    /**
     * 版本4，修改 place_info 表
     * 修改 place_info 为 place_info_backup，作为数据备份
     *
     * @param db
     */
    private void renamePlaceInfoTable(SQLiteDatabase db) {
        String renamePlaceInfoTable = "alter table " + TableInfoDef.TABLE_NAME_PLACE_INFO +
                " rename to " + TABLE_NAME_TEMP_PLACE_INFO;
        Log.d(TAG, "renamePlaceInfoTable: renamePlaceInfoTable=" + renamePlaceInfoTable);
        db.execSQL(renamePlaceInfoTable);
    }

    /**
     * 删除临时表
     */
    private void dropPlaceInfoBackupTable(SQLiteDatabase db) {
        String dropPlaceInfoBackupTable = "drop table " + TABLE_NAME_TEMP_PLACE_INFO;
        Log.d(TAG, "dropPlaceInfoBackupTable: dropPlaceInfoBackupTable=" +
                dropPlaceInfoBackupTable);
        db.execSQL(dropPlaceInfoBackupTable);
    }

    /**
     * 版本5，修改 map_info 表
     * 增加TargetData配置
     *
     * @param db
     */
    private void mapInfoDbAddTargetData(SQLiteDatabase db) {
        String alterTable = "alter table " + TableInfoDef.TABLE_NAME_MAP_INFO +
                " add column " + TableInfoDef.COLUMN_HAS_TARGET_DATA + " int default(0)";
        Log.d(TAG, "mapInfoDbAddTargetData=" + alterTable);
        db.execSQL(alterTable);
    }

    /**
     * 版本6，修改 chassis_info 表
     * 增加LoraConfig配置
     *
     * @param db
     */
    private void addLoraConfigToChassisInfoTable(SQLiteDatabase db) {
        String chassisTable = "alter table " + TableInfoDef.TABLE_NAME_CHASSIS_INFO +
                " add column " + TableInfoDef.COLUMN_LORA_CONFIG + " text";
        Log.d(TAG, "addLoraConfigToChassisInfoTable=" + chassisTable);
        db.execSQL(chassisTable);
    }

    /**
     * 版本4升级，创建 place_info 表
     * <p>
     * 4之后版本升级修改 place_info,不需要修改此sql，
     * 只需要修改初始化创建sql{@link #createPlaceInfoTable(SQLiteDatabase)}
     * </>
     */
    private final void createPlaceInfoTableUpgrade(SQLiteDatabase db) {
        String createPlaceInfoSql = "create table if not exists " + TableInfoDef.TABLE_NAME_PLACE_INFO + "(" +
                TableInfoDef.COLUMN_PLACE_ID + " text not null primary key," +
                TableInfoDef.COLUMN_ICON_URL + " text," +
                TableInfoDef.COLUMN_PLACE_TYPE + " integer," +
                TableInfoDef.COLUMN_PLACE_STATUS + " integer," +
                TableInfoDef.COLUMN_POINT_THETA + " real," +
                TableInfoDef.COLUMN_POINT_X + " real," +
                TableInfoDef.COLUMN_POINT_Y + " real," +
                TableInfoDef.COLUMN_UPDATE_TIME + " TimeStamp not null default (datetime('now','localtime'))," +
                TableInfoDef.COLUMN_MAP_NAME + " text," +
                TableInfoDef.COLUMN_ALIAS + " text," +
                TableInfoDef.COLUMN_CREATE_TIME + " TimeStamp not null default (datetime('now','localtime'))," +
                TableInfoDef.COLUMN_SYNC_STATE + " integer," +
                TableInfoDef.COLUMN_MAP_ID + " text not null," +
                " foreign key(" + TableInfoDef.COLUMN_MAP_ID + "," + TableInfoDef.COLUMN_MAP_NAME + ") " +
                "references " + TableInfoDef.TABLE_NAME_MAP_INFO + "(" + TableInfoDef.COLUMN_MAP_ID + "," +
                TableInfoDef.COLUMN_MAP_NAME + ")" + ")";
        Log.d(TAG, "createPlaceInfoTable:sql:" + createPlaceInfoSql);
        db.execSQL(createPlaceInfoSql);
        createPlaceInfoUpdateTimeTrigger(db);
        createPlaceInfoDeleteTrigger(db);
    }

    /**
     * 版本4升级，创建 chassis_info 表
     * <p>
     * 4之后版本升级修改 chassis_info,不需要修改此sql，
     * 只需要修改初始化创建sql{@link #createChassisInfoTable(SQLiteDatabase)}
     * </>
     */
    private final void createChassisInfoTableUpgrade(SQLiteDatabase db) {
        String createChassisInfoSql = "create table if not exists " + TableInfoDef.TABLE_NAME_CHASSIS_INFO + "(" +
                "_id integer primary key autoincrement," +
                TableInfoDef.COLUMN_ROVER_CONFIG + " text," +
                TableInfoDef.COLUMN_IP_NAVIGATION + " text," +
                TableInfoDef.COLUMN_IP_ROS + " text," +
                TableInfoDef.COLUMN_IP_SDK_ROS + " text," +
                TableInfoDef.COLUMN_SERVER_IP + " text" + ")";
        Log.i(TAG, "createChassisInfoTableUpdate:sql:" + createChassisInfoSql);
        db.execSQL(createChassisInfoSql);
    }

    /**
     * 版本4升级，创建 map_info 表
     * <p>
     * 4之后版本升级修改 map_info,不需要修改此sql，
     * 只需要修改初始化创建sql{@link #createMapInfoTable(SQLiteDatabase)}
     * </>
     */
    private final void createMapInfoTableUpgrade(SQLiteDatabase db) {
        String createMapInfoTableSql = "create table if not exists " + TableInfoDef.TABLE_NAME_MAP_INFO + "(" +
                TableInfoDef.COLUMN_MAP_ID + " text not null," +
                TableInfoDef.COLUMN_MAP_UUID + " text," +
                TableInfoDef.COLUMN_MAP_NAME + " text," +
                TableInfoDef.COLUMN_MAP_TYPE + " integer," +
                TableInfoDef.COLUMN_USE_STATE + " integer," +
                TableInfoDef.COLUMN_MAP_PATH + " text," +
                TableInfoDef.COLUMN_MAP_MD5 + " text," +
                TableInfoDef.COLUMN_MAP_LANGUAGE + " text," +
                TableInfoDef.COLUMN_PATROL_ROUTE + " text," +
                TableInfoDef.COLUMN_POSE_ESTIMATE + " text," +
                TableInfoDef.COLUMN_NAVI_MAP_NAME + " text," +
                TableInfoDef.COLUMN_FORBID_LINE + " integer," +
                TableInfoDef.COLUMN_SYNC_STATE + " integer," +
                TableInfoDef.COLUMN_FINISH_STATE + " integer," +
                TableInfoDef.COLUMN_CREATE_TIME + " TimeStamp not null default (datetime('now','localtime'))," +
                TableInfoDef.COLUMN_UPDATE_TIME + " TimeStamp not null default (datetime('now','localtime'))," +
                "primary key(" + TableInfoDef.COLUMN_MAP_ID + "," + TableInfoDef.COLUMN_MAP_NAME + "))";
        Log.d(TAG, "createMapInfoTableUpgrade: create table sql:" + createMapInfoTableSql);
        db.execSQL(createMapInfoTableSql);
        createMapInfoTrigger(db);
    }

    private void createMappingInfoTable(SQLiteDatabase db) {
        String createMappingInfoSql = "create table if not exists " + TableInfoDef.TABLE_NAME_MAPPING_INFO + "(" +
                TableInfoDef.COLUMN_MAP_NAME + " text," +
                TableInfoDef.COLUMN_PLACE_TYPE + " integer," +
                TableInfoDef.COLUMN_PLACE_CN_NAME + " text," +
                TableInfoDef.COLUMN_PLACE_ID + " text," +
                TableInfoDef.COLUMN_MAPPING_POSE_ID + " text," +
                TableInfoDef.COLUMN_POSE_PRIORITY + " integer," +
                "primary key(" + TableInfoDef.COLUMN_PLACE_ID + "," + TableInfoDef.COLUMN_POSE_PRIORITY + "))";
        Log.d(TAG, "createMappingInfoTable:sql:" + createMappingInfoSql);
        db.execSQL(createMappingInfoSql);
    }

    /**
     * 版本8，修改 place_info 表
     * 增加TargetData配置
     *
     * @param db
     */
    private void placeInfoDbAddTargetData(SQLiteDatabase db) {
        String alterTable = "alter table " + TableInfoDef.TABLE_NAME_PLACE_INFO +
                " add column " + TableInfoDef.COLUMN_IGNORE_DISTANCE + " int default(0)";
        Log.d(TAG, "placeInfoDbAddTargetData=" + alterTable);
        db.execSQL(alterTable);

        alterTable = "alter table " + TableInfoDef.TABLE_NAME_PLACE_INFO +
                " add column " + TableInfoDef.COLUMN_SAFE_DISTANCE + " int default(10)";
        Log.d(TAG, "placeInfoDbAddTargetData=" + alterTable);
        db.execSQL(alterTable);
    }

    /**
     * 版本8，添加多楼层配置信息表
     */
    private void createMultiFloorInfoTable(SQLiteDatabase db) {
        String createMultiFloorInfoSql = "create table if not exists " + TableInfoDef.TABLE_NAME_MULTI_FLOOR_INFO + "(" +
                TableInfoDef.COLUMN_MULTI_FLOOR_ID + " integer primary key autoincrement," +
                TableInfoDef.COLUMN_MULTI_FLOOR_INDEX + " integer UNIQUE," +
                TableInfoDef.COLUMN_MULTI_FLOOR_ALIAS + " text," +
                TableInfoDef.COLUMN_MULTI_FLOOR_STATE + " integer," +
                TableInfoDef.COLUMN_MULTI_MAP_ID + " text," +
                TableInfoDef.COLUMN_MULTI_MAP_NAME + " text," +
                TableInfoDef.COLUMN_MULTI_MAP_UPDATE_TIME + " TimeStamp," +
                TableInfoDef.COLUMN_MULTI_CREATE_TIME + " TimeStamp not null default (datetime('now','localtime'))," +
                TableInfoDef.COLUMN_MULTI_UPDATE_TIME + " TimeStamp not null default (datetime('now','localtime'))," +
                TableInfoDef.COLUMN_MULTI_AVAILABLE_ELEVATORS + " text," +
                "foreign key(" + TableInfoDef.COLUMN_MULTI_MAP_ID + "," + TableInfoDef.COLUMN_MULTI_MAP_NAME + ")" +
                "references " + TableInfoDef.TABLE_NAME_MAP_INFO + "(" + TableInfoDef.COLUMN_MAP_ID + "," +
                TableInfoDef.COLUMN_MAP_NAME + ")" + ")";
        Log.d(TAG, "createMultiFloorInfoTable sql:" + createMultiFloorInfoSql);
        db.execSQL(createMultiFloorInfoSql);
    }

    private void createExtraInfoTable(SQLiteDatabase db) {
        String createMultiFloorInfoSql = "create table if not exists " + TableInfoDef.TABLE_NAME_EXTRA_INFO + "(" +
                TableInfoDef.COLUMN_VISION_MAP_NAME + " text not null," +
                TableInfoDef.COLUMN_VISION_ID + " text," +
                TableInfoDef.COLUMN_VISION_MD5 + " text," +
                "primary key(" + TableInfoDef.COLUMN_VISION_MAP_NAME + "))";
        Log.d(TAG, "createExtraInfoTable sql:" + createMultiFloorInfoSql);
        db.execSQL(createMultiFloorInfoSql);
    }

    /**
     * 版本11，修改 map_info 表
     * 增加 map_version 字段
     *
     * @param db
     */
    private void addMapVersionToMapInfoTable(SQLiteDatabase db) {
        String chassisTable = "alter table " + TableInfoDef.TABLE_NAME_MAP_INFO +
                " add column " + TableInfoDef.COLUMN_MAP_VERSION + " integer";
        Log.d(TAG, "addMapVersionToMapInfoTable=" + chassisTable);
        db.execSQL(chassisTable);
    }

    /**
     * 版本12，修改 map_info 表
     * 增加 map_transit_max_width 字段
     *
     * @param db
     */
    private void addMapTransitMaxWidthToMapInfoTable(SQLiteDatabase db) {
        String chassisTable = "alter table " + TableInfoDef.TABLE_NAME_MAP_INFO +
                " add column " + TableInfoDef.COLUMN_MAP_TRANSIT_MAX_WIDTH + " double";
        Log.d(TAG, "addMapTransitMaxWidthToMapInfoTable=" + chassisTable);
        db.execSQL(chassisTable);
    }

}
