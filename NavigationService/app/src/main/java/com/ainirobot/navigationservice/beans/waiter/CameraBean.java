package com.ainirobot.navigationservice.beans.waiter;

public class CameraBean {

    private int cameraType;
    private boolean available;
    private boolean enableState;

    public CameraBean() {

    }

    public CameraBean(int cameraType, boolean available, boolean enableState) {
        this.cameraType = cameraType;
        this.available = available;
        this.enableState = enableState;
    }

    public int getCameraType() {
        return cameraType;
    }

    public void setCameraType(int cameraType) {
        this.cameraType = cameraType;
    }

    public boolean isAvailable() {
        return available;
    }

    public void setAvailable(boolean available) {
        this.available = available;
    }

    public boolean isEnableState() {
        return enableState;
    }

    public void setEnableState(boolean enableState) {
        this.enableState = enableState;
    }

    @Override
    public String toString() {
        return "CameraBean{" +
                "cameraType='" + cameraType + '\'' +
                ", available=" + available +
                ", enableState=" + enableState +
                '}';
    }

    //    private boolean monoCameraEnable;
//    private boolean depthCameraEnable;
//    private boolean topIrCameraEnable;
//    private boolean chargeIrCameraEnable;
//
//    public CameraBean(){
//
//    }
//
//    public CameraBean(boolean monoEnable, boolean depthEnable, boolean topIrEnable, boolean chargeIrEnable){
//        this.monoCameraEnable = monoEnable;
//        this.depthCameraEnable = depthEnable;
//        this.topIrCameraEnable = topIrEnable;
//        this.chargeIrCameraEnable = chargeIrEnable;
//    }
//
//    public boolean isMonoCameraEnable() {
//        return monoCameraEnable;
//    }
//
//    public void setMonoCameraEnable(boolean monoCameraEnable) {
//        this.monoCameraEnable = monoCameraEnable;
//    }
//
//    public boolean isDepthCameraEnable() {
//        return depthCameraEnable;
//    }
//
//    public void setDepthCameraEnable(boolean depthCameraEnable) {
//        this.depthCameraEnable = depthCameraEnable;
//    }
//
//    public boolean isTopIrCameraEnable() {
//        return topIrCameraEnable;
//    }
//
//    public void setTopIrCameraEnable(boolean topIrCameraEnable) {
//        this.topIrCameraEnable = topIrCameraEnable;
//    }
//
//    public boolean isChargeIrCameraEnable() {
//        return chargeIrCameraEnable;
//    }
//
//    public void setChargeIrCameraEnable(boolean chargeIrCameraEnable) {
//        this.chargeIrCameraEnable = chargeIrCameraEnable;
//    }
//
//    @Override
//    public String toString() {
//        return "CameraBean{" +
//                "monoCameraEnable=" + monoCameraEnable +
//                ", depthCameraEnable=" + depthCameraEnable +
//                ", topIrCameraEnable=" + topIrCameraEnable +
//                ", chargeIrCameraEnable=" + chargeIrCameraEnable +
//                '}';
//    }
}
