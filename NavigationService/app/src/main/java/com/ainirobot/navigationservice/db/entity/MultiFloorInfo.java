package com.ainirobot.navigationservice.db.entity;

import com.ainirobot.coreservice.client.actionbean.Pose;

import java.util.List;

import io.objectbox.annotation.Entity;
import io.objectbox.annotation.Id;
import io.objectbox.annotation.Transient;

/**
 * 如果修改数据库字段，CoreService中的MultiFloorInfo也需要修改，否则该字段使用不到
 * 多楼层配置信息
 */
@Entity
public class MultiFloorInfo {
    //主键
    @Id
    public long floorId;
    //楼层index映射;lora发送给梯控时映射的id
    private int floorIndex;
    //楼层名称;对应真实的楼层名称，如二楼、大厅
    private String floorAlias;
    //楼层类型
    private int floorState;
    //地图名称
    private String mapName;
    //地图中使用的电梯;list不会为空
    private List<String> availableElevators;
    //是否启用电梯等待点
    private int isUseElevatorWaitPoint;
    //电梯等待点
    private List<String> elevatorWaitPoints;
    //电梯等待区
    private String elevatorWaitAreaJson;
    @Transient
    private List<Pose> poseList;

    public MultiFloorInfo() {
    }

    public long getFloorId() {
        return this.floorId;
    }

    public void setFloorId(long floorId) {
        this.floorId = floorId;
    }

    public int getFloorIndex() {
        return this.floorIndex;
    }

    public void setFloorIndex(int floorIndex) {
        this.floorIndex = floorIndex;
    }

    public String getFloorAlias() {
        return this.floorAlias;
    }

    public void setFloorAlias(String floorAlias) {
        this.floorAlias = floorAlias;
    }

    public String getMapName() {
        return this.mapName;
    }

    public void setMapName(String mapName) {
        this.mapName = mapName;
    }

    public int getFloorState() {
        return this.floorState;
    }

    public void setFloorState(int floorState) {
        this.floorState = floorState;
    }

    public List<String> getAvailableElevators() {
        return this.availableElevators;
    }

    public void setAvailableElevators(List<String> availableElevators) {
        this.availableElevators = availableElevators;
    }
    public int getIsUseElevatorWaitPoint() {
        return this.isUseElevatorWaitPoint;
    }

    public void setIsUseElevatorWaitPoint(int isUse) {
        this.isUseElevatorWaitPoint = isUse;
    }

    public List<String> getElevatorWaitPoints() {
        return this.elevatorWaitPoints;
    }

    public void setElevatorWaitPoints(List<String> elevatorWaitPoints) {
        this.elevatorWaitPoints = elevatorWaitPoints;
    }

    public String getElevatorWaitAreaJson() {
        return this.elevatorWaitAreaJson;
    }

    public void setElevatorWaitAreaJson(String elevatorWaitAreaJson) {
        this.elevatorWaitAreaJson = elevatorWaitAreaJson;
    }

    public List<Pose> getPoseList() {
        return this.poseList;
    }

    public void setPoseList(List<Pose> poseList) {
        this.poseList = poseList;
    }

    @Override
    public String toString() {
        return "MultiFloorInfo{floorId=" + this.floorId + ", floorIndex=" + this.floorIndex + ", floorAlias='" + this.floorAlias + '\'' + ", floorState=" + this.floorState + ", mapName='" + this.mapName + '\'' + ", availableElevators=" + this.availableElevators + ", poseList=" + this.poseList + '}';
    }
}
