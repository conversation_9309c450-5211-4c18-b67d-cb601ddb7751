package com.ainirobot.navigationservice.chassisAbility.ota.cmdParser;

import com.ainirobot.navigationservice.chassisAbility.ota.connector.IOtaConnect;

/**
 * cmd 就是一个个原子的command，只管发送和接受，不做业务逻辑处理
 */
public interface IOtaCommand {
    void init();

    boolean isOtaConnected();

    void injectConnector(IOtaConnect connector);

    void sendGetVersionCmd(CmdResListener listener);

    void sendGetUpdateParamsCmd(CmdResListener listener);

    void sendStartUpdateCmd(String otaInfo, CmdResListener listener);

    void sendUpdatePackage(String path);

    void registerEventListener(String type, CmdEventListener listener);

    void unRegisterEventListener(String type);

    interface CmdEventListener {
        void onEvent(Object param);
    }

    interface CmdResListener {
        void onResponse(String response);
    }

    interface OnCmdCnnListener {
        void onConnected();

        void onDisconnected(String channelName);
    }
}
