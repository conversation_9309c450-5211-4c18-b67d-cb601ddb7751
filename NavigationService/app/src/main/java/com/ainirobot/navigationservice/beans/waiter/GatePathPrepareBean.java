package com.ainirobot.navigationservice.beans.waiter;

import com.ainirobot.coreservice.client.actionbean.GateLineUnit;

import java.util.List;

public class GatePathPrepareBean {
    private   final List<GateLineUnit> gateLines;
    private  final List<NaviPathDetail> pathDetailList;

    public GatePathPrepareBean(List<GateLineUnit> gateLines, List<NaviPathDetail> pathDetailList) {
        this.gateLines = gateLines;
        this.pathDetailList = pathDetailList;
    }

    public List<GateLineUnit> getGateLines() {
        return gateLines;
    }

    public List<NaviPathDetail> getPathDetailList() {
        return pathDetailList;
    }
}