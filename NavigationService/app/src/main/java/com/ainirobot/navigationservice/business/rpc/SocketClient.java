/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.business.rpc;

import android.util.Log;

import com.ainirobot.navigationservice.beans.tk1.Message;
import com.ainirobot.navigationservice.utils.IOUtils;
import com.ainirobot.navigationservice.utils.NavigationConfig;

import java.io.DataOutputStream;
import java.io.IOException;
import java.net.Socket;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;


public class SocketClient {
    private final static String TAG = TAGPRE + SocketClient.class.getSimpleName();
    private static final int STATUS_CONNECTED = 0;
    private static final int STATUS_CLOSED = 1;
    private static final int STATUS_CONNECTING = -1;

    private static final Object RECONNECT_LOCK = new Object();

    private Socket mSocket;
    private BlockingQueue<Message> mSendQueue = new LinkedBlockingQueue<>(2);
    private String mHost;
    private int mPort;
    private Thread mSendThread;
    private int mStatus = STATUS_CONNECTING;

    private class SendThread extends Thread {
        @Override
        public void run() {
            DataOutputStream out = null;
            try {
                out = new DataOutputStream(mSocket.getOutputStream());
                while (!isInterrupted()) {
                    Message message = mSendQueue.take();
                    message.writeTo(out, true);
                    out.flush();
                    Log.i(TAG, "send map data: "+message);
                }
                Log.i(TAG, "send map date thread finish");
            } catch (InterruptedException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
                if (!isInterrupted()) {
                    reconnect();
                }
            } finally {
                IOUtils.close(out);
            }
        }
    }

    private class ServerThread extends Thread {

        @Override
        public void run() {
            connect();
        }
    }

    public SocketClient(String host, int port) {
        this.mHost = host;
        this.mPort = port;

        ServerThread serverThread = new ServerThread();
        serverThread.start();
    }

    public SocketClient(int port) {
        this(NavigationConfig.getEth0Ip(), port);
    }


    public boolean sendMessage(Message message) {
        Log.i(TAG, "sendMessage: ");
        return message != null && isConnected() && mSendQueue.offer(message);
    }

    private synchronized void connect() {
        Log.i(TAG, "connect: ");
        if (isConnected()) {
            return;
        }
        try {
            this.mSocket = new Socket(mHost, mPort);
            Log.d(TAG, "connect: Status socket connected: "
                    + ",port=" + mPort + ",host=" + mHost);
            mStatus = STATUS_CONNECTED;

            mSendThread = new SendThread();
            mSendThread.start();
        } catch (IOException e) {
            delayReconnect();
            e.printStackTrace();
        }
    }

    public void close() {
        mStatus = STATUS_CLOSED;
        mSendQueue.clear();
        destroy();
    }

    private void destroy() {
        IOUtils.close(mSocket);
        if (mSendThread != null) {
            mSendThread.interrupt();
            mSendThread = null;
        }
    }

    private synchronized void reconnect() {
        Log.i(TAG, "reconnect: ");
        mStatus = STATUS_CONNECTING;
        synchronized (RECONNECT_LOCK) {
            if (isConnected()) {
                return;
            }
            destroy();
            connect();
        }
    }

    public boolean isConnected() {
        Log.i(TAG, "isConnected: "+ (mStatus == STATUS_CONNECTED));
        return mStatus == STATUS_CONNECTED;
    }

    private void delayReconnect() {
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                connect();
            }
        }, 2000L);
    }
}
