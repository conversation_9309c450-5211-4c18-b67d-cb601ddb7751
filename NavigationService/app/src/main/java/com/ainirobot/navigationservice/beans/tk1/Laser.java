package com.ainirobot.navigationservice.beans.tk1;

public class Laser {
    private double angle;
    private double distance;

    public Laser(double angle, double distance) {
        this.angle = angle;
        this.distance = distance;
    }

    public double getAngle() {
        return angle;
    }

    public void setAngle(double angle) {
        this.angle = angle;
    }

    public double getDistance() {
        return distance;
    }

    public void setDistance(double distance) {
        this.distance = distance;
    }

    @Override
    public String toString() {
        return "angle=" + angle + "(" + Math.toDegrees(angle) + ")" +" distance=" + distance;
    }
}
