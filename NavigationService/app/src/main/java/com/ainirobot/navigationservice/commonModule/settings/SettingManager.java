package com.ainirobot.navigationservice.commonModule.settings;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.icu.text.DecimalFormat;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingListener;
import com.ainirobot.navigationservice.ApplicationWrapper;
import com.ainirobot.navigationservice.Defs.Def;
import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.chassisAbility.ChassisManager;
import com.ainirobot.navigationservice.chassisAbility.MapOutsideManager;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.commonModule.bi.BiManager;
import com.ainirobot.navigationservice.commonModule.configuration.ConfigManager;
import com.ainirobot.navigationservice.commonModule.data.DataManager;


import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

//TimeManager + RobotSettingManager
public class SettingManager {
    private final static String TAG = TAGPRE + SettingManager.class.getSimpleName();
    private boolean pendingIsEnable = true;
    private RoverConfig roverConfig;

    private static final String ACTION_TIME_SET = "android.intent.action.TIME_SET";
    private static final long SPECIAL_YEAR = 946656000000L;//2000/1/1
    private static final String DEFAULT_SERVER_HOST = "multi-robots.ainirobot.com";

    private Context mContext;
    private static SettingManager mInstance;

    private SettingManager() {
    }

    public static synchronized SettingManager getInstance() {
        if (mInstance == null) {
            mInstance = new SettingManager();
        }
        return mInstance;
    }

    public void init(Context mContext) {
        this.mContext = mContext;

        registerListener();
        startTimeSetReceiver();
    }

    /**
     * 每一次连接成功后初始化配置。
     */
    public void initRoverConfig() {
        Log.d(TAG, "[obstacles avoid] init");
        isEnableObstaclesAvoidCurrent(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (status && result != null && result instanceof RoverConfig) {
                    roverConfig = (RoverConfig) result;
                    //这里必须使用"|"保证所有的process都要执行才行
                    if (processUpdateAvoidStopDetectionDistanceConfig(roverConfig)
                            | processUpdateEnablePreventCollision()
                            | processUpdateDeviceType(roverConfig)
                            | processUpdateMultiState(roverConfig)
                            | processUpdateServerIp(roverConfig)
                            | processCheckCliff(roverConfig)
                            | processLostMaxDistanceRange(roverConfig)) {
                        ChassisManager.getInstance().getChassisClient().setRoverConfig(roverConfig, null);
                    }
                }
            }
        });
    }

    /**
     * 监听　robot_settings_lost_max_distance_range　配置项的更新
     *
     * @param roverConfig
     * @return
     */
    private boolean processLostMaxDistanceRange(RoverConfig roverConfig) {
        float range = RobotSettingApi.getInstance().getRobotFloat(Definition.ROBOT_SETTINGS_LOST_MAX_DISTANCE_RANGE);
        if (range == roverConfig.getPositionLostMaxRange()) {
            Log.d(TAG, "processLostMaxDistanceRange: same to old");
            return false;
        }
        boolean b = checkDistanceRange(range);
        Log.d(TAG, "processLostMaxDistanceRange b :" + b + " , range :" + range);
        roverConfig.setPositionLostMaxRange(b ? range : Definition.LOST_MAX_DISTANCE.RANGE_HIGH.getValue());
        return true;
    }

    private boolean checkDistanceRange(float distanceRange) {
        return distanceRange == Definition.LOST_MAX_DISTANCE.RANGE_SUPER_LOW.getValue()
                || distanceRange == Definition.LOST_MAX_DISTANCE.RANGE_LOW.getValue()
                || distanceRange == Definition.LOST_MAX_DISTANCE.RANGE_MIDDLE.getValue()
                || distanceRange == Definition.LOST_MAX_DISTANCE.RANGE_HIGH.getValue();
    }

    private boolean processUpdateDeviceType(RoverConfig roverConfig) {
        int deviceTypeNumber = ConfigManager.getInstance().getDeviceTypeNumber();
        Log.d(TAG, "roverconfig deviceTypeNumber = " + deviceTypeNumber);
        roverConfig.setDeviceType(deviceTypeNumber);
        return true;
    }

    /**
     * 5.11版本config只有多机一个开关状态，5.15版本有多机、防卡死、防碰撞三个开关。
     * 如果低版本升级到5.15版本，多机打开情况下，防碰撞防卡死是关闭状态，则需要将防碰撞打开。
     * 因为5.15版本下，防碰撞防卡死都关闭状态下，多机也会是关闭状态。
     */
    private boolean processUpdateMultiState(RoverConfig roverConfig) {
        boolean isEnableMulti = roverConfig.isEnableMultiRobot();
        boolean isEnableCollision = roverConfig.isEnableCollisionAvoid();
        boolean isEnableStuck = roverConfig.isEnableStuckAvoid();
        Log.d(TAG, "processUpdateMultiState isEnableMulti:" + isEnableMulti
                + " isEnableCollision:" + isEnableCollision + " isEnableStuck:" + isEnableStuck);
        if (isEnableMulti && !isEnableCollision && !isEnableStuck) {
            roverConfig.setEnableCollisionAvoid(true);
            return true;
        }
        return false;
    }

    /**
     * 底盘制定规则，机器人多机域名要写死固定值multi-robots.ainirobot.com
     * 如果机器人之前版本设置的有多机IP或域名，则强制更改为multi-robots.ainirobot.com
     */
    private boolean processUpdateServerIp(RoverConfig roverConfig) {
        String ip = roverConfig.getServerIp();
        Log.d(TAG, "processUpdateServerIp = " + ip);
        if (!TextUtils.isEmpty(ip) && DEFAULT_SERVER_HOST.equals(ip)) {
            return false;
        }
        roverConfig.setServerIp(DEFAULT_SERVER_HOST);
        return true;
    }

    private boolean processUpdateAvoidStopDetectionDistanceConfig(RoverConfig roverConfig) {
        double avoidStopDetectionDistance = getAvoidStopDetectionDistance();
        if (roverConfig.getAvoidStopDetectionDistance() == avoidStopDetectionDistance) {
            return false;
        }

        roverConfig.setAvoidStopDetectionDistance(avoidStopDetectionDistance);
        return true;
    }

    /**
     * 初始化SettingsManager 需要注册监听时间变化
     */
    public void startTimeSetReceiver() {
        IntentFilter timeSetFilter = new IntentFilter(ACTION_TIME_SET);
        mContext.registerReceiver(new TimeSetReceiver(), timeSetFilter);
    }

    public void syncTimeToTk(long currTime) {
        if (currTime > SPECIAL_YEAR) {
            Log.d(TAG, "sync time to tk:" + currTime);
            ChassisManager.getInstance().getChassisClient().setTime(currTime, null);
        }
    }


    private RobotSettingListener mRobotSettingListener = new RobotSettingListener() {

        @Override
        public void onRobotSettingChanged(String key) {
            Log.d(TAG, key + " changed");

            switch (key) {
                case Definition.ROBOT_SETTINGS_OBSTACLES_AVOID_DISTANCE:
                case Definition.ROBOT_OBSTACLES_AVOID:
                case Definition.ROBOT_PREVENT_COLLISION:
                case Definition.ROBOT_SETTINGS_LOST_MAX_DISTANCE_RANGE:
                    isEnableObstaclesAvoidCurrent(mConfigListener);
                    break;
                case Definition.ROBOT_SETTING_MAP_OUTSIDE_WARNING:
                    MapOutsideManager.getInstance().onMapOutsideWarningChanged(
                            RobotSettingApi.getInstance()
                                    .getRobotInt(Definition.ROBOT_SETTING_MAP_OUTSIDE_WARNING));
                    break;
                case Definition.ROBOT_SETTING_BEING_PUSHED_WARNING:
                    MapOutsideManager.getInstance().onBeingPushedWarningChanged(
                            RobotSettingApi.getInstance()
                                    .getRobotInt(Definition.ROBOT_SETTING_BEING_PUSHED_WARNING));
                    break;
                case Definition.ROBOT_CHECK_CLIFF:
                    handleCheckCliffSettingChange();
                    break;
                default:
                    break;
            }
        }
    };

    private void handleCheckCliffSettingChange() {
        Log.d(TAG, "handleCheckCliffSettingChange:");
        ChassisManager.getInstance().getChassisClient().getRoverConfig(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                if (status && result != null && result instanceof RoverConfig) {
                    roverConfig = (RoverConfig) result;
                    Log.d(TAG, "handleCheckCliffSettingChange: roverConfig=" +
                            roverConfig.toString());
                    if (processCheckCliff(roverConfig)) {
                        ChassisManager.getInstance().getChassisClient().
                                setRoverConfig(roverConfig, null);
                    }
                }
            }
        });
    }

    private boolean processCheckCliff(RoverConfig roverConfig) {
        boolean configCheckCliff = roverConfig.isEnableCheckCliff();
        int checkCliff = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_CHECK_CLIFF);
        Log.d(TAG, "processCheckCliff: checkCliff=" + checkCliff);
        boolean settingCheckCliff = checkCliff == Definition.ROBOT_SETTING_ENABLE;
        Log.d(TAG, "processCheckCliff: configCheckCliff=" +
                configCheckCliff + " settingCheckCliff=" + settingCheckCliff);
        if (configCheckCliff == settingCheckCliff) {
            return false;
        }
        roverConfig.setEnableCheckCliff(settingCheckCliff);
        return true;
    }

    private IChassisClient.ChassisResListener mConfigListener = new IChassisClient.ChassisResListener() {
        @Override
        public void onResponse(boolean status, int resultCode, Object result) {
            if (status && result != null && result instanceof RoverConfig) {
                roverConfig = (RoverConfig) result;
                double currentAvoidStopDetectionDistance = roverConfig.getAvoidStopDetectionDistance();
                double avoidStopDetectionDistance = getAvoidStopDetectionDistance();
                boolean needUpdateConfig = false;
                Log.d(TAG, "[detection distance] currentAvoidStopDetectionDistance: " + currentAvoidStopDetectionDistance
                        + ", avoidStopDetectionDistance: " + avoidStopDetectionDistance);

                if (processUpdateAvoidStopDetectionDistanceConfig(roverConfig)) {
                    needUpdateConfig = true;
                }

                if (processUpdateEnablePreventCollision()) {
                    needUpdateConfig = true;
                }

                if (processLostMaxDistanceRange(roverConfig)){
                    needUpdateConfig = true;
                }

                if (needUpdateConfig) {
                    Log.d(TAG, "[obstacles avoid] change to : " + pendingIsEnable + ", avoidStopDetectionDistance: " + avoidStopDetectionDistance);
                    ChassisManager.getInstance().getChassisClient().setRoverConfig(roverConfig, null);

                }
            }
        }
    };

    private double getAvoidStopDetectionDistance() {
        float obstaclesDistance = RobotSettingApi.getInstance().getRobotFloat(
                Definition.ROBOT_SETTINGS_OBSTACLES_AVOID_DISTANCE);

        DecimalFormat decimalFormat = new DecimalFormat(".0");
        return Double.valueOf(decimalFormat.format(obstaclesDistance));
    }

    public void registerListener() {
        RobotSettingApi.getInstance().registerRobotSettingListener(mRobotSettingListener,
                Definition.ROBOT_OBSTACLES_AVOID,
                Definition.ROBOT_SETTINGS_OBSTACLES_AVOID_DISTANCE,
                Definition.ROBOT_PREVENT_COLLISION,
                Definition.ROBOT_SETTING_MAP_OUTSIDE_WARNING,
                Definition.ROBOT_SETTING_BEING_PUSHED_WARNING,
                Definition.ROBOT_CHECK_CLIFF,
                Definition.ROBOT_SETTINGS_LOST_MAX_DISTANCE_RANGE);
    }

    public void unregisterListener() {
        RobotSettingApi.getInstance().unRegisterRobotSettingListener(mRobotSettingListener);
    }

    public String getChassisType() {
        return Def.CLIENT_TK1;
    }

    private void isEnableObstaclesAvoidCurrent(final IChassisClient.ChassisResListener listener) {
        ChassisManager.getInstance().getChassisClient().getRoverConfig(new IChassisClient.ChassisResListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                listener.onResponse(status, resultCode, result);
            }
        });
    }

    private class TimeSetReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            Log.d(TAG, "onReceive: set time");
            String action = intent.getAction();

            if (TextUtils.equals(action, Intent.ACTION_TIME_CHANGED)) {
                syncTimeToTk(System.currentTimeMillis());
            }
        }
    }

    private boolean processUpdateEnablePreventCollision() {
        boolean oldConfig = roverConfig.isEnableVision();
        boolean newConfig = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_PREVENT_COLLISION)
                == Definition.ROBOT_SETTING_ENABLE;
        boolean needUpdate = oldConfig != newConfig;
        Log.d(TAG, "[prevent collision] oldConfig : " + oldConfig + ", newConfig ： " + newConfig);
        if (needUpdate) {
            roverConfig.setEnableVision(newConfig);
        }

        return needUpdate;
    }

    public void updateChassisMultiRobotMode(boolean isMultiMode) {
        BiManager.getInstance().reportMultiStatus(isMultiMode);
        Log.d(TAG, "[multi robot] mode update to : " + isMultiMode);
    }

}
