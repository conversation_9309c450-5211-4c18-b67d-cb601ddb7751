/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.waiter;

public class Targets {

    private int id;
    private double x;
    private double y;
    private double z;
    private double vertical_radius;
    private double horizontal_radius;
    private double diagonal_radius;

    public Targets(){

    }

    public Targets(int id, double x, double y, double z,
                   double vertical_radius, double horizontal_radius, double diagonal_radius){
        this.id = id;
        this.x = x;
        this.y = y;
        this.z = z;
        this.vertical_radius = vertical_radius;
        this.horizontal_radius = horizontal_radius;
        this.diagonal_radius = diagonal_radius;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public double getX() {
        return x;
    }

    public void setX(double x) {
        this.x = x;
    }

    public double getY() {
        return y;
    }

    public void setY(double y) {
        this.y = y;
    }

    public double getZ() {
        return z;
    }

    public void setZ(double z) {
        this.z = z;
    }

    public double getVerticalRadius() {
        return vertical_radius;
    }

    public void setVerticalRadius(double vertical_radius) {
        this.vertical_radius = vertical_radius;
    }

    public double getHorizontalRadius() {
        return horizontal_radius;
    }

    public void setHorizontalRadius(double horizontal_radius) {
        this.horizontal_radius = horizontal_radius;
    }

    public double getDiagonalRadius() {
        return diagonal_radius;
    }

    public void setDiagonalRadius(double diagonal_radius) {
        this.diagonal_radius = diagonal_radius;
    }

    @Override
    public String toString() {
        return "Targets{" +
                "id=" + id +
                ", x=" + x +
                ", y=" + y +
                ", z=" + z +
                ", vertical_radius=" + vertical_radius +
                ", horizontal_radius=" + horizontal_radius +
                ", diagonal_radius=" + diagonal_radius +
                '}';
    }
}
