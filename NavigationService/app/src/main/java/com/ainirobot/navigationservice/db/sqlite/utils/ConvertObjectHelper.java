package com.ainirobot.navigationservice.db.sqlite.utils;

import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_ALIAS;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_CREATE_TIME;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_FINISH_STATE;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_FORBID_LINE;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_HAS_TARGET_DATA;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_ICON_URL;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_IGNORE_DISTANCE;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_IP_NAVIGATION;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_IP_ROS;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_IP_SDK_ROS;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_LANGUAGE_TYPE;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_LORA_CONFIG;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_MAPPING_POSE_ID;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_MAP_ID;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_MAP_LANGUAGE;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_MAP_MD5;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_MAP_NAME;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_MAP_PATH;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_MAP_TYPE;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_MAP_UUID;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_MAP_VERSION;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_MULTI_AVAILABLE_ELEVATORS;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_MULTI_FLOOR_ALIAS;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_MULTI_FLOOR_ID;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_MULTI_FLOOR_INDEX;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_MULTI_FLOOR_STATE;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_MULTI_MAP_NAME;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_NO_DIRECTIONAL_PARKING;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_PATROL_ROUTE;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_PLACE_CN_NAME;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_PLACE_ID;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_PLACE_NAME;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_PLACE_STATUS;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_PLACE_TYPE;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_POINT_THETA;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_POINT_X;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_POINT_Y;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_POSE_ESTIMATE;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_POSE_PRIORITY;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_ROVER_CONFIG;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_SAFE_DISTANCE;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_SERVER_IP;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_SYNC_STATE;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_UPDATE_TIME;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_USE_STATE;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_VISION_ID;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_VISION_MAP_NAME;
import static com.ainirobot.navigationservice.db.sqlite.TableInfoDef.COLUMN_VISION_MD5;

import android.content.ContentValues;
import android.database.Cursor;

import com.ainirobot.navigationservice.db.entity.MapInfo;
import com.ainirobot.navigationservice.commonModule.data.utils.UpdateTimeUtils;
import com.ainirobot.navigationservice.db.entity.ChassisInfo;
import com.ainirobot.navigationservice.db.entity.ExtraInfo;
import com.ainirobot.navigationservice.db.entity.MappingInfo;
import com.ainirobot.navigationservice.db.entity.MultiFloorInfo;
import com.ainirobot.navigationservice.db.entity.PlaceInfo;
import com.ainirobot.navigationservice.db.entity.PlaceName;
import com.ainirobot.navigationservice.utils.GsonUtil;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ConvertObjectHelper {
    public Map<String, Integer> getChassisInfoIndex(Cursor cursor) {
        Map<String, Integer> map = new HashMap<>();
        map.put(COLUMN_ROVER_CONFIG, cursor.getColumnIndex(COLUMN_ROVER_CONFIG));
        map.put(COLUMN_IP_NAVIGATION, cursor.getColumnIndex(COLUMN_IP_NAVIGATION));
        map.put(COLUMN_IP_ROS, cursor.getColumnIndex(COLUMN_IP_ROS));
        map.put(COLUMN_IP_SDK_ROS, cursor.getColumnIndex(COLUMN_IP_SDK_ROS));
        map.put(COLUMN_SERVER_IP, cursor.getColumnIndex(COLUMN_SERVER_IP));
        map.put(COLUMN_LORA_CONFIG, cursor.getColumnIndex(COLUMN_LORA_CONFIG));
        return map;
    }

    public ChassisInfo cursorToChassisInfo(Cursor cursor, Map<String, Integer> map) {
        ChassisInfo chassisInfo = new ChassisInfo();
        chassisInfo.setRoverConfig(getCursorString(cursor, map, COLUMN_ROVER_CONFIG));
        chassisInfo.setIpNavigation(getCursorString(cursor, map, COLUMN_IP_NAVIGATION));
        chassisInfo.setIpRos(getCursorString(cursor, map, COLUMN_IP_ROS));
        chassisInfo.setIpSdkRos(getCursorString(cursor, map, COLUMN_IP_SDK_ROS));
        chassisInfo.setServerIp(getCursorString(cursor, map, COLUMN_SERVER_IP));
        chassisInfo.setMultiRobotConfig(getCursorString(cursor, map, COLUMN_LORA_CONFIG));
        return chassisInfo;
    }

    public ContentValues chassisInfoToContentValues(ChassisInfo chassisInfo) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(COLUMN_ROVER_CONFIG, chassisInfo.getRoverConfig());
        contentValues.put(COLUMN_IP_NAVIGATION, chassisInfo.getIpNavigation());
        contentValues.put(COLUMN_IP_ROS, chassisInfo.getIpRos());
        contentValues.put(COLUMN_IP_SDK_ROS, chassisInfo.getIpSdkRos());
        contentValues.put(COLUMN_SERVER_IP, chassisInfo.getServerIp());
        return contentValues;
    }

    public Map<String, Integer> getPlaceInfoIndex(Cursor cursor) {
        Map<String, Integer> map = new HashMap<>();
        map.put(COLUMN_PLACE_ID, cursor.getColumnIndex(COLUMN_PLACE_ID));
        map.put(COLUMN_ICON_URL, cursor.getColumnIndex(COLUMN_ICON_URL));
        map.put(COLUMN_PLACE_TYPE, cursor.getColumnIndex(COLUMN_PLACE_TYPE));
        map.put(COLUMN_PLACE_STATUS, cursor.getColumnIndex(COLUMN_PLACE_STATUS));
        map.put(COLUMN_POINT_THETA, cursor.getColumnIndex(COLUMN_POINT_THETA));
        map.put(COLUMN_POINT_X, cursor.getColumnIndex(COLUMN_POINT_X));
        map.put(COLUMN_POINT_Y, cursor.getColumnIndex(COLUMN_POINT_Y));
        map.put(COLUMN_CREATE_TIME, cursor.getColumnIndex(COLUMN_CREATE_TIME));
        map.put(COLUMN_UPDATE_TIME, cursor.getColumnIndex(COLUMN_UPDATE_TIME));
        map.put(COLUMN_MAP_NAME, cursor.getColumnIndex(COLUMN_MAP_NAME));
        map.put(COLUMN_MAP_ID, cursor.getColumnIndex(COLUMN_MAP_ID));
        map.put(COLUMN_ALIAS, cursor.getColumnIndex(COLUMN_ALIAS));
        map.put(COLUMN_SYNC_STATE, cursor.getColumnIndex(COLUMN_SYNC_STATE));
        map.put(COLUMN_IGNORE_DISTANCE, cursor.getColumnIndex(COLUMN_IGNORE_DISTANCE));
        map.put(COLUMN_SAFE_DISTANCE, cursor.getColumnIndex(COLUMN_SAFE_DISTANCE));
        map.put(COLUMN_NO_DIRECTIONAL_PARKING, cursor.getColumnIndex(COLUMN_NO_DIRECTIONAL_PARKING));//无方向停靠
        return map;
    }

    public PlaceInfo cursorToPlaceInfo(Cursor cursor, Map<String, Integer> map) {
        PlaceInfo placeBean = new PlaceInfo();
        placeBean.setPlaceId(getCursorString(cursor, map, COLUMN_PLACE_ID));
        placeBean.setIconUrl(getCursorString(cursor, map, COLUMN_ICON_URL));
        placeBean.setPlaceType(getCursorInt(cursor, map, COLUMN_PLACE_TYPE));
        placeBean.setPlaceStatus(getCursorInt(cursor, map, COLUMN_PLACE_STATUS));
        placeBean.setPointTheta(getCursorFloat(cursor, map, COLUMN_POINT_THETA));
        placeBean.setPointX(getCursorFloat(cursor, map, COLUMN_POINT_X));
        placeBean.setPointY(getCursorFloat(cursor, map, COLUMN_POINT_Y));
        placeBean.setCreateTime(getCursorString(cursor, map, COLUMN_CREATE_TIME));
        placeBean.setUpdateTime(UpdateTimeUtils.datetimeToTimestampMillis(getCursorString(cursor, map, COLUMN_UPDATE_TIME)));
        placeBean.setMapName(getCursorString(cursor, map, COLUMN_MAP_NAME));
        placeBean.setAlias(getCursorString(cursor, map, COLUMN_ALIAS));
        placeBean.setSyncState(getCursorInt(cursor, map, COLUMN_SYNC_STATE));
        placeBean.setIgnoreDistance(getCursorInt(cursor, map, COLUMN_IGNORE_DISTANCE) > 0);
        placeBean.setSafeDistance(getCursorInt(cursor, map, COLUMN_SAFE_DISTANCE));
//        placeBean.setNoDirectionalParking(getCursorInt(cursor, map, COLUMN_NO_DIRECTIONAL_PARKING) > 0);//无方向停靠
        return placeBean;
    }

    public ContentValues placeInfoToContentValues(PlaceInfo placeInfo) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(COLUMN_PLACE_ID, placeInfo.getPlaceId());
        contentValues.put(COLUMN_ICON_URL, placeInfo.getIconUrl());
        contentValues.put(COLUMN_PLACE_TYPE, placeInfo.getPlaceType());
        contentValues.put(COLUMN_PLACE_STATUS, placeInfo.getPlaceStatus());
        contentValues.put(COLUMN_POINT_THETA, placeInfo.getPointTheta());
        contentValues.put(COLUMN_POINT_X, placeInfo.getPointX());
        contentValues.put(COLUMN_POINT_Y, placeInfo.getPointY());
        contentValues.put(COLUMN_MAP_NAME, placeInfo.getMapName());
        contentValues.put(COLUMN_ALIAS, placeInfo.getAlias());
        contentValues.put(COLUMN_UPDATE_TIME, placeInfo.getUpdateTime());
        contentValues.put(COLUMN_SYNC_STATE, placeInfo.getSyncState());
        contentValues.put(COLUMN_MAP_ID, "compatible");
        return contentValues;
    }

    public Map<String, Integer> getPlaceNameIndex(Cursor cursor) {
        Map<String, Integer> map = new HashMap<>();
        map.put(COLUMN_PLACE_ID, cursor.getColumnIndex(COLUMN_PLACE_ID));
        map.put(COLUMN_LANGUAGE_TYPE, cursor.getColumnIndex(COLUMN_LANGUAGE_TYPE));
        map.put(COLUMN_PLACE_NAME, cursor.getColumnIndex(COLUMN_PLACE_NAME));
        return map;
    }

    public PlaceName cursorToPlaceName(Cursor cursor, Map<String, Integer> map) {
        PlaceName placeName = new PlaceName();
        placeName.setPlaceId(getCursorString(cursor, map, COLUMN_PLACE_ID));
        placeName.setLanguageType(getCursorString(cursor, map, COLUMN_LANGUAGE_TYPE));
        placeName.setPlaceName(getCursorString(cursor, map, COLUMN_PLACE_NAME));
        return placeName;
    }

    public ContentValues placeNameContentValues(PlaceName placeName) {
        if (placeName == null) {
            return null;
        }
        return placeNameContentValues(placeName.getPlaceId(), placeName.getLanguageType(), placeName.getPlaceName());
    }

    public ContentValues placeNameContentValues(String id, String type, String name) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(COLUMN_PLACE_ID, id);
        contentValues.put(COLUMN_LANGUAGE_TYPE, type);
        contentValues.put(COLUMN_PLACE_NAME, name);
        return contentValues;
    }

    public Map<String, Integer> getMultiFloorInfoIndex(Cursor cursor) {
        Map<String, Integer> map = new HashMap<>();
        map.put(COLUMN_MULTI_FLOOR_ID, cursor.getColumnIndex(COLUMN_MULTI_FLOOR_ID));
        map.put(COLUMN_MULTI_FLOOR_INDEX, cursor.getColumnIndex(COLUMN_MULTI_FLOOR_INDEX));
        map.put(COLUMN_MULTI_FLOOR_ALIAS, cursor.getColumnIndex(COLUMN_MULTI_FLOOR_ALIAS));
        map.put(COLUMN_MULTI_FLOOR_STATE, cursor.getColumnIndex(COLUMN_MULTI_FLOOR_STATE));
        map.put(COLUMN_MULTI_MAP_NAME, cursor.getColumnIndex(COLUMN_MULTI_MAP_NAME));
        map.put(COLUMN_MULTI_AVAILABLE_ELEVATORS, cursor.getColumnIndex(COLUMN_MULTI_AVAILABLE_ELEVATORS));
        return map;
    }

    public MultiFloorInfo cursorToMultiFloorInfo(Cursor cursor, Map<String, Integer> map, Type type) {
        MultiFloorInfo multiFloorInfo = new MultiFloorInfo();
        multiFloorInfo.setFloorIndex(getCursorInt(cursor, map, COLUMN_MULTI_FLOOR_INDEX));
        multiFloorInfo.setFloorAlias(getCursorString(cursor, map, COLUMN_MULTI_FLOOR_ALIAS));
        multiFloorInfo.setFloorState(getCursorInt(cursor, map, COLUMN_MULTI_FLOOR_STATE));
        multiFloorInfo.setMapName(getCursorString(cursor, map, COLUMN_MULTI_MAP_NAME));
        multiFloorInfo.setAvailableElevators(stringToList(getCursorString(cursor, map, COLUMN_MULTI_AVAILABLE_ELEVATORS), type));
        return multiFloorInfo;
    }

    public ContentValues multiFloorInfoToContentValues(MultiFloorInfo multiFloorInfo) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(COLUMN_MULTI_FLOOR_INDEX, multiFloorInfo.getFloorIndex());
        contentValues.put(COLUMN_MULTI_FLOOR_ALIAS, multiFloorInfo.getFloorAlias());
        contentValues.put(COLUMN_MULTI_FLOOR_STATE, multiFloorInfo.getFloorState());
        contentValues.put(COLUMN_MULTI_MAP_NAME, multiFloorInfo.getMapName());
        contentValues.put(COLUMN_MULTI_AVAILABLE_ELEVATORS, GsonUtil.toJson(multiFloorInfo.getAvailableElevators()));
        return contentValues;
    }

    public Map<String, Integer> getMappingInfoIndex(Cursor cursor) {
        Map<String, Integer> map = new HashMap<>();
        map.put(COLUMN_MAP_NAME, cursor.getColumnIndex(COLUMN_MAP_NAME));
        map.put(COLUMN_PLACE_TYPE, cursor.getColumnIndex(COLUMN_PLACE_TYPE));
        map.put(COLUMN_PLACE_CN_NAME, cursor.getColumnIndex(COLUMN_PLACE_CN_NAME));
        map.put(COLUMN_PLACE_ID, cursor.getColumnIndex(COLUMN_PLACE_ID));
        map.put(COLUMN_MAPPING_POSE_ID, cursor.getColumnIndex(COLUMN_MAPPING_POSE_ID));
        map.put(COLUMN_POSE_PRIORITY, cursor.getColumnIndex(COLUMN_POSE_PRIORITY));
        return map;
    }

    public MappingInfo cursorToMappingInfo(Cursor cursor, Map<String, Integer> map) {
        MappingInfo mappingInfo = new MappingInfo();
        mappingInfo.setMapName(getCursorString(cursor, map, COLUMN_MAP_NAME));
        mappingInfo.setPlaceType(getCursorInt(cursor, map, COLUMN_PLACE_TYPE));
        mappingInfo.setPlaceCnName(getCursorString(cursor, map, COLUMN_PLACE_CN_NAME));
        mappingInfo.setPlaceId(getCursorString(cursor, map, COLUMN_PLACE_ID));
        mappingInfo.setMappingPoseId(getCursorString(cursor, map, COLUMN_MAPPING_POSE_ID));
        mappingInfo.setPosePriority(getCursorInt(cursor, map, COLUMN_POSE_PRIORITY));
        return mappingInfo;
    }

    public ContentValues mappingInfoToContentValues(MappingInfo mappingInfo) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(COLUMN_MAP_NAME, mappingInfo.getMapName());
        contentValues.put(COLUMN_PLACE_TYPE, mappingInfo.getPlaceType());
        contentValues.put(COLUMN_PLACE_CN_NAME, mappingInfo.getPlaceCnName());
        contentValues.put(COLUMN_PLACE_ID, mappingInfo.getPlaceId());
        contentValues.put(COLUMN_MAPPING_POSE_ID, mappingInfo.getMappingPoseId());
        contentValues.put(COLUMN_POSE_PRIORITY, mappingInfo.getPosePriority());
        return contentValues;
    }

    public Map<String, Integer> getExtraInfoIndex(Cursor cursor) {
        Map<String, Integer> map = new HashMap<>();
        map.put(COLUMN_VISION_MAP_NAME, cursor.getColumnIndex(COLUMN_VISION_MAP_NAME));
        map.put(COLUMN_VISION_ID, cursor.getColumnIndex(COLUMN_VISION_ID));
        map.put(COLUMN_VISION_MD5, cursor.getColumnIndex(COLUMN_VISION_MD5));
        return map;
    }

    public ExtraInfo cursorToExtraInfo(Cursor cursor, Map<String, Integer> map) {
        return new ExtraInfo(getCursorString(cursor, map, COLUMN_VISION_MAP_NAME), getCursorString(cursor, map, COLUMN_VISION_ID), getCursorString(cursor, map, COLUMN_VISION_MD5));
    }

    public ContentValues extraInfoToContentValues(ExtraInfo extraInfo) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(COLUMN_VISION_MAP_NAME, extraInfo.getMapName());
        contentValues.put(COLUMN_VISION_ID, extraInfo.getExtraId());
        contentValues.put(COLUMN_VISION_MD5, extraInfo.getExtraMd5());
        return contentValues;
    }

    public Map<String, Integer> getMapInfoIndex(Cursor cursor) {
        Map<String, Integer> map = new HashMap<>();
        map.put(COLUMN_MAP_ID, cursor.getColumnIndex(COLUMN_MAP_ID));
        map.put(COLUMN_MAP_NAME, cursor.getColumnIndex(COLUMN_MAP_NAME));
        map.put(COLUMN_MAP_TYPE, cursor.getColumnIndex(COLUMN_MAP_TYPE));
        map.put(COLUMN_USE_STATE, cursor.getColumnIndex(COLUMN_USE_STATE));
        map.put(COLUMN_SYNC_STATE, cursor.getColumnIndex(COLUMN_SYNC_STATE));
        map.put(COLUMN_MAP_PATH, cursor.getColumnIndex(COLUMN_MAP_PATH));
        map.put(COLUMN_CREATE_TIME, cursor.getColumnIndex(COLUMN_CREATE_TIME));
        map.put(COLUMN_UPDATE_TIME, cursor.getColumnIndex(COLUMN_UPDATE_TIME));
        map.put(COLUMN_MAP_MD5, cursor.getColumnIndex(COLUMN_MAP_MD5));
        map.put(COLUMN_MAP_UUID, cursor.getColumnIndex(COLUMN_MAP_UUID));
        map.put(COLUMN_FORBID_LINE, cursor.getColumnIndex(COLUMN_FORBID_LINE));
        map.put(COLUMN_MAP_LANGUAGE, cursor.getColumnIndex(COLUMN_MAP_LANGUAGE));
        map.put(COLUMN_PATROL_ROUTE, cursor.getColumnIndex(COLUMN_PATROL_ROUTE));
        map.put(COLUMN_FINISH_STATE, cursor.getColumnIndex(COLUMN_FINISH_STATE));
        map.put(COLUMN_POSE_ESTIMATE, cursor.getColumnIndex(COLUMN_POSE_ESTIMATE));
        map.put(COLUMN_HAS_TARGET_DATA, cursor.getColumnIndex(COLUMN_HAS_TARGET_DATA));
        map.put(COLUMN_MAP_VERSION, cursor.getColumnIndex(COLUMN_MAP_VERSION));
        return map;
    }

    public MapInfo cursorToMapInfo(Cursor cursor, Map<String, Integer> map) {
        MapInfo mapInfo = new MapInfo();
        mapInfo.setMapId(getCursorString(cursor, map, COLUMN_MAP_ID));
        mapInfo.setMapName(getCursorString(cursor, map, COLUMN_MAP_NAME));
        mapInfo.setMapType(getCursorInt(cursor, map, COLUMN_MAP_TYPE));
        mapInfo.setUseState(getCursorInt(cursor, map, COLUMN_USE_STATE));
        mapInfo.setSyncState(getCursorInt(cursor, map, COLUMN_SYNC_STATE));
        mapInfo.setForbidLine(getCursorInt(cursor, map, COLUMN_FORBID_LINE));
        mapInfo.setMapPath(getCursorString(cursor, map, COLUMN_MAP_PATH));
        mapInfo.setCreateTime(getCursorString(cursor, map, COLUMN_CREATE_TIME));
        mapInfo.setUpdateTime(getCursorString(cursor, map, COLUMN_UPDATE_TIME));
        mapInfo.setMd5(getCursorString(cursor, map, COLUMN_MAP_MD5));
        mapInfo.setMapLanguage(getCursorString(cursor, map, COLUMN_MAP_LANGUAGE));
        mapInfo.setMapUuid(getCursorString(cursor, map, COLUMN_MAP_UUID));
        mapInfo.setPatrolRoute(getCursorString(cursor, map, COLUMN_PATROL_ROUTE));
        mapInfo.setFinishState(getCursorInt(cursor, map, COLUMN_FINISH_STATE));
        mapInfo.setPoseEstimate(getCursorString(cursor, map, COLUMN_POSE_ESTIMATE));
        mapInfo.setTargetData(getCursorInt(cursor, map, COLUMN_HAS_TARGET_DATA));
        mapInfo.setMapVersion(getCursorInt(cursor, map, COLUMN_MAP_VERSION));
        return mapInfo;
    }

    public ContentValues mapInfoToContentValues(MapInfo mapInfo) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(COLUMN_MAP_ID, mapInfo.getMapId());
        contentValues.put(COLUMN_MAP_NAME, mapInfo.getMapName());
        contentValues.put(COLUMN_MAP_TYPE, mapInfo.getMapType());
        contentValues.put(COLUMN_USE_STATE, mapInfo.getUseState());
        contentValues.put(COLUMN_SYNC_STATE, mapInfo.getSyncState());
        contentValues.put(COLUMN_MAP_PATH, mapInfo.getMapPath());
        contentValues.put(COLUMN_CREATE_TIME, mapInfo.getCreateTime());
        contentValues.put(COLUMN_UPDATE_TIME, mapInfo.getUpdateTime());
        contentValues.put(COLUMN_MAP_MD5, mapInfo.getMd5());
        contentValues.put(COLUMN_MAP_UUID, mapInfo.getMapUuid());
        contentValues.put(COLUMN_FORBID_LINE, mapInfo.getForbidLine());
        contentValues.put(COLUMN_MAP_LANGUAGE, mapInfo.getMapLanguage());
        contentValues.put(COLUMN_PATROL_ROUTE, mapInfo.getPatrolRoute());
        contentValues.put(COLUMN_FINISH_STATE, mapInfo.getFinishState());
        contentValues.put(COLUMN_POSE_ESTIMATE, mapInfo.getPoseEstimate());
        contentValues.put(COLUMN_HAS_TARGET_DATA, mapInfo.getTargetData());
        return contentValues;
    }

    public String getCursorString(Cursor cursor, Map<String, Integer> map, String columnKey) {
        Integer column = map.get(columnKey);
        if (column == null || column == -1) {
            column = cursor.getColumnIndex(columnKey);
            map.put(columnKey, column);
        }
        return cursor.getString(column);
    }

    private int getCursorInt(Cursor cursor, Map<String, Integer> map, String columnKey) {
        Integer column = map.get(columnKey);
        if (column == null || column == -1) {
            column = cursor.getColumnIndex(columnKey);
            map.put(columnKey, column);
        }
        return cursor.getInt(column);
    }

    private float getCursorFloat(Cursor cursor, Map<String, Integer> map, String columnKey) {
        Integer column = map.get(columnKey);
        if (column == null || column == -1) {
            column = cursor.getColumnIndex(columnKey);
            map.put(columnKey, column);
        }
        return cursor.getFloat(column);
    }

    private List<String> stringToList(String data, Type type) {
        return GsonUtil.fromJson(data, type);
    }
}
