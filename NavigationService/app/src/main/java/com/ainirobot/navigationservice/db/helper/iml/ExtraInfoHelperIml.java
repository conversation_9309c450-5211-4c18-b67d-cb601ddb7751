package com.ainirobot.navigationservice.db.helper.iml;

import com.ainirobot.navigationservice.db.entity.ExtraInfo;

import java.util.List;

public interface ExtraInfoHelperIml extends BaseHelper<ExtraInfo> {
    boolean deleteExtraData(String mapName);

    ExtraInfo getExtraInfo(String mapName);

    void initExtraInfoData(List<ExtraInfo> extraInfoList);

    boolean updateExtraInfo(ExtraInfo extraInfo);
}
