package com.ainirobot.navigationservice.commonModule.logs;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.navigationservice.chassisAbility.ChassisManager;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.chassisAbility.ota.client.IOtaClient;
import com.ainirobot.navigationservice.commonModule.bi.BiManager;
import com.ainirobot.navigationservice.commonModule.logs.beans.LogCacheBean;
import com.ainirobot.navigationservice.commonModule.logs.db.LogDataHelper;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

public class LogManager {
    private final static String TAG = TAGPRE + LogManager.class.getSimpleName();

    /**
     * Cache the largest number of tasks in db
     */
    public static final int MAX_LOG_TASK_LIMIT = 1000;

    /**
     * Cache the largest number of tasks in memory
     */
    private static final int MAX_CACHE_IN_MEMORY = 100;

    /**
     * the interval of send message to tx1
     */
    public static final long INTERVAL_HANDLE_MESSAGE = 5 * 60 * 1000;

    private static final int STATUS_TX1_PACKED_RETURN = 0;

    private static final long DEFAULT_LAST_CHECK_TIME = 0;

    public static final String TYPE_REMOTE_DISCONNECT = "type_remote_disconnect";
    public static final String TYPE_SOCKET_DISCONNECT = "type_socket_disconnect";
    public static final String TYPE_ESTIMATE_LOST = "type_estimate_lost";
    public static final String TYPE_FORCE_SNAPSHOT = "type_force_snapshot";

    private BiManager biManager;

    private Context mContext;

    private LogDataHelper logDataHelper;

    private ConcurrentHashMap<String, Integer> mCacheLogMap;

    private ConcurrentHashMap<String, ErrorEvent> mFilterRepeatErrorMap;

    private static volatile ScheduledExecutorService mCollectLogExecutor;
    private String mTk1Version = "";
    private long mLastCheckTime = DEFAULT_LAST_CHECK_TIME;
    private LogTaskListener mTaskListener;

    private static LogManager mInstance;
    private LogManager() {

    }

    public static synchronized LogManager getInstance() {
        if (mInstance == null) {
            mInstance = new LogManager();
        }
        return mInstance;
    }

    public void init(Context mContext) {
        this.mContext = mContext;
        this.logDataHelper = new LogDataHelper(mContext);
        this.biManager = BiManager.getInstance();
    }

    public void insertLogTask(final LogCacheBean logBean) {
        try {
            Log.d(Definition.TAG_NAVI_LOG_REPORT, "insertLogTask " + logBean.getCacheId());
            logDataHelper.insertLog(logBean);

            if (collectLogNotOpened()) {
                Log.i(Definition.TAG_NAVI_LOG_REPORT, "collect task has not been started, open now");
                scheduleCollectTask();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public LogCacheBean getLogTaskById(String taskId) {
        try {
            Log.d(Definition.TAG_NAVI_LOG_REPORT, "getLogTaskById " + taskId);
            return logDataHelper.getLogById(taskId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<LogCacheBean> getCacheLogTaskList() {
        try {
            Log.d(Definition.TAG_NAVI_LOG_REPORT, "getCacheLogTaskList ");
            return logDataHelper.getAllLog();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public void clearAllLog() {
        try {
            Log.d(Definition.TAG_NAVI_LOG_REPORT, "clearAllLog ");
            logDataHelper.deleteAllLog();
            if (mFilterRepeatErrorMap != null) {
                mFilterRepeatErrorMap.clear();
            }
            if (mCacheLogMap != null) {
                mCacheLogMap.clear();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void updateLogStatus(final LogCacheBean logBean) {
        try {
            Log.d(Definition.TAG_NAVI_LOG_REPORT, "updateLogStatus " + logBean.getCacheId());
            logDataHelper.updateLogStatus(logBean);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean updateLogStatusById(String taskId, int status) {
        try {
            Log.d(Definition.TAG_NAVI_LOG_REPORT, "updateLogStatusById " + taskId + " and update status is " + status);
            return logDataHelper.updateLogStatusById(taskId, status);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private void updateLogByTime(String cacheId, String time) {
        try {
            Log.d(Definition.TAG_NAVI_LOG_REPORT, "updateLogByTime cacheId " + cacheId);
            logDataHelper.updateLogByTime(cacheId, time);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void deleteLogById(String taskId) {
        try {
            Log.d(Definition.TAG_NAVI_LOG_REPORT, "deleteLogById " + taskId);
            logDataHelper.deleteLogById(taskId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public LogCacheBean getOneLogFromDb() {
        try {
            Log.d(Definition.TAG_NAVI_LOG_REPORT, "getOneLogFromDb ");
            return logDataHelper.getOneLog();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private ConcurrentHashMap<String, Integer> getCacheLogQueue() {
        if (mCacheLogMap == null) {
            mCacheLogMap = new ConcurrentHashMap<>();
        }
        return mCacheLogMap;
    }

    public ConcurrentHashMap<String, ErrorEvent> getFilterRepeatErrorMap() {
        if (mFilterRepeatErrorMap == null) {
            mFilterRepeatErrorMap = new ConcurrentHashMap<>();
        }
        return mFilterRepeatErrorMap;
    }

    public void scheduleCollectTask() {
        synchronized (this) {
            cancelCollectTask();
            if (mCollectLogExecutor == null) {
                mCollectLogExecutor = new ScheduledThreadPoolExecutor(1);
            }
            mCollectLogExecutor.scheduleAtFixedRate(new CollectLogTask(), 0,
                    INTERVAL_HANDLE_MESSAGE, TimeUnit.MILLISECONDS);
        }
    }

    private void cancelCollectTask() {
        if (mCollectLogExecutor != null && !mCollectLogExecutor.isShutdown()) {
            mCollectLogExecutor.shutdownNow();
        }
    }

    public void updateLogStatusIfNecessary(String cacheId) {
        if (TextUtils.isEmpty(cacheId)) {
            Log.d(Definition.TAG_NAVI_LOG_REPORT, "updateLogStatusIfNecessary false, because cache id is empty");
            return;
        }

        if (getCacheLogQueue().containsKey(cacheId)) {
            getCacheLogQueue().remove(cacheId);
            LogCacheBean bean=getLogTaskById(cacheId);
            biManager.eventErrorNavigationReport(bean, cacheId);
            updateLogStatusById(cacheId, Definition.STATUS_LOG_PACK_SUCCESS_TASK);
        }
    }

    public boolean collectLogNotOpened() {
        return mCollectLogExecutor == null;
    }

    /**
     * the entrance of collect log
     * @return perform result
     */
    public synchronized boolean startCollectLogFlow(String cacheId, String time, String type) {
        if (TextUtils.isEmpty(cacheId)) {
            Log.i(Definition.TAG_NAVI_LOG_REPORT, "cacheId is null");
            return false;
        }

        Log.d(TAG, "startCollectLogFlow cacheId:" +cacheId + " time:" +time + " type:" +type);
        sendSnapshotTaskPreMsg();

        if (!checkTimeCondition()) {
            Log.i(Definition.TAG_NAVI_LOG_REPORT, "checkTimeCondition less than 5 minutes");
            return false;
        }

        // to prevent too much memory
        checkMemorySize();

        //过滤重复event事件
        boolean repeatEvent = false;
        ErrorEvent event = null;
        ConcurrentHashMap<String, ErrorEvent> filterRepeatMap = getFilterRepeatErrorMap();

        Log.i(Definition.TAG_NAVI_LOG_REPORT, "mFilterRepeatErrorMap size " + filterRepeatMap.size() + " type:" + type);
        if (filterRepeatMap.containsKey(type)) {
            repeatEvent = true;
            Log.i(Definition.TAG_NAVI_LOG_REPORT, "mFilterRepeatErrorMap repeat type " + type);
            event = filterRepeatMap.get(type);
            if (event.checkTimeCondition()) {
                return false;
            }

            Log.i(Definition.TAG_NAVI_LOG_REPORT, "type ready for insert db:" + type + " time:" + time);
            try {
                event.setLastTime(Long.parseLong(time));
            } catch (NumberFormatException e) {
                e.printStackTrace();
                event.setLastTime(System.currentTimeMillis());
            }
        } else {
            filterRepeatMap.put(type, new ErrorEvent(type, System.currentTimeMillis()));
        }

        final LogCacheBean logCacheBean = new LogCacheBean();
        logCacheBean.setCacheId(cacheId);
        logCacheBean.setTime(repeatEvent && event != null ? String.valueOf(event.getLastTime()) : time);
        logCacheBean.setType(type);
        logCacheBean.setStatus(Definition.STATUS_LOG_DEFAULT_TASK);

        if (ChassisManager.getInstance().getChassisClient().isCommunicating()) {
            Log.i(Definition.TAG_NAVI_LOG_REPORT, "navigation is communicating");
            ChassisManager.getInstance().getOtaClient().getVersion(new IOtaClient.OtaResListener() {
                @Override
                public void onResult(String version) {
                    Log.i(Definition.TAG_NAVI_LOG_REPORT, "getVersion onResult:" + version);
                    try {
                        JSONArray jsonArray = new JSONArray(version);
                        for (int i = 0; i < jsonArray.length(); i++) {
                            JSONObject jsonObject = jsonArray.optJSONObject(i);
                            if (jsonObject != null) {
                                String board = jsonObject.optString("board");
                                if ("tk1".equals(board)) {
                                    mTk1Version = jsonObject.optString("version");
                                    logCacheBean.setTk1Version(mTk1Version);
                                    break;
                                }
                            }
                        }
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    insertLogTask(logCacheBean);
                }
            });
        } else {
            Log.i(Definition.TAG_NAVI_LOG_REPORT, "navigation is not communicating");
            logCacheBean.setTk1Version(mTk1Version);
            insertLogTask(logCacheBean);
        }

        return true;
    }

    private void checkMemorySize() {
        if (mFilterRepeatErrorMap != null && mFilterRepeatErrorMap.size() >= MAX_CACHE_IN_MEMORY) {
            mFilterRepeatErrorMap.clear();
        }

        if (mCacheLogMap != null && mCacheLogMap.size() >= MAX_CACHE_IN_MEMORY) {
            mCacheLogMap.clear();
        }
    }

    private boolean checkTimeCondition() {
        long currReportTime = System.currentTimeMillis();
        // First boot time out of sync in FirstConfig
        if (currReportTime < LogManager.INTERVAL_HANDLE_MESSAGE
                && mLastCheckTime == DEFAULT_LAST_CHECK_TIME) {
            mLastCheckTime = System.currentTimeMillis();
            return true;
        }
        if (currReportTime - mLastCheckTime < LogManager.INTERVAL_HANDLE_MESSAGE) {
            return false;
        }
        mLastCheckTime = System.currentTimeMillis();
        return true;
    }

    public void registSnapshotTaskListener(LogTaskListener listener){
        mTaskListener = listener;
    }

    public void unRegistSnapshotTaskListener(){
        mTaskListener = null;
    }

    private void sendSnapshotTaskPreMsg(){
        if (mTaskListener != null){
            mTaskListener.onNewSnapshotCollectTask();
        }
    }

    class CollectLogTask implements Runnable {

        @Override
        public void run() {
            try {
                //首先判断是否已经断连
                if (!ChassisManager.getInstance().getChassisClient().isCommunicating()) {
                    Log.d(Definition.TAG_NAVI_LOG_REPORT, "navigation disconnect before getOneLogFromDb ");
                    return;
                }

                //每5分钟从缓存中取数据通知TK1
                LogCacheBean logCacheBean = getOneLogFromDb();
                if (logCacheBean == null) {
                    Log.d(Definition.TAG_NAVI_LOG_REPORT, "getOneLogFromDb is empty, not continue");
                    return;
                }

                final String cacheId = logCacheBean.getCacheId();
                Log.d(Definition.TAG_NAVI_LOG_REPORT, "getOneLogFromDb log cache id " + cacheId + " time " + logCacheBean.getTime());
                ChassisManager.getInstance().getChassisClient().takeSnapshot(cacheId, new IChassisClient.ChassisResListener() {
                    @Override
                    public void onResponse(boolean status, int resultCode, Object result) {
                        // 0 代表底盘可以进入打包环节，非0代表不能进行打包，直接置为失败状态
                        if (resultCode == STATUS_TX1_PACKED_RETURN) {
                            //放入内存缓存，等待打包成功失败,如果匹配到了更新数据库状态并删除内存缓存
                            getCacheLogQueue().put(cacheId, Definition.STATUS_LOG_PACKING_TASK);
                            updateLogStatusById(cacheId, Definition.STATUS_LOG_PACKING_TASK);
                        } else {
                            //更新本地数据库失败状态
                            updateLogStatusById(cacheId, Definition.STATUS_LOG_PACK_FAILED_TASK);
                            getCacheLogQueue().remove(cacheId);
                        }
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    public static class ErrorEvent {

        private String eventType;

        private long lastTime;

        ErrorEvent(String eventType, long lastTime) {
            this.eventType = eventType;
            this.lastTime = lastTime;
        }

        private long getLastTime() {
            return lastTime;
        }

        void setLastTime(long lastTime) {
            this.lastTime = lastTime;
        }

        private boolean checkTimeCondition() {
            long tempTime = System.currentTimeMillis();
            Log.i(Definition.TAG_NAVI_LOG_REPORT, "checkTimeCondition:" + tempTime + " time:" + lastTime);
            return tempTime - lastTime < INTERVAL_HANDLE_MESSAGE;
        }
    }

    /**
     * 当需要触发快照任务时，需要提前让底盘打包部分截图，方式真正打包时没有有效图片。
     */
    public interface LogTaskListener {
        void onNewSnapshotCollectTask();
    }

}
