package com.ainirobot.navigationservice.utils;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.navigationservice.commonModule.data.utils.UuidUtils;
import com.ainirobot.navigationservice.db.entity.LocalPlaceInfo;
import com.ainirobot.navigationservice.db.entity.PlaceInfo;

import java.util.ArrayList;
import java.util.List;

public class CovertUtils {

    public static PlaceInfo convertToPlaceInfo(LocalPlaceInfo localPlaceInfo) {
        PlaceInfo placeInfo = new PlaceInfo();
        placeInfo.setPlaceId(localPlaceInfo.getPlaceId());
        placeInfo.setMapName(localPlaceInfo.getMapName());
        placeInfo.setAlias(localPlaceInfo.getAlias());
        placeInfo.setPlaceStatus(localPlaceInfo.getPlaceStatus());
        placeInfo.setPointTheta(localPlaceInfo.getPointTheta());
        placeInfo.setPointX(localPlaceInfo.getPointX());
        placeInfo.setPointY(localPlaceInfo.getPointY());
        placeInfo.setIgnoreDistance(localPlaceInfo.getIgnoreDistance());
        placeInfo.setNoDirectionalParking(localPlaceInfo.getNoDirectionalParking());
        placeInfo.setSafeDistance(localPlaceInfo.getSafeDistance());
        placeInfo.setTypeId(localPlaceInfo.getTypeId());
        placeInfo.setPriority(Definition.SPECIAL_PLACE_HIGH_PRIORITY);
        return placeInfo;
    }

    public static List<PlaceInfo> coverToPlaceInfoList(List<LocalPlaceInfo> localPlaceInfoList) {
        List<PlaceInfo> placeInfoList = new ArrayList<>();
        for(LocalPlaceInfo localPlaceInfo : localPlaceInfoList) {
            PlaceInfo placeInfo = convertToPlaceInfo(localPlaceInfo);
            placeInfoList.add(placeInfo);
        }
        return placeInfoList;
    }

    public static LocalPlaceInfo convertToLocalPlaceInfo(PlaceInfo placeInfo, String language) {
        LocalPlaceInfo localPlaceInfo = new LocalPlaceInfo();
        localPlaceInfo.setMapName(placeInfo.getMapName());
        localPlaceInfo.setAlias(placeInfo.getAlias());
        localPlaceInfo.setPlaceStatus(placeInfo.getPlaceStatus());
        localPlaceInfo.setPointTheta(placeInfo.getPointTheta());
        localPlaceInfo.setPointX(placeInfo.getPointX());
        localPlaceInfo.setPointY(placeInfo.getPointY());
        localPlaceInfo.setIgnoreDistance(placeInfo.getIgnoreDistance());
        localPlaceInfo.setNoDirectionalParking(placeInfo.getNoDirectionalParking());
        localPlaceInfo.setSafeDistance(placeInfo.getSafeDistance());
        localPlaceInfo.setTypeId(placeInfo.getTypeId());
        localPlaceInfo.setPlaceName(placeInfo.getPlaceName(language));
        localPlaceInfo.setPlaceId(placeInfo.getPlaceId());
        localPlaceInfo.setPriority(placeInfo.getPriority());
        return localPlaceInfo;
    }

    public static List<LocalPlaceInfo> covertToLocalPlaceInfoList(List<PlaceInfo> placeInfoList, String language) {
        List<LocalPlaceInfo> localPlaceInfoList = new ArrayList<>();
        for (PlaceInfo placeInfo : placeInfoList) {
            localPlaceInfoList.add(convertToLocalPlaceInfo(placeInfo, language));
        }
        return localPlaceInfoList;
    }
}
