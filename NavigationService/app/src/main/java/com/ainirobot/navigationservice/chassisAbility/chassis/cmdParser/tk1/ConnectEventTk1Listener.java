package com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1;

import com.ainirobot.navigationservice.chassisAbility.chassis.connector.tk1.IChassisConnect;
import com.google.protobuf.ByteString;
import com.google.protobuf.Message;

import ninjia.android.proto.RoverPacketProtoWrapper;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;


public class ConnectEventTk1Listener implements IChassisConnect.EventListener {
    private final static String TAG = TAGPRE + ChassisCommandTk1Impl.class.getSimpleName();

    private ChassisCommandTk1Impl cmdTk1;

    public ConnectEventTk1Listener(ChassisCommandTk1Impl cmdTk1) {
        this.cmdTk1 = cmdTk1;
    }

    @Override
    public void onEvent(Message message) {
        if (message instanceof RoverPacketProtoWrapper.RoverPacketProto) {
            RoverPacketProtoWrapper.RoverPacketProto packet = (RoverPacketProtoWrapper.RoverPacketProto) message;
            String event = packet.getHeader().getCode().name();
            boolean status = packet.getHeader().getStatus();
            int resultCode = packet.getHeader().getResult();
            String params = packet.getHeader().getText();
            ByteString unknownFields = packet.getUnknownFields().toByteString();
            ByteString content = packet.getBody().concat(unknownFields);
            Protocol protocol = Protocol.valueOf(event);
            String eventDef = protocol.type();
//            Log.d(TAG, "onEvent in = " + eventDef);

            cmdTk1.getEventListener(eventDef).onEvent(protocol.parse(params, content));
        }
    }
}
