package com.ainirobot.navigationservice.db.helper.sqlite;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ainirobot.navigationservice.db.DataChangeListener;
import com.ainirobot.navigationservice.db.entity.MapInfo;
import com.ainirobot.navigationservice.db.helper.iml.MapInfoHelperIml;
import com.ainirobot.navigationservice.db.sqlite.TableInfoDef;
import com.ainirobot.navigationservice.db.sqlite.SqliteDbMigrate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MapInfoSqliteHelper extends BaseSqliteHelper<MapInfo> implements MapInfoHelperIml {

    private DataChangeListener mListener;

    /**
     * 用于同步地图使用状态的锁
     */
    private final Object USE_MAP_LOCK = new Object();

    public MapInfoSqliteHelper(SqliteDbMigrate sqliteDbMigrate) {
        super(sqliteDbMigrate, TableInfoDef.TABLE_NAME_MAP_INFO);
    }

    @Override
    protected Map<String, Integer> updateCursorIndexMap(Cursor cursor) {
        return sqliteDbMigrate.getMapInfoIndex(cursor);
    }

    @Override
    public MapInfo getMapByName(String mapName) {
        SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
        Cursor cursor = null;
        MapInfo mapInfo = null;
        try {
            cursor = readDb.query(mTableName, null, TableInfoDef.COLUMN_MAP_NAME + " = ?", new String[]{mapName}, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                mapInfo = sqliteDbMigrate.cursorToMapInfo(cursor, getCursorIndexMap(cursor));
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        Log.d(TAG, "getMapByName: mapInfo = " + mapInfo);
        return mapInfo;
    }

    @Override
    public void deleteMapByName(String mapName) {
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        int rowsDeleted = writeDb.delete(mTableName, TableInfoDef.COLUMN_MAP_NAME + " = ?", new String[]{mapName});
        boolean result = rowsDeleted > 0;
        if (result) {
            notifyDataChange();
        }
        Log.d(TAG, "deleteMapByName mapName = " + mapName + " rowsDeleted = " + rowsDeleted);
    }

    @Nullable
    @Override
    public MapInfo getNavMapInfo() {
        synchronized (USE_MAP_LOCK) {
            SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
            Cursor cursor = null;
            MapInfo mapInfo = null;
            try {
                cursor = readDb.query(mTableName, null, TableInfoDef.COLUMN_USE_STATE + " = ?", new String[]{String.valueOf(MapInfo.UseState.IN_USE)}, null, null, null, "1");
                if (cursor != null && cursor.moveToFirst()) {
                    mapInfo = sqliteDbMigrate.cursorToMapInfo(cursor, getCursorIndexMap(cursor));
                }
            } finally {
                if (cursor != null) {
                    cursor.close();
                }
            }
            Log.d(TAG, "getNavMapInfo: mapInfo = " + mapInfo);
            return mapInfo;
        }
    }

    @Override
    public void clearUseState() {
        synchronized (USE_MAP_LOCK) {
            SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
            ContentValues values = new ContentValues();
            values.put(TableInfoDef.COLUMN_USE_STATE, MapInfo.UseState.NO_USE);
            int rowsUpdated = writeDb.update(mTableName, values, TableInfoDef.COLUMN_USE_STATE + " = ?", new String[]{String.valueOf(MapInfo.UseState.IN_USE)});
            if (rowsUpdated > 0) {
                notifyDataChange();
            }
            Log.d(TAG, "clearUseState rowsUpdated = " + rowsUpdated);
        }
    }

    @Override
    public void updateUseState(MapInfo mapInfo) {
        synchronized (USE_MAP_LOCK) {
            SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
            writeDb.beginTransaction();
            try {
                // 将所有当前处于 IN_USE 状态的记录更新为 NO_USE
                ContentValues noUseValues = new ContentValues();
                noUseValues.put(TableInfoDef.COLUMN_USE_STATE, MapInfo.UseState.NO_USE);
                int noUseRowsUpdated = writeDb.update(
                        mTableName,
                        noUseValues,
                        TableInfoDef.COLUMN_USE_STATE + " = ?",
                        new String[]{String.valueOf(MapInfo.UseState.IN_USE)}
                );
                Log.d(TAG, "updateUseState: " + noUseRowsUpdated + " rows updated to NO_USE");
                // 更新传入的 mapInfo 的状态为 IN_USE
                mapInfo.setUseState(MapInfo.UseState.IN_USE);
                ContentValues inUseValues = sqliteDbMigrate.mapInfoToContentValues(mapInfo);
                // 尝试更新传入的 mapInfo，如果更新失败则插入新记录
                int rowsUpdated = writeDb.update(
                        mTableName,
                        inUseValues,
                        TableInfoDef.COLUMN_MAP_ID + " = ? AND " + TableInfoDef.COLUMN_MAP_NAME + " = ?",
                        new String[]{mapInfo.getMapId(), mapInfo.getMapName()}
                );
                if (rowsUpdated <= 0) {
                    long rowId = writeDb.insert(
                            mTableName,
                            null,
                            inUseValues
                    );
                    if (rowId == -1) {
                        Log.e(TAG, "updateUseState: insert failed for mapId = " + mapInfo);
                    } else {
                        Log.d(TAG, "updateUseState: insert succeeded for mapId = " + mapInfo);
                    }
                } else {
                    Log.d(TAG, "updateUseState: update succeeded for mapId = " + mapInfo);
                }
                writeDb.setTransactionSuccessful();
            } catch (Exception e) {
                Log.e(TAG, "updateUseState: error occurred", e);
            } finally {
                writeDb.endTransaction();
            }
        }
    }

    @NonNull
    @Override
    public List<MapInfo> getMapInfos(String[] mapNames) {
        SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
        List<MapInfo> mapInfos = new ArrayList<>();
        Cursor cursor = null;
        try {
            if (mapNames == null || mapNames.length == 0) {
                cursor = readDb.query(mTableName, null, null, null, null, null, null);
            } else {
                String placeholders = new String(new char[mapNames.length - 1]).replace("\0", "?,") + "?";
                cursor = readDb.query(mTableName, null, TableInfoDef.COLUMN_MAP_NAME + " IN (" + placeholders + ")", mapNames, null, null, null);
            }
            if (cursor != null) {
                Map<String, Integer> map = getCursorIndexMap(cursor);
                while (cursor.moveToNext()) {
                    MapInfo mapInfo = sqliteDbMigrate.cursorToMapInfo(cursor, map);
                    mapInfos.add(mapInfo);
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return mapInfos;
    }

    @Override
    public void initMapInfoData(List<MapInfo> mapInfoList) {
        updateMapInfo(mapInfoList);
    }

    @Override
    public boolean updateMapInfo(MapInfo mapInfo) {
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        ContentValues values = sqliteDbMigrate.mapInfoToContentValues(mapInfo);
        String selection = TableInfoDef.COLUMN_MAP_NAME + " = ?";
        String[] selectionArgs = {String.valueOf(mapInfo.getMapName())};
        // 尝试更新数据
        int rowsUpdated = writeDb.update(mTableName, values, selection, selectionArgs);
        // 如果更新失败（没有记录被更新），尝试插入新记录
        if (rowsUpdated == 0) {
            long rowId = writeDb.insert(mTableName, null, values);
            boolean result = rowId != -1;
            if (result) {
                notifyDataChange();
                Log.d(TAG, "updateMapInfo: insert success mapInfo:" + mapInfo);
            }
            return result;
        } else {
            notifyDataChange();
            Log.d(TAG, "updateMapInfo update rowsUpdated = " + rowsUpdated + " mapInfo:" + mapInfo);
            return true;
        }
    }

    @Override
    public void updateMapInfo(List<MapInfo> mapInfos) {
        if (mapInfos == null || mapInfos.isEmpty()) {
            Log.d(TAG, "updateMapInfo: mapInfos list is null or empty");
            return;
        }
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        writeDb.beginTransaction();
        boolean isUpdated = false;
        try {
            for (MapInfo mapInfo : mapInfos) {
                ContentValues values = sqliteDbMigrate.mapInfoToContentValues(mapInfo);
                String selection = TableInfoDef.COLUMN_MAP_NAME + " = ?";
                String[] selectionArgs = {mapInfo.getMapName()};
                // 尝试更新数据
                int rowsUpdated = writeDb.update(mTableName, values, selection, selectionArgs);
                // 如果更新失败（没有记录被更新），尝试插入新记录
                if (rowsUpdated == 0) {
                    long rowId = writeDb.insert(mTableName, null, values);
                    if (rowId == -1) {
                        Log.d(TAG, "updateMapInfo: insert failed for mapName = " + mapInfo.getMapName());
                    } else {
                        isUpdated = true;
                        Log.d(TAG, "updateMapInfo: insert success for map = " + mapInfo);
                    }
                } else {
                    isUpdated = true;
                    Log.d(TAG, "updateMapInfo: update success for map = " + mapInfo);
                }
            }
            if (isUpdated) {
                writeDb.setTransactionSuccessful();
            }
        } catch (Exception e) {
            Log.e(TAG, "updateMapInfo: exception occurred", e);
        } finally {
            writeDb.endTransaction();
        }
        if (isUpdated) {
            notifyDataChange();
        }
    }

    @Override
    public void setOnDataChangeListener(DataChangeListener listener) {
        this.mListener = listener;
    }

    private void notifyDataChange() {
        if (mListener != null) {
            mListener.onDataChanged();
        }
    }
}
