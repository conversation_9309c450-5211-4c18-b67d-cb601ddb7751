package com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1;

public class EventTk1Def {
    public static final String EVENT_UPDATE_GLOBAL_MAP = "event_update_global_map";
    public static final String EVENT_UPDATE_REALTIME_MAP = "event_update_realtime_map";
    public static final String EVENT_UPDATE_POSE = "event_update_pose";
    public static final String EVENT_UPDATE_LASER = "event_update_laser";
    public static final String EVENT_UPDATE_VELOCITY = "event_update_velocity";
    public static final String EVENT_UPDATE_EVENT = "event_update_event";
    public static final String EVENT_REPORT_STATISTIC = "event_report_statistic";
    public static final String EVENT_WORKING_MODE_STATE_CHANGE = "event_working_mode_state_change";
    public static final String EVENT_UPDATE_MAPPING_POSE = "event_update_mapping_pose";

    public static final String EVENT_RES_COMMAND = "event_res_command";
    public static final String EVENT_UPDATE_MULTI_WORK_MODE = "event_update_multi_work_mode";
}
