package com.ainirobot.navigationservice.business.rpc.algorithm;

import com.ainirobot.navigationservice.business.rpc.SpeedBean;

/**
 * Linear To Mix motion Algorithm
 *
 * @version V1.0.0
 * @date 2019/11/15 15:00
 */
public class LinearToMixMotion extends MotionAlgorithm {

    public LinearToMixMotion(SpeedBean paramSpeedBean) {
        super(paramSpeedBean);
    }

    @Override
    public void motion() {

    }
}
