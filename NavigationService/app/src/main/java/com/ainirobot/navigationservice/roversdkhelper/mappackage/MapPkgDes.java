package com.ainirobot.navigationservice.roversdkhelper.mappackage;

import java.util.ArrayList;
import java.util.List;

/**
 * 地图整包描述，地图包目录文件全集
 */
public class MapPkgDes {

    /**
     * pgm.zip，只在旧的包结构存在
     */
    private List<MapFileDes> pgmZip = new ArrayList<>();

    /**
     * data.zip，只在旧的包结构存在
     */
    private List<MapFileDes> dataZip = new ArrayList<>();

    /**
     * 整包，即小文件数据包，新旧包结构都存在
     */
    private List<MapFileDes> mapPkg = new ArrayList<>();

    /**
     * 大文件扩展包，新旧包结构都可能存在，和地图类型是否包含大文件相关
     */
    private List<MapFileDes> extraFiles = new ArrayList<>();

    public List<MapFileDes> getPgmZip() {
        return pgmZip;
    }

    public MapPkgDes setPgmZip(List<MapFileDes> pgmZip) {
        this.pgmZip = pgmZip;
        return this;
    }

    public List<MapFileDes> getDataZip() {
        return dataZip;
    }

    public MapPkgDes setDataZip(List<MapFileDes> dataZip) {
        this.dataZip = dataZip;
        return this;
    }

    public List<MapFileDes> getMapPkg() {
        return mapPkg;
    }

    public MapPkgDes setMapPkg(List<MapFileDes> mapPkg) {
        this.mapPkg = mapPkg;
        return this;
    }

    public List<MapFileDes> getExtraFiles() {
        return extraFiles;
    }

    public MapPkgDes setExtraFiles(List<MapFileDes> extraFiles) {
        this.extraFiles = extraFiles;
        return this;
    }

    @Override
    public String toString() {
        return "MapPkg{" +
                "pgmZip=" + pgmZip +
                ", dataZip=" + dataZip +
                ", mapPkg=" + mapPkg +
                ", extraFiles=" + extraFiles +
                '}';
    }
}
