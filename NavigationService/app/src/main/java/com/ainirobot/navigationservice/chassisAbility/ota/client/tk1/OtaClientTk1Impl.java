package com.ainirobot.navigationservice.chassisAbility.ota.client.tk1;

import android.content.Context;
import android.util.Log;

import com.ainirobot.navigationservice.chassisAbility.ota.client.IOtaClient;
import com.ainirobot.navigationservice.chassisAbility.ota.cmdParser.IOtaCommand;
import com.ainirobot.navigationservice.chassisAbility.ota.cmdParser.tk1.OtaCommandTk1Impl;
import com.ainirobot.navigationservice.chassisAbility.ota.cmdParser.tk1.ReqDef;
import com.ainirobot.navigationservice.chassisAbility.ota.connector.IOtaConnect;
import com.ainirobot.navigationservice.chassisAbility.ota.connector.tk1.OtaConnectTk1Impl;
import com.ainirobot.navigationservice.chassisAbility.ota.connector.tk1.bean.CmdParam;
import com.ainirobot.navigationservice.utils.GsonUtil;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;
import static com.ainirobot.navigationservice.chassisAbility.ota.cmdParser.tk1.ReqDef.CMD_GET_MOTOR_LEFT_PACKAGE;
import static com.ainirobot.navigationservice.chassisAbility.ota.cmdParser.tk1.ReqDef.CMD_GET_MOTOR_RIGHT_PACKAGE;
import static com.ainirobot.navigationservice.chassisAbility.ota.cmdParser.tk1.ReqDef.CMD_GET_PACKAGE;

public class OtaClientTk1Impl implements IOtaClient {

    private final static String TAG =TAGPRE + OtaClientTk1Impl.class.getSimpleName();

    private Context mContext;
    private IOtaCommand otaCommand;
    private IOtaConnect otaConnect;
    private String mPackagePath = null;
    private String mMotorLeftPackagePath = null;
    private String mMotorRightPackagePath = null;
    public OtaClientTk1Impl() {
        otaCommand = new OtaCommandTk1Impl();
        otaConnect = new OtaConnectTk1Impl();

    }

    @Override
    public void init(Context mContext) {
        this.mContext = mContext;
        if (otaCommand != null) {
            otaCommand.injectConnector(otaConnect);
            registerEventListener();
            otaCommand.init();
        } else {
            throw new RuntimeException("OTA command parser is null");
        }
    }

    @Override
    public void getVersion(final OtaResListener listener) {
        otaCommand.sendGetVersionCmd(new IOtaCommand.CmdResListener() {
            @Override
            public void onResponse(String response) {
                Log.d(TAG, "getVersion = " + response);
                listener.onResult(response);
            }
        });
    }

    @Override
    public void startUpdate(String otaInfo, String[] packagePath, final OtaResListener listener) {
        mPackagePath = packagePath[0];
        mMotorLeftPackagePath = packagePath[1];
        mMotorRightPackagePath = packagePath[2];
        otaCommand.sendStartUpdateCmd(otaInfo, new IOtaCommand.CmdResListener() {
            @Override
            public void onResponse(String response) {
                listener.onResult(response);
            }
        });
    }

    @Override
    public void getUpdateParams(final OtaResListener listener) {
        otaCommand.sendGetUpdateParamsCmd(new IOtaCommand.CmdResListener() {
            @Override
            public void onResponse(String response) {
                listener.onResult(response);
            }
        });
    }

    @Override
    public boolean isOtaConnected() {
        return otaCommand.isOtaConnected();
    }

    private void registerEventListener() {
        otaCommand.registerEventListener(ReqDef.CMD_START_UPDATE, new IOtaCommand.CmdEventListener() {
            @Override
            public void onEvent(Object param) {
                try {
                    String cmdParamJson = (String) param;
                    CmdParam cmdParam = GsonUtil.fromJson(cmdParamJson, CmdParam.class);
                    processSendPackage(cmdParam.getCommand());
                } catch (Exception e) {
                    Log.e(TAG, "process send package error");
                    e.printStackTrace();
                }
            }
        });
    }

    private void processSendPackage(String param) {
        switch (param) {
            case CMD_GET_PACKAGE:
                otaCommand.sendUpdatePackage(mPackagePath);
                break;

            case CMD_GET_MOTOR_LEFT_PACKAGE:
                otaCommand.sendUpdatePackage(mMotorLeftPackagePath);
                break;

            case CMD_GET_MOTOR_RIGHT_PACKAGE:
                otaCommand.sendUpdatePackage(mMotorRightPackagePath);
                break;
                default:
                    break;
        }
    }
}
