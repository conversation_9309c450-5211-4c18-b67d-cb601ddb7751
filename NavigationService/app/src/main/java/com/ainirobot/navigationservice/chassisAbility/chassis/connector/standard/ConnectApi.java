package com.ainirobot.navigationservice.chassisAbility.chassis.connector.standard;

import com.google.protobuf.Message;

public interface ConnectApi {
    void init();

    boolean request(Message message);

    void registerEventListener(EventListener listener);

    void registerConnectListener(OnConnectListener listener);

    void registerResponseListener(ResListener listener);

    interface ResListener {
        void onResponse(Message message);
    }

    interface EventListener {
        void onEvent(Message message);
    }

    interface OnConnectListener {
        void onConnected();//连接成功，必须保证都连接成功，这应该内聚

        void onDisconnected(String channelName);//断连上报哪条路断了
    }
}
