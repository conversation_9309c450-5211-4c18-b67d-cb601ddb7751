package com.ainirobot.navigationservice.commonModule.configuration.beans;

public class ConfigInfo {
    private String deviceType;
    private String connectType;
    private String protocolType;
    private boolean enablePartrol; //start stop
    private boolean enableResetLocation;//地点名字定位
    private boolean enableMotion;// 手动控制 moveDirection motionArc
    private boolean enableGetVersion;// 获取版本号  getVersion
    private boolean enableStartUpdate;//升级配置  startUpdate
    private boolean enableGetUpdateParam; //升级配置 getUpdateParams
    private boolean enableGetSensorStatus;//获取传感器信息 getFullCheckStatus getSensorStatus
    private boolean enableResetPoseEstimate;// 重置定位状态  resetPoseEstimate
    private boolean enableGetSystemInfo; // 获取系统信息 getSystemInformation
    private boolean enableResumeSpecialPlaceTheta;// 恢复某点位角度 resumeSpecialPlaceTheta
    private boolean enableClearCurNaviMap;// 清空导航图，workMode设置为Free， clearCurNaviMap
    private boolean enableGetLogFile; // 获取日志
    private boolean enablePackLogFile;// 打包日志
    private boolean enableCheckCurNaviMap;// 检测当前导航图 checkCurNaviMap
    private boolean enableRadarConfig; //雷达休眠 setRadarState  getRadarState
    private boolean enableSetLocateVision; //视觉重定位  setLocateVision

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getConnectType() {
        return connectType;
    }

    public void setConnectType(String connectType) {
        this.connectType = connectType;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public boolean isEnablePartrol() {
        return enablePartrol;
    }

    public void setEnablePartrol(boolean enablePartrol) {
        this.enablePartrol = enablePartrol;
    }

    public boolean isEnableResetLocation() {
        return enableResetLocation;
    }

    public void setEnableResetLocation(boolean enableResetLocation) {
        this.enableResetLocation = enableResetLocation;
    }

    public boolean isEnableMotion() {
        return enableMotion;
    }

    public void setEnableMotion(boolean enableMotion) {
        this.enableMotion = enableMotion;
    }

    public boolean isEnableGetVersion() {
        return enableGetVersion;
    }

    public void setEnableGetVersion(boolean enableGetVersion) {
        this.enableGetVersion = enableGetVersion;
    }

    public boolean isEnableStartUpdate() {
        return enableStartUpdate;
    }

    public void setEnableStartUpdate(boolean enableStartUpdate) {
        this.enableStartUpdate = enableStartUpdate;
    }

    public boolean isEnableGetUpdateParam() {
        return enableGetUpdateParam;
    }

    public void setEnableGetUpdateParam(boolean enableGetUpdateParam) {
        this.enableGetUpdateParam = enableGetUpdateParam;
    }

    public boolean isEnableGetSensorStatus() {
        return enableGetSensorStatus;
    }

    public void setEnableGetSensorStatus(boolean enableGetSensorStatus) {
        this.enableGetSensorStatus = enableGetSensorStatus;
    }

    public boolean isEnableResetPoseEstimate() {
        return enableResetPoseEstimate;
    }

    public void setEnableResetPoseEstimate(boolean enableResetPoseEstimate) {
        this.enableResetPoseEstimate = enableResetPoseEstimate;
    }

    public boolean isEnableGetSystemInfo() {
        return enableGetSystemInfo;
    }

    public void setEnableGetSystemInfo(boolean enableGetSystemInfo) {
        this.enableGetSystemInfo = enableGetSystemInfo;
    }

    public boolean isEnableResumeSpecialPlaceTheta() {
        return enableResumeSpecialPlaceTheta;
    }

    public void setEnableResumeSpecialPlaceTheta(boolean enableResumeSpecialPlaceTheta) {
        this.enableResumeSpecialPlaceTheta = enableResumeSpecialPlaceTheta;
    }

    public boolean isEnableClearCurNaviMap() {
        return enableClearCurNaviMap;
    }

    public void setEnableClearCurNaviMap(boolean enableClearCurNaviMap) {
        this.enableClearCurNaviMap = enableClearCurNaviMap;
    }

    public boolean isEnableGetLogFile() {
        return enableGetLogFile;
    }

    public void setEnableGetLogFile(boolean enableGetLogFile) {
        this.enableGetLogFile = enableGetLogFile;
    }

    public boolean isEnablePackLogFile() {
        return enablePackLogFile;
    }

    public void setEnablePackLogFile(boolean enablePackLogFile) {
        this.enablePackLogFile = enablePackLogFile;
    }

    public boolean isEnableCheckCurNaviMap() {
        return enableCheckCurNaviMap;
    }

    public void setEnableCheckCurNaviMap(boolean enableCheckCurNaviMap) {
        this.enableCheckCurNaviMap = enableCheckCurNaviMap;
    }

    public boolean isEnableRadarConfig() {
        return enableRadarConfig;
    }

    public void setEnableRadarConfig(boolean enableRadarConfig) {
        this.enableRadarConfig = enableRadarConfig;
    }

    public boolean isEnableSetLocateVision() {
        return enableSetLocateVision;
    }

    public void setEnableSetLocateVision(boolean enableSetLocateVision) {
        this.enableSetLocateVision = enableSetLocateVision;
    }
}
