package com.ainirobot.navigationservice.db;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.navigationservice.db.entity.ChargeArea;
import com.ainirobot.navigationservice.db.entity.ChassisInfo;
import com.ainirobot.navigationservice.db.entity.ExtraInfo;
import com.ainirobot.navigationservice.db.entity.LocalPlaceInfo;
import com.ainirobot.navigationservice.db.entity.MapInfo;
import com.ainirobot.navigationservice.db.entity.MappingInfo;
import com.ainirobot.navigationservice.db.entity.MultiFloorInfo;
import com.ainirobot.navigationservice.db.entity.PlaceInfo;
import com.ainirobot.navigationservice.db.entity.PlaceName;
import com.ainirobot.navigationservice.db.entity.PlaceType;
import com.ainirobot.navigationservice.utils.PropertyUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.stream.JsonReader;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.InputStreamReader;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import io.objectbox.Box;
import io.objectbox.BoxStore;

/**
 * Objectbox 数据库降级兼容处理
 * 降级前备份数据，降级后恢复数据
 */
public class ObjectBoxVersionManager {
    private static final String TAG = "ObjectBoxVersionManager";

    //备份文件夹名称
    private static final String BACKUP_FOLDER_NAME = "objectbox_backup";
    //备份信息json文件名称
    private static final String BACKUP_VERSION_INFO_JSON = "objectbox_backup_info.json";

    private Context mContext;
    private long mBackupStartTime;
    private long mRestoreStartTime;
    private Gson gson;

    private static ObjectBoxVersionManager sInstance;

    private ObjectBoxVersionManager() {
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(PlaceType.class, new PlaceTypeAdapter());
        this.gson = gsonBuilder.create();
    }

    public static ObjectBoxVersionManager getInstance() {
        if (null == sInstance) {
            synchronized (ObjectBoxVersionManager.class) {
                if (null == sInstance) {
                    sInstance = new ObjectBoxVersionManager();
                }
            }
        }
        return sInstance;
    }

    public void init(Context context) {
        Log.d(TAG, "init: ");
        mContext = context;
    }

    //---------------------------------------------------------------------------------------备份数据

    /**
     * OTA降级前备份数据
     *
     * @param downgradedTargetVersion 降级目标版本号
     */
    public void backupData(String downgradedTargetVersion) {
        try {
            mBackupStartTime = System.currentTimeMillis();
            //当前版本小于10.4，不支持备份，10.5版本之后开始支持备份。10.3/10.4只支持恢复数据逻辑即可。
            if(!checkNeedBackupData()){
                Log.d(TAG, "backupData: Current version not support backup.");
                return;
            }

            //检查数据库文件是否存在
            if (!isDatabaseFileExists(mContext)) {
                Log.d(TAG, "backupData: Database file not exists.");
                return;
            }

            Log.d(TAG, "backupData: ---Start---");
            BoxStore boxStore = getBoxStore();

            //版本信息写入json文件
            writeVersionInfoToJson(downgradedTargetVersion);

            //objectbox数据表转换为json文件
            exportAllDataToJson(boxStore);

            //删除objectbox数据库文件(必须删文件，只删表数据不行)
            deleteObjectBoxDatabaseFiles();

        } catch (Exception e) {
            Log.e(TAG, "backupData: Error backing up data", e);
        } finally {
            closeBoxStore();
            Log.d(TAG, "backupData: ---End---.耗时：" + (System.currentTimeMillis() - mBackupStartTime) + "ms");
        }
    }

    private void deleteObjectBoxDatabaseFiles() {
        Log.d(TAG, "deleteObjectBoxDatabaseFiles: ");
        File dbDirectory = new File(mContext.getFilesDir(), "objectbox/objectbox");
        if (dbDirectory.exists()) {
            for (File file : dbDirectory.listFiles()) {
                boolean delete = file.delete();
                Log.d(TAG, "deleteObjectBoxDatabaseFiles: delete file " + file.getAbsolutePath() + " result: " + delete);
            }
            dbDirectory.delete();
        }
        Log.d(TAG, "deleteObjectBoxDatabaseFiles: Database files deleted done.");
    }

    private boolean isDatabaseFileExists(Context context) {
        // 获取数据库文件的路径
        File dbFile = new File(context.getFilesDir(), "objectbox/objectbox/data.mdb");
        // 返回文件是否存在
        return dbFile.exists();
    }

    private <T> void exportDataToJson(Class<T> clazz, File outputFile, BoxStore boxStore) {
        Log.d(TAG, "exportDataToJson: clazz=" + clazz.getSimpleName()
                + ", outputFile=" + outputFile.getAbsolutePath() + "--Start--");

        Box<T> box = null;
        long total = 0;

        try {
            box = boxStore.boxFor(clazz);
            total = box.count();

            // 如果没有数据，直接返回
            if (total <= 0) {
                Log.d(TAG, "exportDataToJson: No data to export.");
                return;
            }

            int limit = 5000;

            try (FileWriter writer = new FileWriter(outputFile)) {
                for (long offset = 0; offset < total; offset += limit) {
                    List<T> items = box.query().build().find(offset, limit);
                    String jsonBatch = gson.toJson(items);
                    if (offset != 0 && jsonBatch.startsWith("[")) {
                        jsonBatch = jsonBatch.substring(1);
                    }
                    if (offset + limit < total) {
                        if (jsonBatch.endsWith("]")) {
                            jsonBatch = jsonBatch.substring(0, jsonBatch.length() - 1) + ",";
                        }
                    }
                    Log.d(TAG, "exportDataToJson: " + jsonBatch);
                    writer.write(jsonBatch);
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "exportDataToJson: Error occurred", e);
        }
    }

    /**
     * 版本信息写入json文件
     */
    private void writeVersionInfoToJson(String downgradedTargetVersion) {
        String currentVersion = PropertyUtil.
                getSystemProperties("ro.product.releasenum", "1.100.0");
        Log.d(TAG, "writeVersionInfoToJson: currentVersion=" + currentVersion
                + ", downgradedTargetVersion=" + downgradedTargetVersion);

        File backupFile = new File(getBackupDir(), BACKUP_VERSION_INFO_JSON);//writeVersionInfoToJson
        if (backupFile.exists()) {
            backupFile.delete(); // 删除已存在的文件
        }

        // 创建 JSON 对象并写入数据
        BackupVersionInfo versionInfo = new BackupVersionInfo(currentVersion, downgradedTargetVersion);
        try (FileWriter writer = new FileWriter(backupFile)) {
            writer.write(gson.toJson(versionInfo));
            writer.flush();
            Log.d(TAG, "writeVersionInfoToJson: Version info written to JSON file successfully.");
        } catch (Exception e) {
            Log.e(TAG, "writeVersionInfoToJson: Error writing version info to JSON file", e);
        }
    }

    private BackupVersionInfo getBackupVersionInfoFromJson() {
        File backupFile = new File(getBackupDir(), BACKUP_VERSION_INFO_JSON);
        if (!backupFile.exists()) {
            Log.d(TAG, "getBackupVersionInfoFromJson: Backup version info file not exists.");
            return null;
        }

        try (FileReader reader = new FileReader(backupFile)) {
            // 读取 JSON 文件内容
            BackupVersionInfo versionInfo = gson.fromJson(reader, BackupVersionInfo.class);
            Log.d(TAG, "getBackupVersionInfoFromJson: versionInfo=" + versionInfo);
            return versionInfo;
        } catch (Exception e) {
            Log.e(TAG, "getBackupVersionInfoFromJson: Error reading backup version info from JSON file", e);
            return null;
        }
    }

    //创建备份文件的目录
    private File getBackupDir() {
        File backupFolder = new File(mContext.getFilesDir(), BACKUP_FOLDER_NAME);
        String backupFolderPath = backupFolder.getAbsolutePath();
        Log.d(TAG, "getBackupDir: backupFolderPath=" + backupFolderPath);
        File backupDir = new File(backupFolderPath);
        if (!backupDir.exists()) {
            backupDir.mkdirs(); // 创建目录
        }
        return backupDir;
    }

    /**
     * 获取BoxStore
     * 只在方法内部作为临时变量使用，不作为全局变量，用完即关闭
     */
    private BoxStore getBoxStore() {
        boolean isSqlite = NavigationDataManager.getInstance().dbModeIsSqlite();
        Log.d(TAG, "getBoxStore: isSqlite=" + isSqlite);
        //从 NavigationDataManager 中获取BoxStore
        //如果是sqlite，必须在备份/恢复后关闭数据库，避免切换数据库类型时发生不匹配问题
        BoxStore boxStore = NavigationDataManager.getInstance().getBoxStore();
        return boxStore;
    }

    private void closeBoxStore() {
        if (NavigationDataManager.getInstance().dbModeIsSqlite()) {
            //当前数据库类型是sqlite，使用完BoxStore后关闭数据库
            Log.d(TAG, "closeBoxStore: closeLastDb");
            NavigationDataManager.getInstance().closeLastDb();
        }
    }


    /**
     * 10.5以上版本需要备份数据
     */
    private boolean checkNeedBackupData() {
        String currentVersion = PropertyUtil.
                getSystemProperties("ro.product.releasenum", "1.100.0");
        if(TextUtils.isEmpty(currentVersion)){
            currentVersion = "1.100.0";
        }
        Log.d(TAG, "checkRoomVersion: currentVersion=" + currentVersion);
        String limitVersion = "10.5";

        Pattern pattern = Pattern.compile("^V(\\d+\\.\\d+)");
        if (pattern.matcher(currentVersion).find()) {
            currentVersion = currentVersion.substring(1);
        }
        Log.d(TAG, "checkRoomVersion: currentVersion=" + currentVersion
                + " limitVersion=" + limitVersion);

        return needBackupData(currentVersion, limitVersion);
    }

    private boolean needBackupData(String version1, String version2) {
        String[] parts1 = version1.split("\\.");
        String[] parts2 = version2.split("\\.");

        int length = Math.max(parts1.length, parts2.length);
        for (int i = 0; i < length; i++) {
            int num1 = i < parts1.length ? Integer.parseInt(parts1[i]) : 0;
            int num2 = i < parts2.length ? Integer.parseInt(parts2[i]) : 0;

            if (num1 < num2) {
                return false;  // No need to backup if version1 < version2
            }
            if (num1 > num2) {
                return true; // Need to backup if version1 > version2
            }
        }
        return true; // Need to backup if versions are equal
    }

    /**
     * 导出所有数据到json文件
     * 注意：*** 当前版本不支持的表不导出，注释掉即可 ***
     */
    private void exportAllDataToJson(BoxStore boxStore) {
        File backupDir = getBackupDir();//exportAllDataToJson
        //10.5之前版本都有的表
//        exportDataToJson(ChassisInfo.class, new File(backupDir, "ChassisInfo.json"), boxStore);//导航配置降级前会清空，不做降级兼容，不备份
        exportDataToJson(MapInfo.class, new File(backupDir, "MapInfo.json"), boxStore);
        exportDataToJson(ExtraInfo.class, new File(backupDir, "ExtraInfo.json"), boxStore);
        exportDataToJson(MappingInfo.class, new File(backupDir, "MappingInfo.json"), boxStore);
        exportDataToJson(MultiFloorInfo.class, new File(backupDir, "MultiFloorInfo.json"), boxStore);
        exportDataToJson(PlaceInfo.class, new File(backupDir, "PlaceInfo.json"), boxStore);
        exportDataToJson(PlaceName.class, new File(backupDir, "PlaceName.json"), boxStore);
        exportDataToJson(PlaceType.class, new File(backupDir, "PlaceType.json"), boxStore);
        exportDataToJson(LocalPlaceInfo.class, new File(backupDir, "LocalPlaceInfo.json"), boxStore);
        //10.5版本新增
        exportDataToJson(ChargeArea.class, new File(backupDir, "ChargeArea.json"), boxStore);
    }

    /**
     * json文件转换为objectbox数据表
     * 注意：*** 当前版本不支持的表不导入，注释掉即可 ***
     */
    private void importAllDataFromJson(BoxStore boxStore) {
        File backupDir = getBackupDir();//importAllDataFromJson
        //10.5之前版本都有的表
//        importDataFromJson(ChassisInfo.class, new File(backupDir, "ChassisInfo.json"), boxStore);//导航配置降级前会清空，不做降级兼容，不备份
        importDataFromJson(MapInfo.class, new File(backupDir, "MapInfo.json"), boxStore);
        importDataFromJson(ExtraInfo.class, new File(backupDir, "ExtraInfo.json"), boxStore);
        importDataFromJson(MappingInfo.class, new File(backupDir, "MappingInfo.json"), boxStore);
        importDataFromJson(MultiFloorInfo.class, new File(backupDir, "MultiFloorInfo.json"), boxStore);
        importDataFromJson(PlaceInfo.class, new File(backupDir, "PlaceInfo.json"), boxStore);
        importDataFromJson(PlaceName.class, new File(backupDir, "PlaceName.json"), boxStore);
        importDataFromJson(PlaceType.class, new File(backupDir, "PlaceType.json"), boxStore);
        importDataFromJson(LocalPlaceInfo.class, new File(backupDir, "LocalPlaceInfo.json"), boxStore);
        //10.5版本新增
        importDataFromJson(ChargeArea.class, new File(backupDir, "ChargeArea.json"), boxStore);
    }

    //---------------------------------------------------------------------------------------恢复数据

    /**
     * OTA降级后恢复数据
     */
    public void restoreData() {
        try {
            mRestoreStartTime = System.currentTimeMillis();
            if (!isBackupFilesExist()) {
                Log.d(TAG, "restoreData: Backup files not exist.");
                return;
            }

            //校验room版本，当前版本是否目标版本
            if (!isCurrentVersionMatchTargetVersion()) {
                Log.d(TAG, "restoreData: Current version not match target version.");
                return;
            }

            Log.d(TAG, "restoreData: ---Start---");
            BoxStore boxStore = getBoxStore();

            //json文件转换为objectbox数据表
            importAllDataFromJson(boxStore);

            //清空备份文件夹
            clearBackupDir();

        } catch (Exception e) {
            Log.e(TAG, "backupData: Error backing up data", e);
        } finally {
            closeBoxStore();
            Log.d(TAG, "restoreData: ---End---.耗时：" + (System.currentTimeMillis() - mRestoreStartTime) + "ms");
        }
    }

    private <T> void importDataFromJson(Class<T> clazz, File inputFile, BoxStore boxStore) {
        Log.d(TAG, "importDataFromJson: clazz=" + clazz.getSimpleName()
                + ", inputFile=" + inputFile.getAbsolutePath() + "--Start--");

        if (!inputFile.exists()) {
            Log.d(TAG, "importDataFromJson: inputFile not exists.");
            return;
        }

        Box<T> box = null;

        try {
            box = boxStore.boxFor(clazz);
            box.removeAll(); // 清空表数据

            try (FileInputStream fis = new FileInputStream(inputFile);
                 JsonReader reader = new JsonReader(new InputStreamReader(fis, "UTF-8"))) {
                reader.beginArray();
                List<T> items = new ArrayList<>();
                int batchSize = 5000; // 批量大小

                while (reader.hasNext()) {
                    try {
                        T item = gson.fromJson(reader, clazz);

                        // 将对象的 id 设置为 0，以避免 ID 冲突
                        if (item instanceof MapInfo) {
                            ((MapInfo) item).id = 0; // 确保 ID 为 0
                        } else if (item instanceof ExtraInfo) {
                            ((ExtraInfo) item).id = 0;
                        } else if (item instanceof MappingInfo) {
                            ((MappingInfo) item).id = 0;
                        } else if (item instanceof MultiFloorInfo) {
                            ((MultiFloorInfo) item).floorId = 0;
                        } else if (item instanceof PlaceInfo) {
                            ((PlaceInfo) item).id = 0;
                        } else if (item instanceof PlaceName) {
                            ((PlaceName) item).id = 0;
                        } else if (item instanceof PlaceType) {
                            ((PlaceType) item).id = 0;
                        } else if (item instanceof LocalPlaceInfo) {
                            ((LocalPlaceInfo) item).id = 0;
                        } else if (item instanceof ChargeArea) {
                            ((ChargeArea) item).id = 0;
                        }

                        items.add(item);

                        if (items.size() >= batchSize) {
                            box.put(items); // 批量插入
                            items.clear();
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "importDataFromJson: Error occurred", e);
                        break;
                    }
                }

                // 插入剩余的数据
                if (!items.isEmpty()) {
                    box.put(items);
                }
                reader.endArray();
                Log.d(TAG, "importDataFromJson: Data imported from JSON file successfully.");
            }

        } catch (Exception e) {
            Log.e(TAG, "importDataFromJson: Error occurred", e);
        }
    }

    private void clearBackupDir() {
        Log.d(TAG, "clearBackupDir: ");
        File backupDir = getBackupDir();//clearBackupDir
        File[] files = backupDir.listFiles();
        if (files != null) {
            for (File file : files) {
                file.delete();
            }
        }
    }

    //校验room版本，当前版本是否目标版本
    private boolean isCurrentVersionMatchTargetVersion() {
        String currentVersion = PropertyUtil.
                getSystemProperties("ro.product.releasenum", "1.100.0");
        String downgradedTargetVersion = getDowngradedTargetVersion();
        Log.d(TAG, "isCurrentVersionMatchTargetVersion: currentVersion=" + currentVersion
                + ", downgradedTargetVersion=" + downgradedTargetVersion);
        return currentVersion.equals(downgradedTargetVersion);
    }

    //获取降级目标版本号
    private String getDowngradedTargetVersion() {
        BackupVersionInfo versionInfo = getBackupVersionInfoFromJson();
        if (versionInfo != null) {
            return versionInfo.getDowngradedTargetVersion();
        }
        return "";
    }

    //检查备份文件是否存在，目录存在且不为空
    private boolean isBackupFilesExist() {
        File backupDir = getBackupDir();//isBackupFilesExist
        if (backupDir.exists() && backupDir.isDirectory()) {
            File[] files = backupDir.listFiles();
            return files != null && files.length > 0;
        }
        return false;
    }

    /**
     * 声明bean类，表示备份信息
     * currentVersion：当前版本
     * downgradedTargetVersion：降级目标版本
     */
    private static class BackupVersionInfo {
        private String currentVersion;
        private String downgradedTargetVersion;

        public BackupVersionInfo(String currentVersion, String downgradedTargetVersion) {
            this.currentVersion = currentVersion;
            this.downgradedTargetVersion = downgradedTargetVersion;
        }

        public String getCurrentVersion() {
            return currentVersion;
        }

        public void setCurrentVersion(String currentVersion) {
            this.currentVersion = currentVersion;
        }

        public String getDowngradedTargetVersion() {
            return downgradedTargetVersion;
        }

        public void setDowngradedTargetVersion(String downgradedTargetVersion) {
            this.downgradedTargetVersion = downgradedTargetVersion;
        }

        @Override
        public String toString() {
            return "BackupVersionInfo{" +
                    "currentVersion='" + currentVersion + '\'' +
                    ", downgradedTargetVersion='" + downgradedTargetVersion + '\'' +
                    '}';
        }
    }

    // 自定义 Gson 适配器
    private class PlaceTypeAdapter implements JsonDeserializer<PlaceType> {
        @Override
        public PlaceType deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            JsonObject jsonObject = json.getAsJsonObject();

            // 反序列化基本字段
            int typeId = jsonObject.get("typeId").getAsInt();
            String typeName = jsonObject.get("typeName").getAsString();
            String typeDescription = jsonObject.get("typeDescription").getAsString();

            // 创建 PlaceType 实例
            PlaceType placeType = new PlaceType(typeId, typeName, typeDescription);

            // 处理 ToMany 字段
            if (jsonObject.has("placeNameToMany")) {
                JsonArray placeNamesArray = jsonObject.getAsJsonArray("placeNameToMany");
                for (JsonElement element : placeNamesArray) {
                    PlaceName placeName = context.deserialize(element, PlaceName.class);
                    placeType.placeNameToMany.add(placeName);
                }
            }

            return placeType;
        }
    }

}
