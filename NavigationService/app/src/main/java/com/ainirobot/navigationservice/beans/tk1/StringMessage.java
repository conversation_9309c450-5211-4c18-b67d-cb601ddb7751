/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.tk1;

import android.text.TextUtils;
import android.util.Log;

import java.io.ByteArrayOutputStream;
import java.io.DataOutput;
import java.io.IOException;

public class StringMessage extends Message {

    private String content;

    public StringMessage() {
        super(Message.MSG_TYPE_STRING);
    }

    @Override
    public void writeTo(DataOutput out) throws IOException {
        out.writeInt((int) getLength());
        out.writeLong(getLength());
        out.writeBytes(content);
    }

    @Override
    public void readData(ReverseInputStream in) throws IOException {
        int length = in.readInt();
        long destLength = in.readLong();

        Log.d("Navigation", "StringMessage receive length : "
                + length + "  destLength : " + destLength);

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int len;
        byte[] buffer = new byte[4096];
        while (destLength > 0) {
            len = buffer.length;
            if (destLength < len) {
                len = (int) destLength;
            }
            len = in.read(buffer,0,len);
            if (len != -1) {
                out.write(buffer,0,len);
                destLength -= len;
            }
        }
        content = new String(out.toByteArray());

        Log.d("Navigation", "StringMessage receive content : " + content);
    }

    @Override
    public long getLength() {
        if(TextUtils.isEmpty(content)){
            return 0;
        }
        return content.length();
    }
}
