package com.ainirobot.navigationservice.beans.standard;

public class LandMarkBean {
    String name;
    String id;
    PoseInfo poseInfo;

    public LandMarkBean(String name, String id, PoseInfo poseInfo) {
        this.name = name;
        this.id = id;
        this.poseInfo = poseInfo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public PoseInfo getPoseInfo() {
        return poseInfo;
    }

    public void setPoseInfo(PoseInfo poseInfo) {
        this.poseInfo = poseInfo;
    }

    @Override
    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append("name = ");
        sb.append(this.name);
        sb.append(", id = ");
        sb.append(this.id);
        sb.append(", poseInfo = ");
        sb.append(this.poseInfo.toString());
        return sb.toString();
    }
}
