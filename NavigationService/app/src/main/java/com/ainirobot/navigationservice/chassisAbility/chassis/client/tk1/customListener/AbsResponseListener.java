package com.ainirobot.navigationservice.chassisAbility.chassis.client.tk1.customListener;

import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.utils.ResponseUtils;


public abstract class AbsResponseListener implements IChassisClient.ChassisResListener {

    protected abstract void doResponse(String response);

    protected String getResponse(int resultCode) {
        return ResponseUtils.getResponseStrByCode(resultCode);
    }
}
