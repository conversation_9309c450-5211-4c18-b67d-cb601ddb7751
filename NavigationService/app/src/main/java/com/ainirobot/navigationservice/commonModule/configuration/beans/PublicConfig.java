package com.ainirobot.navigationservice.commonModule.configuration.beans;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class PublicConfig {

    @SerializedName("地图兼容性配置")
    private MapCompatibilityConfig mapCompatibilityConfig;

    public MapCompatibilityConfig getMapCompatibilityConfig() {
        return mapCompatibilityConfig;
    }

    @Override
    public String toString() {
        return "PublicConfig{" +
                "mapCompatibilityConfig=" + mapCompatibilityConfig +
                '}';
    }

    public static class MapCompatibilityConfig {

        @SerializedName("最高兼容版本")
        private int mapCompatibleVersion;
        @SerializedName("视觉类型")
        private List<Integer> visionTypes;
        @SerializedName("标识码类型")
        private List<Integer> targetTypes;

        public int getMapVersion() {
            return mapCompatibleVersion;
        }

        public void setMapVersion(int mapCompatibleVersion) {
            this.mapCompatibleVersion = mapCompatibleVersion;
        }

        public List<Integer> getVisionTypes() {
            return visionTypes;
        }

        public void setVisionTypes(List<Integer> visionTypes) {
            this.visionTypes = visionTypes;
        }

        public List<Integer> getTargetTypes() {
            return targetTypes;
        }

        public void setTargetTypes(List<Integer> targetTypes) {
            this.targetTypes = targetTypes;
        }

        @Override
        public String toString() {
            return "MapCompatibilityConfig{" +
                    "mapCompatibleVersion='" + mapCompatibleVersion + '\'' +
                    ", visionTypes=" + visionTypes +
                    ", targetTypes=" + targetTypes +
                    '}';
        }
    }

}
