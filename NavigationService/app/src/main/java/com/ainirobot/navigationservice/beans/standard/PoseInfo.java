/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.standard;

/**
 * This expresses a position and orientation on a 2D manifold.
 */
public class PoseInfo {

    private final double x, y, theta;
    private int status;

    public PoseInfo(double x, double y, double theta) {
        this.x = x;
        this.y = y;
        this.theta = theta;
    }

    public PoseInfo(final double x, final double y, final double theta, final int status) {
        this.x = x;
        this.y = y;
        this.theta = theta;
        this.status = status;
    }

    public double getX() {
        return x;
    }

    public double getY() {
        return y;
    }

    public double getTheta() {
        return theta;
    }

    public int getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return "x=" + x + "  y=" + y + " theta=" + theta + "  status=" + status;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null || !(obj instanceof PoseInfo)) {
            return false;
        }

        PoseInfo pose = (PoseInfo) obj;
        return this.x == pose.getX()
            && this.y == pose.getY()
            && this.theta == pose.getTheta()
            && this.status == pose.status;
    }
}
