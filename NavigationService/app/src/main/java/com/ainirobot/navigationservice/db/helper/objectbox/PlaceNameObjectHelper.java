package com.ainirobot.navigationservice.db.helper.objectbox;

import android.util.Log;

import com.ainirobot.navigationservice.db.entity.PlaceName;
import com.ainirobot.navigationservice.db.entity.PlaceName_;
import com.ainirobot.navigationservice.db.helper.iml.PlaceNameHelperIml;

import java.util.List;

import io.objectbox.Box;
import io.objectbox.BoxStore;
import io.objectbox.query.Query;
import io.objectbox.query.QueryBuilder;

public class PlaceNameObjectHelper extends BaseObjectHelper<PlaceName> implements PlaceNameHelperIml {
    public PlaceNameObjectHelper(BoxStore boxStore) {
        super(boxStore);
    }

    @Override
    public String[] getPlaceIdsByName(String placeName) {
        Query<PlaceName> placeNameQuery = getBox()
                .query(PlaceName_.placeName.equal(placeName))
                .build();
        String[] placeIds = placeNameQuery.property(PlaceName_.placeId).findStrings();
        placeNameQuery.close();
        return placeIds;
    }

    @Override
    public String[] getPlaceIdsByNameList(String[] nameArr) {
        Query<PlaceName> placeNameQuery = getBox()
                .query(PlaceName_.placeName.oneOf(nameArr))
                .build();
        String[] placeIds = placeNameQuery.property(PlaceName_.placeId).findStrings();
        placeNameQuery.close();
        return placeIds;
    }

    @Override
    public List<PlaceName> getPlaceNameByPlaceId(String[] placeIds) {
        Query<PlaceName> nameQuery = getPlaceNameQuery(placeIds);
        List<PlaceName> placeNames = nameQuery.find();
        nameQuery.close();
        return placeNames;
    }

    @Override
    public void updatePlace(String[] idArr, List<String> newNameList, String language) {
        Box<PlaceName> placeNameBox = getBox();
        Query<PlaceName> nameQuery = placeNameBox.query(PlaceName_.placeId.oneOf(idArr))
                .equal(PlaceName_.languageType, language, QueryBuilder.StringOrder.CASE_SENSITIVE)
                .build();
        List<PlaceName> placeNames = nameQuery.find();
        nameQuery.close();
        for (int i = 0; i < placeNames.size(); i++) {
            PlaceName placeName = placeNames.get(i);
            String name = newNameList.get(i);
            placeName.setPlaceName(name);
        }
        placeNameBox.put(placeNames);
    }

    @Override
    public void initPlaceNameData(List<PlaceName> nameList) {
        if (null == nameList || nameList.isEmpty()) {
            Log.d(TAG, "initPlaceNameData: list is null");
            return;
        }
        Log.d(TAG, "initPlaceNameData: start");
        Box<PlaceName> placeNameBox = getBox();
        placeNameBox.removeAll();
        placeNameBox.put(nameList);
        Log.d(TAG, "initPlaceNameData: " + placeNameBox.count());
    }

    @Override
    public boolean updatePlaceNames(List<PlaceName> placeNames) {
        Box<PlaceName> placeInfoBox = getBox();
        placeInfoBox.put(placeNames);
        return true;
    }

    @Override
    public void deletePlaceNameByPlaceId(String[] placeIds) {
        if (null == placeIds) {
            Log.d(TAG, "deletePlaceNameByPlaceId placeIds is null");
            return;
        }
        Query<PlaceName> nameQuery = getPlaceNameQuery(placeIds);
        boolean remove = nameQuery.remove() > 0;
        nameQuery.close();
        Log.d(TAG, "deletePlaceNameByPlaceId : " + remove);
    }

    @Override
    public boolean hasPlaceNameData() {
        Box<PlaceName> placeNameBox = getBox();
        long count = placeNameBox.count();
        Log.d(TAG, "hasPlaceNameData count: " + count);
        return count > 0;
    }

    private Query<PlaceName> getPlaceNameQuery(String[] placeIds) {
        return getBox()
                .query(PlaceName_.placeId.oneOf(placeIds)).build();
    }
}