package com.ainirobot.navigationservice.db.helper.objectbox;

import com.ainirobot.navigationservice.Defs.Def;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;

import io.objectbox.Box;
import io.objectbox.BoxStore;
import io.objectbox.query.Query;

public class BaseObjectHelper<T> {

    protected final String TAG;
    protected final BoxStore boxStore;

    private Box<T> mBox;
    private final Class<T> mDbType;

    public BaseObjectHelper(BoxStore boxStore) {
        this.boxStore = boxStore;
        TAG = Def.MAP_DB_PRE + "Ob_Help_" + this.getClass().getSimpleName();
        Type superclass = getClass().getGenericSuperclass();
        if (superclass instanceof ParameterizedType) {
            ParameterizedType parameterized = (ParameterizedType) superclass;
            mDbType = (Class<T>) parameterized.getActualTypeArguments()[0];
        } else {
            throw new RuntimeException("Invalid superclass type");
        }
    }

    public Box<T> getBox() {
        if (mBox == null) {
            mBox = boxStore.boxFor(mDbType);
        }
        return mBox;
    }

    public void deleteAllData() {
        Box<T> chassisInfoBox = getBox();
        chassisInfoBox.removeAll();
    }

    public List<T> getAllDbInfo() {
        Query<T> infoQuery = getBox().query().build();
        List<T> chassisInfos = infoQuery.find();
        infoQuery.close();
        return chassisInfos;
    }
}
