package com.ainirobot.navigationservice.commonModule.bi.bean;

public class BiCorrectLocationBean {
    private BiPoseBean guess_pose;
    private BiPoseBean vision_pose;
    private BiPoseBean vision_refine_pose;
    private int locate_type;
    private double lost_distance;
    private int arg1;
    private int arg2;
    private double arg3;
    private double arg4;
    private String message;

    public BiCorrectLocationBean() {

    }

    public BiPoseBean getGuess_pose() {
        return guess_pose;
    }

    public void setGuess_pose(BiPoseBean guess_pose) {
        this.guess_pose = guess_pose;
    }

    public BiPoseBean getVision_pose() {
        return vision_pose;
    }

    public void setVision_pose(BiPoseBean vision_pose) {
        this.vision_pose = vision_pose;
    }

    public BiPoseBean getVision_refine_pose() {
        return vision_refine_pose;
    }

    public void setVision_refine_pose(BiPoseBean vision_refine_pose) {
        this.vision_refine_pose = vision_refine_pose;
    }

    public int getLocate_type() {
        return locate_type;
    }

    public void setLocate_type(int locate_type) {
        this.locate_type = locate_type;
    }

    public double getLost_distance() {
        return lost_distance;
    }

    public void setLost_distance(double lost_distance) {
        this.lost_distance = lost_distance;
    }

    public int getArg1() {
        return arg1;
    }

    public void setArg1(int arg1) {
        this.arg1 = arg1;
    }

    public int getArg2() {
        return arg2;
    }

    public void setArg2(int arg2) {
        this.arg2 = arg2;
    }

    public double getArg3() {
        return arg3;
    }

    public void setArg3(double arg3) {
        this.arg3 = arg3;
    }

    public double getArg4() {
        return arg4;
    }

    public void setArg4(double arg4) {
        this.arg4 = arg4;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "ChassisReportBean{" +
                "guess_pose=" + guess_pose +
                ", vision_pose=" + vision_pose +
                ", vision_refine_pose=" + vision_refine_pose +
                ", locate_type=" + locate_type +
                ", lost_distance=" + lost_distance +
                ", arg1=" + arg1 +
                ", arg2=" + arg2 +
                ", arg3=" + arg3 +
                ", arg4=" + arg4 +
                ", message='" + message + '\'' +
                '}';
    }
}
