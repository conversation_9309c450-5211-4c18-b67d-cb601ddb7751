package com.ainirobot.navigationservice.db.entity;

import java.util.List;

import io.objectbox.annotation.Entity;
import io.objectbox.annotation.Id;

/**
 * 充电等候区表
 */
@Entity
public class ChargeArea {
    @Id
    public long id;
    public int areaId;
    private String areaAlias;
    //地图名称
    private String mapName;
    //是否本机使用
    private int useState;
    //可用的充电区域等候点
    private List<String> availableWaitPoints;
    //可用的充电区域充电桩
    private List<String> availableChargePiles;

    public ChargeArea() {
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }
    public int getAreaId() {
        return areaId;
    }

    public void setAreaId(int areaId) {
        this.areaId = areaId;
    }

    public String getMapName() {
        return mapName;
    }

    public void setMapName(String mapName) {
        this.mapName = mapName;
    }

    public List<String> getAvailableWaitPoints() {
        return availableWaitPoints;
    }

    public void setAvailableWaitPoints(List<String> availableWaitPoints) {
        this.availableWaitPoints = availableWaitPoints;
    }

    public List<String> getAvailableChargePiles() {
        return availableChargePiles;
    }

    public void setAvailableChargePiles(List<String> availableChargePiles) {
        this.availableChargePiles = availableChargePiles;
    }

    public String getAreaAlias() {
        return areaAlias;
    }

    public void setAreaAlias(String areaAlias) {
        this.areaAlias = areaAlias;
    }

    public int getUseState() {
        return useState;
    }

    public void setUseState(int useState) {
        this.useState = useState;
    }
}
