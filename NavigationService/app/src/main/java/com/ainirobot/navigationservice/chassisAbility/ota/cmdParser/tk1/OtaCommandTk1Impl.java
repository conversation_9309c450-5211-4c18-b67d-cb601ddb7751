package com.ainirobot.navigationservice.chassisAbility.ota.cmdParser.tk1;


import android.util.Log;

import com.ainirobot.navigationservice.chassisAbility.ota.Constant;
import com.ainirobot.navigationservice.chassisAbility.ota.cmdParser.IOtaCommand;
import com.ainirobot.navigationservice.chassisAbility.ota.connector.IOtaConnect;
import com.ainirobot.navigationservice.chassisAbility.ota.connector.tk1.bean.CmdParam;
import com.ainirobot.navigationservice.chassisAbility.ota.connector.tk1.bean.OtaReqMessage;
import com.ainirobot.navigationservice.chassisAbility.ota.connector.tk1.bean.OtaResMessage;
import com.ainirobot.navigationservice.utils.GsonUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.concurrent.ConcurrentHashMap;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

public class OtaCommandTk1Impl implements IOtaCommand {

    private final static String TAG = TAGPRE + OtaCommandTk1Impl.class.getSimpleName();

    //目前实现，并没有考虑到连续发起CMD 请求的情况
    private ConcurrentHashMap<String, CmdResListener> mResListenerMap;
    private ConcurrentHashMap<String, CmdEventListener> mEventListenerMap;
    private IOtaConnect connector;

    public OtaCommandTk1Impl() {
        mResListenerMap = new ConcurrentHashMap<>();
        mEventListenerMap = new ConcurrentHashMap<>();
    }

    @Override
    public void init() {
        if (connector != null) {
            connector.registerResponseListener(cnnResListener);
            connector.registerEventListener(cnnEventListener);
            connector.registerConnectListener(cnnOnConnectListener);
            connector.init();
        } else {
            throw new RuntimeException("Ota Cmd, no connector available");
        }
    }

    @Override
    public void injectConnector(IOtaConnect connector) {
        this.connector = connector;
    }

    @Override
    public void sendGetUpdateParamsCmd(CmdResListener listener) {
        CmdParam cmdParam = new CmdParam(ReqDef.CMD_GET_UPDATE_PARAMS, null);
        OtaReqMessage otaReqMessage = new OtaReqMessage(OtaReqMessage.STRING_MSG, GsonUtil.toJson(cmdParam));
        connector.request(GsonUtil.toJson(otaReqMessage));
        mResListenerMap.put(ReqDef.CMD_GET_UPDATE_PARAMS, listener);
    }

    @Override
    public void sendGetVersionCmd(CmdResListener listener) {
        CmdParam cmdParam = new CmdParam(ReqDef.CMD_GET_VERSION, null);
        OtaReqMessage otaReqMessage = new OtaReqMessage(OtaReqMessage.STRING_MSG, GsonUtil.toJson(cmdParam));
        connector.request(GsonUtil.toJson(otaReqMessage));
        mResListenerMap.put(ReqDef.CMD_GET_VERSION, listener);
    }

    @Override
    public void sendStartUpdateCmd(String otaInfo, CmdResListener listener) {
        CmdParam cmdParam = new CmdParam(ReqDef.CMD_START_UPDATE, otaInfo);
        OtaReqMessage otaReqMessage = new OtaReqMessage(OtaReqMessage.STRING_MSG, GsonUtil.toJson(cmdParam));
        connector.request(GsonUtil.toJson(otaReqMessage));
        mResListenerMap.put(ReqDef.CMD_START_UPDATE, listener);
    }

    /**
     * 这个不属于命令，发送文件，不需要listener
     * @param path
     */
    @Override
    public void sendUpdatePackage(String path) {
        OtaReqMessage otaReqMessage = new OtaReqMessage(OtaReqMessage.FILE_MSG, path);
        connector.request(GsonUtil.toJson(otaReqMessage));
    }

    @Override
    public void registerEventListener(String type, CmdEventListener listener) {
        if (mEventListenerMap != null) {
            mEventListenerMap.put(type, listener);
        }
    }

    @Override
    public void unRegisterEventListener(String type) {

    }

    @Override
    public boolean isOtaConnected() {
        return otaConnected;
    }

    private IOtaConnect.CnnResListener cnnResListener= new IOtaConnect.CnnResListener() {
        @Override
        public void onResponse(String message) {
            // 获取package，实际上是走reponse通道的三条请求命令，请求升级包，因此需要走主动上报的逻辑通知otaClient
            try {
                OtaResMessage otaResMessage = GsonUtil.fromJson(message, OtaResMessage.class);
                switch (otaResMessage.getType()) {
                    case OtaResMessage.ERROR_RES:
                        processErrorRes(otaResMessage.getMsg());
                        break;
                    case OtaResMessage.SUC_RES:
                        processSucRes(otaResMessage.getMsg());
                        break;
                        default:
                            break;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    };

    private void processSucRes(String sucMsg) {
        /*CmdParam cmdParam = GsonUtil.fromJson(sucMsg, CmdParam.class);
        这块不能简单的用CmdParam来解析，因为TK1返回来的params参数是一个数组很操蛋。
        但是要返回给上层的却是一个string，所以只能手动解析了。*/
        String command;
        String params;
        try {
            JSONObject json = new JSONObject(sucMsg);
            command = json.getString("command");
            params = json.optString("params");
        } catch (JSONException e) {
            Log.d(TAG, "parse TK1 response error");
            e.printStackTrace();
            return;
        }
        switch (command) {
            case ReqDef.CMD_START_UPDATE:
            case ReqDef.CMD_GET_VERSION:
            case ReqDef.CMD_GET_UPDATE_PARAMS:
                sendResponse(command, params);
                break;
            case ReqDef.CMD_GET_MOTOR_LEFT_PACKAGE:
            case ReqDef.CMD_GET_MOTOR_RIGHT_PACKAGE:
            case ReqDef.CMD_GET_PACKAGE:
                /*
                 * 由于GET_PACKAGE 命令从属于START_UPDATE，其并非真正的response，也没有listener处理，所以通过event通道上报到otaClient进行处理
                 */
                sendEvent(ReqDef.CMD_START_UPDATE, sucMsg);
                break;
                default:
                    break;
        }
    }


    private void sendEvent(String type, String cmdParam) {
        CmdEventListener listener = mEventListenerMap.get(type);
        if (listener != null) {
            listener.onEvent(cmdParam);
        }
    }

    private void processErrorRes(String errorMsg) {
        OtaReqMessage otaReqMessage = GsonUtil.fromJson(errorMsg, OtaReqMessage.class);
        switch (otaReqMessage.getType()) {
            case OtaReqMessage.STRING_MSG:
                stringMsgError(otaReqMessage.getMsg());
                break;
            case OtaReqMessage.FILE_MSG:
                sendResponse(ReqDef.CMD_START_UPDATE, Constant.FAIL_MSG);
                break;
            default:
                break;
        }
    }


    private synchronized void stringMsgError(String info) {
        CmdParam cmdParam = GsonUtil.fromJson(info, CmdParam.class);
        sendResponse(cmdParam.getCommand(), Constant.FAIL_MSG);
    }

    private void sendResponse(String cmd, String params) {
        CmdResListener listener = mResListenerMap.get(cmd);
        if (listener != null) {
            listener.onResponse(params);
            mResListenerMap.remove(cmd);
        }
    }


    private IOtaConnect.CnnEventListener cnnEventListener = new IOtaConnect.CnnEventListener() {
        @Override
        public void onEvent(String message) {

        }
    };

    private volatile boolean otaConnected = false;
    private IOtaConnect.CnnOnConnectListener cnnOnConnectListener = new IOtaConnect.CnnOnConnectListener() {
        @Override
        public void onConnected() {
            otaConnected = true;
        }

        @Override
        public void onDisconnected() {
            otaConnected = false;
        }
    };
}
