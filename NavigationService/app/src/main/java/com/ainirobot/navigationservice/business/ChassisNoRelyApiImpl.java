package com.ainirobot.navigationservice.business;

import static com.ainirobot.coreservice.client.Definition.FAILED;
import static com.ainirobot.coreservice.client.Definition.PARAMS_ERROR;
import static com.ainirobot.coreservice.client.Definition.SUCCEED;
import static com.ainirobot.navigationservice.Defs.Def.CHANNEL_COMMAND;
import static com.ainirobot.navigationservice.Defs.Def.CHANNEL_EVENT;
import static com.ainirobot.navigationservice.Defs.Def.CHASSIS_REMOTE;
import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.util.SparseArray;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;

import com.ainirobot.base.util.FileUtils;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.actionbean.CheckObstacleBean;
import com.ainirobot.coreservice.client.actionbean.CruiseRouteBean;
import com.ainirobot.coreservice.client.actionbean.NaviCmdTimeOutBean;
import com.ainirobot.coreservice.client.actionbean.ObstacleInAreaBean;
import com.ainirobot.coreservice.client.hardware.RobotCore;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.utils.ZipUtils;
import com.ainirobot.navigationservice.Defs.Def;
import com.ainirobot.navigationservice.beans.MappingPose;
import com.ainirobot.navigationservice.beans.tk1.Laser;
import com.ainirobot.navigationservice.beans.tk1.Map;
import com.ainirobot.navigationservice.beans.tk1.Message;
import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.beans.tk1.Pose3D;
import com.ainirobot.navigationservice.beans.tk1.Statistic;
import com.ainirobot.navigationservice.beans.tk1.Velocity;
import com.ainirobot.navigationservice.beans.waiter.SubDeviceBean;
import com.ainirobot.navigationservice.business.rpc.SocketClient;
import com.ainirobot.navigationservice.chassisAbility.ChassisManager;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.chassisAbility.controller.BasicMotionProcess;
import com.ainirobot.navigationservice.chassisAbility.ota.Constant;
import com.ainirobot.navigationservice.commonModule.bi.BiManager;
import com.ainirobot.navigationservice.commonModule.bi.report.NavigationApiReport;
import com.ainirobot.navigationservice.commonModule.data.DataManager;
import com.ainirobot.navigationservice.commonModule.data.utils.UpdateTimeUtils;
import com.ainirobot.navigationservice.commonModule.logs.LogManager;
import com.ainirobot.navigationservice.commonModule.logs.beans.LogCacheBean;
import com.ainirobot.navigationservice.db.DataChangeListener;
import com.ainirobot.navigationservice.db.NavigationDataManager;
import com.ainirobot.navigationservice.db.ObjectBoxVersionManager;
import com.ainirobot.navigationservice.db.entity.ChargeArea;
import com.ainirobot.navigationservice.db.entity.ExtraInfo;
import com.ainirobot.navigationservice.db.entity.GateRelationInfo;
import com.ainirobot.navigationservice.db.entity.MapInfo;
import com.ainirobot.navigationservice.db.entity.MappingInfo;
import com.ainirobot.navigationservice.db.entity.MultiFloorInfo;
import com.ainirobot.navigationservice.db.entity.PlaceInfo;
import com.ainirobot.navigationservice.db.entity.PlaceName;
import com.ainirobot.navigationservice.roversdkhelper.MapFileHelper;
import com.ainirobot.navigationservice.roversdkhelper.mappackage.MapPkgHelper;
import com.ainirobot.navigationservice.roversdkhelper.maptype.MapTypeHelper;
import com.ainirobot.navigationservice.utils.GsonUtil;
import com.ainirobot.navigationservice.utils.MD5;
import com.ainirobot.navigationservice.utils.MapUtils;
import com.ainirobot.navigationservice.utils.TempCode;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;

import ninjia.android.proto.ChassisPacketProtoWrapper;

public class ChassisNoRelyApiImpl implements ChassisNoRelyApi {
    private final static String TAG = TAGPRE + ChassisNoRelyApiImpl.class.getSimpleName();

    private IChassisClient chassisClient;

    private BiManager biManager;

    private LogManager logManager;

    private Context mContext;

    private Gson mGson;

    private HashMap<String, List<Integer>> mStatusClients;

    private SparseArray<List<String>> mSocketTypes;
    private SparseArray<SocketClient> mSockets;

    private HandlerThread workHT;
    private Handler workHD;
    private static final String SWITCH_DATABASE_CRITICAL_VERSION = "10.3";

    @Override
    public void init(Context mContext) {
        this.mContext = mContext;
        mSockets = new SparseArray<>();
        mSocketTypes = new SparseArray<>();
        mStatusClients = new HashMap<>();
        chassisClient = ChassisManager.getInstance().getChassisClient();
        biManager = BiManager.getInstance();
        logManager = LogManager.getInstance();
        mGson = new Gson();

        chassisClient.setEventListener(mEventListener);
        checkChargePile();

        workHT = new HandlerThread(TAG);
        workHT.start();
        workHD = new WorkHandler(workHT.getLooper());

        startObserveNaviPropFile();

        NavigationDataManager.getInstance().registerDataChangeListener(multiFloorDataChangeListener);
        NavigationDataManager.getInstance().registerChargeAreaChangeListener(chargeAreaChangeListener);
    }

    /**
     * 多楼层数据变化监听
     */
    NavigationDataManager.MultiFloorChangeListener multiFloorDataChangeListener = new NavigationDataManager.MultiFloorChangeListener() {
        @Override
        public void onDataChanged() {
            Log.d(TAG, "multiFloorDataChangeListener:onDataChanged:");
            sendMultiFloorConfigReport();
        }
    };

    NavigationDataManager.ChargeAreaChangeListener chargeAreaChangeListener = new NavigationDataManager.ChargeAreaChangeListener() {
        @Override
        public void onDataChanged() {
            Log.d(TAG, "multiFloorDataChangeListener:onDataChanged:");
            sendChargeAreaConfigReport();
        }
    };

    private void startObserveNaviPropFile() {
        NavigationDataManager.getInstance().registerMapInfoListener(dataChangeListener);
    }

    private DataChangeListener dataChangeListener = () -> {
        Log.d(TAG, "MapInfo DataChangeListener:onChanged");
        sendStatusReport(Definition.REPORT_NAVI_CONFIG, "navi_update_map_info");
    };

    private void checkChargePile() {
        PlaceInfo placeBean = null;
        placeBean = NavigationDataManager.getInstance().getPlaceByType(Definition.CHARGING_POINT_TYPE, Definition.SPECIAL_PLACE_HIGH_PRIORITY);
        if (placeBean == null) {
            placeBean = NavigationDataManager.getInstance().getPlaceByName(Definition.START_BACK_CHARGE_POSE);
        }
        sendStatusReport(Definition.STATUS_SET_CHARGE_PILE, String.valueOf(placeBean != null));
    }

    @Override
    public boolean getLocation(String cmdType, String param) {
        String placeName = param;
        int typeId = 0;
        int priority = 0;
        try {
            JSONObject jsonObject = new JSONObject(param);
            placeName = jsonObject.has("placeName") ? jsonObject.optString("placeName") : "";
            typeId = jsonObject.has(Definition.JSON_NAVI_TYPE_ID) ? jsonObject.optInt(Definition.JSON_NAVI_TYPE_ID) : typeId;
            priority = jsonObject.has(Definition.JSON_NAVI_PRIORITY) ? jsonObject.optInt(Definition.JSON_NAVI_PRIORITY) : priority;
        } catch (Exception e) {
            Log.e(TAG, "Failed to parse cmdParam as JSON: " + param, e);
            e.printStackTrace();
        }
        PlaceInfo place;
        if (typeId != Definition.NORMAL_POINT_TYPE) {
            place = NavigationDataManager.getInstance().getPlaceByType(typeId, priority);
            Log.d(TAG, "Command get location: place = " + (place != null ? place.toString() : "null"));
        } else {
            placeName = NavigationDataManager.getInstance().getPlaceNameHighPriority(placeName);
            place = NavigationDataManager.getInstance().getPlaceByName(placeName);
            Log.d(TAG, "Command get location: placeName=" + placeName +
                    " place=" + (place != null ? place.toString() : "null"));
        }
        JSONObject result = new JSONObject();
        try {
            result.put(Definition.JSON_NAVI_SITE_EXIST, place != null);
            result.put(Definition.JSON_REMOTE_POS_NAME, placeName);
            if (place != null) {
                result.put(Definition.JSON_NAVI_POSITION_X, place.getPointX());
                result.put(Definition.JSON_NAVI_POSITION_Y, place.getPointY());
                result.put(Definition.JSON_NAVI_POSITION_THETA, place.getPointTheta());
                result.put(Definition.JSON_NAVI_TYPE_ID, place.getTypeId());
                result.put(Definition.JSON_NAVI_PRIORITY, place.getPriority());
            }
            Log.d(TAG, "Command get location: result=" + result);
            sendAsyncResponse(cmdType, result.toString());
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        sendFailedAsyncResponse(cmdType, "not exits");
        return false;
    }

    @Override
    public boolean setLocation(String cmdType, String placeName) {
        Log.d(TAG, "Command set location : " + placeName);
        int status = setPlace(placeName);
        Log.d(TAG, "Command set location : status=" + status);
        sendAsyncResponse(cmdType, String.valueOf(status));
        if (status == Pose.MAP_STATUS_NORMAL_AREA) {
            sendPlaceDataUpdateReport();//setLocation
        }
        return true;
    }

    @Override
    public boolean getNavigationStatus(String cmdType) {
        Log.d(TAG, "Command get navigation status isMoving : " + chassisClient.isMoving());
        JSONObject param = new JSONObject();
        try {
            param.put(Definition.JSON_NAVI_IN_NAVIGATION, chassisClient.isMoving());
            sendAsyncResponse(cmdType, param.toString());
            return true;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public boolean startRoutePlan(String cmdType) {
        Log.d(TAG, "Command start route plan");
        boolean result = chassisClient.startPlanRoute();
        sendAsyncResponse(cmdType, result ? SUCCEED : FAILED);
        return result;
    }

    @Override
    public boolean stopRoutePlan(String cmdType) {
        List<Pose> points = new ArrayList<>();//chassisClient.getRoutePoints();
        Log.d(TAG, "Command stop route plan points size : " + points);
        sendAsyncResponse(cmdType, mGson.toJson(points));
        return true;
    }

    @Override
    public boolean isInLocation(String cmdType, String params) {
        Log.d(TAG, "Command is in location");
        try {
            JSONObject json = new JSONObject(params);
            String placeId = json.optString(Definition.JSON_NAVI_PLACE_ID);
            String placeName = json.optString(Definition.JSON_NAVI_TARGET_PLACE_NAME);
            // 地点名称映射 兼容多语言特殊点名称
            placeName = NavigationDataManager.getInstance().getPlaceNameHighPriority(placeName);
            double range = json.optDouble(Definition.JSON_NAVI_COORDINATE_DEVIATION, 0);
            boolean checkTheta = json.optBoolean(Definition.IS_IN_LOCATION_CHECK_THETA, false);
            int typeId = json.optInt(Definition.JSON_NAVI_TYPE_ID, Definition.NORMAL_POINT_TYPE);
            int priority = json.optInt(Definition.JSON_NAVI_PRIORITY, Definition.SPECIAL_PLACE_HIGH_PRIORITY);
            boolean isInLocation = isInLocation(placeId, placeName, range, checkTheta, typeId, priority);

            JSONObject result = new JSONObject();
            result.put(Definition.JSON_NAVI_IS_IN_LOCATION, isInLocation);
            sendAsyncResponse(cmdType, result.toString());
            return true;
        } catch (JSONException e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, PARAMS_ERROR);
            return false;
        }
    }

    @Override
    public boolean getPlace(String cmdType, String param) {
        String placeName = param;
        try {
            JSONObject jsonObject = new JSONObject(param);
            placeName = jsonObject.optString("placeName");
        } catch (Exception e) {
            e.printStackTrace();
        }
        PlaceInfo placeBean = NavigationDataManager.getInstance().getPlaceByName(placeName);
        Log.d(TAG, "Command get place:" + param + ", place:" + (placeBean == null ? "null"
                : DataManager.placeInfoToJson(placeBean)));
        JSONObject json = new JSONObject();
        try {
            if (placeBean != null) {
                json.put(Definition.JSON_NAVI_POSITION_X, placeBean.getPointX());
                json.put(Definition.JSON_NAVI_POSITION_Y, placeBean.getPointY());
                json.put(Definition.JSON_NAVI_POSITION_THETA, placeBean.getPointTheta());
            }
            sendAsyncResponse(cmdType, json.toString());
            return true;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        sendAsyncResponse(cmdType, "not exits");
        return false;
    }

    /**
     * 旧版本设点，只进行存储操作
     *
     * @param cmdType
     * @param value
     * @return
     */
    @Override
    public boolean setPoseLocation(String cmdType, String value) {
        Log.d(TAG, "Command set pose location:" + value);
        Pose pose = stringToPose(value);
        String placeName = stringToPlace(value);
        if (pose != null) {
            pose.setName(placeName);
            Log.d(TAG, "Command set pose location pose:" + pose.toString());
            NavigationDataManager.getInstance().deleteAndUpdatePlace(pose);
            sendAsyncResponse(cmdType, SUCCEED);
            sendPlaceDataUpdateReport();//setPoseLocation
            return true;
        }
        sendAsyncResponse(cmdType, FAILED);
        return false;
    }

    @Override
    public boolean getPlaceList(String cmdType, String language) {
        Log.d(TAG, "getPlaceList: cmdType=" + cmdType + "  language=" + language);
        language = TextUtils.isEmpty(language) ? DataManager.DEFAULT_LANGUAGE : language;
        String placeList = mGson.toJson(NavigationDataManager.getInstance().getPlaceListOldVersion(language));
        placeList = ZipUtils.zipMapData(cmdType, mGson, placeList);
        Log.d(TAG, "getPlaceList: placeList=" + placeList);
        sendAsyncResponse(cmdType, placeList);
        return true;
    }

    /**
     * 获取地图地点列表
     *
     * @param cmdType
     * @param cmdParam Map name. Get current map place if null.
     * @return
     */
    @Override
    public boolean getInternationalPlaceList(String cmdType, String cmdParam) {
        Log.d(TAG, "getInternationalPlaceList: mapName=" + cmdParam);
        String mapName = cmdParam;//直接以字符串形式传入地图名
        boolean printLog = false;
        try {
            //以json形式传入地图名
            JSONObject jsonObject = new JSONObject(cmdParam);
            mapName = jsonObject.optString(Definition.JSON_MAP_NAME, mapName);
            printLog = jsonObject.optBoolean(Definition.JSON_PRINT_LOG, false);
        } catch (Exception e) { //地图名称为空时，走获取当前地图名称逻辑，所以这里需要捕获异常保证下面逻辑正常执行
            e.printStackTrace();
        }
        long startTime = System.currentTimeMillis();
        List<PlaceInfo> placeBeanList = NavigationDataManager.getInstance().getPlaceList(mapName, printLog);
        int listSize = placeBeanList != null ? placeBeanList.size() : 0;
        Log.d(TAG, "getInternationalPlaceList:End Total cost time:" + (System.currentTimeMillis() - startTime) +
                "ms, mapName=" + mapName + ", listSize=" + listSize);
        String responseStr = mGson.toJson(placeBeanList);
        responseStr = ZipUtils.zipMapData(cmdType, mGson, responseStr);
        Log.d(TAG, "getInternationalPlaceList: responseStr=" + responseStr);
        sendAsyncResponse(cmdType, responseStr);
        return true;
    }

    @Override
    public boolean updatePlaceList(String cmdType, String cmdParam, String language) {
        Log.d(TAG, "Command get update place list ：language=" + language);
        Type type = new TypeToken<List<PlaceInfo>>() {
        }.getType();
        cmdParam = ZipUtils.unzipMapData(mGson, cmdParam);
        List<PlaceInfo> placeList = mGson.fromJson(cmdParam, type);
        if (placeList != null && placeList.size() > 0) {
            String mapName = placeList.get(0).getMapName();
            MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
            if (mapInfo != null) {
                if (TextUtils.isEmpty(mapInfo.getMapLanguage())) {
                    String mapLanguage = DataManager.getInstance().parseMapLanguage(mapInfo);
                    mapInfo.setMapLanguage(mapLanguage);
                    NavigationDataManager.getInstance().updateMapInfo(mapInfo);
                }
            }
            Log.d(TAG, "Command get update place list ：mapName=" + mapName);
            if (TextUtils.isEmpty(mapName)) {
                sendAsyncResponse(cmdType, FAILED);
                return false;
            }
            NavigationDataManager.getInstance().updatePlaceInfoListByScene(placeList, mapName);
            JSONObject object = new JSONObject();
            try {
                object.put(Definition.ROBOT_LANGUAGE, language);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            sendAsyncResponse(cmdType, object.toString());
            sendPlaceDataUpdateReport();//updatePlaceList
        } else {
            Log.d(TAG, "Command get update place list ：list is null");
            sendAsyncResponse(cmdType, FAILED);
        }
        return true;
    }

    @Override
    public boolean getPlaceListWithNameList(String cmdType, String param) {
        Log.d(TAG, "getPlaceListWithNameList : " + cmdType + " || " + param);
        Type type = new TypeToken<List<String>>() {
        }.getType();
        try {
            List<String> names = mGson.fromJson(param, type);
            List<PlaceInfo> placeInfos = NavigationDataManager.getInstance().getPlaceByName(names);
            List<com.ainirobot.coreservice.client.actionbean.Pose> points = new ArrayList<>();
            for (PlaceInfo placeBean : placeInfos) {
                com.ainirobot.coreservice.client.actionbean.Pose poseResult = new com.ainirobot.coreservice.client.actionbean.Pose();
                poseResult.setX(placeBean.getPointX());
                poseResult.setY(placeBean.getPointY());
                poseResult.setTheta(placeBean.getPointTheta());
                points.add(poseResult);
            }
            String placesData = mGson.toJson(points);
            placesData = ZipUtils.zipMapData(cmdType, mGson, placesData);
            sendAsyncResponse(cmdType, placesData);
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, PARAMS_ERROR);
            return false;
        }
        return true;
    }

    @Override
    public boolean getPlaceListByMapName(String cmdType, String mapName, String language) {
        language = TextUtils.isEmpty(language) ? DataManager.DEFAULT_LANGUAGE : language;
        try {
            JSONObject jsonObject = new JSONObject(mapName);
            mapName = jsonObject.optString("placeName");
        } catch (Exception e) {
            e.printStackTrace();
        }
        //map-info 建图中充电桩重定位成功，从数据库获取点位列表
        String placeList = mGson.toJson(NavigationDataManager.getInstance().getPlaceListOldVersion(language, mapName));
        placeList = ZipUtils.zipMapData(cmdType, mGson, placeList);
        Log.d(TAG, "getPlaceListByMapName: cmdType " + cmdType + ", mapName = " + mapName
                + ", language = " + language + ", placeList = " + placeList);
        sendAsyncResponse(cmdType, placeList);
        return true;
    }

    @Override
    public boolean hasPlaceInMap(String cmdType, String param) {
        String mapName = "";
        String placeName = "";
        try {
            //以json形式传入地图名
            JSONObject jsonObject = new JSONObject(param);
            mapName = jsonObject.optString(Definition.JSON_MAP_NAME);
            placeName = jsonObject.optString(Definition.JSON_PLACE_NAME);
        } catch (Exception e) { //地图名称为空时，走获取当前地图名称逻辑，所以这里需要捕获异常保证下面逻辑正常执行
            e.printStackTrace();
        }
        boolean hasPlace = NavigationDataManager.getInstance().hasPlaceInMap(mapName, placeName);
        Log.d(TAG, "hasPlaceInMapName: cmdType " + cmdType + ", mapName = " + mapName
                + ", placeName = " + placeName);
        sendAsyncResponse(cmdType, String.valueOf(hasPlace));
        return true;
    }

    @Override
    public boolean getPlacesByType(String cmdType, String param) {
        Log.d(TAG, "getPlacesByType : " + cmdType + " || " + param);
        int typeId = 0;
        try {
            JSONObject jsonObject = new JSONObject(param);
            typeId = jsonObject.getInt(Definition.JSON_NAVI_TYPE_ID);
            Log.d(TAG, "getPlacesByType typeId: " + typeId);
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, PARAMS_ERROR);
            return false;
        }
        List<PlaceInfo> placeInfoList = NavigationDataManager.getInstance().getPlacesByType(typeId);
        String data = mGson.toJson(placeInfoList);
        data = ZipUtils.zipMapData(cmdType, mGson, data);
        sendAsyncResponse(cmdType, data);
        return false;
    }

    @Override
    public boolean isEstimate(String cmdType) {
        Log.d(TAG, "Command is estimate");
        if (!isChassisReady()) {
            sendAsyncResponse(cmdType, "false");
            return false;
        }
        boolean isEstimate = chassisClient.isPoseEstimate();
        sendAsyncResponse(cmdType, String.valueOf(isEstimate));
        return true;
    }

    @Override
    public boolean savePoseEstimate(String cmdType) {
        Log.d(TAG, "Command save pose estimate");
        boolean status = savePoseEstimate();
        if (status) {
            sendAsyncResponse(cmdType, SUCCEED);
            return true;
        }

        sendAsyncResponse(cmdType, FAILED);
        return false;
    }

    @Override
    public boolean getCurrentMap(String cmdType) {
        Log.d(TAG, "Command get current map");
        String mapName = NavigationDataManager.getInstance().getMapName();
        if (!TextUtils.isEmpty(mapName)) {
            sendAsyncResponse(cmdType, mapName);
            return true;
        }
        sendAsyncResponse(cmdType, null);
        return false;
    }

    @Override
    public boolean getCurrentPose(String cmdType) {
        Log.d(TAG, "Command get current map");
        Pose pose = chassisClient.getCurrentPose();
        if (pose != null) {
            try {
                JSONObject json = new JSONObject();
                json.put("px", pose.getX());
                json.put("py", pose.getY());
                json.put("theta", pose.getTheta());
                sendAsyncResponse(cmdType, json.toString());
                return true;
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        sendAsyncResponse(cmdType, null);
        return false;
    }

    @Override
    public boolean getCurrentPoseWithoutEstimate(String cmdType) {
        Log.d(TAG, "Command get current pose without estimate.");
        Pose pose = chassisClient.getCurrentPoseWithoutEstimate();
        if (pose != null) {
            try {
                JSONObject json = new JSONObject();
                json.put("px", pose.getX());
                json.put("py", pose.getY());
                json.put("theta", pose.getTheta());
                sendAsyncResponse(cmdType, json.toString());
                return true;
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        sendAsyncResponse(cmdType, null);
        return false;
    }

    //TODO: 2020/8/4 目前看只有建图中小程序在使用，暂不适配
    @Override
    @Deprecated
    public boolean setMapInfo(String cmdType, String params) {
        Log.d(TAG, "Command Deprecated,set map info params" + params);
        sendAsyncResponse(cmdType, FAILED);
        return false;
    }

    // TODO: 2020/8/4 巡逻路线在不处理
    @Override
    public boolean setPatrolList(String cmdType, String params) {
        Log.d(TAG, "Command set patrol list");
        Type type = new TypeToken<List<Pose>>() {
        }.getType();

        List<Pose> points;
        String mapName;
        try {
            JSONObject json = new JSONObject(params);
            mapName = json.getString("map_name");
            points = mGson.fromJson(json.getString("patrol_list"), type);
            MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
            if (null != mapInfo) {
                mapInfo.setPatrolRoute(mGson.toJson(points));
                NavigationDataManager.getInstance().updateMapInfo(mapInfo);
            }
            sendAsyncResponse(cmdType, SUCCEED);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        sendAsyncResponse(cmdType, FAILED);
        return true;
    }

    @Override
    public boolean getPatrolList(String cmdType, String params) {
        Log.d(TAG, "Command get patrol list");
        String mapName;
        try {
            JSONObject json = new JSONObject(params);
            mapName = json.getString("map_name");
            if (mapName == null || mapName.isEmpty()) {
                sendAsyncResponse(cmdType, FAILED);
                return false;
            }

            MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
            String patrolRoute;
            if (null == mapInfo || TextUtils.isEmpty(patrolRoute = mapInfo.getPatrolRoute())) {
                patrolRoute = mGson.toJson(new ArrayList<>());
            }
            sendAsyncResponse(cmdType, patrolRoute);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        sendAsyncResponse(cmdType, FAILED);
        return false;
    }

    @Override
    public boolean refreshMd5(String cmdType, String mapName) {
        Log.d(TAG, "Command refresh md5");
        refreshMd5(mapName);
        sendAsyncResponse(cmdType, "OK");
        return true;
    }

    /**
     * 下载地图后解析地点数据
     *
     * @param cmdType
     * @param cmdParam Map name
     * @return
     */
    @Override
    public boolean parseDownloadMapPlacePropToNaviProp(final String cmdType, final String cmdParam) {
        Log.d(TAG, "parseDownloadMapPlacePropToNaviProp: mapName=" + cmdParam);
        new Thread(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = null;
                String mapName = null;
                try {
                    jsonObject = new JSONObject(cmdParam);
                    mapName = jsonObject.optString(Definition.JSON_MAP_NAME);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (TextUtils.isEmpty(mapName)) {
                    Log.d(TAG, "parseDownloadMapPlacePropToNaviProp: mapName= is null");
                    sendAsyncResponse(cmdType, FAILED);
                    return;
                }
                NavigationDataManager.getInstance().parseCloudInfoToLocalMapInfo(mapName, jsonObject);
                NavigationDataManager.getInstance().deletePlaceByMapName(mapName);
                NavigationDataManager.getInstance().parsePlacePropToNaviProp(mapName);
                NavigationDataManager.getInstance().parseCloudInfoTurnstileJson(mapName);
                //判断如果有road.json数据，需要将数据中转成road_graph.data存储到data.zip内
                MapUtils.RoadDataState dataState = MapUtils.transferRoadJson2Data(mapName);//parseDownloadMapPlacePropToNaviProp
                //判断如果有target.json数据，需要将数据中转成targets.data存储到pgm.zip内
                MapUtils.TargetState targetState = MapUtils.transferTargetJson2Data(mapName);
                //mapAreaConfig.json 2 data
                MapUtils.MapAreaDataState mapAreaDataState =  MapUtils.transferMapAreaJson2Data(mapName);
                Log.d(TAG, " parseMapData: mapAreaDataState = " +mapAreaDataState);
                boolean isHasTargetData = MapUtils.isHasTargetsInfo(mapName);
                Log.d(TAG, "parseDownloadMapPlacePropToNaviProp dataState:" + dataState
                        + " targetState:" + targetState + " target:" + isHasTargetData);
                MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
                if (null != mapInfo) {
                    int mapType = isHasTargetData ? MapInfo.TargetDataState.HAS_TARGET_DATA : MapInfo.TargetDataState.NO_TARGET_DATA;
                    mapInfo.setTargetData(mapType);
                    NavigationDataManager.getInstance().updateMapInfo(mapInfo);
                }
                if (dataState == MapUtils.RoadDataState.MAP_NAME_ERROR
                        || dataState == MapUtils.RoadDataState.UNKNOWN_ERROR) {
                    sendAsyncResponse(cmdType, FAILED);
                    return;
                }
                if (targetState == MapUtils.TargetState.MAP_NAME_ERROR
                        || targetState == MapUtils.TargetState.UNKNOWN_ERROR) {
                    sendAsyncResponse(cmdType, FAILED);
                    return;
                }
                sendAsyncResponse(cmdType, SUCCEED);
                sendPlaceDataUpdateReport();//parseDownloadMapPlacePropToNaviProp
            }
        }).start();
        return true;
    }

    /**
     * 上传地图时调用，将地点信息保存到 robot/config/地图名称/place.properties 中
     *
     * @param cmdType
     * @param mapName
     * @return
     */
    @Override
    public boolean savePlaceListToPlaceFile(String cmdType, String mapName) {
        Log.e(TAG, "savePlaceListToPlaceFile: mapName=" + mapName);
        MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
        if (mapInfo == null) {
            Log.e(TAG, "savePlaceListToPlaceFile: not found map " + mapName);
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        NavigationDataManager.getInstance().savePlaceToPlaceFile(mapName, mapInfo);
        sendAsyncResponse(cmdType, SUCCEED);
        return true;
    }

    @Override
    public boolean setCruiseRoute(String cmdType, String params) {
        CruiseRouteBean route;
        try {
            route = mGson.fromJson(params, CruiseRouteBean.class);
            MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(route.getMapName());
            if (null != mapInfo) {
                mapInfo.setPatrolRoute(params);
                NavigationDataManager.getInstance().updateMapInfo(mapInfo);
            } else {
                Log.d(TAG, "setCruiseRoute : mapInfo in null");
            }

            Log.i(TAG, "setCruiseRoute route= " + route.toString());
            sendAsyncResponse(cmdType, SUCCEED);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        sendAsyncResponse(cmdType, FAILED);
        return true;
    }

    @Override
    public boolean getCruiseRoute(String cmdType, String params) {
        String mapName;
        try {
            JSONObject json = new JSONObject(params);
            mapName = json.getString("map_name");
            Log.i(TAG, "getCruiseRoute mapName= " + mapName);
            if (TextUtils.isEmpty(mapName)) {
                sendAsyncResponse(cmdType, FAILED);
                return false;
            }

            MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
            String patrolRoute;
            if (null != mapInfo && !TextUtils.isEmpty(patrolRoute = mapInfo.getPatrolRoute())) {
                Log.i(TAG, "getCruiseRoute route= " + patrolRoute);
                sendAsyncResponse(cmdType, patrolRoute);
                return true;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        sendAsyncResponse(cmdType, FAILED);
        return false;
    }

    @Override
    public boolean editPlaceProcess(String cmdType, String cmdParam) {
        try {
            boolean editPlaceRslt = true;
            String language = NavigationDataManager.getInstance().getCurrentMapLanguage();
            JSONArray opers = new JSONArray(cmdParam);
            long startTime = System.currentTimeMillis();
            List<Pose> addPoses = new ArrayList<>();
            List<String> deleteIds = new ArrayList<>();
            List<String> deleteNames = new ArrayList<>();
            List<JSONObject> renameById = new ArrayList<>();
            List<JSONObject> renameByOldName = new ArrayList<>();
            List<JSONObject> replacePlaces = new ArrayList<>();
            HashMap<String, Boolean> editForbidLine = new HashMap<>();
            for (int i = 0; i < opers.length(); i++) {
                JSONObject oper = opers.getJSONObject(i);
                Log.d(TAG, "editPlaceProcess: oper = " + oper.toString());
                switch (oper.optString("oper")) {
                    case Definition.ADDPLACE:
                        JSONObject newPlace = oper.optJSONObject(Definition.NEWPLACEBEAN);
                        String addName = newPlace.optString(Definition.NEWPLACENAME);
                        boolean ignoreDistance = newPlace.optBoolean(Definition.NEWPLACE_IGNORE_DISTANCE);
                        //无方向停靠
                        boolean noDirectionalParking = newPlace.optBoolean(Definition.NO_DIRECTIONAL_PARKING);
                        int safeDistance = newPlace.optInt(Definition.NEWPLACE_SAFE_DISTANCE);
                        int typeId = newPlace.optInt(Definition.NEWPLACE_TYPE_ID);
                        int priority = newPlace.optInt(Definition.JSON_NAVI_PRIORITY);
                        Pose pose = jsonToPose(newPlace.optJSONObject(Definition.POSE));
                        pose.setName(addName);
                        pose.setNoDirectionalParking(noDirectionalParking);
                        pose.setIgnoreDistance(ignoreDistance);
                        pose.setSafeDistance(safeDistance);
                        pose.setTypeId(typeId);
                        pose.setPriority(priority);
                        //先去重
                        Iterator<Pose> poseIterator = addPoses.iterator();
                        while (poseIterator.hasNext()) {
                            Pose poseAdded = poseIterator.next();
                            if (TextUtils.equals(poseAdded.getName(), addName)) {
                                poseIterator.remove();
                            }
                        }
                        addPoses.add(pose);
                        //去掉前面删除该点的操作
                        Iterator<String> delIterator = deleteNames.iterator();
                        while (delIterator.hasNext()) {
                            String delName = delIterator.next();
                            if (TextUtils.equals(delName, addName)) {
                                delIterator.remove();
                            }
                        }
                        break;
                    case Definition.DELPLACE:
                        String delId = oper.optString(Definition.JSON_NAVI_PLACE_ID);
                        String delName = oper.optString(Definition.DELNAME);
                        if (TextUtils.isEmpty(delId)) {
                            deleteNames.add(delName);
                        } else {
                            deleteIds.add(delId);
                        }
                        break;
                    case Definition.RENAMEPLACE: // 这里目前被MapTool，编辑点位处调用
                        JSONObject renameBean = oper.optJSONObject(Definition.RENAMEBEAN);
                        String id = renameBean.optString(Definition.JSON_NAVI_PLACE_ID);
                        if (TextUtils.isEmpty(id)) {
                            // renameBean=RenameBean{oldName='待机点1', newName='待机点666'}
                            renameByOldName.add(renameBean); // 添加renameBean json
                        } else {
                            renameById.add(renameBean);
                        }
                        break;
                    case Definition.REPLACEPLACE:
                        // replaceBean=ReplaceBean{pose2d=Pose2d{x=0.016539547592401505, y=-0.010084151290357113, t=0.34352174401283264, status = 0, obsDistance = 1.0E100}, placeName='待机点666'}
                        JSONObject replaceBean = oper.optJSONObject(Definition.REPLACEBEAN);
                        String placeName = replaceBean.optString(Definition.REPLACEPLACENAME);
                        if (!TextUtils.isEmpty(placeName)) {
                            replacePlaces.add(replaceBean);
                        }
                        break;
                    case Definition.FORBIDLINE:
                        String mapName = oper.optString(Definition.JSON_MAP_NAME);
                        boolean forbidLineFlag = oper.optBoolean(Definition.TYPE_FORBIDLINE);
                        editForbidLine.put(mapName, forbidLineFlag);
                        break;
                    default:
                        break;
                }
            }
            Log.d(TAG, "editPlaceProcess: data-parse:cost time:" + (System.currentTimeMillis() - startTime));

            if (!processAddPlace(addPoses)) {
                Log.i(TAG, "editPlaceProcess: Add place fail");
                editPlaceRslt = false;
            }
            if (!processDelPlace(deleteIds, deleteNames)) {
                Log.i(TAG, "editPlaceProcess: Delete place fail");
                editPlaceRslt = false;
            }
            if (!processRename(renameByOldName)) {
                Log.i(TAG, "editPlaceProcess: Rename place fail");
                editPlaceRslt = false;
            }
            if (!processReplace(replacePlaces)) {
                Log.i(TAG, "editPlaceProcess: Replace place fail");
                editPlaceRslt = false;
            }
            if (!processTypeForbidLine(editForbidLine)) {
                Log.i(TAG, "editPlaceProcess: Set forbid line fail");
                editPlaceRslt = false;
            }
            Log.d(TAG, "editPlaceProcess: End:cost time:" + (System.currentTimeMillis() - startTime));

            if (editPlaceRslt) {
                sendAsyncResponse(cmdType, SUCCEED);
                sendPlaceDataUpdateReport();//editPlaceProcess
            } else {
                sendAsyncResponse(cmdType, FAILED);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public boolean reportCmdTimeOut(String cmdType, String params) {
        Log.d(Definition.TAG_NAVI_LOG_REPORT, "reportCmdTimeOut: " + params);

        if (TextUtils.isEmpty(params)) {
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        NaviCmdTimeOutBean bean = mGson.fromJson(params, NaviCmdTimeOutBean.class);
        if (bean == null) {
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        if (!chassisClient.isCommunicating()) {
            Log.i(Definition.TAG_NAVI_LOG_REPORT, "navigation not disconnect or ready");
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }

        boolean insertResult = logManager.startCollectLogFlow(bean.getCacheId(),
                String.valueOf(bean.getTimestamp()), bean.getType());
        sendAsyncResponse(cmdType, insertResult ? SUCCEED : FAILED);
        return insertResult;
    }

    @Override
    public boolean timeOutMsgDelete(String cmdType, String params) {
        if (TextUtils.isEmpty(params)) {
            sendAsyncResponse(cmdType, PARAMS_ERROR);
            return false;
        }

        try {
            JSONObject json = new JSONObject(params);
            String cacheId = json.getString(Definition.JSON_NAVI_TIME_OUT_MSG_ID);
            if (TextUtils.isEmpty(cacheId)) {
                sendAsyncResponse(cmdType, PARAMS_ERROR);
                return false;
            }
            logManager.deleteLogById(cacheId);
            sendAsyncResponse(cmdType, SUCCEED);
            return true;
        } catch (JSONException e) {
            e.printStackTrace();
        }

        sendAsyncResponse(cmdType, FAILED);
        return false;
    }

    @Override
    public boolean getLogTaskById(String cmdType, String cacheId) {
        if (TextUtils.isEmpty(cacheId)) {
            sendAsyncResponse(cmdType, PARAMS_ERROR);
            return false;
        }

        try {
            LogCacheBean logCacheBean = logManager.getLogTaskById(cacheId);
            sendAsyncResponse(cmdType, logCacheBean.toString());
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }

        sendAsyncResponse(cmdType, FAILED);
        return false;
    }

    @Override
    public boolean updateLogStatusById(String cmdType, String cmdParam) {
        if (TextUtils.isEmpty(cmdParam)) {
            sendAsyncResponse(cmdType, PARAMS_ERROR);
            return false;
        }

        try {
            JSONObject json = new JSONObject(cmdParam);
            String cacheId = json.getString(Definition.JSON_NAVI_LOG_CACHE_ID);
            int logStatus = json.getInt(Definition.JSON_NAVI_LOG_CACHE_STATUS);
            if (TextUtils.isEmpty(cacheId)) {
                sendAsyncResponse(cmdType, PARAMS_ERROR);
                return false;
            }
            boolean isSuccess = logManager.updateLogStatusById(cacheId, logStatus);
            sendAsyncResponse(cmdType, isSuccess ? SUCCEED : FAILED);
            return isSuccess;
        } catch (JSONException e) {
            e.printStackTrace();
        }

        sendAsyncResponse(cmdType, FAILED);
        return false;
    }

    @Override
    public boolean renameMap(String cmdType, String cmdParam) {
        Log.d(TAG, "renameMap: cmdType=" + cmdType + " cmdParam=" + cmdParam);
        boolean isSuccess = false;
        try {
            JSONObject json = new JSONObject(cmdParam);
            String oldName = json.optString(Definition.OLD_MAP_NAME);
            String newName = json.optString(Definition.NEW_MAP_NAME);
            if (TextUtils.isEmpty(oldName) || TextUtils.isEmpty(newName)) {
                Log.d(TAG, "renameMap: mapName is null");
                isSuccess = false;
            } else {
                //修改数据库
                isSuccess = NavigationDataManager.getInstance().renameMap(oldName, newName);
                Log.d(TAG, "renameMap: isSuccess=" + isSuccess);
                //修改 mapinfo.json 文件信息
                MapUtils.saveMapInfoJson(oldName, NavigationDataManager.getInstance().getMapByName(newName));//renameMap
                sendPlaceDataUpdateReport();//renameMap
            }
            sendAsyncResponse(cmdType, isSuccess ? SUCCEED : FAILED);
            return isSuccess;
        } catch (JSONException e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean clearCruiseRoute(String cmdType, String params) {
        String mapName;
        try {
            JSONObject json = new JSONObject(params);
            mapName = json.getString("map_name");
            Log.i(TAG, "clearCruiseRoute mapName= " + mapName);
            if (mapName == null || mapName.isEmpty()) {
                sendAsyncResponse(cmdType, FAILED);
                return false;
            }
            boolean result = false;
            MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
            if (null != mapInfo) {
                mapInfo.setPatrolRoute("");
                result = NavigationDataManager.getInstance().updateMapInfo(mapInfo);
            }
            sendAsyncResponse(cmdType, result ? SUCCEED : FAILED);
            return result;
        } catch (JSONException e) {
            e.printStackTrace();
        }

        sendAsyncResponse(cmdType, FAILED);
        return false;
    }

    @Override
    public boolean checkPosePositionTheta(String cmdType, String params) {
        Log.d(TAG, "checkPosePositionTheta");
        try {
            JSONObject json = new JSONObject(params);
            String placeId = json.optString(Definition.JSON_NAVI_PLACE_ID);
            String placeName = json.optString(Definition.JSON_NAVI_TARGET_PLACE_NAME);
            double range = json.optDouble(Definition.JSON_NAVI_COORDINATE_DEVIATION, 0);
            double angle = json.optDouble(Definition.JSON_NAVI_COMPARE_ANGLE, 0);
            double theta = Math.toRadians(angle);
            boolean isInLocation = checkPosePosition(placeId, placeName, range, theta);

            JSONObject result = new JSONObject();
            result.put(Definition.JSON_NAVI_IS_IN_LOCATION, isInLocation);
            sendAsyncResponse(cmdType, result.toString());
            return true;
        } catch (JSONException e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, PARAMS_ERROR);
            return false;
        }
    }

    @Override
    public boolean checkObstacle(String cmdType, String cmdParam) {
        CheckObstacleBean beanParam;
        try {
            beanParam = mGson.fromJson(cmdParam, CheckObstacleBean.class);
            sendAsyncResponse(cmdType, chassisClient.hasObstacle(beanParam.getStartAngle(),
                    beanParam.getEndAngle(), beanParam.getDistance()) + "");
        } catch (Exception e) {
            sendAsyncResponse(cmdType, true + "");
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public boolean hasObstacleInArea(String cmdType, String cmdParam) {
        ObstacleInAreaBean beanParam;
        try {
            beanParam = mGson.fromJson(cmdParam, ObstacleInAreaBean.class);
            sendAsyncResponse(cmdType, chassisClient.hasObstacleInArea(beanParam.getStartAngle(),
                    beanParam.getEndAngle(), beanParam.getMinDistance(), beanParam.getMaxDistance()) + "");
        } catch (Exception e) {
            sendAsyncResponse(cmdType, true + "");
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public boolean startStatusSocket(String type, int socketPort) {
        Log.d(TAG, "startStatusSocket: type=" + type + " socketPort=" + socketPort);
        if (mSockets.get(socketPort) == null) {
            Log.d(TAG, "startStatusSocket: Null and create new socket client!");
            SocketClient socketClient = new SocketClient(socketPort);
            mSockets.put(socketPort, socketClient);
        }

        addSocketClient(type, socketPort);
        addSocketTranStatus(type, socketPort);
        return true;
    }

    @Override
    public boolean closeStatusSocket(String type, int socketPort) {
        Log.d(TAG, "closeStatusSocket: type=" + type + " socketPort=" + socketPort);
        List<Integer> ports = mStatusClients.get(type);
        if (ports != null) {
            ports.remove(Integer.valueOf(socketPort));
        }

        List<String> types = mSocketTypes.get(socketPort);
        if (types != null) {
            types.remove(type);
            if (types.isEmpty()) {
                SocketClient client = mSockets.get(socketPort);
                if (client != null) {
                    client.close();
                }
                mSockets.remove(socketPort);
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean checkSocketConnected(String cmdType) {
        sendAsyncResponse(cmdType, String.valueOf(isChassisReady()));
        return true;
    }

    @Override
    public boolean getMapInfo(String cmdType, String cmdParam) {
        Log.d(TAG, "getMapInfo: mapName=" + cmdParam);
        MapInfo mapInfo = null;
        if (TextUtils.isEmpty(cmdParam) || "{}".equals(cmdParam)) {
            mapInfo = NavigationDataManager.getInstance().getNavMapInfo();
        } else {
            mapInfo = NavigationDataManager.getInstance().getMapByName(cmdParam);
        }
        if (mapInfo == null) {
            sendAsyncResponse(cmdType, FAILED);
            return true;
        }
        mapInfo.updateNaviType();
        Log.d(TAG, "getMapInfo: mapInfo=" + mapInfo.toString());
        sendAsyncResponse(cmdType, mGson.toJson(mapInfo));
        return true;
    }

    @Override
    public boolean updateMapSyncState(String cmdType, String cmdParam) {
        Log.d(TAG, "updateMapSyncState: mapName=" + cmdParam);
        boolean result = false;
        try {
            JSONObject object = new JSONObject(cmdParam);
            String mapName = object.getString(Definition.JSON_MAP_NAME);
            boolean isMapState = object.getBoolean(Definition.JSON_MAP_IS_MAP_STATE);
            boolean needReUpload = object.getBoolean(Definition.JSON_MAP_NEED_RE_UPLOAD);
            MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
            if (null != mapInfo) {
                mapInfo.setSyncState(getMapSyncState(mapInfo.getSyncState(), isMapState, needReUpload));
                result = NavigationDataManager.getInstance().updateMapInfo(mapInfo);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            result = false;
        }
        Log.d(TAG, "updateMapSyncState: result=" + result);
        sendAsyncResponse(cmdType, result ? SUCCEED : FAILED);
        return true;
    }

    @Override
    public boolean updateMapUuid(String cmdType, String cmdParam) {
        Log.d(TAG, "updateMapUuid: cmdParam=" + cmdParam);
        boolean result = false;
        try {
            JSONObject object = new JSONObject(cmdParam);
            String mapName = object.getString(Definition.JSON_MAP_NAME);
            String uuid = object.getString(Definition.JSON_MAP_UUID);
            MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
            if (null != mapInfo) {
                mapInfo.setMapUuid(uuid);
                result = NavigationDataManager.getInstance().updateMapInfo(mapInfo);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            result = false;
        }
        Log.d(TAG, "updateMapUuid: result=" + result);
        sendAsyncResponse(cmdType, result ? SUCCEED : FAILED);
        return true;
    }

    @Override
    public boolean updateMapFinishState(String cmdType, String cmdParam) {
        Log.d(TAG, "updateMapFinishState: mapName=" + cmdParam);
        boolean result = false;
        try {
            JSONObject object = new JSONObject(cmdParam);
            String mapName = object.getString(Definition.JSON_MAP_NAME);
            int syncState = object.getInt(Definition.JSON_MAP_FINISH_STATE);
            boolean isReset = object.getBoolean(Definition.JSON_MAP_FINISH_STATE_IS_RESET);
            MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
            if (null != mapInfo) {
                syncState = isReset ? syncState : (mapInfo.getFinishState() | syncState);
                mapInfo.setFinishState(syncState);
                result = NavigationDataManager.getInstance().updateMapInfo(mapInfo);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            result = false;
        }
        Log.d(TAG, "updateMapFinishState: result=" + result);
        sendAsyncResponse(cmdType, result ? SUCCEED : FAILED);
        return true;
    }

    @Override
    public boolean updateMapForbidLine(String cmdType, String cmdParam) {
        Log.d(TAG, "updateMapForbidLine: mapName=" + cmdParam);
        boolean result = false;
        try {
            JSONObject object = new JSONObject(cmdParam);
            String mapName = object.getString(Definition.JSON_MAP_NAME);
            int forbidLine = object.getInt(Definition.JSON_MAP_FORBID_LINE);
            MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
            if (null != mapInfo) {
                mapInfo.setForbidLine(forbidLine);
                result = NavigationDataManager.getInstance().updateMapInfo(mapInfo);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            result = false;
        }
        Log.d(TAG, "updateMapForbidLine: result=" + result);
        sendAsyncResponse(cmdType, result ? SUCCEED : FAILED);
        return true;
    }

    @Override
    public boolean updateMapUpdateTime(String cmdType, String cmdParam) {
        Log.d(TAG, "updateMapUpdateTime: mapName=" + cmdParam);
        boolean result = false;
        try {
            JSONObject object = new JSONObject(cmdParam);
            String mapName = object.getString(Definition.JSON_MAP_NAME);
            long updateTime = object.getLong(Definition.JSON_MAP_UPDATE_TIME);
            MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
            if (null != mapInfo) {
                mapInfo.setUpdateTime(UpdateTimeUtils.timestampToDateSeconds(updateTime));
                result = NavigationDataManager.getInstance().updateMapInfo(mapInfo);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            result = false;
        }
        Log.d(TAG, "updateMapUpdateTime: result=" + result);
        sendAsyncResponse(cmdType, result ? SUCCEED : FAILED);
        return true;
    }

    @Override
    public boolean addMapInfo(String cmdType, String cmdParam) {
        Log.d(TAG, "addMapInfo:" + cmdParam);
        try {
            JSONObject object = new JSONObject(cmdParam);
            String mapName = object.getString(Definition.JSON_MAP_NAME);
            String md5 = object.getString(Definition.JSON_MAP_MD5);
            String mapUuid = object.getString(Definition.JSON_MAP_UUID);
            String updateTime = object.getString(Definition.JSON_MAP_UPDATE_TIME);
            NavigationDataManager.getInstance().addMapInfo(mapName, md5, mapUuid, updateTime);
            sendAsyncResponse(cmdType, SUCCEED);
        } catch (JSONException e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
        }
        return false;
    }

    /**
     * TODO 抽取公共方法处理地图数据，和{@link #parseDownloadMapPlacePropToNaviProp(String, String)}地图信息解析部分的逻辑保持一致，后续优化
     * 由于招财豹解析地图除了转存地图数据和点位信息，也会转存巡线 路线信息，其中耗时操作较多，会导致部分地图查询接口超时
     * @param cmdType
     * @param cmdParam
     * @return
     */
    @Override
    public boolean parseMapData(final String cmdType, final String cmdParam) {
        Log.d(TAG, "parseMapData: params=" + cmdParam);
        try {
            JSONObject jsonObject = new JSONObject(cmdParam);
            final String mapName = jsonObject.optString(Definition.JSON_MAP_NAME);
            String dataType = jsonObject.optString(Definition.JSON_DATA_TYPE);
            if (TextUtils.isEmpty(mapName) || TextUtils.isEmpty(dataType)) {
                sendAsyncResponse(cmdType, FAILED);
                return false;
            }
            if (!Definition.JSON_DATA_ROAD.equals(dataType)) {
                sendAsyncResponse(cmdType, FAILED);
                return false;
            }
            new Thread(new Runnable() {
                @Override
                public void run() {
                    //TODO mapAreaConfig.json 2 data
                    MapUtils.MapAreaDataState mapAreaDataState =  MapUtils.transferMapAreaJson2Data(mapName);
                    Log.d(TAG, " parseMapData: mapAreaDataState = " +mapAreaDataState);
                    //判断如果有road.json数据，需要将数据中转成road_graph.data存储到data.zip内
                    MapUtils.RoadDataState dataState = MapUtils.transferRoadJson2Data(mapName);//parseMapData
                    boolean isHasTargetData = MapUtils.isHasTargetsInfo(mapName);
                    MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
                    boolean result = false;
                    if (null != mapInfo) {
                        mapInfo.setTargetData(isHasTargetData ? MapInfo.TargetDataState.HAS_TARGET_DATA :
                                MapInfo.TargetDataState.NO_TARGET_DATA);
                        result = NavigationDataManager.getInstance().updateMapInfo(mapInfo);
                    }
                    Log.d(TAG, "parseMapData:" + dataState + " target:" + isHasTargetData + " result:" + result);
                    if (dataState == MapUtils.RoadDataState.MAP_NAME_ERROR
                            || dataState == MapUtils.RoadDataState.UNKNOWN_ERROR) {
                        sendAsyncResponse(cmdType, FAILED);
                        return;
                    }
                    sendAsyncResponse(cmdType, SUCCEED);
                }
            }).start();
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
        }
        return false;
    }

    @Override
    public boolean getMultiRobotSettingConfig(String cmdType, String cmdParam) {
        try {
            String loraConfig = NavigationDataManager.getInstance().getMultiRobotConfig();
            if (!TextUtils.isEmpty(loraConfig)) {
                sendAsyncResponse(cmdType, loraConfig);
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        sendAsyncResponse(cmdType, FAILED);
        return false;
    }


    @Override
    public boolean setMappingPlace(String cmdType, String cmdParam) {
        Log.d(TAG, "setMappingPlace: params=" + cmdParam);
        try {
            JSONObject jsonObject = new JSONObject(cmdParam);
            String mapName = jsonObject.optString(Definition.JSON_MAP_NAME);
            int placeType = jsonObject.optInt(Definition.JSON_PLACE_TYPE);
            String placeCnName = jsonObject.optString(Definition.JSON_PLACE_CN_NAME);
            String placeId = jsonObject.optString(Definition.JSON_NAVI_PLACE_ID);
            String mappingPlaceId = jsonObject.optString(Definition.JSON_MAPPING_PLACE_ID);
            MappingInfo mappingInfo = new MappingInfo();
            mappingInfo.setMapName(mapName);
            mappingInfo.setPlaceType(placeType);
            mappingInfo.setPlaceCnName(placeCnName);
            mappingInfo.setPlaceId(placeId);
            mappingInfo.setMappingPoseId(mappingPlaceId);
            boolean result = NavigationDataManager.getInstance().updateMappingInfo(mappingInfo);
            Log.d(TAG, "setMappingPlace: result=" + result);
            sendAsyncResponse(cmdType, result ? SUCCEED : FAILED);
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
        }
        return false;
    }

    @Override
    public boolean getMappingInfo(String cmdType, String cmdParam) {
        Log.d(TAG, "getMappingInfo: params=" + cmdParam);
        try {
            JSONObject jsonObject = new JSONObject(cmdParam);
            String mapName = jsonObject.optString(Definition.JSON_MAP_NAME);
            List<MappingInfo> mappingInfos = NavigationDataManager.getInstance().getMappingInfoByMapName(mapName);
            Log.d(TAG, "getMappingInfo: mappingInfos=" +
                    ((mappingInfos == null || mappingInfos.size() <= 0) ?
                            "NULL" : mappingInfos.toString()));
            sendAsyncResponse(cmdType, ((mappingInfos == null || mappingInfos.size() <= 0) ?
                    FAILED : mGson.toJson(mappingInfos)));
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
        }
        return false;
    }

    @Override
    public boolean saveRoadData(String cmdType, String cmdParam) {
        Log.d(TAG, "saveRoadData: cmdParam=" + cmdParam);
        try {
            JSONObject jsonObject = new JSONObject(cmdParam);
            String mapName = jsonObject.optString(Definition.JSON_MAP_NAME);
            String roadJson = jsonObject.optString("roadJson");
            boolean result = MapUtils.saveLocalRoadData(mapName, roadJson);
            Log.d(TAG, "saveRoadData: result=" + result);
            sendAsyncResponse(cmdType, result ? SUCCEED : FAILED);
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
        }
        return false;
    }

    @Override
    public boolean saveGateData(String cmdType, String cmdParam) {
        Log.d(TAG, "saveGateData: cmdParam=" + cmdParam);
        try {
            JSONObject jsonObject = new JSONObject(cmdParam);
            String mapName = jsonObject.optString(Definition.JSON_MAP_NAME);
            String gateJson = jsonObject.optString("gateJson");
            boolean result = MapUtils.saveLocalGateData(mapName, gateJson);
            Log.d(TAG, "saveGateData: result=" + result);
            sendAsyncResponse(cmdType, result ? SUCCEED : FAILED);
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
        }
        return false;
    }

    @Override
    public boolean getAdditionalDevice(String cmdType) {
        SubDeviceBean bean = MapFileHelper.getSubDeviceInfo();
        sendAsyncResponse(cmdType, bean == null ? "" : mGson.toJson(bean));
        return true;
    }

    @Override
    public boolean insertMultiFloorConfig(String cmdType, String cmdParam) {
        try {
            Type multiFloorListType = new TypeToken<List<MultiFloorInfo>>() {
            }.getType();
            List<MultiFloorInfo> infoList = mGson.fromJson(cmdParam, multiFloorListType);
            for (MultiFloorInfo info : infoList) {
                if (invalidIndexMultiFloorParam(cmdType, info)) {
                    sendAsyncResponse(cmdType, FAILED);
                    return false;
                }
            }
            boolean insertState = NavigationDataManager.getInstance().updateMultiFloorInfo(infoList);
            sendAsyncResponse(cmdType, insertState ? SUCCEED : FAILED);
            return insertState;
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
    }

    @Override
    public boolean updateMultiFloorConfig(String cmdType, String cmdParam) {
        try {
            JSONObject object = new JSONObject(cmdParam);
            String type = object.getString(Definition.JSON_TYPE); //Def.JSON_TYPE
            String data = object.getString(Definition.JSON_VALUE); //def.JSON_VALUE
            MultiFloorInfo info = mGson.fromJson(data, MultiFloorInfo.class);
            if (invalidMultiFloorParam(cmdType, info)) {
                sendAsyncResponse(cmdType, FAILED);
                return false;
            }
            boolean insertState = NavigationDataManager.getInstance().updateMultiFloorInfoByType(info, type);
            sendAsyncResponse(cmdType, insertState ? SUCCEED : FAILED);
            return insertState;
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
    }


    /**
     * 返回全部多层配置信息
     */
    @Override
    public boolean getMultiFloorConfig(String cmdType, String cmdParam) {
        try {
            List<MultiFloorInfo> infoList = NavigationDataManager.getInstance().getMultiFloorConfig();
            sendAsyncResponse(cmdType, mGson.toJson(infoList));
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
    }

    @Override
    public boolean getMultiFloorConfigAndPose(String cmdType, String cmdParam, String language) {
        try {
            long startTime = System.currentTimeMillis();
            List<MultiFloorInfo> infoList = NavigationDataManager.getInstance().getMultiFloorConfigAndPose(language,
                    //命令类型是CMD_NAVI_GET_MULTI_FLOOR_CONFIG_AND_POSE 不会去除特殊点位
                    TextUtils.equals(cmdType, Definition.CMD_NAVI_GET_MULTI_FLOOR_CONFIG_AND_POSE));
            Log.d(TAG, "getMultiFloorConfigAndPose cost:" + (System.currentTimeMillis() - startTime));
            String multiFloorData = mGson.toJson(infoList);
            multiFloorData = ZipUtils.zipMapData(cmdType, mGson, multiFloorData);
            sendAsyncResponse(cmdType, multiFloorData);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
    }

    @Override
    public boolean removeMultiFloorConfig(String cmdType, String cmdParam) {
        try {
            MultiFloorInfo info = mGson.fromJson(cmdParam, MultiFloorInfo.class);
            if (invalidMultiFloorParam(cmdType, info)) {
                sendAsyncResponse(cmdType, FAILED);
                return false;
            }
            boolean removedState = NavigationDataManager.getInstance().deleteMultiFloorInfo(info);
            sendAsyncResponse(cmdType, removedState ? SUCCEED : FAILED);
            return removedState;
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
    }

    @Override
    public boolean getChargeAreaConfig(String cmdType, String cmdParam) {
        try {
            List<ChargeArea> infoList = NavigationDataManager.getInstance().getChargeAreaConfig();
            sendAsyncResponse(cmdType, mGson.toJson(infoList));
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
    }

    @Override
    public boolean insertChargeAreaConfig(String cmdType, String cmdParam) {
        try {
            Type chargeAreaListType = new TypeToken<List<ChargeArea>>() {
            }.getType();
            List<ChargeArea> infoList = mGson.fromJson(cmdParam, chargeAreaListType);
            for (ChargeArea info : infoList) {
                if (TextUtils.isEmpty(info.getMapName())) {
                    sendAsyncResponse(cmdType, FAILED);
                    return false;
                }
            }
            boolean insertState = NavigationDataManager.getInstance().updateChargeAreaInfo(infoList);
            sendAsyncResponse(cmdType, insertState ? SUCCEED : FAILED);
            return insertState;
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
    }

    @Override
    public boolean updateChargeAreaConfig(String cmdType, String cmdParam) {
        try {
            JSONObject object = new JSONObject(cmdParam);
            String data = object.getString(Definition.JSON_VALUE); //def.JSON_VALUE
            ChargeArea info = mGson.fromJson(data, ChargeArea.class);
            if (TextUtils.isEmpty(info.getMapName())) {
                sendAsyncResponse(cmdType, FAILED);
                return false;
            }
            boolean insertState = NavigationDataManager.getInstance().updateChargeAreaInfoByType(info);
            sendAsyncResponse(cmdType, insertState ? SUCCEED : FAILED);
            return insertState;
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
    }

    @Override
    public boolean removeChargeAreaConfig(String cmdType, String cmdParam) {
        try {
            ChargeArea info = mGson.fromJson(cmdParam, ChargeArea.class);
            if (TextUtils.isEmpty(info.getMapName())) {
                sendAsyncResponse(cmdType, FAILED);
                return false;
            }
            boolean removedState = NavigationDataManager.getInstance().deleteChargeAreaInfo(info);
            sendAsyncResponse(cmdType, removedState ? SUCCEED : FAILED);
            return removedState;
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
    }

    /**
     * 有更新操作时，上报更新后的多层配置信息
     */
    private void sendMultiFloorConfigReport() {
        sendStatusReport(Definition.STATUS_MULTI_FLOOR_CONFIG_UPDATE,
                mGson.toJson(NavigationDataManager.getInstance().getMultiFloorConfig()));
    }

    /**
     * 有更新操作时，上报更新充电区域配置信息
     */
    private void sendChargeAreaConfigReport() {
        sendStatusReport(Definition.STATUS_CHARGE_AREA_CONFIG_UPDATE,
                mGson.toJson(NavigationDataManager.getInstance().getChargeAreaConfig()));
    }

    /**
     * 检查是否有 mapId
     */
    private boolean invalidMultiFloorParam(String cmdType, MultiFloorInfo info) {
        if (TextUtils.isEmpty(info.getMapName())) {
            sendFailedAsyncResponse(cmdType, Definition.ERROR_ELEVATOR_INFO_NEED_MAP_NAME);
            return true;
        }
        return false;
    }

    /**
     * 检查新增是否有 floorIndex及mapId
     * 同时检查参数中可用电梯数是否大于1
     */
    private boolean invalidIndexMultiFloorParam(String cmdType, MultiFloorInfo info) {
//        if (info.getFloorIndex() <= 0) {    //** 楼层映射从1开始
//            sendFailedAsyncResponse(cmdType, Definition.ERROR_ELEVATOR_INFO_FLOOR_INDEX);
//            return true;
//        }
        if (TextUtils.isEmpty(info.getMapName())) {
            sendFailedAsyncResponse(cmdType, Definition.ERROR_ELEVATOR_INFO_NEED_MAP_NAME);
            return true;
        }
        if (info.getAvailableElevators() == null || info.getAvailableElevators().size() <= 0) {
            sendFailedAsyncResponse(cmdType, Definition.ERROR_ELEVATOR_INFO_NEED_ELEVATOR);
            return true;
        }
        return false;
    }

    private boolean checkPosePosition(String placeId, String placeName, double range, double angle) {
        PlaceInfo placeBean;
        if (TextUtils.isEmpty(placeId)) {
            placeBean = NavigationDataManager.getInstance().getPlaceByName(placeName);
        } else {
            placeBean = NavigationDataManager.getInstance().getPlaceById(placeId);
        }
        if (null == placeBean) {
            return false;
        }
        Pose mCurrentPose = chassisClient.getCurrentPose();
        double distance = Math.sqrt(Math.pow((placeBean.getPointX() - mCurrentPose.getX()), 2)
                + Math.pow((placeBean.getPointY() - mCurrentPose.getY()), 2));
        double theta = placeBean.getPointTheta() - mCurrentPose.getTheta();
        if (theta > Math.PI) {
            theta = theta - 2 * Math.PI;
        } else if (theta < -Math.PI) {
            theta = theta + 2 * Math.PI;
        } else {
            Log.d(TAG, "theta not beyond Math.PI");
        }

        if (distance < range && Math.abs(theta) < angle) {
            return true;
        }
        return false;
    }

//    @Deprecated
//    private boolean processAddPlace(JSONObject oper, String language) throws Exception {
//        JSONObject newPlace = oper.optJSONObject(Definition.NEWPLACEBEAN);
//        String name = newPlace.optString(Definition.NEWPLACENAME);
//        Pose pose = jsonToPose(newPlace.optJSONObject(Definition.POSE));
//        pose.setName(name);
//        Log.d(TAG, "processAddPlace language = " + language + ", pose = " + pose.toString());
//        placeDataManager.deletePlaceByName(name);
//        return placeDataManager.addPlace(pose, language);
//    }
//
//    @Deprecated
//    private boolean processDelPlace(JSONObject oper) throws Exception {
//        String id = oper.optString(Definition.JSON_NAVI_PLACE_ID);
//        String name = oper.optString(Definition.DELNAME);
//        Log.d(TAG, "processDelPlace: id=" + id + " name=" + name);
//        if (TextUtils.isEmpty(id)) {
//            placeDataManager.deletePlaceByName(name);
//        } else {
//            placeDataManager.deletePlaceById(id);
//        }
//        return true;
//    }
//
//    @Deprecated
//    private boolean processRenamePlace(JSONObject oper, String language) throws Exception {
//        JSONObject renameBean = oper.optJSONObject(Definition.RENAMEBEAN);
//        String id = renameBean.optString(Definition.JSON_NAVI_PLACE_ID);
//        String oldName = renameBean.optString(Definition.OLD_NAME);
//        String newName = renameBean.optString(Definition.NEW_NAME);
//        Log.d(TAG, "processRenamePlace oldId = " + id +
//                ",oldName = " + oldName + ", newName = " + newName);
//        boolean result;
//        if (TextUtils.isEmpty(id)) {
//            placeDataManager.deletePlaceByName(newName);
//            result = placeDataManager.renamePlaceByName(oldName, newName, language);
//        } else {
//            result = placeDataManager.renamePlaceById(id, newName, language);
//        }
//        sendPlaceDataUpdateReport();
//        return result;
//    }
//
//    @Deprecated
//    private boolean processTypeForbidLine(JSONObject oper) {
//        String mapName = oper.optString(Definition.JSON_MAP_NAME);
//        boolean forbidLineFlag = oper.optBoolean(Definition.TYPE_FORBIDLINE);
//        Log.d(TAG, "processTypeForbidLine mapName = " + mapName +
//                ", forbidLineFlag = " + forbidLineFlag);
//        return dataManager.saveForbidLineFlag(mapName, forbidLineFlag);
//    }

    private void refreshMd5(String mapName) {
        File mapPgm = MapFileHelper.getMapPgm(mapName);
        String newMd5 = MD5.getFileMd5(mapPgm);
        if (!TextUtils.isEmpty(newMd5)) {
            MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
            if (null != mapInfo) {
                mapInfo.setMd5(newMd5);
                NavigationDataManager.getInstance().updateMapInfo(mapInfo);
            } else {
                Log.d(TAG, "refreshMd5 no such map");
            }
        }
        Log.d(TAG, "refreshMd5: pgm path=" + mapPgm.getAbsolutePath() + ", newMd5=" + newMd5);
    }

    private Pose jsonToPose(JSONObject json) {
        Pose pose = null;
        try {
            float x = Float.parseFloat(json.get("px").toString());
            float y = Float.parseFloat(json.get("py").toString());
            float theta = Float.parseFloat(json.get("theta").toString());
            pose = new Pose(x, y, theta);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return pose;
    }

    private boolean savePoseEstimate() {
        Pose pose = chassisClient.getCurrentPose();
        if (pose != null) {
            NavigationDataManager.getInstance().updateMapInfoEstimate(mGson.toJson(pose));
            return true;
        }
        return false;
    }

    private boolean isInLocation(String placeId, String placeName, double range, boolean checkTheta, int typeId, int priority) {
        PlaceInfo placeBean;
        if (TextUtils.isEmpty(placeId)) {
            placeBean = NavigationDataManager.getInstance().getPlaceByName(placeName);
        } else if (typeId != Definition.NORMAL_POINT_TYPE) {
            placeBean = NavigationDataManager.getInstance().getPlaceByType(typeId, priority);
        } else {
            placeBean = NavigationDataManager.getInstance().getPlaceByName(placeName);
        }
        if (null == placeBean) {
            return false;
        }
        Pose mCurrentPose = chassisClient.getCurrentPose();
        if (null == mCurrentPose) {
            return false;
        }
        return (!checkTheta || Math.abs(mCurrentPose.getTheta() - placeBean.getPointTheta()) <= 0.5) &&
                (Math.sqrt(Math.pow((placeBean.getPointX() - mCurrentPose.getX()), 2) +
                        Math.pow((placeBean.getPointY() - mCurrentPose.getY()), 2)) < range);
    }

    private Pose stringToPose(String jsonString) {
        Pose pose = null;
        try {
            JSONObject json = new JSONObject(jsonString);

            float x = json.has("x") ? Float.valueOf(json.get("x").toString())
                    : Float.valueOf(json.get("px").toString());
            float y = json.has("y") ? Float.valueOf(json.get("y").toString())
                    : Float.valueOf(json.get("py").toString());
            float theta = Float.valueOf(json.get("theta").toString());
            int typeId = json.has("typeId") ? json.optInt("typeId") : 0;
            int priority = json.has("priority") ? json.optInt("priority") : 0;
            pose = new Pose(x, y, theta, typeId, priority);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return pose;
    }

    private String stringToPlace(String jsonString) {
        String placeName = null;
        try {
            JSONObject json = new JSONObject(jsonString);
            placeName = json.getString("name");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return placeName;
    }

    private int setPlace(String placeName) {
        Pose pose = chassisClient.getCurrentPose();

        if (isChassisReady() && pose != null) {
            int status = pose.getStatus();
            Log.d(TAG, "setPlace: status=" + status);
            if (status == 0) {
                pose.setName(placeName);
                NavigationDataManager.getInstance().deleteAndUpdatePlace(pose);
                Log.d(TAG, "setPlace: placeName=" + placeName);
                return status;
            }

            return status;
        }

        return -1;
    }

    private boolean isChassisReady() {
        return chassisClient.isSocketConnected();
    }


    private IChassisClient.ChassisEventListener mEventListener = new IChassisClient.ChassisEventListener() {

        @Override
        public void onPoseUpdate(Pose pose) {
            JSONObject param = new JSONObject();
            try {
                param.put(Definition.JSON_NAVI_POSITION_X, pose.getX());
                param.put(Definition.JSON_NAVI_POSITION_Y, pose.getY());
                param.put(Definition.JSON_NAVI_POSITION_THETA, pose.getTheta());
                param.put(Definition.JSON_NAVI_POSITION_STATUS, pose.getStatus());
                param.put(Definition.JSON_NAVI_REALTIME_OBSTACLE_DISTANCE, pose.getObsDistance());
                sendStatusReport(Definition.STATUS_POSE, param.toString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onMapUpdate(Map map) {
            Log.i(TAG, "onMapUpdate: map:" + map);
            sendStatusBySocket(Definition.STATUS_MAP, map);
        }

        @Override
        public void onPushUpdate(String pushDistance) {
            sendStatusReport(Definition.STATUS_PUSH_EXCEED_DISTANCE, pushDistance);
        }

        @Override
        public void onOutMap(int key) {
            RobotCore.sendExceptionReport(RobotOS.NAVIGATION_SERVICE, Constant.STATUS_OUT_MAP, String.valueOf(key));
        }

        @Override
        public void onVelocityUpdate(Velocity velocity) {
            JSONObject param = new JSONObject();
            try {
                param.put(Definition.JSON_NAVI_LINEAR_SPEED_X, velocity.getX());
                param.put(Definition.JSON_NAVI_ANGULAR_SPEED_Z, velocity.getZ());
                sendStatusReport(Definition.STATUS_SPEED, param.toString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onEvent(String key, Object status) {
            JSONObject param = new JSONObject();
            try {
                param.put(key, status);
                sendStatusReport(Definition.STATUS_EVENT, param.toString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onException(String type, String data) {
            sendExceptionReport(type, data);
        }

        @Override
        public void onPoseEstimate(boolean isPoseEstimate) {
            try {
                JSONObject json = new JSONObject();
                json.put("isPoseEstimate", isPoseEstimate);
                sendStatusReport(Definition.STATUS_POSE_ESTIMATE, json.toString());
                try {
                    RobotSettings.setRobotInt(mContext, Definition.ROBOT_SETTINGS_POSE_ESTIMATE,
                            isPoseEstimate ? 1 : 0);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onStatisticUpdate(Statistic statistic) {
            biManager.reportNavigationLog(statistic);
        }

        @Override
        public void onPackLogEnd(String from, Object status) {
            sendStatusReport(Definition.STATUS_PACK_LOG_END, (String) status);
        }

        @Override
        public void onTakeSnapshotEnd(String from, String fileID, String path) {
            try {
                JSONObject msg = new JSONObject();
                msg.put(Definition.JSON_NAVI_FILE_ID, fileID);
                msg.put(Definition.JSON_NAVI_FILE_PATH, path);
                int snapResult = Definition.SnapResult.success.getValue();
                if (TextUtils.isEmpty(fileID))
                    snapResult = Definition.SnapResult.file_id_error.getValue();
                if (TextUtils.isEmpty(path))
                    snapResult = Definition.SnapResult.file_path_error.getValue();
                msg.put(Definition.JSON_NAVI_SNAP_RESULT, snapResult);
                sendStatusReport(Definition.STATUS_TAKE_SNAPSHOT_END, msg.toString());

                //更新缓存
                logManager.updateLogStatusIfNecessary(fileID);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onChassisDisconnect(String msg, String type) {
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("msg", msg);
                jsonObject.put("type", type);
                sendStatusReport(Definition.STATUS_CHASSIS_DISCONNECT_EVENT, jsonObject.toString());
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onChassisConnectStatus(String channelName, String state) {
            if (TextUtils.isEmpty(state)) {
                return;
            }
            if (state.equals(Definition.STATUS_HW_CONNECTED)) {
                if (TextUtils.isEmpty(channelName)) {
                    sendStatusReport(Definition.STATUS_CHASSIS_COMMAND, state);
                    sendStatusReport(Definition.STATUS_CHASSIS_EVENT, state);
                    sendStatusReport(Definition.STATUS_CHASSIS_REMOTE, state);
                } else {
                    if (channelName.equals(CHANNEL_EVENT)) {
                        sendStatusReport(Definition.STATUS_CHASSIS_EVENT, state);
                    } else if (channelName.equals(CHANNEL_COMMAND)) {
                        sendStatusReport(Definition.STATUS_CHASSIS_COMMAND, state);
                    } else if (channelName.equals(CHASSIS_REMOTE)) {
                        sendStatusReport(Definition.STATUS_CHASSIS_REMOTE, state);
                    } else {

                    }
                }
            } else {
                if (channelName.equals(CHANNEL_EVENT)) {
                    sendStatusReport(Definition.STATUS_CHASSIS_EVENT, state);
                } else if (channelName.equals(CHANNEL_COMMAND)) {
                    sendStatusReport(Definition.STATUS_CHASSIS_COMMAND, state);
                } else if (channelName.equals(CHASSIS_REMOTE)) {
                    sendStatusReport(Definition.STATUS_CHASSIS_REMOTE, state);
                } else {

                }
            }

        }

        @Override
        public void OnUpdateCreateMapProcess(String additional) {
            sendStatusReport(Definition.STATUS_UPDATE_CREATE_MAP_PROCESS, additional);
        }

        @Override
        public void OnRadarUpdate(boolean openRadar) {
            OnRadarUpdate(openRadar, openRadar ?
                    Definition.RADAR_STATUS_OPENED :
                    Definition.RADAR_STATUS_CLOSED);
        }

        @Override
        public void OnRadarUpdate(boolean openRadar, int status) {
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put(Definition.JSON_NAVI_OPEN_RADAR, openRadar);
                jsonObject.put(Definition.JSON_NAVI_RADAR_STATUS, status);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            sendStatusReport(Definition.STATUS_RADAR, jsonObject.toString());
        }

        @Override
        public void onChassisServiceState(boolean isReady) {
            sendStatusReport(Definition.STATUS_NAVI_SERVICE_OK, isReady + "");
        }

        @RequiresApi(api = Build.VERSION_CODES.N)
        @Override
        public void onMonoInfoUpdate(String additional) {
            int count = 0;
            double ratio = 0.0;

            try {
                JSONObject jsonObject = new JSONObject(additional);
                count = jsonObject.getInt("count");
                ratio = jsonObject.getDouble("ratio");
            } catch (JSONException e) {
                e.printStackTrace();
            }

            mMonoTotalPeopleCount += count;
            mMonoTotalFrameCount++;

            if (!workHD.hasMessages(EVENT_START_SITU_SERVICE)) {
                Log.d(TAG, "onMonoInfoUpdate send start situ service msg.");
                workHD.sendEmptyMessageDelayed(EVENT_START_SITU_SERVICE, 5 * 60 * 1000);
            }
        }

        @Override
        public void onLaserDataUpdate(ArrayList<Laser> data) {
            sendStatusReport(Definition.STATUS_OBSTACLE_INFO, mGson.toJson(data));
        }

        @Override
        public void onAvoidStateChange(boolean isStopping) {
            try {
                JSONObject json = new JSONObject();
                json.put(Definition.JSON_NAVI_IS_AVOID_STOPPING, isStopping);
                sendStatusReport(Definition.STATUS_AVOID_STOPPING, json.toString());
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onStaticAvoidStateChange(boolean isStopping) {
            try {
                JSONObject json = new JSONObject();
                json.put(Definition.JSON_NAVI_IS_AVOID_STOPPING, isStopping);
                sendStatusReport(Definition.STATUS_STATIC_AVOID_STOPPING, json.toString());
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onObstacleReport() {
            try {
                sendStatusReport("status_obstacle", "status_obstacle");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onOdometerUpdate(double move, double leftAcc, double rightAcc) {
            try {
                JSONObject object = new JSONObject();
                object.put("moveDistance", move);
                object.put("leftAcc", leftAcc);
                object.put("rightAcc", rightAcc);
                sendStatusReport("status_odometer_report", object.toString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }


        @Override
        public void onMappingPoseUpdate(List<MappingPose> list) {
            if(list == null || list.size() == 0){
                return;
            }
            Log.i(TAG, "onMappingPoseUpdate: size=" + list.size());
            MappingPose.updatePoses(list);//onMappingPoseUpdate

            List<MappingPose> mappingPoseList = MappingPose.getPoseList();
            JSONArray array = new JSONArray();
            for (MappingPose mappingPose : mappingPoseList) {
                try {
                    JSONObject object = new JSONObject(mappingPose.toJson());
                    array.put(object);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
            Log.i(TAG, "onMappingPoseUpdate: array=" + array);
            sendStatusReport(Definition.STATUS_MAPPING_POSE_UPDATE, array.toString());
        }

        @Override
        public void onMultipleRobotStatusUpdate(String type, String multiData) {
//            Log.v(TAG, "onMultipleRobotStatusUpdate type :" + type + "," + multiData);
            if (Definition.STATUS_MULTIPLE_ROBOT_NO_DATA.equals(type)) {
                biManager.pushNavigationReport(Def.LORA_NO_DATA, NavigationApiReport.ACTION_TYPE_EVENT, multiData);
//                sendStatusReport(type, Definition.MULTIPLE_STATUS_ERROR_LORA_DEVICES_EXCEPTION + "");
                return;
            }
            sendStatusReport(type, multiData);
        }

        @Override
        public void onLoraDataUpdate(String loraData) {
            sendStatusReport(Definition.STATUS_FACTORY_TEST_LORA_DATA, loraData);
        }

        @Override
        public void onMappingVisionInfoUpdate(String additional) {
            sendStatusReport(Definition.STATUS_MAPPING_VISION_INFO_UPDATE, additional);
        }

        @Override
        public void onLineDataUpdate(@NonNull List<com.ainirobot.coreservice.client.actionbean.Pose> list) {
            String json = GsonUtil.toJson(list);
            Log.d(TAG, "ReportLineData: " + json.length());
            sendStatusReport(Definition.STATUS_LINE_DATA, json);
        }
    };

    private void sendStatusReport(String type, String data) {
        RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, type, data);
    }

    private void sendExceptionReport(String type, String data) {
        RobotCore.sendExceptionReport(RobotOS.NAVIGATION_SERVICE, type, data);
    }

    private void addSocketClient(String type, int port) {
        if (mStatusClients.containsKey(type)) {
            List<Integer> ports = mStatusClients.get(type);
            if (!ports.contains(port)) {
                ports.add(port);
            }
        } else {
            List<Integer> ports = new ArrayList<>();
            ports.add(port);
            mStatusClients.put(type, ports);
        }
    }

    private void addSocketTranStatus(String type, int port) {
        List<String> types = mSocketTypes.get(port);
        if (types != null) {
            if (!types.contains(type)) {
                types.add(type);
            }
        } else {
            types = new ArrayList<>();
            types.add(type);
            mSocketTypes.put(port, types);
        }
    }

    private void sendStatusBySocket(String type, Message message) {
        Log.i(TAG, "sendStatusBySocket: type:" + type + " msg:" + message);
        List<Integer> ports = mStatusClients.get(type);
        if (ports == null) {
            Log.w(TAG, "sendStatusBySocket: not found port:");
            return;
        }
        for (Integer port : ports) {
            Log.i(TAG, "sendStatusBySocket: port:" + port);
            SocketClient client = mSockets.get(port);
            if (client != null) {
                Log.i(TAG, "sendStatusBySocket: find socket client");
                boolean result = client.sendMessage(message);
                Log.i(TAG, "sendStatusBySocket: send result=" + result);
            }
        }
    }


    private final int EVENT_START_SITU_SERVICE = 2;
    private int mMonoTotalPeopleCount = 0;
    private int mMonoTotalFrameCount = 0;

    private class WorkHandler extends Handler {

        public WorkHandler(Looper looper) {
            super(looper);
        }

        @RequiresApi(api = Build.VERSION_CODES.N)
        @Override
        public void handleMessage(android.os.Message msg) {
            switch (msg.what) {
                case EVENT_START_SITU_SERVICE:
                    double peopleCountPerFrame = 0f;
                    if (mMonoTotalFrameCount != 0 && mMonoTotalPeopleCount != 0) {
//                        peopleCountPerFrame = Double.parseDouble(new DecimalFormat("#.0").format((double) (mMonoTotalPeopleCount / mMonoTotalFrameCount)));
                        Log.d(TAG, "handleMessage: mMonoTotalPeopleCount / mMonoTotalFrameCount " + mMonoTotalPeopleCount + " : " + mMonoTotalFrameCount);
                        peopleCountPerFrame = Double.parseDouble(String.format(Locale.ENGLISH, "%.1f", (double) (mMonoTotalPeopleCount / mMonoTotalFrameCount)));
                    }
                    Log.d(TAG, "onMonoInfoUpdate peopleCountPerFrame = " + peopleCountPerFrame + ", TotalPeopleCount = " + mMonoTotalPeopleCount
                            + ", TotalFrameCount = " + mMonoTotalFrameCount);
                    if (peopleCountPerFrame > 3.5f) {
                        startSituService();
                    } else {
                        mMonoTotalPeopleCount = 0;
                        mMonoTotalFrameCount = 0;
                    }
                    break;

                default:
                    break;
            }
        }
    }

    private void startSituService() {
        Log.d(TAG, "ready start situ service");
        if (RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_ALLOW_AUTO_SITU_SERVICE) == 0) {
            Log.d(TAG, "auto situ service switch if off, return!!");
            return;
        }
        ComponentName component = new ComponentName(
                "com.ainirobot.settings",
                "com.ainirobot.settings.SituService");
        Intent intent = new Intent();
        intent.setComponent(component);
        intent.putExtra("robot.intent.extra.START_FROM", "auto");
        mContext.startService(intent);
    }

    private void sendAsyncResponse(String type, String message) {
        RobotCore.sendAsyncResponse(type, Definition.RESULT_SUCCEED, message);
    }

    private void sendAsyncResponse(String type, String message, String extraData) {
        RobotCore.sendAsyncResponse(type, Definition.RESULT_SUCCEED, message, extraData);
    }

    private void sendFailedAsyncResponse(String type, String message) {
        RobotCore.sendAsyncResponse(type, Definition.RESULT_FAILED, message);
    }

    private void sendPlaceDataUpdateReport() {
        DataManager.getInstance().sendPlaceDataUpdateReport();
    }

    private boolean processAddPlace(List<Pose> poses) {
        if (poses == null || poses.size() <= 0) {
            return true;
        }
        Log.d(TAG, "processAddPlace: poses=" + poses.toString());
        boolean result = NavigationDataManager.getInstance().updatePlaceInfoList(poses);
        Log.d(TAG, "processAddPlace: result=" + result);
        return result;
    }

    private boolean processDelPlace(List<String> ids, List<String> names) {
        Log.d(TAG, "processDelPlace ids: " + new Gson().toJson(ids) + " names: " + names.toString());
        if ((ids == null || ids.size() <= 0) && (names == null || names.size() <= 0)) {
            return true;
        }
        boolean resultId = true;
        boolean resultName = true;
        boolean isNapPosNotSeparate = NavigationDataManager.getInstance().isNapPosNotSeparate();
        if (ids != null && ids.size() > 0) {
            resultId = NavigationDataManager.getInstance().deletePlaceByPlaceIds(ids);
            if (isNapPosNotSeparate) NavigationDataManager.getInstance().deleteLocalPlaceByPlaceIds(ids);
        }
        if (names != null && names.size() > 0) {
            resultName = NavigationDataManager.getInstance().deletePlaceByNameList(names);
            if (isNapPosNotSeparate) NavigationDataManager.getInstance().deleteLocalPlaceByNameList(names);
        }
        Log.d(TAG, "processDelPlace: resultId=" + resultId + " resultName=" + resultName);
        return resultId & resultName;
    }

    private boolean processRename(List<JSONObject> names) {
        if (names == null || names.isEmpty()) {
            return true;
        }
        String mapName = NavigationDataManager.getInstance().getMapName();
        HashMap<String, String> renameMap = new HashMap<>();
        for (JSONObject renameBean : names) {
            String oldName = renameBean.optString(Definition.OLD_NAME);
            String newName = renameBean.optString(Definition.NEW_NAME);
            if (oldName.isEmpty() || newName.isEmpty()) {
                continue;
            }
            renameMap.put(oldName, newName);
        }
        List<String> oldNameList = new ArrayList<>(renameMap.keySet());
        // 获取重命名(编辑)点位在PlaceName表中的数据
        String[] oldNameIdArray = NavigationDataManager.getInstance().getPlaceIdsByNameList(oldNameList);
        // 根据oldNameIdArray查找在PlaceInfo表中存在的数据对象，如果存在进行更新
        List<PlaceInfo> renamePlaceInfos = NavigationDataManager.getInstance().getPlaceInfosByPlaceIds(oldNameIdArray, mapName);
        return NavigationDataManager.getInstance().updatePlaceNameByRename(renamePlaceInfos, renameMap);
    }

    private boolean processReplace(List<JSONObject> replacePlaces) {
        if (replacePlaces == null || replacePlaces.isEmpty()) {
            return true;
        }
        String mapName = NavigationDataManager.getInstance().getMapName();
        List<String> replacePlaceNames = new ArrayList<>();
        HashMap<String, JSONObject> replaceMap = new HashMap<>();

        // 提取所有需要替换的点位名称并建立名称与替换数据的映射
        for (JSONObject replaceBean : replacePlaces) {
            String placeName = replaceBean.optString(Definition.REPLACEPLACENAME);
            if (!TextUtils.isEmpty(placeName)) {
                replacePlaceNames.add(placeName);
                replaceMap.put(placeName, replaceBean);
            }
        }
        // 获取替换点位在PlaceName表中的placeId
        String[] replaceNameIdArray = NavigationDataManager.getInstance().getPlaceIdsByNameList(replacePlaceNames);
        // 根据placeId查找在PlaceInfo表中存在的数据对象
        List<PlaceInfo> placeInfos = NavigationDataManager.getInstance().getPlaceInfosByPlaceIds(replaceNameIdArray, mapName);
        return NavigationDataManager.getInstance().updatePlaceNameByReplace(placeInfos, replaceMap);
    }

    private boolean processRenamePlace(List<JSONObject> ids, List<JSONObject> names, String language) {
        if ((ids == null || ids.size() <= 0) && (names == null || names.size() <= 0)) {
            return true;
        }
        boolean resultDel = true;
        boolean resultId = true;
        if (ids != null && ids.size() > 0) {
            List<String> idList = new ArrayList<>();
            List<String> newNameList = new ArrayList<>();
            for (JSONObject jsonObject : ids) {
                String id = jsonObject.optString(Definition.JSON_NAVI_PLACE_ID);
                String newName = jsonObject.optString(Definition.NEW_NAME);
                idList.add(id);
                newNameList.add(newName);
            }
            resultDel = NavigationDataManager.getInstance().deletePlaceByNameList(newNameList);
            resultId = NavigationDataManager.getInstance().updatePlaceByIdList(idList, newNameList, language);
        }
        boolean resultName = true;
        if (names != null && names.size() > 0) {
            List<String> oldNames = new ArrayList<>();
            List<String> newNames = new ArrayList<>();
            for (JSONObject jsonObject : names) {
                String oldName = jsonObject.optString(Definition.OLD_NAME);
                String newName = jsonObject.optString(Definition.NEW_NAME);
                oldNames.add(oldName);
                newNames.add(newName);
            }
            resultDel = NavigationDataManager.getInstance().deletePlaceByNameList(newNames);
            resultName = NavigationDataManager.getInstance().updatePlaceByNameList(oldNames, newNames, language);
        }
        sendPlaceDataUpdateReport();//processRenamePlace
        return resultId & resultName & resultDel;
    }

    private boolean processTypeForbidLine(HashMap<String, Boolean> editForbidLine) {
        if (editForbidLine == null || editForbidLine.size() <= 0) {
            return true;
        }
        List<MapInfo> mapInfos = NavigationDataManager.getInstance().getMapByName(editForbidLine.keySet().toArray(new String[0]));
        for (MapInfo mapInfo : mapInfos) {
            Boolean forbidLineFlag = editForbidLine.get(mapInfo.getMapName());
            if (null == forbidLineFlag) {
                forbidLineFlag = false;
            }
            mapInfo.setForbidLine(forbidLineFlag ? MapInfo.ForbidLine.HAS_FORBID_LINE :
                    MapInfo.ForbidLine.NO_FORBID_LINE);
        }
        NavigationDataManager.getInstance().updateMapInfo(mapInfos);
        return true;
    }

    @Override
    public boolean startDataSetRecord(String cmdType, String cmdParam) {
        try {
            JSONObject jsonObject = new JSONObject(cmdParam);
            chassisClient.startDataSetRecord(jsonObject.getString(Definition.JSON_NAVI_SENSOR_LIST));
            sendAsyncResponse(cmdType, SUCCEED);
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
        }
        return true;
    }

    @Override
    public boolean stopDataSetRecord(String cmdType, String cmdParam) {
        try {
            JSONObject jsonObject = new JSONObject(cmdParam);
            chassisClient.stopDataSetRecord(jsonObject.getBoolean(Definition.JSON_NAVI_SAVE_LOCAL_SENSOR_DATA));
            sendAsyncResponse(cmdType, SUCCEED);
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
        }
        return true;
    }

    @Override
    public boolean uploadNaviDataSet(String cmdType, String cmdParam) {
        try {
            JSONObject jsonObject = new JSONObject(cmdParam);
            chassisClient.uploadNaviDataSet(jsonObject.getString(Definition.JSON_NAVI_SENSOR_LIST));
            sendAsyncResponse(cmdType, SUCCEED);
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
        }
        return true;
    }

    @Override
    public boolean isMapHasVision(String cmdType, String cmdParam) {
        Log.d(TAG, "isMapHasVision: cmdParam=" + cmdParam);
        boolean hasVision = false;
        try {
            JSONObject jsonObject;
            if (TextUtils.isEmpty(cmdParam)) {
                jsonObject = new JSONObject();
            } else {
                jsonObject = new JSONObject(cmdParam);
            }
            if (jsonObject.has(Definition.JSON_NAVI_MAP_TYPE)) {
                int mapType = jsonObject.getInt(Definition.JSON_NAVI_MAP_TYPE);
                hasVision = MapTypeHelper.checkRobotAndMapSupportVision(mapType);
            } else {
                MapInfo mapInfo;
                if (jsonObject.has(Definition.JSON_MAP_NAME)) {
                    String mapName = jsonObject.getString(Definition.JSON_MAP_NAME);
                    mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
                } else {
                    mapInfo = NavigationDataManager.getInstance().getNavMapInfo();
                    Log.d(TAG, "isMapHasVision: getNavMapInfo=" + mapInfo);
                }
                if (null != mapInfo) {
                    hasVision = MapTypeHelper.checkRobotAndMapSupportVision(mapInfo.getMapType());
                } else {
                    Log.e(TAG, "isMapHasVision: mapInfo is null");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        Log.d(TAG, "isMapHasVision: hasVision=" + hasVision);
        sendAsyncResponse(cmdType, String.valueOf(hasVision));
        return true;
    }


    @Override
    public boolean updateNavigationExtraData(String cmdType, String cmdParams) {
        Log.d(TAG, "updateNavigationExtraData: cmdParams=" + cmdParams);
        ExtraInfo extraInfo = mGson.fromJson(cmdParams, ExtraInfo.class);
        boolean updateResult = NavigationDataManager.getInstance().updateExtraInfo(extraInfo);
        Log.d(TAG, "updateNavigationExtraData: updateResult=" + updateResult);
        sendAsyncResponse(cmdType, updateResult ? SUCCEED : FAILED);
        return true;
    }

    @Override
    public boolean getNavigationExtraData(String cmdType, String mapName) {
        MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
        if (null == mapInfo) {
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        boolean hasVision = MapTypeHelper.checkRobotAndMapSupportVision(mapInfo.getMapType());
        ExtraInfo extraInfo = hasVision ? NavigationDataManager.getInstance().getExtraInfo(mapName) : new ExtraInfo(mapName);
        sendAsyncResponse(cmdType, extraInfo != null ? SUCCEED : FAILED, mGson.toJson(extraInfo));
        return true;
    }

    @Override
    public boolean hasNavigationExtraData(String cmdType, String cmdParams) {
        MapPkgHelper.hasNavigationExtraData(cmdParams, (success, path, extraData) -> sendAsyncResponse(cmdType, success ? SUCCEED : FAILED, path));
        return true;
    }

    @Override
    public boolean zipNavigationExtraData(String cmdType, String cmdParams) {
        MapPkgHelper.zipNavigationExtraData(cmdParams, (success, path, extraData) -> {
            Log.e(TAG, "zipNavigationExtraData: " + extraData);
            ChassisNoRelyApiImpl.this.sendAsyncResponse(cmdType, success ? SUCCEED : FAILED, path);
        });
        return true;
    }

    @Override
    public boolean unzipNavigationExtraData(String cmdType, String cmdParams) {
        MapPkgHelper.unzipNavigationExtraData(cmdParams, (success, path, extraData) -> {
            Log.e(TAG, "unzipNavigationExtraData: " + extraData);
            ChassisNoRelyApiImpl.this.sendAsyncResponse(cmdType, success ? SUCCEED : FAILED, path);
        });
        return true;
    }

    @Override
    public boolean zipMapFile(String cmdType, String cmdParam) {
        MapPkgHelper.localPkg2Server(cmdParam, (success, path, extraData) -> sendAsyncResponse(cmdType, success ? SUCCEED : FAILED, path));
        return true;
    }

    @Override
    public boolean unzipMapFile(String cmdType, String cmdParam) {
        MapPkgHelper.serverPkg2Local(cmdParam, (success, path, extraData) -> sendAsyncResponse(cmdType, success ? SUCCEED : FAILED, path));
        return true;
    }

    @Override
    public boolean getLocalMapInfoList(String cmdType) {
        Log.d(TAG, "getLocalMapInfoList: ");
        sendAsyncResponse(cmdType, mGson.toJson(NavigationDataManager.getInstance().getAllMap()));
        return true;
    }

    @Override
    public boolean getMapInfoBySdMapNames(String cmdType) {
        List<String> mapNameList = MapFileHelper.getMapList();
        sendAsyncResponse(cmdType,
                mGson.toJson(NavigationDataManager.getInstance().getMapByName(mapNameList.toArray(new String[0]))));
        return true;
    }

    @Override
    public boolean getCurrentMapName(String cmdType) {
        sendAsyncResponse(cmdType, NavigationDataManager.getInstance().getMapName());
        return true;
    }

    @Override
    public boolean getMotionDistance(String cmdType) {
        double totalDistance = BasicMotionProcess.getInstance().getTotalDistance();
        Log.d(TAG, "getMotionDistance: totalDistance=" + totalDistance);
        sendAsyncResponse(cmdType, String.valueOf(totalDistance));
        return true;
    }

    private int getMapSyncState(int syncState, boolean isMapState, boolean needReUpload) {
        int defSyncState;
        if (isMapState) {
            defSyncState = MapInfo.SyncState.UPLOADED_MAP;
        } else {
            defSyncState = MapInfo.SyncState.UPLOADED_PLACE;
        }
        if (needReUpload) {
            syncState = syncState & ~defSyncState;
        } else {
            syncState = syncState | defSyncState;
        }
        return syncState;
    }

    @Override
    public boolean copyImportMapFile(String cmdType, String cmdParam) {
        MapPkgHelper.copyImportMapFile(cmdParam, (success, path, extraData) -> sendAsyncResponse(cmdType, success ? SUCCEED : FAILED, path));
        return true;
    }

    @Override
    public boolean otaDowngrade(String cmdType, String cmdParam) {
        Log.d(TAG, "otaDowngrade: cmdParam=" + cmdParam);
        //删除本地地图文件
        //删除导航数据库
        boolean isDeleteLocalMap = false;
        String downgradeVersion = "";
        String downgradedTargetFullVersion = "";
        try {
            JSONObject jsonObject = new JSONObject(cmdParam);
             isDeleteLocalMap = jsonObject.optBoolean(Definition.JSON_NAVI_IS_DELETE_LOCAL_MAP, false);
             downgradeVersion = jsonObject.optString(Definition.JSON_NAVI_DOWNGRADE_TARGET_VERSION, "");
             downgradedTargetFullVersion = jsonObject.optString(Definition.JSON_NAVI_DOWNGRADE_TARGET_FULL_VERSION, "");
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (isDeleteLocalMap) {
            //删除本地地图文件
            FileUtils.deleteDir(new File(MapFileHelper.getRobotMapPath()), false);
            //清空数据库
            NavigationDataManager.getInstance().deleteAllData();
        } else {
            //删除导航配置
            NavigationDataManager.getInstance().deleteNavigationConfig();
            Log.d(TAG, "downgradeVersion=" + downgradeVersion);
            if (!downgradeVersion.isEmpty() && TempCode.needMigrateData(downgradeVersion, SWITCH_DATABASE_CRITICAL_VERSION)) {
                //降级目标版本小于10.3，需要迁移数据库到sqlite
                Log.d(TAG, "otaDowngrade: needMigrateData=" + TempCode.needMigrateData(downgradeVersion, SWITCH_DATABASE_CRITICAL_VERSION));
                //迁移地图数据库
                NavigationDataManager.getInstance().migrateMapDataToSqlite(mContext);
                //重置迁移状态
                NavigationDataManager.getInstance().updateMigrationState(mContext, false);
                //恢复数据库文件
                NavigationDataManager.getInstance().restoreDbFile(mContext);
            } else {
                Log.d(TAG, "otaDowngrade: do not needMigrateData");
            }
        }

        if (!downgradeVersion.isEmpty() && !TempCode.needMigrateData(downgradeVersion, SWITCH_DATABASE_CRITICAL_VERSION)) {
            //降级目标版本大于等于10.3，需要备份数据。小于10.3版本是sqlite数据库，不存在objectbox降级崩溃问题，不需要备份数据。
            ObjectBoxVersionManager.getInstance().backupData(downgradedTargetFullVersion);
        } else {
            Log.d(TAG, "otaDowngrade: do not need backupData");
        }

        sendAsyncResponse(cmdType, SUCCEED);
        return true;
    }

    @Override
    public boolean findByGateIds(String cmdType, String cmdParam) {
        if (TextUtils.isEmpty(cmdParam)) {
            Log.e(TAG, "findByGateIds: cmdParam is empty");
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        try {
            JSONObject jsonObject = new JSONObject(cmdParam);
            String gateIds = jsonObject.optString("gateIds",null);
            if(gateIds != null){
                String[] gateDatas = gateIds.split(",");
                JSONObject  result = NavigationDataManager.getInstance().findByGateIds(gateDatas);
                sendAsyncResponse(cmdType, String.valueOf(result));
            }else{
                sendAsyncResponse(cmdType, FAILED);
            }
        } catch (Exception e) {
            Log.e(TAG, "findByGateIds: " + e.getMessage());
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        return true;
    }

    @Override
    public boolean findByLineIds(String cmdType, String cmdParam) {
        if (TextUtils.isEmpty(cmdParam)) {
            Log.e(TAG, "findByLineIds: cmdParam is empty");
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        try {
            int[] gateLineIds = parseIntArrayFrom(cmdParam);
            if (gateLineIds == null || gateLineIds.length == 0) {
                sendAsyncResponse(cmdType, FAILED);
            }else{
                JSONObject  result = NavigationDataManager.getInstance().findByLineIds(gateLineIds);
                sendAsyncResponse(cmdType, String.valueOf(result));
            }
        } catch (Exception e) {
            Log.e(TAG, "findByLineIds: " + e.getMessage());
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        return true;
    }

    @Override
    public boolean batchInsertOrUpdateGate(String cmdType, String cmdParam) {
        if (TextUtils.isEmpty(cmdParam)) {
            Log.e(TAG, "batchInsertOrUpdateGate: cmdParam is empty");
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        try {
            Type gateRelationInfoType = new TypeToken<List<GateRelationInfo>>() {}.getType();
            List<GateRelationInfo> gateRelationInfo = mGson.fromJson(cmdParam, gateRelationInfoType);
            Log.e(TAG, "batchInsertOrUpdateGate: gateRelationInfos" + gateRelationInfo);
            if(gateRelationInfo != null){
                JSONObject  result = NavigationDataManager.getInstance().batchInsertOrUpdateGate(gateRelationInfo);
                sendAsyncResponse(cmdType, String.valueOf(result));
            }else{
                sendAsyncResponse(cmdType, FAILED);
            }
        } catch (Exception e) {
            Log.e(TAG, "batchInsertOrUpdateGate: " + e.getMessage());
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        return true;
    }

    @Override
    public boolean deleteByLineIds(String cmdType, String cmdParam) {
        if (TextUtils.isEmpty(cmdParam)) {
            Log.e(TAG, "deleteByLineIds: cmdParam is empty");
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        try {
            Log.e(TAG, "deleteByLineIds: cmdParam" + cmdParam);
            int[] gateLineIds = parseIntArrayFrom(cmdParam);
            Log.e(TAG, "deleteByLineIds: gateLineIds" + gateLineIds);
            if (gateLineIds == null || gateLineIds.length == 0) {
                sendAsyncResponse(cmdType, FAILED);
            }else{
                JSONObject  result = NavigationDataManager.getInstance().deleteByLineIds(gateLineIds);
                sendAsyncResponse(cmdType, String.valueOf(result));
            }
        } catch (Exception e) {
            Log.e(TAG, "deleteByLineIds: " + e.getMessage());
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        return true;
    }



    @Override
    public boolean deleteExceptLineIds(String cmdType, String cmdParam) {
        if (TextUtils.isEmpty(cmdParam)) {
            Log.e(TAG, "deleteExceptLineIds: cmdParam is empty");
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        try {
            Log.d(TAG, "deleteExceptLineIds: cmdParam" + cmdParam);
            int[] gateLineIds = parseIntArrayFrom(cmdParam);
            Log.d(TAG, "deleteExceptLineIds: gateLineIds" + Arrays.toString(gateLineIds));
            if (gateLineIds == null || gateLineIds.length == 0) {
                sendAsyncResponse(cmdType, FAILED);
            }else{
                JSONObject result = NavigationDataManager.getInstance().deleteExceptLineIds(gateLineIds);
                sendAsyncResponse(cmdType, result.toString());
            }
        } catch (Exception e) {
            Log.e(TAG, "deleteExceptLineIds: " + e.getMessage());
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        return true;
    }

    @Override
    public boolean deleteByGateIds(String cmdType, String cmdParam) {
        if (TextUtils.isEmpty(cmdParam)) {
            Log.e(TAG, "deleteByGateIds: cmdParam is empty");
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        try {
            String[] gateDatas = cmdParam.split(",");
            Log.e(TAG, "deleteByGateIds: {}" + Arrays.toString(gateDatas));
            JSONObject  result = NavigationDataManager.getInstance().deleteByGateIds(gateDatas);
            sendAsyncResponse(cmdType, String.valueOf(result));
        } catch (Exception e) {
            Log.e(TAG, "deleteByGateIds: " + e.getMessage());
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
        return true;
    }


    @Override
    public boolean getAllGateRelationData(String cmdType, String cmdParam) {
        try {
            JSONObject gateRelationInfos = NavigationDataManager.getInstance().getAllGateRelationData();
            Log.d(TAG, "getAllGateRelationData: " + gateRelationInfos.toString());
            sendAsyncResponse(cmdType, gateRelationInfos.toString());
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            sendAsyncResponse(cmdType, FAILED);
            return false;
        }
    }

    /**
     * 从JSON字符串中解析整数数组
     * @param arrayString 字符串
     * @return 解析出的整数数组，如果解析失败则返回null
     */
    private int[] parseIntArrayFrom(String arrayString) {
        String trimmedString = arrayString.substring(1, arrayString.length() - 1);
        // 如果字符串为空，返回空数组
        if (trimmedString.trim().isEmpty()) {
            return new int[]{};
        }
        return Arrays.stream(trimmedString.split(", ")).mapToInt(Integer::parseInt).toArray();
    }
}
