package com.ainirobot.navigationservice.commonModule.logs.db;

public class Column {

    public enum Constraint {
        UNIQUE("UNIQUE"), NOT("NOT"), NULL("NULL"), CHECK("CHECK"), FOREIGN_KEY("FOREIGN KEY"),
        PRIMARY_KEY("PRIMARY KEY"), AUTO_INCREMENT("PRIMARY KEY AUTOINCREMENT"),
        IDENTITY("identity(1,1)");

        private String value;

        Constraint(String value) {
            this.value = value;
        }

        @Override
        public String toString() {
            return value;
        }
    }

    public enum DataType {
        NULL, INTEGER, REAL, TEXT, BLOB,TIMESTAMP
    }

    private String mColumnName;

    private Constraint mConstraint;

    private DataType mDataType;

    Column(String columnName, Constraint constraint, DataType dataType) {
        mColumnName = columnName;
        mConstraint = constraint;
        mDataType = dataType;
    }

    String getColumnName() {
        return mColumnName;
    }

    public Constraint getConstraint() {
        return mConstraint;
    }

    DataType getDataType() {
        return mDataType;
    }
}
