package com.ainirobot.navigationservice.db.helper.iml;

import com.ainirobot.navigationservice.db.entity.LocalPlaceInfo;

import java.util.List;

public interface LocalPlaceInfoIml extends BaseHelper<LocalPlaceInfo> {
    void initLocalPlaceInfoData();
    void updateLocalPlaceInfo(List<LocalPlaceInfo> localPlaceInfoList);
    void updateLocalPlaceInfo(LocalPlaceInfo localPlaceInfo);
    void updateLocalPlaceInfo(String mapName);
    String[] deleteLocalPlaceInfoByIds(String mapName, String[] placeIds);
    // 删除当前数据库下对应mapName的数据
    String[] deleteLocalPlaceInfo(String mapName);
    String[] deleteLocalPlaceByTypeId(String mapName, int typeId);
    List<LocalPlaceInfo> getLocalPlaceInfo(String mapName);
    LocalPlaceInfo getLocalPlaceInfoByTypeId(String mapName, int typeId);
    boolean containsLocalPlaceInfoByMapName(String mapName);
    boolean hasLocalPlaceInfoData();
}
