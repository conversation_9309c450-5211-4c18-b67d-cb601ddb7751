/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import androidx.annotation.RequiresApi;
import androidx.core.app.NotificationCompat;

import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.hardware.RobotCore;
import com.ainirobot.coreservice.client.listener.InitListener;
import com.ainirobot.navigationservice.commonModule.data.DataManager;
import com.ainirobot.navigationservice.commonModule.settings.SettingManager;
import com.ainirobot.navigationservice.db.NavigationDataManager;
import com.ainirobot.navigationservice.roversdkhelper.mappackage.MapPkgHelper;
import com.ainirobot.robotlog.RobotLog;


public class NavigationService extends Service {
    private final static String TAG = NavigationService.class.getSimpleName();

    private final int NOTIFICATION_ID = 1001;

    private Context mContext;

    public NavigationService() {

    }

    @Override
    public void onCreate() {
        Log.d(TAG, "onCreate: Navigation service!");
        mContext = getApplicationContext();
        RobotLog.init(mContext);

        MapPkgHelper.transferLocalMap2NewDirectoryStructure();

        RobotCore.init(this, new InitListener() {
            @Override
            public void onFinish() {
                Log.d(TAG, "onCreate: Robot core init finish!");
                NavigationDataManager.getInstance().updateChargingPileStatus();
            }
        });
        DataManager.getInstance().init();

        RobotCore.registerHWService(RobotOS.NAVIGATION_SERVICE, new CommandProcessor(this));
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        int result = super.onStartCommand(intent, flags, startId);


        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            startForeground();
        }

        return result;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        SettingManager.getInstance().unregisterListener();
        super.onDestroy();
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    private void startForeground() {
        String CHANNEL_ID = "com.ainirobot.navigationservice";
        String CHANNEL_NAME = "Navigation";
        NotificationChannel notificationChannel = null;
        NotificationCompat.Builder builder = null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            notificationChannel = new NotificationChannel(CHANNEL_ID, CHANNEL_NAME, NotificationManager.IMPORTANCE_HIGH);
            NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            notificationManager.createNotificationChannel(notificationChannel);
        }

        builder = new NotificationCompat.Builder(this, CHANNEL_ID).
                setContentTitle(getString(R.string.app_name)).
                setWhen(System.currentTimeMillis()).
                setSmallIcon(R.mipmap.orion_default_icon);

        builder.setPriority(Notification.PRIORITY_MAX)
                .setCategory(String.valueOf(Notification.FLAG_ONGOING_EVENT))
                .setVisibility(Notification.VISIBILITY_PUBLIC)
                .setColor(ApplicationWrapper.getContext().getColor(R.color.white));
        Intent intent2 = new Intent();
        PendingIntent pi = PendingIntent.getBroadcast(this, 0, intent2, 0);
        builder.setFullScreenIntent(pi, true);

        Notification notification = builder.build();
        startForeground(NOTIFICATION_ID, notification);
    }

}
