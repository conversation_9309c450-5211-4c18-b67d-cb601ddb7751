/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */


package com.ainirobot.navigationservice.utils;

import android.os.Build;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.google.protobuf.ByteString;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;

/**
 * 文件操作
 */
public class FileUtils {
    private static final String TAG = FileUtils.class.getSimpleName();

    public static String loadFileData2String(File dataFile) {
        try {
            FileInputStream fis = new FileInputStream(dataFile);
            int total = fis.available();
            byte[] dataBytes = new byte[total];
            int len = fis.read(dataBytes);
            if (total == len) {
                return new String(dataBytes);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static boolean store(ByteString data, File file) {
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(file);
            data.writeTo(fos);
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        } finally {
            IOUtils.close(fos);
        }
    }

    /**
     * Reads an InputStream to a ninjia.android.file
     *
     * @param input    InputStream to read
     * @param dataSize The data size to read
     * @param file     File into which to store the read data
     * @return <code>true</code> if store success </code> otherwise
     * @note The input stream is not closed
     */
    public static boolean store(InputStream input, long dataSize, File file) {
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(file);
            byte[] buff = new byte[1024 * 10];
            while (true) {
                if (dataSize == 0) {
                    break;
                }

                int length = (int) Math.min(buff.length, dataSize);
                int result = input.read(buff, 0, length);
                if (result == -1) {
                    break;
                }
                fos.write(buff, 0, result);
                dataSize -= result;
                fos.flush();
            }
            return true;
        } catch (IOException ignored) {
            Log.d("fileutils", "read ninjia.android.file io exception");
            ignored.printStackTrace();
            return false;
        } finally {
            IOUtils.close(fos);
        }
    }

    /**
     * Mapped File way MappedByteBuffer
     *
     * @param filename
     * @return bytes mapped from filename
     * @throws IOException
     */
    public static byte[] toByteArray(final String filename) throws IOException {

        FileChannel fc = null;
        try {
            fc = new RandomAccessFile(filename, "r").getChannel();
            MappedByteBuffer byteBuffer = fc.map(FileChannel.MapMode.READ_ONLY, 0,
                    fc.size()).load();
            System.out.println(byteBuffer.isLoaded());
            byte[] result = new byte[(int) fc.size()];
            if (byteBuffer.remaining() > 0) {
                byteBuffer.get(result, 0, byteBuffer.remaining());
            }
            return result;
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            if (fc != null) {
                try {
                    fc.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        }
    }

    /**
     * 文件移动
     * 源文件不存在renameTo方法返回false但不会报错，所以在工具方法中加入主动检查源文件逻辑；
     * 目标文件如果存在，会被默认覆盖；
     *
     * @param sourcePath 文件/文件夹全路径
     * @param targetPath 文件/文件夹全路径
     */
    public static boolean renameFileTo(String sourcePath, String targetPath) {
        Log.d(TAG, "renameFileTo: sourcePath=" + sourcePath + " targetPath=" + targetPath);
        return renameFileTo(new File(sourcePath), new File(targetPath));
    }

    public static boolean renameFileTo(File source, File target) {
        Log.d(TAG, "renameFileTo: source=" + source + " target=" + target);
        if (!source.exists()) {
            Log.d(TAG, "renameFileTo: Source file not exits!");
            return false;
        }
        File targetPatenFile = target.getParentFile();
        if (!targetPatenFile.exists()) {
            boolean mkdirResult = targetPatenFile.mkdirs();
            Log.d(TAG, "renameFileTo: Target parent file dir not exits! " +
                    " mkdirResult=" + mkdirResult);
        }
        boolean result = source.renameTo(target);
        Log.d(TAG, "renameFileTo: result=" + result);
        return result;
    }

    /**
     * 文件移动或复制
     * 源文件不存在renameTo方法返回false但不会报错，所以在工具方法中加入主动检查源文件逻辑；
     * 目标文件如果存在，会被默认覆盖；
     *
     * @param sourcePath 原始文件路径
     * @param destinationDirectory 目的文件夹路径
     * @return 移动或复制后的文件
     */
    public static File moveOrCopyFile(String sourcePath, String destinationDirectory) {
        File sourceFile = new File(sourcePath);
        File destinationDir = new File(destinationDirectory);

        // 确保目标目录存在
        if (!destinationDir.exists()) {
            destinationDir.mkdirs();
        }

        String fileName = sourceFile.getName();
        // 创建目标文件路径
        File destinationFile = new File(destinationDir, fileName);

        try {
            // 尝试移动文件
            Files.move(sourceFile.toPath(), destinationFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            Log.d(TAG, "moveOrCopyFile success: sourcePath = " + sourcePath + ", destinationPath = " + destinationFile.getPath());
            return destinationFile;
        } catch (Exception moveException) {
            Log.d(TAG, "moveOrCopyFile failed , attempting to copy: " + moveException.getMessage());
            try {
                // 如果移动失败，尝试复制
                Files.copy(sourceFile.toPath(), destinationFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                Log.d(TAG, "moveOrCopyFile File copied successfully: sourcePath = " + sourcePath + ", destinationPath = " + destinationFile.getPath());
                return destinationFile;
            } catch (Exception copyException) {
                copyException.printStackTrace();
                Log.d(TAG, "moveOrCopyFile File copy failed: sourcePath = " + sourcePath + ", destinationPath = " + destinationFile.getPath());
            }
        }
        return null;
    }

    public static void deleteFile(File targetFile) {
        if (targetFile != null && targetFile.exists()) {
            boolean deleted = targetFile.delete();
            if (!deleted) {
                Log.e(TAG, "deleteFile failed: " + targetFile.getPath());
            }
        }
    }
}
