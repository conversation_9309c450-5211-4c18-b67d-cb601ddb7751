package com.ainirobot.navigationservice.business.rpc.algorithm;

import android.util.Log;

import com.ainirobot.navigationservice.business.rpc.SpeedBean;
import com.ainirobot.navigationservice.business.rpc.SpeedChecker;

/**
 * motion Algorithm factory
 *
 * @version V1.0.0
 * @date 2019/11/15 15:00
 */
public class MotionAlgorithmFactory {

    private static final String TAG = "MotionAlgorithmFactory";

    public static MotionAlgorithm check(SpeedBean target) {
        Log.i(TAG, "check: target=" + target + "  current=" + MotionAlgorithm.current);
        if (SpeedChecker.isLinearMotion(MotionAlgorithm.current, target)) {
            Log.i(TAG, "check: linear motion");
            return new LinearMotion(target);
        }
        if (SpeedChecker.isAngularMotion(MotionAlgorithm.current, target)) {
            Log.i(TAG, "check: angular motion");
            return new AngularMotion(target);
        }
        if (SpeedChecker.isLinearToAngularMotion(MotionAlgorithm.current, target)) {
            Log.i(TAG, "check: linear to angular motion");
            return new LinearToAngularMotion(target);
        }
        if (SpeedChecker.isAngularToLinearMotion(MotionAlgorithm.current, target)) {
            Log.i(TAG, "check: angular to linear motion");
            return new AngularToLinearMotion(target);
        }
        if (SpeedChecker.isMixMotion(MotionAlgorithm.current, target)) {
            Log.i(TAG, "check: is mix motion");
            return new MixMotion(target);
        }
        return new SingleLSStop();
    }
}
