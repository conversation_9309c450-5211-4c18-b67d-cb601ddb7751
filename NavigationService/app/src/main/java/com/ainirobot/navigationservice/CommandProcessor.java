/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.ERROR_LOG;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.LOG_FILE;
import static com.ainirobot.navigationservice.Defs.Def.TK1Def.SHOT_LOG;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.os.ParcelFileDescriptor;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.IInspectCallBack;
import com.ainirobot.coreservice.bean.Command;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.ashmem.ShareMemoryFactory;
import com.ainirobot.coreservice.client.hardware.HWService;
import com.ainirobot.coreservice.client.hardware.RobotCore;
import com.ainirobot.coreservice.config.ServiceConfig;
import com.ainirobot.navigationservice.business.ChassisNoRelyApi;
import com.ainirobot.navigationservice.business.ChassisNoRelyApiImpl;
import com.ainirobot.navigationservice.business.ChassisRelyApi;
import com.ainirobot.navigationservice.business.ChassisRelyApiImpl;
import com.ainirobot.navigationservice.chassisAbility.ChassisManager;
import com.ainirobot.navigationservice.chassisAbility.IrLedManager;
import com.ainirobot.navigationservice.commonModule.bi.BiManager;
import com.ainirobot.navigationservice.commonModule.configuration.ConfigManager;
import com.ainirobot.navigationservice.commonModule.logs.LogManager;
import com.ainirobot.navigationservice.commonModule.settings.SettingManager;
import com.ainirobot.navigationservice.commonModule.uwb.UwbManager;
import com.ainirobot.navigationservice.roversdkhelper.MapFileHelper;
import com.ainirobot.navigationservice.utils.MapUtils;

import java.io.File;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * Receive and process commands from CoreService
 */
public class CommandProcessor extends HWService {
    private final static String TAG = TAGPRE + CommandProcessor.class.getSimpleName();
    private final int EVENT_CMD_RELY_API = 1;
    private final int EVENT_CMD_NO_RELY_API = 2;
    private final static String REGIST_CALL_BACK_OK = "registCallBackOk";

    private final String KEY_CMD_TYPE = "cmdType";
    private final String KEY_PARAMS = "params";
    private final String KEY_LANGUAGE = "language";

    private ParcelFileDescriptor mGetMapPgmPfd = null;

    private Context context;

    private Handler mCommandHandlerRelyApi;
    private Handler mCommandHandlerNoRelyApi;

    private class CommandHandler extends Handler {

        public CommandHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(android.os.Message msg) {
            switch (msg.what) {
                case EVENT_CMD_RELY_API:
                    Bundle bundle = msg.getData();
                    String cmdType = bundle.getString(KEY_CMD_TYPE);
                    String params = bundle.getString(KEY_PARAMS);
                    String language = bundle.getString(KEY_LANGUAGE);

                    try {
                        handleAsyncCommand(cmdType, params, language);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;

                default:
                    break;
            }
        }
    }

    private class CommandHandlerNoRelyApi extends Handler {
        public CommandHandlerNoRelyApi(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case EVENT_CMD_NO_RELY_API:
                    Bundle bundle = msg.getData();
                    String cmdType = bundle.getString(KEY_CMD_TYPE);
                    String params = bundle.getString(KEY_PARAMS);
                    String language = bundle.getString(KEY_LANGUAGE);
                    try {
                        handleAsyncCommandNoRelyApi(cmdType, params, language);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;

                default:
                    break;
            }

        }
    }

    private ChassisRelyApi chassisRelyApi;
    private ChassisNoRelyApi chassisNoRelyApi;

    public CommandProcessor(Context context) {
        this.context = context.getApplicationContext();

        HandlerThread commandThread = new HandlerThread("CommandThread");
        commandThread.start();
        mCommandHandlerRelyApi = new CommandHandler(commandThread.getLooper());

        HandlerThread commandThreadNoRely = new HandlerThread("CommandThreadNoRely");
        commandThreadNoRely.start();
        mCommandHandlerNoRelyApi = new CommandHandlerNoRelyApi(commandThreadNoRely.getLooper());

//        ApplicationWrapper.getContext().registerReceiver(new TestReceiver(chassisNoRelyApi, chassisRelyApi), new IntentFilter("TEST_CMD"));
    }

    //TODO init 业务api管理
    private void initBusinessApi() {
        chassisNoRelyApi = new ChassisNoRelyApiImpl();
        chassisRelyApi = new ChassisRelyApiImpl();
        chassisRelyApi.init(this.context);
        chassisNoRelyApi.init(this.context);
    }

    @Override
    public boolean startStatusSocket(String type, int socketPort) throws RemoteException {
        return chassisNoRelyApi.startStatusSocket(type, socketPort);
    }

    @Override
    public boolean closeStatusSocket(String type, int socketPort) throws RemoteException {
        return chassisNoRelyApi.closeStatusSocket(type, socketPort);
    }

    @Override
    public List<Command> onMount(String serviceName, ServiceConfig serviceConfig) {
        Log.d(TAG, "onMount, serviceName=" + serviceName);
        ConfigManager.getInstance().init(serviceConfig, context);
        BiManager.getInstance().init();
        ChassisManager.getInstance().init(context);
        SettingManager.getInstance().init(context);
        LogManager.getInstance().init(context);
        if (ProductInfo.isMiniProduct()) {
            UwbManager.getInstance().init();
        }
        initBusinessApi();

        //避免navi挂掉后重启，导致irLed灯状态不对，所以在这里初始化打开一次led灯
        IrLedManager.getInstance().openIrLed();
        IrLedManager.getInstance().startListenOdometer();

        return CommandRegistry.getTK1Commands();
    }

    @Override
    public void onInspectStart(IInspectCallBack iInspectCallBack) {
        ConfigManager.getInstance().startInspect(iInspectCallBack);

        RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, Definition.STATUS_NAVI_SERVICE_OK, REGIST_CALL_BACK_OK);
    }

    @Override
    public boolean onUpgrade(String s, String s1) {
        return false;
    }

    @Override
    public void onReset() {
        Log.i(TAG, "onReset: ");
        //修复急停后ModuleApp调用stopMove没有权限
        chassisRelyApi.stopMove(Definition.CMD_NAVI_STOP_MOVE);
        chassisRelyApi.cancelNavigation(Definition.CMD_NAVI_STOP_NAVIGATION);
    }

    @Override
    public void onAsyncCommand(String type, String params, String language) {
        Log.d(TAG, " onAsyncCommand:: type = " + type + ", params = " + params
                + ",language = " + language);
        Message message = getMessage(EVENT_CMD_RELY_API, type, params, language);
        mCommandHandlerRelyApi.sendMessage(message);
        Message messageNpRelAPi = getMessage(EVENT_CMD_NO_RELY_API, type, params, language);
        mCommandHandlerNoRelyApi.sendMessage(messageNpRelAPi);
    }

    private Message getMessage(int messType, String type, String params, String language) {
        android.os.Message message = new android.os.Message();
        message.what = messType;
        Bundle bundle = new Bundle();
        bundle.putString(KEY_CMD_TYPE, type);
        bundle.putString(KEY_PARAMS, params);
        bundle.putString(KEY_LANGUAGE, language);
        message.setData(bundle);
        return message;
    }

    @Override
    public String onSyncCommand(String s, String s1, String language) {
        return null;
    }

    public boolean handleAsyncCommand(String cmdType, String cmdParam, String language)
            throws RemoteException {
        switch (cmdType) {
            case Definition.CMD_NAVI_REMOVE_MAP:
                return chassisRelyApi.removeMap(cmdType, cmdParam);

            case Definition.CMD_NAVI_GO_LOCATION: //目前只在core中使用，没有对外提供
                return chassisRelyApi.goLocation(cmdType, cmdParam);

            case Definition.CMD_NAVI_STOP_NAVIGATION:
                return chassisRelyApi.cancelNavigation(cmdType);

            case Definition.CMD_NAVI_STOP_MOVE:
                return chassisRelyApi.stopMove(cmdType);

            case Definition.CMD_NAVI_CRUISE_START:
                return chassisRelyApi.startPatrol(cmdType, cmdParam);

            case Definition.CMD_NAVI_CRUISE_STOP:
                return chassisRelyApi.stopPatrol(cmdType);

            case Definition.CMD_NAVI_RELOCATION:
                return chassisRelyApi.resetLocation(cmdType, cmdParam);

            case Definition.CMD_NAVI_MOVE_DIRECTION:
                return chassisRelyApi.moveDirection(cmdType, cmdParam);

            case Definition.CMD_NAVI_ROTATE_IN_PLACE:
                return chassisRelyApi.rotateInPlace(cmdType, cmdParam);

            case Definition.CMD_NAVI_MOVE_DIRECTION_ANGLE:
                return chassisRelyApi.motion(cmdParam);

            case Definition.CMD_NAVI_MOVE_DIRECTION_ANGLE_CONTROL:
                return chassisRelyApi.motionControlWithObstacles(cmdParam);

            case Definition.CMD_NAVI_MOVE_DIRECTION_ANGLE_OBSTACLES:
                return chassisRelyApi.motionWithObstacles(cmdParam);

            case Definition.CMD_NAVI_MOVE_DIRECTION_ANGLE_SOFT:
                return chassisRelyApi.motionSoft(cmdParam);

            case Definition.CMD_NAVI_MOVE_DISTANCE_ANGLE:
                return chassisRelyApi.motionArc(cmdParam);

            case Definition.CMD_NAVI_MOVE_DISTANCE_ANGLE_WITH_OBSTACLES:
                return chassisRelyApi.motionArcWithObstacles(cmdParam);

            case Definition.CMD_NAVI_SET_MIN_OBSTACLES_DISTANCE:
                return chassisRelyApi.setMinObstaclesDistance(cmdType, cmdParam);

            case Definition.CMD_NAVI_RESET_MIN_OBSTACLES_DISTANCE:
                return chassisRelyApi.resetMinObstaclesDistance(cmdType);

            case Definition.CMD_NAVI_START_CREATING_MAP:
                return chassisRelyApi.startCreatingMap(cmdType, cmdParam);

            case Definition.CMD_NAVI_STOP_CREATING_MAP:
                return chassisRelyApi.stopCreatingMap(cmdType, cmdParam);

            case Definition.CMD_NAVI_SET_POSE_ESTIMATE:
                return chassisRelyApi.setPoseEstimate(cmdType, cmdParam);

            case Definition.CMD_NAVI_SWITCH_MAP:
                return chassisRelyApi.switchMap(cmdType, cmdParam);

            case Definition.CMD_NAVI_LOAD_CURRENT_MAP:
                return chassisRelyApi.loadCurrentMap(cmdType, cmdParam);

            case Definition.CMD_NAVI_GO_POSITION:
                return chassisRelyApi.goPosition(cmdType, cmdParam);

            case Definition.CMD_NAVI_GO_POSITION_BY_TYPE:
                return chassisRelyApi.goPositionByType(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_VERSION:
                return chassisRelyApi.getVersion(cmdType);

            case Definition.CMD_NAVI_START_UPDATE:
                return chassisRelyApi.startUpdate(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_UPDATE_PARAMS:
                return chassisRelyApi.getUpdateParams(cmdType);

            case Definition.CMD_NAVI_GO_DEFAULT_THETA:
                return chassisRelyApi.goDefaultTheta(cmdType);

            case Definition.CMD_NAVI_GET_FULL_CHECK_STATUS:
                return chassisRelyApi.getFullCheckStatus(cmdType);

            case Definition.CMD_NAVI_GET_SENSOR_STATUS:
                return chassisRelyApi.getSensorStatus(cmdType);

            case Definition.CMD_NAVI_SWITCH_AUTO_CHARGE_MODE:
                return chassisRelyApi.switchChargeMode(cmdType);

            case Definition.CMD_NAVI_SWITCH_MANUAL_MODE:
                return chassisRelyApi.switchManualMode(cmdType);

            case Definition.CMD_NAVI_LOCATE_VISION:
                return chassisRelyApi.setLocateVision(cmdType);

            case Definition.CMD_NAVI_RESET_ESTIMATE:
                return chassisRelyApi.resetPoseEstimate(cmdType);

            case Definition.CMD_NAVI_SET_CONFIG:
                return chassisRelyApi.setRoverConfig(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_CONFIG:
                return chassisRelyApi.getRoverConfig(cmdType);

            case Definition.CMD_NAVI_GET_SERIAL_NUMBER:
                return chassisRelyApi.getSystemInformation(cmdType);

            case Definition.CMD_NAVI_RESUME_SPECIAL_PLACE_THETA:
                return chassisRelyApi.resumeSpecialPlaceTheta(cmdType, cmdParam);

            case Definition.CMD_NAVI_SET_FIXED_ESTIMATE:
                return chassisRelyApi.setFixedEstimate(cmdType, cmdParam);

            case Definition.CMD_NAVI_CLEAR_CUR_NAVI_MAP:
                return chassisRelyApi.clearCurNaviMap(cmdType);

            case Definition.CMD_NAVI_GET_ERROR_LOG:
                return chassisRelyApi.getLogFile(cmdType, cmdParam, ERROR_LOG);

            case Definition.CMD_NAVI_PACK_LOG_FILE:
                return chassisRelyApi.packLogFile(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_LOG_FILE:
                return chassisRelyApi.getLogFile(cmdType, cmdParam, LOG_FILE);
            case Definition.CMD_NAVI_CHECK_CUR_NAVI_MAP:
                return chassisRelyApi.checkCurNaviMap(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_SHOT_LOG:
                return chassisRelyApi.getLogFile(cmdType, cmdParam, SHOT_LOG);

            case Definition.CMD_NAVI_SET_RADAR_STATUS:
                return chassisRelyApi.setRadarState(cmdType, cmdParam);

            case Definition.CMD_NAVI_QUERY_RADAR_STATUS:
                return chassisRelyApi.getRadarState(cmdType);

            case Definition.CMD_NAVI_TURN_BY_NAVIGATION:
                return chassisRelyApi.turnByNavigation(cmdType, cmdParam);

            case Definition.CMD_NAVI_RECOVERY:
                return chassisRelyApi.recoveryNavigation(cmdType);

            case Definition.CMD_NAVI_VISION_CHARGE_START:
                return chassisRelyApi.goCharge(cmdType, cmdParam);
            case Definition.CMD_NAVI_VISION_CHARGE_STOP:
                return chassisRelyApi.stopCharge(cmdType);
            case Definition.CMD_NAVI_ADD_MAPPING_POSE:
                return chassisRelyApi.addMappingPose(cmdType, cmdParam);
            case Definition.CMD_NAVI_DELETE_MAPPING_POSE:
                return chassisRelyApi.deleteMappingPose(cmdType, cmdParam);
            case Definition.CMD_NAVI_SET_FORCE_ESTIMATE:
                return chassisRelyApi.setForceEstimate(cmdType, cmdParam);
            case Definition.CMD_NAVI_RENAME_MAPPING_POSE:
                return chassisRelyApi.renameMappingPose(cmdType, cmdParam);
            case Definition.CMD_NAVI_GET_MAP_STATUS:
                return chassisRelyApi.getMapStatus(cmdType, cmdParam);

            case Definition.CMD_NAVI_START_EXTEND_MAP:
                return chassisRelyApi.startExtendMap(cmdType, cmdParam);

            case Definition.CMD_NAVI_CANCEL_CREATE_MAP:
                return chassisRelyApi.cancelCreateMap(cmdType, cmdParam);

            case Definition.CMD_NAVI_SET_RELOCATION:
                return chassisRelyApi.setChassisRelocation(cmdType, cmdParam);

            case Definition.CMD_NAVI_MOVE_ANGLE_PID:
                return chassisRelyApi.motionPid(cmdParam);

            case Definition.CMD_NAVI_SET_CAMERA_STATE:
                return chassisRelyApi.setCameraEnable(cmdType, cmdParam);

            case Definition.CMD_NAVI_SET_MULTI_ROBOT_CONFIG:
                return chassisRelyApi.setMultiRobotSettingConfigData(cmdType, cmdParam);

            case Definition.CMD_NAVI_SEND_MULTI_ROBOT_MESSAGE:
                return chassisRelyApi.sendMultiRobotMsgData(cmdType, cmdParam);

            case Definition.CMD_NAVI_SET_MULTI_ROBOT_TEST_ENABLE:
                return chassisRelyApi.setLoraTestMode(cmdType, cmdParam);

            case Definition.CMD_NAVI_RESET_MULTI_ROBOT_CONFIG:
                return chassisRelyApi.resetLoraDefaultConfig(cmdType, cmdParam);

            case Definition.CMD_NAVI_SET_WHEEL_CONTROL_MODE:
                return chassisRelyApi.setWheelControlMode(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_NAVI_PATH_INFO:
                return chassisRelyApi.getNaviPathInfo(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_NAVI_PATH_INFO_TO_GOALS:
                return chassisRelyApi.getNaviPathInfoToGoals(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_NAVI_PATH_DETAIL:
                return chassisRelyApi.getNaviPathDetail(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_NAVI_GATE_PASSING_ROUTE:
                return chassisRelyApi.getNaviGatePassingRoute(cmdType, cmdParam);

            case Definition.CMD_NAVI_ENABLE_REPORT_LINE_DATA:
                return chassisRelyApi.enableReportLineData(cmdType, cmdParam);

            case Definition.CMD_NAVI_ENABLE_DEPTH_IMAGE:
                return chassisRelyApi.enableRgbdDepthImage(cmdType, cmdParam);

            case Definition.CMD_NAVI_ENABLE_IR_IMAGE:
                return chassisRelyApi.enableTopIRImage(cmdType, cmdParam);

            case Definition.CMD_NAVI_AUTO_DRAW_ROAD:
                return chassisRelyApi.autoDrawRoad(cmdType, cmdParam);

            case Definition.CMD_NAVI_STOP_EXPANSION_MAP:
                return chassisRelyApi.stopExtendMap(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_NAVI_ANGLE_SPEED:
                return chassisRelyApi.getNaviParams(cmdType, cmdParam);

            case Definition.CMD_NAVI_PAUSE_NAVIGATION:
                return chassisRelyApi.naviPause(cmdType, cmdParam);

            case Definition.CMD_NAVI_ALIGN_START:
                return chassisRelyApi.gotoAlign(cmdType, cmdParam);

            case Definition.CMD_NAVI_ALIGN_CANCEL:
                return chassisRelyApi.cancelAlign(cmdType, cmdParam);

            case Definition.CMD_NAVI_START_HUMAN_FOLLOW:
                return chassisRelyApi.startHumanFollowing(cmdType, cmdParam);

            case Definition.CMD_NAVI_STOP_HUMAN_FOLLOW:
                return chassisRelyApi.stopHumanFollowing(cmdType, cmdParam);

            case Definition.CMD_NAVI_DETECT_QRCODE_BY_PIC:
                return chassisRelyApi.detectQrCodeByPic(cmdType, cmdParam);

            case Definition.CMD_NAVI_WRITE_MULTI_ROBOT_EXTRA_DATA:
                return chassisRelyApi.setMultiRobotWriteExtraData(cmdType, cmdParam);
            case Definition.CMD_NAVI_GET_NAVI_MULT_GATE_PASSING_ROUTE:
                return chassisRelyApi.getMultiNaviGatePassingRoute(cmdType, cmdParam);
            default:
                return false;
        }
    }

    public boolean handleAsyncCommandNoRelyApi(String cmdType, String cmdParam, String language) {
        switch (cmdType) {
            case Definition.CMD_NAVI_GET_LOCATION:
            case Definition.CMD_NAVI_GET_LOCATION_HIGH_FREQUENCY:
            case Definition.CMD_NAVI_GET_PLACE_NAME:
                return chassisNoRelyApi.getLocation(cmdType, cmdParam);

            case Definition.CMD_NAVI_SET_LOCATION:
                return chassisNoRelyApi.setLocation(cmdType, cmdParam);

            case Definition.CMD_NAVI_IS_IN_NAVIGATION:
                return chassisNoRelyApi.getNavigationStatus(cmdType);

            case Definition.CMD_NAVI_CRUISELAYOUT_START:
                return chassisNoRelyApi.startRoutePlan(cmdType);

            case Definition.CMD_NAVI_CRUISELAYOUT_STOP:
                return chassisNoRelyApi.stopRoutePlan(cmdType);

            case Definition.CMD_NAVI_IS_IN_LOCATION:
                return chassisNoRelyApi.isInLocation(cmdType, cmdParam);

            case Definition.CMD_NAVI_SET_POSE_LOCATION:
                return chassisNoRelyApi.setPoseLocation(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_PLACE_LIST:
            case Definition.CMD_NAVI_GET_PLACE_LIST_WITH_NAME:
                return chassisNoRelyApi.getPlaceList(cmdType, language);

            case "cmd_navi_get_international_place_list_inter":
            case Definition.CMD_NAVI_GET_INTERNATIONAL_PLACE_LIST:
            case Definition.CMD_NAVI_GET_INTERNATIONAL_PLACE_LIST_FOR_REPORT:
            case Definition.CMD_NAVI_GET_PLACELIST_BY_MAPNAME_MINI:
                return chassisNoRelyApi.getInternationalPlaceList(cmdType, cmdParam);

            case Definition.CMD_NAVI_UPDATE_PLACE_LIST:
                return chassisNoRelyApi.updatePlaceList(cmdType, cmdParam, language);

            case Definition.CMD_NAVI_GET_PLACELIST_WITH_NAMELIST:
                return chassisNoRelyApi.getPlaceListWithNameList(cmdType, cmdParam);

            case Definition.CMD_NAVI_IS_ESTIMATE:
                return chassisNoRelyApi.isEstimate(cmdType);

            case Definition.CMD_NAVI_SAVE_ESTIMATE:
                return chassisNoRelyApi.savePoseEstimate(cmdType);

            case Definition.CMD_NAVI_GET_MAP_NAME:
                return chassisNoRelyApi.getCurrentMap(cmdType);

            case Definition.CMD_NAVI_GET_POSITION:
                return chassisNoRelyApi.getCurrentPose(cmdType);

            case Definition.CMD_NAVI_GET_POSITION_WITHOUT_ESTIMATE:
                return chassisNoRelyApi.getCurrentPoseWithoutEstimate(cmdType);

            case Definition.CMD_NAVI_SET_MAP_INFO:
                return chassisNoRelyApi.setMapInfo(cmdType, cmdParam);

            case Definition.CMD_NAVI_SET_PATROL_LIST:
                return chassisNoRelyApi.setPatrolList(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_PATROL_LIST:
                return chassisNoRelyApi.getPatrolList(cmdType, cmdParam);

            case Definition.CMD_NAVI_REFRESH_MD5:
                return chassisNoRelyApi.refreshMd5(cmdType, cmdParam);

            case Definition.CMD_NAVI_PARSE_PLACE_LIST:
                return chassisNoRelyApi.parseDownloadMapPlacePropToNaviProp(cmdType, cmdParam);

            case Definition.CMD_NAVI_SAVE_ROAD_DATA:
                return chassisNoRelyApi.saveRoadData(cmdType, cmdParam);

            case Definition.CMD_NAVI_SAVE_GATE_DATA:
                return chassisNoRelyApi.saveGateData(cmdType, cmdParam);

            case Definition.CMD_NAVI_SAVE_PLACE_LIST:
                return chassisNoRelyApi.savePlaceListToPlaceFile(cmdType, cmdParam);

            case Definition.CMD_NAVI_SET_CRUISE_ROUTE:
                return chassisNoRelyApi.setCruiseRoute(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_CRUISE_ROUTE:
                return chassisNoRelyApi.getCruiseRoute(cmdType, cmdParam);

            case Definition.CMD_NAVI_EDIT_PLACE:
                return chassisNoRelyApi.editPlaceProcess(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_PLACELIST_BY_MAPNAME:
                return chassisNoRelyApi.getPlaceListByMapName(cmdType, cmdParam, language);

            case Definition.CMD_NAVI_HAS_PLACE_IN_MAPNAME:
                return chassisNoRelyApi.hasPlaceInMap(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_PLACELIST_BY_TYPE:
                return chassisNoRelyApi.getPlacesByType(cmdType, cmdParam);

            case Definition.CMD_NAVI_TIME_OUT_REPORT:
                return chassisNoRelyApi.reportCmdTimeOut(cmdType, cmdParam);

            case Definition.CMD_NAVI_TIME_OUT_MSG_DELETE:
                return chassisNoRelyApi.timeOutMsgDelete(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_LOG_BY_ID:
                return chassisNoRelyApi.getLogTaskById(cmdType, cmdParam);

            case Definition.CMD_NAVI_UPDATE_LOG_STATUS_BY_ID:
                return chassisNoRelyApi.updateLogStatusById(cmdType, cmdParam);

            case Definition.CMD_NAVI_RENAME_MAP:
                return chassisNoRelyApi.renameMap(cmdType, cmdParam);

            case Definition.CMD_NAVI_CLEAR_CRUISE_ROUTE:
                return chassisNoRelyApi.clearCruiseRoute(cmdType, cmdParam);

            case Definition.CMD_NAVI_CHECK_POSE_POSITION:
                return chassisNoRelyApi.checkPosePositionTheta(cmdType, cmdParam);

            case Definition.CMD_NAVI_CHECK_OBSTACLE:
                return chassisNoRelyApi.checkObstacle(cmdType, cmdParam);

            case Definition.CMD_NAVI_HAS_OBSTACLE_IN_AREA:
                return chassisNoRelyApi.hasObstacleInArea(cmdType, cmdParam);

            case Definition.CMD_NAVI_CONNECT_STATUS:
                return chassisNoRelyApi.checkSocketConnected(cmdType);

            case Definition.CMD_NAVI_GET_MAP_INFO:
                return chassisNoRelyApi.getMapInfo(cmdType, cmdParam);

            case Definition.CMD_NAVI_SET_MAP_UUID:
                return chassisNoRelyApi.updateMapUuid(cmdType, cmdParam);

            case Definition.CMD_NAVI_SET_MAP_SYNC_STATE:
                return chassisNoRelyApi.updateMapSyncState(cmdType, cmdParam);

            case Definition.CMD_NAVI_SET_MAP_FINISH_STATE:
                return chassisNoRelyApi.updateMapFinishState(cmdType, cmdParam);

            case Definition.CMD_NAVI_SET_MAP_FORBID_LINE:
                return chassisNoRelyApi.updateMapForbidLine(cmdType, cmdParam);

            case Definition.CMD_NAVI_SET_MAP_UPDATE_TIME:
                return chassisNoRelyApi.updateMapUpdateTime(cmdType, cmdParam);

            case Definition.CMD_NAVI_ADD_MAP_INFO:
                return chassisNoRelyApi.addMapInfo(cmdType, cmdParam);

            case Definition.CMD_NAVI_PARSE_MAP_DATA:
                return chassisNoRelyApi.parseMapData(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_MULTI_ROBOT_CONFIG:
                return chassisNoRelyApi.getMultiRobotSettingConfig(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_ADDITIONAL_DEVICES:
                return chassisNoRelyApi.getAdditionalDevice(cmdType);

            case Definition.CMD_NAVI_SET_MAPPING_PLACE:
                return chassisNoRelyApi.setMappingPlace(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_MAPPING_INFO:
                return chassisNoRelyApi.getMappingInfo(cmdType, cmdParam);

            case Definition.CMD_NAVI_START_DATA_SET_RECORD:
                return chassisNoRelyApi.startDataSetRecord(cmdType, cmdParam);

            case Definition.CMD_NAVI_STOP_DATA_SET_RECORD:
                return chassisNoRelyApi.stopDataSetRecord(cmdType, cmdParam);

            case Definition.CMD_NAVI_UPLOAD_NAVI_DATA_SET:
                return chassisNoRelyApi.uploadNaviDataSet(cmdType, cmdParam);


            case Definition.CMD_NAVI_QUERY_MULTI_FLOOR_CONFIG:
                return chassisNoRelyApi.getMultiFloorConfig(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_MULTI_FLOOR_CONFIG_AND_POSE:
            case Definition.CMD_NAVI_GET_MULTI_FLOOR_CONFIG_AND_COMMON_POSE:
                return chassisNoRelyApi.getMultiFloorConfigAndPose(cmdType, cmdParam, language);

            case Definition.CMD_NAVI_INSERT_MULTI_FLOOR_CONFIG:
                return chassisNoRelyApi.insertMultiFloorConfig(cmdType, cmdParam);

            case Definition.CMD_NAVI_UPDATE_MULTI_FLOOR_CONFIG:
                return chassisNoRelyApi.updateMultiFloorConfig(cmdType, cmdParam);

            case Definition.CMD_NAVI_REMOVE_MULTI_FLOOR_CONFIG:
                return chassisNoRelyApi.removeMultiFloorConfig(cmdType, cmdParam);

            case Definition.CMD_NAVI_QUERY_CHARGE_AREA_CONFIG:
                return chassisNoRelyApi.getChargeAreaConfig(cmdType, cmdParam);

            case Definition.CMD_NAVI_INSERT_CHARGE_AREA_CONFIG:
                return chassisNoRelyApi.insertChargeAreaConfig(cmdType, cmdParam);

            case Definition.CMD_NAVI_UPDATE_CHARGE_AREA_CONFIG:
                return chassisNoRelyApi.updateChargeAreaConfig(cmdType, cmdParam);

            case Definition.CMD_NAVI_REMOVE_CHARGE_AREA_CONFIG:
                return chassisNoRelyApi.removeChargeAreaConfig(cmdType, cmdParam);

            case Definition.CMD_NAVI_SET_EXTRA_FILE_DATA:
                return chassisNoRelyApi.updateNavigationExtraData(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_EXTRA_FILE_DATA:
                return chassisNoRelyApi.getNavigationExtraData(cmdType, cmdParam);

            case Definition.CMD_NAVI_HAS_EXTRA_FILE:
                return chassisNoRelyApi.hasNavigationExtraData(cmdType, cmdParam);

            case Definition.CMD_NAVI_ZIP_EXTRA_FILE:
                return chassisNoRelyApi.zipNavigationExtraData(cmdType, cmdParam);

            case Definition.CMD_NAVI_UNZIP_EXTRA_FILE:
                return chassisNoRelyApi.unzipNavigationExtraData(cmdType, cmdParam);

            case Definition.CMD_NAVI_ZIP_MAP_FILE:
                return chassisNoRelyApi.zipMapFile(cmdType, cmdParam);

            case Definition.CMD_NAVI_UNZIP_MAP_FILE:
                return chassisNoRelyApi.unzipMapFile(cmdType, cmdParam);

            case Definition.CMD_NAVI_COPY_IMPORT_MAP_FILE:
                return chassisNoRelyApi.copyImportMapFile(cmdType, cmdParam);

            case Definition.CMD_NAVI_MAP_HAS_VISION:
                return chassisNoRelyApi.isMapHasVision(cmdType, cmdParam);

            case Definition.CMD_NAVI_GET_LOCAL_MAP_INFO_LIST:
                return chassisNoRelyApi.getLocalMapInfoList(cmdType);

            case Definition.CMD_NAVI_GET_MAP_INFO_BY_SD_MAP_NAMES:
                return chassisNoRelyApi.getMapInfoBySdMapNames(cmdType);

            case Definition.CMD_NAVI_GET_CURRENT_MAP_NAME:
                return chassisNoRelyApi.getCurrentMapName(cmdType);

            case Definition.CMD_NAVI_GET_MOTION_DISTANCE:
                return chassisNoRelyApi.getMotionDistance(cmdType);

            case Definition.CMD_NAVI_OTA_DOWNGRADE:
                return chassisNoRelyApi.otaDowngrade(cmdType, cmdParam);
            //闸机关系维护
            case Definition.CMD_NAVI_FIND_GATE_RELATION:
                return chassisNoRelyApi.getAllGateRelationData(cmdType, cmdParam);

            case Definition.CMD_NAVI_FIND_BY_GATE_IDS:
                return chassisNoRelyApi.findByGateIds(cmdType, cmdParam);

            case Definition.CMD_NAVI_FIND_BY_LINE_IDS:
                return chassisNoRelyApi.findByLineIds(cmdType, cmdParam);

            case Definition.CMD_NAVI_BATCH_INSERT_OR_UPDATE_GATE:
                return chassisNoRelyApi.batchInsertOrUpdateGate(cmdType, cmdParam);

            case Definition.CMD_NAVI_DELETE_BY_LINE_IDS:
                return chassisNoRelyApi.deleteByLineIds(cmdType, cmdParam);

            case Definition.CMD_NAVI_DELETE_BY_GATE_IDS:
                return chassisNoRelyApi.deleteByGateIds(cmdType, cmdParam);

            case Definition.CMD_NAVI_DELETE_EXCEPT_LINE_IDS:
                return chassisNoRelyApi.deleteExceptLineIds(cmdType, cmdParam);
            default:
                return false;
        }
    }

    @Override
    public String getBoardName() throws RemoteException {
        return null;
    }

    @Override
    public boolean isNeedUpgrade(String s, String s1) throws RemoteException {
        return false;
    }

    @Override
    public String getVersion(String s) throws RemoteException {
        return null;
    }

    @Override
    public ParcelFileDescriptor getParcelFileDescriptor(String type, String mapName) throws RemoteException {
        Log.d(this.TAG, "getParcelFileDescriptor: type=" + type + " mapName=" + mapName);
        switch (type) {
            case Definition.TYPE_PFD_MAP_PGM_GET:
                return getMapPgmPFD(type, mapName);

            default:
                return null;
        }
    }

    @Override
    public boolean setParcelFileDescriptor(String type, ParcelFileDescriptor pfd, String mapName) throws RemoteException {
        Log.d(this.TAG, "setParcelFileDescriptor: type=" + type + " pfd=" + pfd + " mapName=" + mapName);
        switch (type) {
            case Definition.TYPE_PFD_MAP_PGM_GET:
                return setMapPgmPFD(pfd, mapName);

            default:
                return false;
        }
    }

    @Override
    public void releaseParcelFileDescriptor(String type) throws RemoteException {
        Log.d(this.TAG, "releaseParcelFileDescriptor: type=" + type);
        switch (type) {
            case Definition.TYPE_PFD_MAP_PGM_GET:
                releaseMapPgmPFD();
                break;

            default:
                break;
        }
    }

    private ParcelFileDescriptor getMapPgmPFD(String type, String mapName) throws RemoteException {
        Log.d(this.TAG, "getMapPgmPFD: type=" + type + " mapName=" + mapName);
        if (mGetMapPgmPfd != null) {
            try {
                mGetMapPgmPfd.close();
                mGetMapPgmPfd = null;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        File mapPgm = MapFileHelper.getMapPgm(mapName);
        try {
            FileInputStream fis = new FileInputStream(mapPgm);
            Log.d(TAG, "getMapPgmPFD: fis size=" + fis.available());
            byte[] bytesArray = new byte[(int) mapPgm.length()];
            fis.read(bytesArray);
            fis.close();
            mGetMapPgmPfd = ShareMemoryFactory.createPfdFromMemoryFile(type, bytesArray);
            Log.d(TAG, "getMapPgmPFD: mGetMapPgmPfd=" + mGetMapPgmPfd);
            return mGetMapPgmPfd;
        } catch (IOException e) {
            Log.d(TAG, "getMapPgmPFD:IOException: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    private boolean setMapPgmPFD(ParcelFileDescriptor pfd, String mapName) {
        Log.d(this.TAG, "setMapPgmPFD: pfd=" + pfd + " mapName=" + mapName);
        FileDescriptor fileDescriptor = pfd.getFileDescriptor();
        FileInputStream fileInputStream = new FileInputStream(fileDescriptor);
        FileOutputStream fos = null;
        File mapPgm = MapFileHelper.getMapPgm(mapName);
        try {
            Log.d(TAG, "setMapPgmPFD: available=");
            fos = new FileOutputStream(mapPgm);
            boolean result = MapUtils.saveRoverMapToPgm(fileInputStream, fos);
            Log.d(TAG, "setMapPgmPFD: result=" + result);
            return true;
        } catch (IOException e) {
            Log.d(this.TAG, "setMapPgmPFD: IOException: " + e.getMessage());
            e.printStackTrace();
        } finally {
            Log.d(TAG, "setMapPgmPFD:finally: Close pfd!");
            try {
                if (pfd != null) pfd.close();
                if (fos != null) fos.close();
                if (fileInputStream != null) fileInputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    private void releaseMapPgmPFD() {
        if (mGetMapPgmPfd != null) {
            Log.d(this.TAG, "releaseParcelFileDescriptor: Close mGetMapPgmPfd!");
            try {
                mGetMapPgmPfd.close();
                mGetMapPgmPfd = null;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

}
