/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice;

import android.app.Application;
import android.content.Context;
import android.util.Log;

import com.ainirobot.robotlog.RobotLog;

public class ApplicationWrapper extends Application {

    private static ApplicationWrapper mWrapper;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d("ApplicationWrapper", "onCreate: ");
        RobotLog.init(this);
        mWrapper = this;
    }

    public static Context getContext() {
        return mWrapper.getApplicationContext();
    }
}
