package com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1;

import android.util.Log;

import com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.tk1.IChassisCommand.ResponseListener;
import com.ainirobot.navigationservice.beans.tk1.MotionMode;
import com.ainirobot.navigationservice.beans.tk1.RelocateMode;
import com.ainirobot.navigationservice.beans.tk1.WorkMode;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.List;
import java.util.Queue;

import static com.ainirobot.navigationservice.Defs.Def.ResultCode.FAIL_NO_REASON;
import static com.ainirobot.navigationservice.Defs.Def.ResultCode.SUCCESS;


public class Operations {

    private ChassisCommandTk1Impl mApi;
    private ResponseListener mListener;
    private Queue<Oper> mOpers = new ArrayDeque<>();

    public Operations(ChassisCommandTk1Impl api, Queue<Oper> opers, ResponseListener listener) {
        this.mApi = api;
        this.mListener = listener;
        mOpers = opers;
    }

    public Operations add(Oper oper) {
        this.mOpers.add(oper);
        return this;
    }

    public void start() {
        if (mOpers == null || mOpers.isEmpty()) {
            return;
        }
        startNext();
    }

    private void startNext() {
        Log.e("operations", " Command operations startNext : " + mOpers.size());
        if (mOpers.isEmpty()) {
            if (mListener != null) {
                //TODO 所有的成功，都是这块返回的。
                mListener.onResponse(true, SUCCESS, null);
            }
            mApi.removeOperations(this);
            return;
        }

        final Oper oper = mOpers.poll();
        final ResponseListener listener = new ResponseListener() {
            @Override
            public void onResponse(boolean status, int resultCode, Object result) {
                boolean isContinue = oper.onResponse(status, resultCode, result);
                if (isContinue) {
                    startNext();
                } else {
                    //TODO 没必要存在，一旦continue 返回false，则说明指令已经执行完毕。
                    if (mListener != null) {
                        mListener.onResponse(false, resultCode, null);
                    }
                }
            }
        };
        oper.perform(mApi, listener);
    }

    public static class SetGoal extends Oper {
        public SetGoal(double x, double y, double theta,
                       double linear, double angular, int moveType,
                       int rotateType, float desRange) {
            super(Protocol.SET_GOAL, x, y, theta,
                    linear, angular, moveType, rotateType, desRange);
        }
    }

    public static class AddGlobalMap extends Oper {
        String mMapName;
        String mNaviMapName;
        ResponseListener mListener;
        List<Boolean> mResponses;

        public AddGlobalMap(String naviMapName, String mapName) {
            super(Protocol.ADD_MAP, naviMapName);
            this.mNaviMapName = naviMapName;
            this.mMapName = mapName;
            mResponses = new ArrayList<>();
        }

        @Override
        public boolean perform(ChassisCommandTk1Impl api, ResponseListener listener) {
            Log.e("operations", "AddGlobalMap perform");
            mListener = listener;
            api.addGlobalMapData(mMapName, mNaviMapName, new ResponseListener() {
                @Override
                public void onResponse(boolean status, int resultCode, Object result) {
                    response(status);
                }
            });

            api.addGlobalMapPgm(mMapName, mNaviMapName, new ResponseListener() {
                @Override
                public void onResponse(boolean status, int resultCode, Object result) {
                    response(status);
                }
            });
            return true;
        }

        private void response(boolean status) {
            mResponses.add(status);
            if (mResponses.size() <= 1 || mListener == null) {
                return;
            }

            for (Boolean response : mResponses) {
                if (!response) {
                    mListener.onResponse(false, FAIL_NO_REASON, "add global map failed");
                    return;
                }
            }
            mListener.onResponse(true, SUCCESS, "add global map succeed");
        }
    }

    public static class SetNavigationMap extends Oper {

        public SetNavigationMap(String naviMapName) {
            super(Protocol.SET_NAVIGATION_MAP, naviMapName);
        }
    }

    public static class GetMapList extends Oper {

        public GetMapList() {
            super(Protocol.GET_MAP_LIST);
        }
    }

    public static class SetPoseEstimate extends Oper {

        public SetPoseEstimate(RelocateMode mode, double x, double y, double t) {
            super(Protocol.SET_RELOCATE, mode, x, y, t);
        }
    }

    public static class TakeSnapshot extends Oper {

        public TakeSnapshot(String logID) {
            super(Protocol.TAKE_SNAP_SHOT, logID);
        }
    }

    public static class PackLog extends Oper {

        public PackLog(long startTime, long endTime) {
            super(Protocol.PACK_LOG, startTime, endTime);
        }
    }

    public static class SetTime extends Oper {

        public SetTime(long currTime) {
            super(Protocol.SET_TIME, currTime);
        }
    }

    public static class RemoveTkMap extends Oper {

        public RemoveTkMap(String mapName) {
            super(Protocol.DELETE_MAP, mapName);
        }
    }

    public static class Recovery extends Oper {

        public Recovery () {
            super(Protocol.STORAGE_RECOVERY);
        }
    }

    public static class GetGlobalMap extends Oper {
        String mMapName;
        String mNaviMapName;
        ResponseListener mListener;
        List<Boolean> mResponses;

        public GetGlobalMap(String naviMapName, String mapName) {
            super(Protocol.GET_GLOBAL_MAP_FILE);
            this.mNaviMapName = naviMapName;
            this.mMapName = mapName;
            mResponses = new ArrayList<>();
        }

        @Override
        public boolean perform(ChassisCommandTk1Impl api, ResponseListener listener) {
            mListener = listener;
            api.getGlobalMapData(mMapName, mNaviMapName, new ResponseListener() {
                @Override
                public void onResponse(boolean status, int resultCode, Object result) {
                    response(status);
                }
            });

            api.getGlobalMapPgm(mMapName, mNaviMapName, new ResponseListener() {
                @Override
                public void onResponse(boolean status, int resultCode, Object result) {
                    response(status);
                }
            });
            return true;
        }

        private void response(boolean status) {
            mResponses.add(status);
            if (mResponses.size() <= 1 || mListener == null) {
                return;
            }

            for (Boolean response : mResponses) {
                if (!response) {
                    mListener.onResponse(false, FAIL_NO_REASON, "Get global map failed");
                    return;
                }
            }
            mListener.onResponse(true, SUCCESS, "Get global map succeed");
        }
    }

    public static class GetLogFile extends Oper {
        String mPath;
        String mFileType;
        ResponseListener mListener;
        List<Boolean> mResponses;

        public GetLogFile(String path, String fileType) {
            super(Protocol.GET_ERROR_LOG);
            this.mPath = path;
            this.mFileType = fileType;
            mResponses = new ArrayList<>();
        }

        @Override
        public boolean perform(ChassisCommandTk1Impl api, ResponseListener listener) {
            mListener = listener;
            api.getLogFile(mPath, mFileType, new ResponseListener() {
                @Override
                public void onResponse(boolean status, int resultCode, Object result) {
                    response(status, result);
                }
            });
            return true;
        }

        private void response(boolean status, Object result) {
            mResponses.add(status);
            if (mResponses.size() < 1 || mListener == null) {
                return;
            }

            for (Boolean response : mResponses) {
                if (!response) {
                    mListener.onResponse(false, FAIL_NO_REASON, "");
                    return;
                }
            }
            mListener.onResponse(true, SUCCESS, result);
        }
    }

    public static class StartCreateMap extends Oper {

        public StartCreateMap(String naviMapName) {
            super(Protocol.START_CREATE_MAP, naviMapName);
        }
    }

    public static class StopCreateMap extends Oper {
        public StopCreateMap() {
            super(Protocol.STOP_CREATE_MAP);
        }
    }

    public static class SaveMap extends Oper {
        public SaveMap () {
            super(Protocol.SAVE_MAP);
        }
    }

    public static class SetWorkMode extends Oper {
        private WorkMode mWorkMode;

        public SetWorkMode(WorkMode mode) {
            super(Protocol.SET_WORKING_MODE);
            this.mWorkMode = mode;
        }

        @Override
        public boolean perform(ChassisCommandTk1Impl api, ResponseListener listener) {
            return api.setWorkingMode(mWorkMode, listener);
        }
    }

    public static class GetWorkMode extends Oper {
        public GetWorkMode() {
            super(Protocol.GET_WORKING_MODE_STATE);
        }
    }

    public static class SetMotionMode extends Oper {
        private MotionMode mMotionMode;

        public SetMotionMode(MotionMode motionMode) {
            super(Protocol.SET_MOTION_MODE);
            this.mMotionMode = motionMode;
        }

        @Override
        public boolean perform(ChassisCommandTk1Impl api, ResponseListener listener) {
            return api.setMotionMode(mMotionMode, listener);
        }
    }

    public static class GetSensorStatus extends Oper {
        public GetSensorStatus() {
            super(Protocol.SELF_CHECK_ALL);
        }
    }

    public static class BaseMotion extends Oper {
        public BaseMotion(double angularSpeed, double linearSpeed) {
            super(Protocol.SEND_PRIMITIVE_MOVING_COMMAND, angularSpeed, linearSpeed);
        }
    }

    public static class CancelNavigation extends Oper {
        public CancelNavigation() {
            super(Protocol.CANCEL_AUTO_MOVING);
        }
    }

    public static class Oper {
        Protocol mProtocol;
        Object[] mParams;

        public Oper(Protocol protocol, Object... params) {
            this.mProtocol = protocol;
            mParams = params;
        }

        public boolean onResponse(boolean status, int resultCode, Object result) {
            return status;
        }

        public boolean perform(ChassisCommandTk1Impl api, ResponseListener listener) {
            return api.sendCommand(mProtocol, listener, mParams);
        }
    }
}
