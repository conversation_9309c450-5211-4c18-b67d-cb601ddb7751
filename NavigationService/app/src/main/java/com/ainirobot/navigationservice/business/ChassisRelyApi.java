package com.ainirobot.navigationservice.business;

import android.content.Context;

/**
 * 依赖调用底盘接口实现接口逻辑
 *
 * 业务逻辑在这一层抽象出来，理论上后续加地盘，这里不能动。
 * 那么不同地盘，调用接口的返回，要在Client实现中去做适配。
 */
public interface ChassisRelyApi {

    void init(Context mContext);
    int onGetHWStatus(String cmdType);
    boolean removeMap(String cmdType, String mapName);
    boolean goLocation(String cmdType, String cmdParam);
    boolean cancelNavigation(String cmdType);
    boolean stopMove(String cmdType);
    boolean startPatrol(String cmdType, String cmdParam);
    boolean stopPatrol(String cmdType);
    boolean resetLocation(String cmdType, String cmdParam);
    boolean moveDirection(String cmdType, String cmdParam);
    boolean rotateInPlace(String cmdType, String cmdParam);
    boolean motionArc(String cmdParam);
    boolean motionControlWithObstacles(String cmdParam);
    boolean motionArcWithObstacles(String cmdParam);
    boolean startCreatingMap(String cmdType, String cmdParam);
    boolean stopCreatingMap(String cmdType, String cmdParam);
    boolean stopExtendMap(String cmdType, String cmdParam);
    boolean setPoseEstimate(String cmdType, String cmdParam);
    boolean setFixedEstimate(String cmdType, String cmdParam);
    boolean setForceEstimate(String cmdType, String cmdParam);
    boolean setChassisRelocation(String cmdType, String cmdParam);
    boolean switchMap(String cmdType, String cmdParam);
    boolean goPosition(String cmdType, String cmdParam);
    boolean goPositionByType(String cmdType, String cmdParam);
    boolean getVersion(String cmdType);
    boolean startUpdate(String cmdType, String cmdParam);
    boolean getUpdateParams(String cmdType);
    boolean motion(String cmdParam);
    boolean motionWithObstacles(String cmdParam);
    boolean motionSoft(String cmdParam);
    boolean goDefaultTheta(String cmdType);
    boolean getFullCheckStatus(String cmdType);
    boolean getSensorStatus(String cmdType);
    boolean switchChargeMode(String cmdType);
    boolean switchManualMode(String cmdType);
    boolean resetPoseEstimate(String cmdType);
    boolean setRoverConfig(String cmdType, String cmdParam);
    boolean getRoverConfig(String cmdType);
    boolean getSystemInformation(String cmdType);
    boolean resumeSpecialPlaceTheta(String cmdType, String cmdParam);
    boolean loadCurrentMap(String cmdType, String cmdParam);
    boolean clearCurNaviMap(String cmdType);
    boolean getLogFile(String cmdType, String cmdParam, String type);
    boolean packLogFile(String cmdType, String cmdParam);
    boolean checkCurNaviMap(String cmdType, String cmdParam);
    boolean setRadarState(String cmdType, String cmdParam);
    boolean getRadarState(String cmdType);
    boolean turnByNavigation(String cmdType, String cmdParam);
    boolean setLocateVision(String cmdType);
    boolean recoveryNavigation(String cmdType);
    boolean setMinObstaclesDistance(String cmdType, String cmdParam);
    boolean resetMinObstaclesDistance(String cmdType);
    boolean goCharge(String cmdType, String cmdParam);
    boolean stopCharge(String cmdType);
    boolean getMapStatus(String cmdType, String cmdParam);
    boolean addMappingPose(String cmdType, String param);
    boolean deleteMappingPose(String cmdType, String name);
    boolean renameMappingPose(String cmdType, String cmdParam);
    boolean startExtendMap(String cmdType, String cmdParam);
    boolean cancelCreateMap(String cmdType, String cmdParam);
    boolean motionPid(String params);

    /**
     * 关闭底盘摄像头，目前仅有招财豹topIR有效，其他产品线和相机暂不支持开关
     */
    boolean setCameraEnable(String cmdType, String cmdParam);
    /**
     * 下发多机参数配置信息
     */
    boolean setMultiRobotSettingConfigData(String cmdType, String cmdParam);
    /**
     * 下发多机数据
     */
    boolean sendMultiRobotMsgData(String cmdType, String cmdParam);
    /**
     * 打开lora收发测试模式,仅招财豹工厂版本使用
     */
    boolean setLoraTestMode(String cmdType, String cmdParam);

    /**
     * 重置Lora配置，仅招财豹工厂版本使用
     */
    boolean resetLoraDefaultConfig(String cmdType, String cmdParam);

    /**
     * 控制轮子状态
     */
    boolean setWheelControlMode(String cmdType, String cmdParam);

    /**
     * 获取从当前点到目标点的路线信息
     * @param cmdType command type
     * @param cmdParam 目标点信息，实际十个目标点集合
     * @return
     */
    boolean getNaviPathInfoToGoals(String cmdType, String cmdParam);

    /**
     * 获取从A点到B的路线信息
     *
     * @param cmdType command type
     * @param cmdParam A点坐标+B点坐标
     * @return
     */
    boolean getNaviPathInfo(String cmdType, String cmdParam);

    /**
     * 获取从A点到B的路线详情
     *
     * @param cmdType command type
     * @param cmdParam A点坐标+B点坐标
     * @return
     */
    boolean getNaviPathDetail(String cmdType, String cmdParam);

    /**
     * 获取从从当前点位到达目标点位通过的闸机线端点
     *
     * @param cmdType command type
     * @param cmdParam A点坐标+B点坐标
     * @return
     */
    boolean getNaviGatePassingRoute(String cmdType, String cmdParam);

    /**
     * 指令控制 底盘是否要给上层上报雷达数据
     */
    boolean enableReportLineData(String cmdType, String cmdParam);

    /**
     * 指令控制 底盘是否要给上层上报RGBD深度图
     */
    boolean enableRgbdDepthImage(String cmdType, String cmdParam);

    /**
     * 指令控制 底盘是否要给上层上报Top IR图
     */
    boolean enableTopIRImage(String cmdType, String cmdParam);

    /**
     * 自动绘制巡线
     */
    boolean autoDrawRoad(String cmdType, String cmdParam);

    /**
     * 获得导航参数信息
     */
    boolean getNaviParams(String cmdType, String cmdParam);

    /**
     * 导航暂停接口
     */
    boolean naviPause(String cmdType, String cmdParam);

    boolean gotoAlign(String cmdType, String cmdParam);

    /**
     * 取消精准对齐
     */
    boolean cancelAlign(String cmdType, String cmdParam);

    boolean startHumanFollowing(String cmdType, String cmdParam);

    boolean stopHumanFollowing(String cmdType, String cmdParam);

    boolean detectQrCodeByPic(String cmdType, String cmdParam);
    boolean setMultiRobotWriteExtraData(String cmdType, String cmdParam);

    /**
     * 获取从从当前点位到达目标点位通过的闸机线端点集合
     *
     * @param cmdType command type
     * @param cmdParam A点坐标+B点坐标
     * @return
     */
    boolean getMultiNaviGatePassingRoute(String cmdType, String cmdParam);
}
