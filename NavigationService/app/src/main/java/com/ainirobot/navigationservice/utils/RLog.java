package com.ainirobot.navigationservice.utils;

import android.os.Build;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;

import java.util.concurrent.ConcurrentHashMap;

public final class RLog {

    private static final int LEVEL_V = 1 << 0;
    private static final int LEVEL_I = 1 << 1;
    private static final int LEVEL_D = 1 << 2;

    public enum LOG_LEVEL{
        LEVEL_V, LEVEL_I, LEVEL_D
    }

    private static ConcurrentHashMap<String, LogInfo> mPruneLog = new ConcurrentHashMap<>();
    private static int mLevel = LEVEL_I | LEVEL_D;

    static {
        if("user".equals(Build.TYPE)){
            setLogLevel(LOG_LEVEL.LEVEL_I);
        } else {
//            setLogLevel(LOG_LEVEL.LEVEL_V);
            setLogLevel(LOG_LEVEL.LEVEL_I);
        }
    }

    public synchronized static void pruneLog(String tag, String msg, long period) {
        if(TextUtils.isEmpty(tag)){
            return;
        }
        if (mPruneLog.containsKey(tag)){
            LogInfo log = mPruneLog.get(tag);
            long currentTime = SystemClock.elapsedRealtime();
            if(currentTime - log.getPreTime() > period){
                i(tag, msg + "   ++++++ expire count: "+log.getCount());
                log.setPreTime(currentTime);
                log.setCount(0);
            } else {
                log.setCount(log.getCount()+1);
            }
        }else {
            LogInfo log = new LogInfo();
            log.setCount(0);
            log.setPreTime(SystemClock.currentThreadTimeMillis());
            i(tag, msg + "   ++++++ expire count: "+log.getCount());
            mPruneLog.put(tag, log);
        }
    }

    public static boolean setLogLevel(LOG_LEVEL level){
        Log.i("RLog", "set log levle: " + level);
        switch (level){
            case LEVEL_V:
                mLevel = LEVEL_V | LEVEL_D | LEVEL_I;
                return true;

            case LEVEL_I:
                mLevel = LEVEL_I | LEVEL_D;
                return true;

            case LEVEL_D:
                mLevel = LEVEL_D;
                return true;

            default:
                return false;
        }
    }

    public static void v(String tag, String msg){
        if((mLevel & LEVEL_V) == LEVEL_V){
            Log.v(tag, msg);
        }
    }

    public static void i(String tag, String msg){
        if((mLevel & LEVEL_I) == LEVEL_I){
            Log.i(tag, msg);
        }
    }

    public static void d(String tag, String msg){
        if((mLevel & LEVEL_D) == LEVEL_D){
            Log.d(tag, msg);
        }
    }


    private static class LogInfo {
        private long preTime;
        private long count;

        public long getPreTime() {
            return preTime;
        }

        public void setPreTime(long preTime) {
            this.preTime = preTime;
        }

        public long getCount() {
            return count;
        }

        public void setCount(long count) {
            this.count = count;
        }
    }
}
