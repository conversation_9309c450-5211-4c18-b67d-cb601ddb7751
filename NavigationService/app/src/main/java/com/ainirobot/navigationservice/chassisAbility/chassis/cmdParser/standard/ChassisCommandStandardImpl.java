package com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard;

import android.util.Log;
import android.util.SparseArray;

import com.ainirobot.navigationservice.beans.standard.LandMarkBean;
import com.ainirobot.navigationservice.beans.standard.NaviSpeedParam;
import com.ainirobot.navigationservice.beans.standard.PoseInfo;
import com.ainirobot.navigationservice.chassisAbility.chassis.connector.standard.ConnectApi;
import com.ainirobot.navigationservice.protocol.bean.LandMarkBeanCreator.LandMarkBeanProto;
import com.ainirobot.navigationservice.protocol.bean.PoseBeanCreator.PoseBeanProto;
import com.ainirobot.navigationservice.protocol.request.ReqAddLandMarkCreator.ParamAddLandMark;
import com.ainirobot.navigationservice.protocol.request.ReqDeleteLandMarkCreator.ParamDeleteLandMark;
import com.ainirobot.navigationservice.protocol.request.ReqDeleteMapCreator.ParamDeleteMap;
import com.ainirobot.navigationservice.protocol.request.ReqForceForwardMoveCreator.ParamForceForwardMove;
import com.ainirobot.navigationservice.protocol.request.ReqForceRotationMoveCreator.ParamForceRotationMove;
import com.ainirobot.navigationservice.protocol.request.ReqGetLandMarkListCreator.ParamGetLandMarkList;
import com.ainirobot.navigationservice.protocol.request.ReqGetMapInfoCreator.ParamGetMapInfo;
import com.ainirobot.navigationservice.protocol.request.ReqHeadCreator.ReqHeadProto;
import com.ainirobot.navigationservice.protocol.request.ReqModifyLandmarkInfoCreator.ParamModifyLandmarkInfo;
import com.ainirobot.navigationservice.protocol.request.ReqPacketCreator.ReqPacketProto;
import com.ainirobot.navigationservice.protocol.request.ReqSaveMappingPoseCreator.ParamSaveMappingPose;
import com.ainirobot.navigationservice.protocol.request.ReqSetControlModeCreator.ParamSetControlMode;
import com.ainirobot.navigationservice.protocol.request.ReqSetNaviSpeedParamCreator;
import com.ainirobot.navigationservice.protocol.request.ReqStartCreateMapCreator.ParamStartCreateMap;
import com.ainirobot.navigationservice.protocol.request.ReqStopCreateMapCreator;
import com.ainirobot.navigationservice.protocol.request.ReqSwitchMapCreator.ParamSwitchMap;
import com.google.protobuf.ByteString;
import com.google.protobuf.Message;

import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import static com.ainirobot.navigationservice.Defs.Def.StandardDef.CANCEL;
import static com.ainirobot.navigationservice.Defs.Def.StandardDef.EXECUTION;
import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_ADD_LANDMARK;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_CHARGE_PILE_DOCKING;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_CHARGE_PILE_RECOGNIZE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_CHARGE_PILE_SEARCH;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_DELETE_LANDMARK;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_DELETE_MAP;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_FORCE_FORWARD_MOVE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_FORCE_ROTATION_MOVE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_GET_CUR_MAP_INFO;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_GET_LANDMARK_LIST;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_GET_LOCALIZATION_STATE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_GET_MAP_INFO;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_GET_MAP_LIST;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_GET_REALTIME_MAP;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_GET_SENSOR_STATUS;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_MODIFY_LANDMARK_INFO;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_SAVE_MAPPING_POSE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_SET_CONTROL_MODE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_SET_FIX_POINT_MODE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_SET_MANUAL_MODE;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_SET_NAVI_GOAL_POINT;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_SET_NAVI_SPEED_PARAM;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_SET_RELOCALIZATION;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_START_CREATE_MAP;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_STOP_CREATE_MAP;
import static com.ainirobot.navigationservice.chassisAbility.chassis.cmdParser.standard.ReqCmdStandardDef.REQ_SWITCH_MAP;

public class ChassisCommandStandardImpl implements CmdStandardApi {


    private final static String TAG = TAGPRE + ChassisCommandStandardImpl.class.getSimpleName();

    private ConnectApi connector;
    SparseArray<CMDRequest> mRequestList = new SparseArray<CMDRequest>();
    ConcurrentHashMap<String, Integer> cmdReqIdMap = new ConcurrentHashMap<>();
    private ConcurrentHashMap<String, EventListener> eventListenerHashMap = new ConcurrentHashMap<>();

    @Override
    public void registerEventListener(String type, EventListener listener) {
        Log.d(TAG, "reg event listener = " + type);
        eventListenerHashMap.put(type, listener);
    }

    @Override
    public void unRegisterEventListener(String type) {
        eventListenerHashMap.remove(type);
    }

    public EventListener getEventListener(String type) {
        return eventListenerHashMap.get(type);
    }



    private ConnectApi.OnConnectListener connectListener = new ConnectApi.OnConnectListener() {
        @Override
        public void onConnected() {

        }

        @Override
        public void onDisconnected(String channelName) {

        }
    };

    @Override
    public void injectConnector(ConnectApi connectImpl) {
        Log.d(TAG, "injectConnector");
        connector = connectImpl;
        connector.registerResponseListener(new ConnectResStandardListener(this));
        connector.registerEventListener(new ConnectEventStandardListener(this));
        connector.registerConnectListener(connectListener);
        connector.init();
    }

    @Override
    public void enterManualMode(ResponseListener listener) {
        processReqSetManualMode(1, listener);
    }

    @Override
    public void exitManualMode(ResponseListener listener) {
        processReqSetManualMode(0, listener);
    }

    /**
     *
     * @param type 0 means cancel, 1 means execution
     * @param listener
     */
    private void processReqSetManualMode(int type, ResponseListener listener) {
        Log.d(TAG, "processReqSetManualMode type = " + type);
        CMDRequest cr = generateCR(type, REQ_SET_MANUAL_MODE, listener);
        if (cr == null) {
            Log.d(TAG, "no CMDRequest available");
            return;
        }
        cr.setType(type);
        send(cr);
    }

    private CMDRequest generateCR(int type, String cmdName, ResponseListener listener) {
        CMDRequest cr = CMDRequest.obtain(cmdName, listener);;
        if (type == CANCEL) {
            if (cmdReqIdMap.get(cmdName) != null) {
                //每一个Cancel，必须对应一个已执行任务
                int reqId = cmdReqIdMap.get(cmdName);
                CMDRequest crPaired = findRequestFromList(reqId);
                if (crPaired == null) {
                    //有任务记录，但是实际上任务没在请求列表，所以清除
                    cmdReqIdMap.remove(cmdName);
                    Log.e(TAG, "no paired task in requestList");
//                    return null;
                } else {
                    cr.setPairedReqId(reqId);//save paired task，to remove from list
                }
            } else {
                Log.e(TAG, "no paired task in cmdReqIdMap");
//                return null;
            }
        } else {
            //TODO 暂时不考虑多次发起任务的情况
//            cr = CMDRequest.obtain(cmdName, listener);
        }
        return cr;
    }

    @Override
    public void enterRemoteControlMode(double vX, double vTheta, ResponseListener listener) {
        processReqSetRemoteControlMode(1, vX, vTheta, listener);
    }

    @Override
    public void exitRemoteControlMode(ResponseListener listener) {
        processReqSetRemoteControlMode(0, 0, 0, listener);
    }

    /**
     *
     * @param type 0 means cancel, 1 means execution
     * @param listener
     */
    private void processReqSetRemoteControlMode(int type, double vX, double vTheta, ResponseListener listener) {
        Log.d(TAG, "processReqSetRemoteControlMode type = " + type);
        CMDRequest cr = generateCR(type, REQ_SET_CONTROL_MODE, listener);
        if (cr == null) {
            Log.d(TAG, "no CMDRequest available");
            return;
        }
        cr.setType(type);

        if (type == EXECUTION) {
            ParamSetControlMode param = ParamSetControlMode.newBuilder().setVX(vX).setVTheta(vTheta).build();
            cr.setParam(param);
        }

        send(cr);
    }

    @Override
    public void enterFixPointMode(ResponseListener listener) {
        processReqSetFixPointMode(1, listener);
    }

    @Override
    public void exitFixPointMode(ResponseListener listener) {
        processReqSetFixPointMode(0, listener);
    }

    /**
     *
     * @param type 0 means cancel, 1 means execution
     * @param listener
     */
    private void processReqSetFixPointMode(int type, ResponseListener listener) {
        Log.d(TAG, "processReqSetFixPointMode type = " + type);
        CMDRequest cr = generateCR(type, REQ_SET_FIX_POINT_MODE, listener);
        if (cr == null) {
            Log.d(TAG, "no CMDRequest available");
            return;
        }
        cr.setType(type);
        send(cr);
    }


    @Override
    public void exeChargePileRecognize(ResponseListener listener) {
        processReqChargePileRecognize(1, listener);
    }

    @Override
    public void cancelChargePileRecognize(ResponseListener listener) {
        processReqChargePileRecognize(0, listener);
    }

    /**
     *
     * @param type 0 means cancel, 1 means execution
     * @param listener
     */
    private void processReqChargePileRecognize(int type, ResponseListener listener) {
        Log.d(TAG, "processReqChargePileRecognize type = " + type);
        CMDRequest cr = generateCR(type, REQ_CHARGE_PILE_RECOGNIZE, listener);
        if (cr == null) {
            Log.d(TAG, "no CMDRequest available");
            return;
        }
        cr.setType(type);
        send(cr);
    }


    @Override
    public void exeChargePileSearch(ResponseListener listener) {
        processReqChargePileSearch(1, listener);
    }

    @Override
    public void cancelChargePileSearch(ResponseListener listener) {
        processReqChargePileSearch(0, listener);
    }

    /**
     *
     * @param type 0 means cancel, 1 means execution
     * @param listener
     */
    private void processReqChargePileSearch(int type, ResponseListener listener) {
        Log.d(TAG, "processReqChargePileRecognize type = " + type);
        CMDRequest cr = generateCR(type, REQ_CHARGE_PILE_SEARCH, listener);
        if (cr == null) {
            Log.d(TAG, "no CMDRequest available");
            return;
        }
        cr.setType(type);
        send(cr);
    }

    @Override
    public void exeChargePileDocking(ResponseListener listener) {
        processReqChargePileDocking(1, listener);
    }

    @Override
    public void cancelChargePileDocking(ResponseListener listener) {
        processReqChargePileDocking(0, listener);
    }

    /**
     *
     * @param type 0 means cancel, 1 means execution
     * @param listener
     */
    private void processReqChargePileDocking(int type, ResponseListener listener) {
        Log.d(TAG, "processReqChargePileDocking type = " + type);
        CMDRequest cr = generateCR(type, REQ_CHARGE_PILE_DOCKING, listener);
        if (cr == null) {
            Log.d(TAG, "no CMDRequest available");
            return;
        }
        cr.setType(type);
        send(cr);
    }

    @Override
    public void exeForceForwardMove(double distance, double vX, ResponseListener listener) {
        processReqForceForwardMove(1, distance, vX, listener);
    }

    @Override
    public void cancelForceForwardMove(ResponseListener listener) {
        processReqForceForwardMove(0, 0, 0, listener);
    }

    /**
     *
     * @param type 0 means cancel, 1 means execution
     * @param listener
     */
    private void processReqForceForwardMove(int type, double distance, double vX, ResponseListener listener) {
        Log.d(TAG, "processReqForceForwardMove type = " + type);
        CMDRequest cr = generateCR(type, REQ_FORCE_FORWARD_MOVE, listener);
        if (cr == null) {
            Log.d(TAG, "no CMDRequest available");
            return;
        }
        cr.setType(type);
        if (type == EXECUTION) {
            ParamForceForwardMove param = ParamForceForwardMove.newBuilder()
                    .setDistance(distance).setVX(vX).build();
            cr.setParam(param);
        }
        send(cr);
    }

    @Override
    public void exeForceRotationMove(double angle, double vTheta, ResponseListener listener) {
        processReqForceRotationMove(1, angle, vTheta, listener);
    }

    @Override
    public void cancelForceRotationMove(ResponseListener listener) {
        processReqForceRotationMove(0, 0, 0, listener);
    }


    /**
     *
     * @param type 0 means cancel, 1 means execution
     * @param listener
     */
    private void processReqForceRotationMove(int type, double angle, double vTheta, ResponseListener listener) {
        Log.d(TAG, "processReqForceRotationMove type = " + type);
        CMDRequest cr = generateCR(type, REQ_FORCE_ROTATION_MOVE, listener);
        if (cr == null) {
            Log.d(TAG, "no CMDRequest available");
            return;
        }
        cr.setType(type);
        if (type == EXECUTION) {
            ParamForceRotationMove param = ParamForceRotationMove.newBuilder()
                    .setAngle(angle).setVTheta(vTheta).build();
            cr.setParam(param);
        }
        send(cr);
    }

    @Override
    public void exeNaviGoPlace(PoseInfo pose, ResponseListener listener) {
        processReqSetNaviGoal(1, pose, listener);
    }

    @Override
    public void cancelNaviGoPlace(ResponseListener listener) {
        processReqSetNaviGoal(0, null, listener);
    }

    /**
     *
     * @param type 0 means cancel, 1 means execution
     * @param listener
     */
    private void processReqSetNaviGoal(int type, PoseInfo pose, ResponseListener listener) {
        Log.d(TAG, "processReqSetNaviGoal type = " + type);
        CMDRequest cr = generateCR(type, REQ_SET_NAVI_GOAL_POINT, listener);
        if (cr == null) {
            Log.d(TAG, "no CMDRequest available");
            return;
        }
        cr.setType(type);
        if (type == EXECUTION) {
            PoseBeanProto param = PoseBeanProto.newBuilder().setX(pose.getX()).setY(pose.getY())
                    .setTheta(pose.getTheta()).build();
            cr.setParam(param);
        }
        send(cr);
    }

    @Override
    public void saveMappingPose(int index, String fileName, ResponseListener listener) {
        processReqSaveMappingPose(1, index, fileName, listener);
    }

    @Override
    public void cancelSaveMappingPose(int index, String fileName, ResponseListener listener) {
        processReqSaveMappingPose(0, index, fileName, listener);
    }

    /**
     *
     * @param type 0 means cancel, 1 means execution
     * @param listener
     */
    private void processReqSaveMappingPose(int type, int index, String fileName, ResponseListener listener) {
        Log.d(TAG, "processReqSaveMappingPose type = " + type);
        CMDRequest cr = generateCR(type, REQ_SAVE_MAPPING_POSE, listener);
        if (cr == null) {
            Log.d(TAG, "no CMDRequest available");
            return;
        }
        cr.setType(type);
        if (type == EXECUTION) {
            ParamSaveMappingPose param = ParamSaveMappingPose.newBuilder().setPoseIndex(index)
                    .setPoseName(fileName).build();
            cr.setParam(param);
        }
        send(cr);
    }

    @Override
    public void getSensorStatus(ResponseListener listener) {
        processReqGetSensorStatus(1, listener);
    }

    @Override
    public void cancelGetSensorStatus(ResponseListener listener) {
        processReqGetSensorStatus(0, listener);
    }

    /**
     *
     * @param type 0 means cancel, 1 means execution
     * @param listener
     */
    private void processReqGetSensorStatus(int type, ResponseListener listener) {
        Log.d(TAG, "processReqSaveMappingPose type = " + type);
        CMDRequest cr = generateCR(type, REQ_GET_SENSOR_STATUS, listener);
        if (cr == null) {
            Log.d(TAG, "no CMDRequest available");
            return;
        }
        cr.setType(type);
        send(cr);
    }

    @Override
    public void deleteMap(String mapId, ResponseListener listener) {
        Log.d(TAG, "deleteMap mapId = " + mapId);
        CMDRequest cr = CMDRequest.obtain(REQ_DELETE_MAP, listener);
        ParamDeleteMap param = ParamDeleteMap.newBuilder().setMapId(mapId).build();
        cr.setParam(param);
        send(cr);
    }

    @Override
    public void startCreateMap(String mapName, int floor, String tag, ResponseListener listener) {
        Log.d(TAG, "startCreateMap mapName = " + mapName + ", floor = " + floor + ", tag = " + tag);
        CMDRequest cr = CMDRequest.obtain(REQ_START_CREATE_MAP, listener);
        ParamStartCreateMap param = ParamStartCreateMap.newBuilder().setMapName(mapName)
                .setFloor(floor).setTag(tag).build();
        cr.setParam(param);
        send(cr);
    }

    @Override
    public void getMapInfo(String mapId, ResponseListener listener) {
        Log.d(TAG, "getMapInfo mapId = " + mapId);
        CMDRequest cr = CMDRequest.obtain(REQ_GET_MAP_INFO, listener);
        ParamGetMapInfo param = ParamGetMapInfo.newBuilder().setMapId(mapId).build();
        cr.setParam(param);
        send(cr);
    }

    @Override
    public void getLandMarkList(String mapId, ResponseListener listener) {
        Log.d(TAG, "getLandMarkList mapId = " + mapId);
        CMDRequest cr = CMDRequest.obtain(REQ_GET_LANDMARK_LIST, listener);
        ParamGetLandMarkList param = ParamGetLandMarkList.newBuilder().setMapId(mapId).build();
        cr.setParam(param);
        send(cr);
    }

    @Override
    public void modifyLandmarkInfo(String mapId, String landMarkId, LandMarkBean newLandMark, ResponseListener listener) {
        Log.d(TAG, "modifyLandmarkInfo mapId = " + mapId + ", landMarkId = " + landMarkId);
        CMDRequest cr = CMDRequest.obtain(REQ_MODIFY_LANDMARK_INFO, listener);

        PoseBeanProto pose = PoseBeanProto.newBuilder().setX(newLandMark.getPoseInfo().getX())
                .setY(newLandMark.getPoseInfo().getY()).setTheta(newLandMark.getPoseInfo().getTheta())
                .build();
        LandMarkBeanProto landMark = LandMarkBeanProto.newBuilder().setName(newLandMark.getName())
                .setId(newLandMark.getId()).setPose(pose).build();
        ParamModifyLandmarkInfo param = ParamModifyLandmarkInfo.newBuilder().setMapId(mapId)
                .setLandMarkId(landMarkId).setNewLandMark(landMark).build();

        cr.setParam(param);
        send(cr);
    }

    @Override
    public void deleteLandMark(String mapId, String landMarkId, ResponseListener listener) {
        Log.d(TAG, "deleteLandMark mapId = " + mapId + ", landMarkId = " + landMarkId);
        CMDRequest cr = CMDRequest.obtain(REQ_DELETE_LANDMARK, listener);

        ParamDeleteLandMark param = ParamDeleteLandMark.newBuilder().setLandMarkId(landMarkId)
                .setMapId(mapId).build();

        cr.setParam(param);
        send(cr);
    }

    @Override
    public void addNewLandMark(String mapId, LandMarkBean newLandMark, ResponseListener listener) {
        Log.d(TAG, "addNewLandMark mapId = " + mapId);
        CMDRequest cr = CMDRequest.obtain(REQ_ADD_LANDMARK, listener);

        PoseBeanProto pose = PoseBeanProto.newBuilder().setX(newLandMark.getPoseInfo().getX())
                .setY(newLandMark.getPoseInfo().getY()).setTheta(newLandMark.getPoseInfo().getTheta())
                .build();
        LandMarkBeanProto landMark = LandMarkBeanProto.newBuilder().setName(newLandMark.getName())
                .setId(newLandMark.getId()).setPose(pose).build();
        ParamAddLandMark param = ParamAddLandMark.newBuilder().setMapId(mapId).setNewLandMark(landMark).build();

        cr.setParam(param);
        send(cr);
    }

    @Override
    public void getCurMapInfo(ResponseListener listener) {
        Log.d(TAG, "getCurMapInfo");
        CMDRequest cr = CMDRequest.obtain(REQ_GET_CUR_MAP_INFO, listener);
        send(cr);
    }

    @Override
    public void switchMap(String mapId, ResponseListener listener) {
        Log.d(TAG, "switchMap mapId = " + mapId);
        CMDRequest cr = CMDRequest.obtain(REQ_SWITCH_MAP, listener);
        ParamSwitchMap param = ParamSwitchMap.newBuilder()
                .setMapId(mapId).build();
        cr.setParam(param);
        send(cr);
    }

    @Override
    public void getRealTimeMap(ResponseListener listener) {
        Log.d(TAG, "getRealTimeMap");
        CMDRequest cr = CMDRequest.obtain(REQ_GET_REALTIME_MAP, listener);
        send(cr);
    }

    @Override
    public void getMapList(ResponseListener listener) {
        Log.d(TAG, "getMapList");
        CMDRequest cr = CMDRequest.obtain(REQ_GET_MAP_LIST, listener);
        send(cr);
    }

    @Override
    public void stopCreateMap(boolean save, ResponseListener listener) {
        Log.d(TAG, "stopCreateMap");
        CMDRequest cr = CMDRequest.obtain(REQ_STOP_CREATE_MAP, listener);
        ReqStopCreateMapCreator.ParamStopCreateMap param = ReqStopCreateMapCreator.ParamStopCreateMap
                .newBuilder().setSave(save).build();
        cr.setParam(param);
        send(cr);
    }

    @Override
    public void setRelocalization(PoseInfo pose, ResponseListener listener) {
        Log.d(TAG, "setRelocalization in");
        CMDRequest cr = CMDRequest.obtain(REQ_SET_RELOCALIZATION, listener);
        PoseBeanProto param = PoseBeanProto.newBuilder().setX(pose.getX()).setY(pose.getY())
                .setTheta(pose.getTheta()).build();
        cr.setParam(param);
        cr.setType(EXECUTION);
        send(cr);
    }

    @Override
    public void getRelocalizationState(ResponseListener listener) {
        Log.d(TAG, "getRelocalization in");
        CMDRequest cr = CMDRequest.obtain(REQ_GET_LOCALIZATION_STATE, listener);
        cr.setType(EXECUTION);
        send(cr);
    }

    @Override
    public void setNaviSpeedParam(NaviSpeedParam param, ResponseListener listener) {
        Log.d(TAG, "setNaviSpeedParam in");
        CMDRequest cr = CMDRequest.obtain(REQ_SET_NAVI_SPEED_PARAM, listener);
        cr.setType(EXECUTION);
        ReqSetNaviSpeedParamCreator.ParamSetNaviSpeed paramSpeed = ReqSetNaviSpeedParamCreator.ParamSetNaviSpeed.newBuilder()
                .setVX(param.getvX()).setVTheta(param.getvTheta())
                .setAX(param.getaX()).setATheta(param.getaTheta()).build();

        cr.setParam(paramSpeed);
        send(cr);
    }

    public void releaseAndRemoveCr(CMDRequest cr) {
        if (cr != null) {
            cr.release();
            removeRequestFromList(cr.mReqId);
        }
    }

    public CMDRequest findRequestFromList(int reqId) {
        CMDRequest cr = null;
        synchronized (mRequestList) {
            cr = mRequestList.get(reqId);
        }

        return cr;
    }

    private void removeRequestFromList(int reqId) {
        CMDRequest cr = null;
        synchronized (mRequestList) {
            cr = mRequestList.get(reqId);
            if (cr != null) {
                mRequestList.remove(reqId);
            }
        }
    }

    private void addToRequestList(CMDRequest cr) {
        synchronized (mRequestList) {
            mRequestList.append(cr.mReqId, cr);
        }
    }

    private void addToCmdReqIdMap(CMDRequest cr) {
        if (cr != null) {
            if (cr.getType() == EXECUTION) {
                cmdReqIdMap.put(cr.cmdName, cr.mReqId);
            }
        }
    }

    public void removeCmdReqIdMap(CMDRequest cr) {
        if (cr != null) {
            if (cr.getType() == EXECUTION) {
                cmdReqIdMap.remove(cr.cmdName);
            }
        }
    }

    private void send(CMDRequest cr) {
        if (cr == null) {
            Log.d(TAG, "send error cr is null");
            return;
        }
        if (connector != null) {
            //generate requestHeader from CMDRequest
            ReqHeadProto reqHead = ReqHeadProto.newBuilder().setCommand(cr.cmdName).setReqId(cr.mReqId)
                    .setType(cr.type).setMsg(cr.msg).setTimeStamp(cr.timeStamp).build();
            //trans Message param to ByteString
            ByteString paramBs;
            ReqPacketProto reqPacketProto;
            // if no param，do not set null， will throw NullPointerException
            if (cr.param != null) {
                paramBs = cr.param.toByteString();
                reqPacketProto = ReqPacketProto.newBuilder().setHeader(reqHead)
                        .setParam(paramBs).build();
            } else {
                reqPacketProto = ReqPacketProto.newBuilder().setHeader(reqHead).build();
            }
            boolean sendStatus = connector.request(reqPacketProto);
            Log.d(TAG, "cmdName = " + cr.cmdName + ", cmdReqId = " + cr.mReqId + " ,sendStatus = " + sendStatus);

            if (sendStatus) {
                //add request to requestList
                addToRequestList(cr);
                addToCmdReqIdMap(cr);//保存执行的任务ID
            }
        } else {
            Log.e(TAG, "Connector is not available");
            cr.release();
        }
    }
}

class CMDRequest {
    static final String LOG_TAG = "ProtoRequest";

    //***** Class Variables
    static Random sRandom = new Random();
    static AtomicInteger sNextReqId = new AtomicInteger(1);
    private static Object sPoolSync = new Object();
    private static CMDRequest sPool = null;
    private static int sPoolSize = 0;
    private static final int MAX_POOL_SIZE = 4;

    //***** Instance Variables
    String cmdName;//can not be changed
    int mReqId;//can not be changed
    int pairedReqId;//cancel TASK paired with a executing task reqId
    int type;//can be changed if needed
    String msg = "";//can be changed if needed
    long timeStamp;
    Message param;

    CmdStandardApi.ResponseListener mResponseListener;
    CMDRequest mNext;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Message getParam() {
        return param;
    }

    public void setParam(Message param) {
        this.param = param;
    }

    public CmdStandardApi.ResponseListener getResponseListener() {
        return mResponseListener;
    }

    public void setResponseListener(CmdStandardApi.ResponseListener mResponseListener) {
        this.mResponseListener = mResponseListener;
    }

    public int getPairedReqId() {
        return pairedReqId;
    }

    public void setPairedReqId(int pairedReqId) {
        this.pairedReqId = pairedReqId;
    }

    /**
     * Retrieves a new CMDRequest instance from the pool.
     *
     * @param cmdName
     * @param listener  sent response
     * @return a CMDRequest instance from the pool.
     */
    static CMDRequest obtain(String cmdName, CmdStandardApi.ResponseListener listener) {
        CMDRequest cr = null;

        synchronized (sPoolSync) {
            if (sPool != null) {
                cr = sPool;
                sPool = cr.mNext;
                cr.mNext = null;
                sPoolSize--;
            }
        }

        if (cr == null) {
            cr = new CMDRequest();
        }

        cr.cmdName = cmdName;//set command string
        cr.mReqId = sNextReqId.getAndIncrement();//get mReqId
        while (cr.mReqId == 0) {
            //reqId不允许为0，0用作一个特殊值，标志没有reqId
            cr.mReqId = sNextReqId.getAndIncrement();
        }
        cr.pairedReqId = 0;
        cr.timeStamp = System.currentTimeMillis();//set timeStamp
        cr.mResponseListener = listener;//set responseListener
        if (listener == null) {
            throw new NullPointerException("response listener is null");
        }
        return cr;
    }


    /**
     * Returns a RILRequest instance to the pool.
     *
     * Note: This should only be called once per use.
     */
    void release() {
        synchronized (sPoolSync) {
            if (sPoolSize < MAX_POOL_SIZE) {
                mNext = sPool;
                sPool = this;
                sPoolSize++;
                mResponseListener = null;
                param = null;
                msg = "";
            }
        }
    }

    private CMDRequest() {
    }

    static void
    resetSerial() {
        // use a random so that on recovery we probably don't mix old requests
        // with new.
        sNextReqId.set(sRandom.nextInt());
    }

    String
    serialString() {
        StringBuilder sb = new StringBuilder(8);
        String sn;

        long adjustedSerial = (((long)mReqId) - Integer.MIN_VALUE)%10000;

        sn = Long.toString(adjustedSerial);

        sb.append('[');
        for (int i = 0, s = sn.length() ; i < 4 - s; i++) {
            sb.append('0');
        }

        sb.append(sn);
        sb.append(']');
        return sb.toString();
    }

    @Override
    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append("cmdName = ");
        sb.append(this.cmdName);
        sb.append("\nmReqId = ");
        sb.append(this.mReqId);
        sb.append("\ntype = ");
        sb.append(this.type);
        return super.toString();
    }
}
