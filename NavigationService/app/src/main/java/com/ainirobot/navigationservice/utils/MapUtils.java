package com.ainirobot.navigationservice.utils;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.base.analytics.utils.Md5Util;
import com.ainirobot.navigationservice.beans.waiter.BasePoseBean;
import com.ainirobot.navigationservice.beans.waiter.MapAreaJsonData;
import com.ainirobot.navigationservice.beans.waiter.MapRuleAreasData;
import com.ainirobot.navigationservice.beans.waiter.RemoteTargetsBean;
import com.ainirobot.navigationservice.beans.waiter.RoadGraph;
import com.ainirobot.navigationservice.beans.waiter.RoadGraphEdge;
import com.ainirobot.navigationservice.beans.waiter.RoadGraphNode;
import com.ainirobot.navigationservice.beans.waiter.Targets;
import com.ainirobot.navigationservice.beans.waiter.TargetsErrorInfo;
import com.ainirobot.navigationservice.beans.waiter.TargetsSafeZone;
import com.ainirobot.navigationservice.beans.waiter.Vector2d;
import com.ainirobot.navigationservice.commonModule.bi.BiManager;
import com.ainirobot.navigationservice.db.NavigationDataManager;
import com.ainirobot.navigationservice.db.entity.MapInfo;
import com.ainirobot.navigationservice.roversdkhelper.MapFileHelper;
import com.ainirobot.navigationservice.roversdkhelper.mappackage.MapFileDes;
import com.ainirobot.navigationservice.roversdkhelper.mappackage.MapPkgDes;
import com.ainirobot.navigationservice.roversdkhelper.mappackage.MapPkgHelper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONTokener;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.List;

import ninjia.android.proto.ChassisDataProtoWrapper;
import ninjia.android.proto.MappingTrackProtoWrapper;
import ninjia.android.proto.RoadGraphProtoWrapper;
import ninjia.android.proto.TargetsDataProtoWrapper;
import ninjia.android.proto.Vector2dProtoWrapper;

public class MapUtils {

    private static final String TAG = MapUtils.class.getSimpleName();

    private static final String MAP_NAME = "mapName";
    private static final String MAP_TARGET_NUMBER = "mapTargetNum";
    private static final String MAP_HAS_ROAD_GRAPH = "hasRoadGraph";

    public enum MapAreaDataState {
        MAP_NAME_ERROR,
        UNKNOWN_ERROR,
        Map_AREA_JSON_EMPTY,
        GENERATE_NEW_TARGETS_DATA_FAIL,
        SAVE_SUCCESS
    }

    public enum RoadDataState {
        MAP_NAME_ERROR,
        UNKNOWN_ERROR,
        ROAD_JSON_EMPTY,
        GENERATE_NEW_TARGETS_DATA_FAIL,
        SAVE_SUCCESS
    }

    public enum TargetState {
        MAP_NAME_ERROR,
        UNKNOWN_ERROR,
        TARGET_JSON_EMPTY,
        GENERATE_NEW_TARGETS_DATA_FAIL,
        SAVE_SUCCESS
    }

    /**
     * 获取 mapinfo.json 信息
     */
    public static MapInfo getMapInfoFromJsonFile(String mapName) {
        Log.d(TAG, "getMapInfoFromJsonFile: " + mapName);
        try {
            MapInfo mapInfo = loadLocalMapInfo(mapName);
            if (mapInfo != null) {
                Log.d(TAG, "getMapInfoFromJsonFile: mapInfo=" + mapInfo);
                return mapInfo;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static MapInfo loadLocalMapInfo(String mapName) {
        File mapInfoFile = MapFileHelper.getMapInfoJsonFile(mapName);//loadLocalMapInfo
        if (!mapInfoFile.exists() || mapInfoFile.length() <= 0) {
            return null;
        }
        String mapInfoJson = FileUtils.loadFileData2String(mapInfoFile);
        if (TextUtils.isEmpty(mapInfoJson)) {
            return null;
        }
        MapInfo mapInfoBean = null;
        Gson mGson = new Gson();
        try {
            JSONObject jsonObject = new JSONObject(mapInfoJson);
            String info = jsonObject.optString("mapInfo");
            mapInfoBean = mGson.fromJson(info, MapInfo.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return mapInfoBean;

    }

    public static boolean isHasTargetsInfo(String mapName) {
        Log.d(TAG, "isHasTargetsInfo: " + mapName);
        try {
            RemoteTargetsBean targetsBean = loadMapTargetInfo(mapName);
            if (targetsBean != null) {
                if (!targetsBean.getTargetsData().isEmpty()) {
                    return true;
                }

                if (!targetsBean.getTargetsErrorData().isEmpty()) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static RemoteTargetsBean loadMapTargetInfo(String mapName) {
        File targetFile = MapFileHelper.getTargetsJsonFile(mapName);
        if (!targetFile.exists() || targetFile.length() <= 0) {
            return null;
        }
        String targetJson = FileUtils.loadFileData2String(targetFile);
        if (TextUtils.isEmpty(targetJson)) {
            return null;
        }
        RemoteTargetsBean targetsBean = null;
        Gson mGson = new Gson();
        try {
            // WARNING: 2021/1/7 注意: 机器人target数据存在两种版本，一种无异常信息的，是JSONArray，另一种是JSONObject
            Object json = new JSONTokener(targetJson).nextValue();
            if (json instanceof JSONObject) {
                targetsBean = mGson.fromJson(targetJson, RemoteTargetsBean.class);
            } else if (json instanceof JSONArray) {
                ArrayList<Targets> targetList = mGson.fromJson(targetJson,
                        new TypeToken<ArrayList<Targets>>() {
                        }.getType());
                targetsBean = new RemoteTargetsBean();
                targetsBean.setTargetsData(targetList);
                targetsBean.setTargetsErrorData(null);
            } else {

            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return targetsBean;

    }

    /**
     * 转存跑堂二维码地图的target数据，如果targets.data文件不存在则视为成功。
     *
     * @param mapName 需要转存的地图名称
     * @return 转存target.json数据是否成功 1表示成功
     */
    public static int transferTargetsData2Json(String mapName) {
        try {
            if (TextUtils.isEmpty(mapName)) {
                Log.d(TAG, "transferTargetsData2Json: Map name is empty!");
                return 0;
            }
            File targetsDataFile = MapFileHelper.getTargetsData(mapName);//transferTargetsData2Json
            if (!targetsDataFile.exists()) {
                Log.d(TAG, "transferTargetsData2Json: Targets data is not exist!");
                return 0;
            }
            InputStream in = new FileInputStream(targetsDataFile);
            Log.d(TAG, "transferTargetsData2Json: available=" + in.available());
            in.skip(4);
            TargetsDataProtoWrapper.TargetsDataProto data = TargetsDataProtoWrapper.TargetsDataProto.parseFrom(in);
            Log.d(TAG, "transferTargetsData2Json: data=" + data.toString());

            ArrayList<TargetsErrorInfo> errorList = new ArrayList<>();
            int repeatErrorCnt = 0;
            ArrayList<Integer> errorIdList = new ArrayList<>();
            for (TargetsDataProtoWrapper.TargetErrorInfoProto errorInfo : data.getTargetsErrorList()) {
                TargetsErrorInfo oneError = new TargetsErrorInfo(errorInfo.getId(),
                        errorInfo.getErrorCodeValue(),
                        errorInfo.getErrorMsg());
                errorList.add(oneError);
                if (errorInfo.getErrorCodeValue() == TargetsDataProtoWrapper.TargetErrorInfoProto.Code.kReprojectionErrorLarge_VALUE
                        || errorInfo.getErrorCodeValue() == TargetsDataProtoWrapper.TargetErrorInfoProto.Code.kTiltAngleLarge_VALUE) {
                    if (errorIdList.isEmpty() || !errorIdList.contains(oneError.getId())) {
                        errorIdList.add(oneError.getId());
                    }
                } else if (errorInfo.getErrorCodeValue() == TargetsDataProtoWrapper.TargetErrorInfoProto.Code.kTagRepeated_VALUE) {
                    if (errorIdList.isEmpty() || !errorIdList.contains(oneError.getId())) {
                        repeatErrorCnt++;
                        errorIdList.add(oneError.getId());
                    }
                } else {

                }
            }

            //targetList是所有的点位信息（不包括重复点位）
            int normalTargetCnt = 0;
            ArrayList<Targets> targetList = new ArrayList<>();
            for (TargetsDataProtoWrapper.TargetDataProto targetData : data.getTargetsList()) {
                Targets oneTarget = new Targets(targetData.getId(),
                        targetData.getX(),
                        targetData.getY(),
                        targetData.getZ(),
                        targetData.hasRange() ? targetData.getRange().getVerticalRadius() : 0,
                        targetData.hasRange() ? targetData.getRange().getHorizontalRadius() : 0,
                        targetData.hasRange() ? targetData.getRange().getDiagonalRadius() : 0);
                targetList.add(oneTarget);
                if (errorIdList.isEmpty()) {
                    normalTargetCnt++;
                    continue;
                }
                if (!errorIdList.contains(targetData.getId())) {
                    normalTargetCnt++;
                }
            }

            //safeList是所有的点位信息（不包括重复点位）
            ArrayList<TargetsSafeZone> safeList = new ArrayList<>();
            for (TargetsDataProtoWrapper.DangerZoneTargetProto safeData : data.getDangerZoneTargetsList()) {
                TargetsSafeZone oneTarget = new TargetsSafeZone(safeData.getId(), safeData.getEnableDistance());
                safeList.add(oneTarget);
            }

            if (targetList.isEmpty() && errorList.isEmpty()) {
                Log.d(TAG, "transferTargetsData2Json: Current map target info is empty!");
                return 0;
            }
            RemoteTargetsBean targetsBean = new RemoteTargetsBean(1, targetList, errorList, safeList);
            Gson gson = new Gson();
            File file = createTargetJsonFile(mapName);
            saveJsonData(file, gson.toJson(targetsBean));//transferTargetsData2Json
            BiManager.getInstance().reportCreateMapResult(mapName,
                    0,
                    normalTargetCnt + repeatErrorCnt,
                    normalTargetCnt,
                    errorIdList.size(),
                    gson.toJson(errorList),
                    gson.toJson(targetList));
            Log.d(TAG, "transferTargetsData2Json: " +
                    " dataInfo targetsCnt:" + data.getTargetsCount() +
                    " errorCnt:" + data.getTargetsErrorCount() +
                    " safeCnt" + data.getDangerZoneTargetsCount());
        } catch (Exception e) {
            Log.d(TAG, "transferTargetsData2Json:Exception: e=" + e.getMessage());
            e.printStackTrace();
            return 0;
        }
        return 1;
    }

    /**
     * 转存建图路径的mapping_track数据，mapping_track.data文件不存在则视为成功。
     *
     * @param mapName 需要转存的地图名称
     * @return 转存mapping_track.json数据是否成功 1表示成功
     */
    public static int transferTrackData2Json(String mapName) {
        try {
            if (TextUtils.isEmpty(mapName)) {
                Log.d(TAG, "transferTrackData2Json: Map name is empty");
                return 0;
            }
            File trackDataFile = MapFileHelper.getTrackData(mapName);//transferTrackData2Json
            if (!trackDataFile.exists()) {
                Log.d(TAG, "transferTrackData2Json: Track data is not exist");
                return 0;
            }
            InputStream in = new FileInputStream(trackDataFile);
            in.skip(4);
            MappingTrackProtoWrapper.MappingTrackProto data = MappingTrackProtoWrapper.MappingTrackProto.parseFrom(in);

            ArrayList<BasePoseBean> trackList = new ArrayList<>();
            for (MappingTrackProtoWrapper.PoseProto trackData : data.getPosesList()) {
                BasePoseBean pose = new BasePoseBean(trackData.getPosition().getX(), trackData.getPosition().getY(), trackData.getTheta());
                trackList.add(pose);
            }

            if (trackList.isEmpty()) {
                Log.d(TAG, "transferTrackData2Json: Current map mapping track is empty!");
                return 0;
            }
            Gson gson = new Gson();
            File file = createTrackJsonFile(mapName);
            saveJsonData(file, gson.toJson(trackList));//transferTrackData2Json
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return 1;
    }

    /**
     * 保存本地闸机线数据
     * 1.删除旧 turnstile.json
     * 2.更新 turnstile.json
     *
     * */
    public static boolean saveLocalGateData(String mapName, String gateJson){
        if (TextUtils.isEmpty(mapName) || TextUtils.isEmpty(gateJson)) {
            Log.d(TAG, "saveLocalGateData: Params error!");
            return false;
        }
        Log.d(TAG, "saveLocalGateData: mapName=" + mapName + " gateJson=" + gateJson);
        //删除旧 turnstile.json
        File roadFile = MapFileHelper.getGateJsonFile(mapName);//saveLocalRoadData
        if (roadFile.exists()) {
            roadFile.delete();
        }
        //更新 turnstile.json
        String roadJsonPath = MapFileHelper.getGateJsonPath(mapName);//saveLocalRoadData
        File gateFileNew = new File(roadJsonPath);
        try {
            FileOutputStream fos = new FileOutputStream(gateFileNew);
            fos.write(gateJson.getBytes());
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return true;
    }


    /**
     * 保存本地巡线数据
     * 1. 删除旧 road.json
     * 2. 更新 road.json
     * 3. 更新 road_graph.data
     * 4. 并重新打包 data.zip
     */
    public static boolean saveLocalRoadData(String mapName, String roadJson) {
        if (TextUtils.isEmpty(mapName) || TextUtils.isEmpty(roadJson)) {
            Log.d(TAG, "saveLocalRoadData: Params error!");
            return false;
        }
        Log.d(TAG, "saveLocalRoadData: mapName=" + mapName + " roadJson=" + roadJson);
        //删除旧 road.json
        File roadFile = MapFileHelper.getRoadJsonFile(mapName);//saveLocalRoadData
        if (roadFile.exists()) {
            roadFile.delete();
        }
        //更新 road.json
        String roadJsonPath = MapFileHelper.getRoadJsonPath(mapName);//saveLocalRoadData
        File roadFileNew = new File(roadJsonPath);
        try {
            FileOutputStream fos = new FileOutputStream(roadFileNew);
            fos.write(roadJson.getBytes());
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        //更新 road_graph.data，并重新打包 data.zip
        RoadDataState roadDataState = transferRoadJson2Data(mapName);//saveLocalRoadData
        Log.d(TAG, "saveLocalRoadData: roadDataState=" + roadDataState);
        return RoadDataState.SAVE_SUCCESS == roadDataState
                || RoadDataState.ROAD_JSON_EMPTY == roadDataState;
    }

    /**
     * 转存路线信息到机器人地图文件内，如果没有road.json则表示该地图无需生成road_graph.data数据
     */
    public static RoadDataState transferRoadJson2Data(String mapName) {
        try {
            if (TextUtils.isEmpty(mapName)) {
                Log.d(TAG, "transferRoadJson2Data: mapName is empty");
                return RoadDataState.MAP_NAME_ERROR;
            }
            File roadJsonFile = MapFileHelper.getRoadJsonFile(mapName);//transferRoadJson2Data
            if (!roadJsonFile.exists()) {
                Log.d(TAG, "transferRoadJson2Data: targetJsonFile not exists!");
                return RoadDataState.ROAD_JSON_EMPTY;
            }
            //读取地图文件夹内road信息
            String roadJson = FileUtils.loadFileData2String(roadJsonFile);
            //将road.json解析成地图所需RoadGraphProto数据
            RoadGraphProtoWrapper.RoadGraphProto roadGraphProto = generateRoadGraphData(roadJson, mapName);
            if (roadGraphProto == null) {
                Log.d(TAG, "transferRoadJson2Data: targetJson empty!");
                return RoadDataState.ROAD_JSON_EMPTY;
            }

            //生成临时的road_graph.data到地图根目录
            boolean tempRoadData = generateTempRoadData(roadGraphProto, mapName);
            if (!tempRoadData) {
                Log.d(TAG, "transferRoadJson2Data: Generate temp road data fail!");
                return RoadDataState.GENERATE_NEW_TARGETS_DATA_FAIL;
            }
            //替换旧的road_graph.data
            File tempRoadDataFile = MapFileHelper.getRoadDataTempFile(mapName);//transferRoadJson2Data
            File roadDataFile = MapFileHelper.getRoadData(mapName);//transferRoadJson2Data
            boolean renameResult = FileUtils.renameFileTo(tempRoadDataFile, roadDataFile);
            if (!renameResult) {
                Log.d(TAG, "transferRoadJson2Data: Rename failed!");
                return RoadDataState.UNKNOWN_ERROR;
            }
            Log.d(TAG, "transferRoadJson2Data: Success:" + mapName);
        } catch (Exception e) {
            e.printStackTrace();
            return RoadDataState.UNKNOWN_ERROR;
        }
        return RoadDataState.SAVE_SUCCESS;
    }

    private static boolean generateTempRoadData(RoadGraphProtoWrapper.RoadGraphProto roadGraphProto, String mapName)
            throws IOException {
        if (roadGraphProto == null) {
            Log.d(TAG, "generateTempRoadData: Road data is null!");
            return false;
        }
        File tempRoadDataFile = MapFileHelper.getRoadDataTempFile(mapName);//generateTempRoadData
        if (tempRoadDataFile.exists()) {
            tempRoadDataFile.delete();
        }
        FileOutputStream dataFos = new FileOutputStream(tempRoadDataFile);
        byte[] roadDataByte = roadGraphProto.toByteArray();
        byte[] dataLength = convertLongToBytes(roadDataByte.length);
        dataFos.write(dataLength);
        dataFos.write(roadDataByte);
        dataFos.close();
        Log.d(TAG, "generateTempRoadData: suc:" + mapName);
        return true;
    }

    private static boolean generateTempMapAreaData(ChassisDataProtoWrapper.MapRuleAreasProto mapRuleAreasProto, String mapName)
            throws IOException {
        if (mapRuleAreasProto == null) {
            Log.d(TAG, " generateTempMapAreaData:: map area data is null!");
            return false;
        }
        File tempRoadDataFile = MapFileHelper.getMapAreaDataTempFile(mapName);
        if (tempRoadDataFile.exists()) {
            tempRoadDataFile.delete();
        }
        FileOutputStream dataFos = new FileOutputStream(tempRoadDataFile);
        byte[] roadDataByte = mapRuleAreasProto.toByteArray();
        byte[] dataLength = convertLongToBytes(roadDataByte.length);
        dataFos.write(dataLength);
        dataFos.write(roadDataByte);
        dataFos.close();
        Log.d(TAG, " generateTempMapAreaData: suc:" + mapName);
        return true;
    }

    /**
     * 将road.json转成对应的protobuf数据
     *
     * @param roadJson road.json对应的数据
     * @return
     */
    private static RoadGraphProtoWrapper.RoadGraphProto generateRoadGraphData(String roadJson, String mapName) {
        if (TextUtils.isEmpty(roadJson)) {
            Log.d(TAG, "generateRoadGraphData: local road json file is empty");
            return null;
        }
        Gson gson = new Gson();
        RoadGraph roadGraph = gson.fromJson(roadJson, RoadGraph.class);
        Log.d(TAG, "generateRoadGraphData: road graph info :" + roadGraph.toString());
        if (roadGraph == null || (roadGraph.getEdges().isEmpty() && roadGraph.getNodes().isEmpty())) {
            Log.d(TAG, "generateRoadGraphData: road graph info is null");
            return null;
        }
        RoadGraphProtoWrapper.RoadGraphProto.Builder builder = RoadGraphProtoWrapper.RoadGraphProto.newBuilder();
        for (int i = 0; i < roadGraph.getEdges().size(); i++) {
            RoadGraphEdge roadGraphEdge = roadGraph.getEdges().get(i);
            RoadGraphProtoWrapper.RoadGraphEdgeProto.Builder edgeBuilder = RoadGraphProtoWrapper.RoadGraphEdgeProto.newBuilder();
            edgeBuilder.setId(roadGraphEdge.getId());
            RoadGraphProtoWrapper.RoadGraphEdgeProto.Rule rule;
            switch (roadGraphEdge.getRule()) {
                case 0:
                    rule = RoadGraphProtoWrapper.RoadGraphEdgeProto.Rule.kDefault;
                    break;
                case 1:
                    rule = RoadGraphProtoWrapper.RoadGraphEdgeProto.Rule.kOnlyForward;
                    break;
                case 2:
                    rule = RoadGraphProtoWrapper.RoadGraphEdgeProto.Rule.kOnlyReverse;
                    break;
                case 3:
                    rule = RoadGraphProtoWrapper.RoadGraphEdgeProto.Rule.kMultiPass;
                    break;
                default:
                    rule = RoadGraphProtoWrapper.RoadGraphEdgeProto.Rule.kDefault;
                    break;
            }
            edgeBuilder.setRule(rule);
            edgeBuilder.setNodeStartId(roadGraphEdge.getNode_start_id());
            edgeBuilder.setNodeEndId(roadGraphEdge.getNode_end_id());
            edgeBuilder.setRoadWidth(roadGraphEdge.getLine_width());
            edgeBuilder.setSpeedLimit(roadGraphEdge.getLinear_speed_limit());
            edgeBuilder.setRetrogradeCost(roadGraphEdge.getRetrograde_cost());
            edgeBuilder.setParkingCost(roadGraphEdge.getParking_cost());
            edgeBuilder.setSceneType(roadGraphEdge.getScene_type());
            builder.addEdges(edgeBuilder);
        }
        for (int j = 0; j < roadGraph.getNodes().size(); j++) {
            RoadGraphNode node = roadGraph.getNodes().get(j);
            Vector2d position = node.getPosition();
            RoadGraphProtoWrapper.RoadGraphNodeProto.Builder nodeBuilder = RoadGraphProtoWrapper.RoadGraphNodeProto.newBuilder();
            nodeBuilder.setId(node.getId());
            Vector2dProtoWrapper.Vector2dProto.Builder positionBuilder = Vector2dProtoWrapper.Vector2dProto.newBuilder();
            positionBuilder.setX(position.getX());
            positionBuilder.setY(position.getY());
            nodeBuilder.setPosition(positionBuilder);
            builder.addNodes(nodeBuilder);
        }

        // 设置机器人最大宽度
        builder.setMaxRobotDiameter(NavigationDataManager.getInstance().getMapByName(mapName).getTransit_max_width());

        RoadGraphProtoWrapper.RoadGraphProto roadGraphProto = builder.build();
        Log.d(TAG, "generateRoadGraphData: create road graph data:" + roadGraphProto);
        return roadGraphProto;
    }

    /**
     * 将map_area.json转成对应的protobuf数据
     * @param mapName
     * @return
     */
    public static MapAreaDataState transferMapAreaJson2Data(String mapName) {
        try {
            if (TextUtils.isEmpty(mapName)) {
                Log.d(TAG, " transferMapAreaJson2Data:: mapName is empty");
                return MapAreaDataState.MAP_NAME_ERROR;
            }
            File mapAreaJsonFile = MapFileHelper.getMapAreaJsonFile(mapName);
            if (!mapAreaJsonFile.exists()) {
                Log.d(TAG, " transferMapAreaJson2Data:: mapAreaJsonFile not exists!");
                return MapAreaDataState.Map_AREA_JSON_EMPTY;
            }

            //读取地图文件夹内mapArea信息
            String mapAreaJson = FileUtils.loadFileData2String(mapAreaJsonFile);
            //map_area.json解析成地图所需MapRuleAreasProto数据
            ChassisDataProtoWrapper.MapRuleAreasProto mapRuleAreasProto = generateMapAreaProtoData(mapAreaJson);
            if (mapRuleAreasProto == null) {
                Log.d(TAG, " transferMapAreaJson2Data:: mapAreaJsonFile empty! Delete map_area.data file!");
                //删除旧的map_area.data，因为map_area.json为空
                File mapAreaDataFile = MapFileHelper.getMapAreaData(mapName);
                if (mapAreaDataFile.exists()) {
                    mapAreaDataFile.delete();
                }
                return MapAreaDataState.Map_AREA_JSON_EMPTY;
            }
            //生成临时的map_area.data到地图根目录
            boolean tempRoadData = generateTempMapAreaData(mapRuleAreasProto, mapName);
            if (!tempRoadData) {
                Log.d(TAG, "transferRoadJson2Data: Generate temp road data fail!");
                return MapAreaDataState.GENERATE_NEW_TARGETS_DATA_FAIL;
            }
            //替换旧的map_area.data
            File tempMapAreaDataFile = MapFileHelper.getMapAreaDataTempFile(mapName);
            File roadDataFile = MapFileHelper.getMapAreaData(mapName);
            boolean renameResult = FileUtils.renameFileTo(tempMapAreaDataFile, roadDataFile);
            if (!renameResult) {
                Log.d(TAG, " transferMapAreaJson2Data:: Rename failed!");
                return MapAreaDataState.UNKNOWN_ERROR;
            }
            Log.d(TAG, " transferMapAreaJson2Data:: Success:" + mapName);

        }catch (Exception e){
            e.printStackTrace();
            return MapAreaDataState.UNKNOWN_ERROR;
        }

        return MapAreaDataState.SAVE_SUCCESS;
    }

    /**
     * map_area.json数据转成对应的protobuf数据
     * @param mapAreaJson
     * @return
     */
    private static ChassisDataProtoWrapper.MapRuleAreasProto generateMapAreaProtoData(String mapAreaJson){
        Log.d(TAG, " generateMapAreaProtoData:: mapAreaJson：" + mapAreaJson);
        if (TextUtils.isEmpty(mapAreaJson)) {
            Log.d(TAG, " generateMapAreaProtoData:: local road json file is empty");
            return null;
        }
        Gson gson = new Gson();
        MapAreaJsonData mapAreaJsonData = gson.fromJson(mapAreaJson, MapAreaJsonData.class);
        Log.d(TAG, " generateMapAreaProtoData:: mapAreaJsonData: " + mapAreaJsonData.toString());
        if (mapAreaJsonData == null || (mapAreaJsonData.getAreas().isEmpty())) {
            Log.d(TAG, " generateMapAreaProtoData:: rgbdConfigData areas info is null");
            return null;
        }
        ChassisDataProtoWrapper.MapRuleAreasProto.Builder builder = ChassisDataProtoWrapper.MapRuleAreasProto.newBuilder();
        for (int i=0; i<mapAreaJsonData.getAreas().size(); i++){

            MapRuleAreasData areas = mapAreaJsonData.getAreas().get(i);

            ChassisDataProtoWrapper.MapRuleProto.Builder ruleBuilder = ChassisDataProtoWrapper.MapRuleProto.newBuilder();
            int downLevel = getLevel(areas.getDown_level());
            Log.d(TAG, " generateMapAreaProtoData:: 转化前Down_level = " + areas.getDown_level() + " 转化后downLevel = " + downLevel);
            ruleBuilder.setPoints3DDownLevel(ChassisDataProtoWrapper.Obs3dFilterLevelProto.Level.forNumber(downLevel));
            int upLevel = getLevel(areas.getUp_level());
            Log.d(TAG, " generateMapAreaProtoData:: 转化前Up_level = " + areas.getUp_level() + " 转化后upLevel = " + upLevel);
            ruleBuilder.setPoints3DUpLevel(ChassisDataProtoWrapper.Obs3dFilterLevelProto.Level.forNumber(upLevel));
            Log.d(TAG, " generateMapAreaProtoData:: isForce_lock_wheel = " + areas.isForce_lock_wheel());
            ruleBuilder.setForceLockWheel(areas.isForce_lock_wheel());
            builder.addRules(ruleBuilder);

            ArrayList<Vector2d> pointsList = areas.getPoints();

            ChassisDataProtoWrapper.MapAreaProto.Builder areaBuilder = ChassisDataProtoWrapper.MapAreaProto.newBuilder();
            for (int j=0; j<pointsList.size(); j++){
                Vector2d pointsData = pointsList.get(j);
                Vector2dProtoWrapper.Vector2dProto.Builder pointBuilder = Vector2dProtoWrapper.Vector2dProto.newBuilder();
                pointBuilder.setX(pointsData.getX());
                pointBuilder.setY(pointsData.getY());
                areaBuilder.addPoints(pointBuilder);
            }
            builder.addAreas(areaBuilder);
        }

        ChassisDataProtoWrapper.MapRuleAreasProto mapRuleAreasProto = builder.build();
        Log.d(TAG, " generateMapAreaProtoData:: create map areas proto data: \n" + mapRuleAreasProto);
        return mapRuleAreasProto;
    }

    /**
     * 服务端下发的level转成底盘的level
     * @param level 下视等级 或者 上视等级
     * @return
     */
    public static int getLevel(int level){
        switch (level){
            case 0:
                return ChassisDataProtoWrapper.Obs3dFilterLevelProto.Level.kLevel_0.getNumber();
            case 1:
                return ChassisDataProtoWrapper.Obs3dFilterLevelProto.Level.kLevel_1.getNumber();
            case 2:
                return ChassisDataProtoWrapper.Obs3dFilterLevelProto.Level.kLevel_2.getNumber();
            case 3:
                return ChassisDataProtoWrapper.Obs3dFilterLevelProto.Level.kLevel_3.getNumber();
            case 4:
                return ChassisDataProtoWrapper.Obs3dFilterLevelProto.Level.kLevel_4.getNumber();
        }
        return ChassisDataProtoWrapper.Obs3dFilterLevelProto.Level.kUnknown.getNumber();
    }

    /**
     * 转存标识码信息到机器人地图文件内，如果没有target.json则表示该地图无需生成targets.data数据
     */
    public static TargetState transferTargetJson2Data(String mapName) {
        try {
            if (TextUtils.isEmpty(mapName)) {
                Log.d(TAG, "transferTargetJson2Data: mapName is empty");
                return TargetState.MAP_NAME_ERROR;
            }
            File targetJsonFile = MapFileHelper.getTargetsJsonFile(mapName);//transferTargetJson2Data
            if (!targetJsonFile.exists()) {
                Log.d(TAG, "transferTargetJson2Data: targetJsonFile not exists!");
                return TargetState.TARGET_JSON_EMPTY;
            }
            //读取地图文件夹内target信息
            String targetJson = FileUtils.loadFileData2String(targetJsonFile);
            //target.json解析成地图所需TargetsDataProto数据
            TargetsDataProtoWrapper.TargetsDataProto targetsDataProto =
                    generateTargetDataProto(targetJson);
            if (targetsDataProto == null) {
                Log.d(TAG, "transferTargetJson2Data: targetJson empty!");
                return TargetState.TARGET_JSON_EMPTY;
            }
            //生成临时的targets.data到地图根目录
            boolean newTargetsData = generateTempTargetsData(targetsDataProto, mapName);
            if (!newTargetsData) {
                Log.d(TAG, "transferTargetJson2Data: Generate new targets data fail!");
                return TargetState.GENERATE_NEW_TARGETS_DATA_FAIL;
            }
            //替换旧的targets.data
            File tempTargetsDataFile = MapFileHelper.getTargetsDataTempFile(mapName);//transferTargetJson2Data
            File targetsDataFile = MapFileHelper.getTargetsData(mapName);//transferTargetJson2Data
            boolean renameResult = FileUtils.renameFileTo(tempTargetsDataFile, targetsDataFile);//transferTargetJson2Data
            if (!renameResult) {
                Log.d(TAG, "transferTargetJson2Data: Rename failed!");
                return TargetState.UNKNOWN_ERROR;
            }
            Log.d(TAG, "transferTargetJson2Data: Success:" + mapName);
        } catch (Exception e) {
            e.printStackTrace();
            return TargetState.UNKNOWN_ERROR;
        }
        return TargetState.SAVE_SUCCESS;
    }

    /**
     * 将targe.json转成对应的protobuf数据
     *
     * @param targetJson target.json对应的数据
     * @return
     */
    private static TargetsDataProtoWrapper.TargetsDataProto generateTargetDataProto(String targetJson) {
        if (TextUtils.isEmpty(targetJson)) {
            Log.d(TAG, "generateTargetDataProto: local road json file is empty");
            return null;
        }
        Gson gson = new Gson();
        RemoteTargetsBean targetsBean = gson.fromJson(targetJson, RemoteTargetsBean.class);
        Log.d(TAG, "generateTargetDataProto: targets info :" + targetsBean.toString());

        TargetsDataProtoWrapper.TargetsDataProto.Builder builder = TargetsDataProtoWrapper.TargetsDataProto.newBuilder();
        if (targetsBean.getTargetsData() != null) {
            for (int i = 0; i < targetsBean.getTargetsData().size(); i++) {
                Targets target = targetsBean.getTargetsData().get(i);
                TargetsDataProtoWrapper.TargetDataProto.Builder targetBuilder = TargetsDataProtoWrapper.TargetDataProto.newBuilder();
                targetBuilder.setId(target.getId());
                targetBuilder.setX(target.getX());
                targetBuilder.setY(target.getY());
                targetBuilder.setZ(target.getZ());
                TargetsDataProtoWrapper.TargetDataProto.TargetDisplayRange.Builder rangeBuilder
                        = TargetsDataProtoWrapper.TargetDataProto.TargetDisplayRange.newBuilder();
                rangeBuilder.setVerticalRadius(target.getVerticalRadius());
                rangeBuilder.setHorizontalRadius(target.getHorizontalRadius());
                rangeBuilder.setDiagonalRadius(target.getDiagonalRadius());
                targetBuilder.setRange(rangeBuilder);
                builder.addTargets(targetBuilder);
            }
        } else {
            Log.e(TAG, "generateTargetDataProto: targetData is null");
        }
        if (targetsBean.getTargetsErrorData() != null) {
            for (int j = 0; j < targetsBean.getTargetsErrorData().size(); j++) {
                TargetsErrorInfo targetError = targetsBean.getTargetsErrorData().get(j);
                TargetsDataProtoWrapper.TargetErrorInfoProto.Builder errorBuilder = TargetsDataProtoWrapper.TargetErrorInfoProto.newBuilder();
                errorBuilder.setId(targetError.getId());
                errorBuilder.setErrorCodeValue(targetError.getErrorCode());
                errorBuilder.setErrorMsg(targetError.getErrorMsg());
                builder.addTargetsError(errorBuilder);
            }
        } else {
            Log.e(TAG, "generateTargetDataProto: targetsErrorData is null");
        }
        if (targetsBean.getTargetSafeData() != null) {
            for (int k = 0; k < targetsBean.getTargetSafeData().size(); k++) {
                TargetsSafeZone safeZone = targetsBean.getTargetSafeData().get(k);
                TargetsDataProtoWrapper.DangerZoneTargetProto.Builder dangerBuilder = TargetsDataProtoWrapper.DangerZoneTargetProto.newBuilder();
                safeZone.setId(safeZone.getId());
                safeZone.setEnableDistance(safeZone.getEnableDistance());
                builder.addDangerZoneTargets(dangerBuilder);
            }
        } else {
            Log.e(TAG, "generateTargetDataProto: targetSafeData is null");
        }
        TargetsDataProtoWrapper.TargetsDataProto targetsDataProto = builder.build();
        Log.d(TAG, "generateTargetDataProto: create targets data:" + targetsDataProto.toString());
        return targetsDataProto;
    }

    /**
     * 底盘在读取时先读取高位，所以需要反向存储
     * 底盘存储的文件大小长度为4字节，所以此处强制使用4字节存储数据。
     *
     * @param n 文件大小，单位字节
     * @return
     */
    private static byte[] convertLongToBytes(int n) {
        byte[] result = new byte[4];
        result[3] = (byte) ((n >> 24) & 0xFF);
        result[2] = (byte) ((n >> 16) & 0xFF);
        result[1] = (byte) ((n >> 8) & 0xFF);
        result[0] = (byte) (n & 0xFF);
        return result;
    }

    private static File createTargetJsonFile(String mapName) {
        return createNewFile(MapFileHelper.getTargetsJsonFile(mapName));
    }

    private static File createTrackJsonFile(String mapName) {
        return createNewFile(MapFileHelper.getTrackJsonFile(mapName));
    }

    private static void saveJsonData(File file, String jsonStr) throws IOException {
        if (!file.exists() || TextUtils.isEmpty(jsonStr)) {
            return;
        }
        Writer writer = null;
        try {
            FileOutputStream out = new FileOutputStream(file);
            writer = new OutputStreamWriter(out);
            writer.write(jsonStr);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (writer != null) {
                writer.flush();
                writer.close();
            }
        }
    }

    private static boolean generateTempTargetsData(TargetsDataProtoWrapper.TargetsDataProto targetsDataProto, String mapName)
            throws IOException {
        if (targetsDataProto == null) {
            Log.d(TAG, "generateTempTargetsData: Targets data is null!");
            return false;
        }
        File tempTargetDataFile = MapFileHelper.getTargetsDataTempFile(mapName);//generateTempTargetsData
        if (tempTargetDataFile.exists()) {
            tempTargetDataFile.delete();
        }
        FileOutputStream dataFos = new FileOutputStream(tempTargetDataFile);
        byte[] targetDataByte = targetsDataProto.toByteArray();
        byte[] dataLength = convertLongToBytes(targetDataByte.length);
        dataFos.write(dataLength);
        dataFos.write(targetDataByte);
        dataFos.close();
        Log.d(TAG, "generateTempTargetsData: suc:" + mapName);
        return true;
    }

    /**
     * 删除文件
     *
     * @param file      文件或文件夹
     * @param removeDir 是否移除文件夹
     */
    private static void deleteFile(File file, boolean removeDir) {
        if (file.exists() && file.isDirectory()) {
            File[] files = file.listFiles();
            for (int i = 0; i < files.length; i++) {
                File f = files[i];
                deleteFile(f, true);
            }
            if (removeDir) {
                file.delete();
            }
        } else if (file.exists()) {
            file.delete();
        } else {

        }
    }

    public static void saveMapInfoJson(String mapName, MapInfo mapInfo) {
        Log.d(TAG, "saveMapInfoJson: mapName=" + mapName +
                " mapInfo=" + (mapInfo != null ? mapInfo.toString() : "null"));
        Gson gson = new Gson();
        try {
            if (TextUtils.isEmpty(mapName) || mapInfo == null) {
                return;
            }
            File file = createMapInfoJsonFile(mapName);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("version", 1);
            jsonObject.put("mapInfo", gson.toJson(mapInfo));
            String jsonStr = jsonObject.toString();
            saveJsonData(file, jsonStr);//saveMapInfoJson
        } catch (Exception e) {
            Log.d(TAG, "saveMapInfoJson:Exception: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void saveMapConfig(String mapName) {
        if (TextUtils.isEmpty(mapName)) {
            return;
        }
        try {
            saveJsonData(createNewFile(MapFileHelper.getMapConfig(mapName)),
                    GsonUtil.toJson(getMapPkgConfig(mapName)));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static MapPkgDes getMapPkgConfig(String mapName) {
        MapPkgDes mapPkgDes = new MapPkgDes();

        MapPkgDes defaultMapPkg = MapPkgHelper.getDefaultMapPkg();
        mapPkgDes.setPgmZip(defaultMapPkg.getPgmZip());
        mapPkgDes.setDataZip(defaultMapPkg.getDataZip());
        mapPkgDes.setMapPkg(defaultMapPkg.getMapPkg());
        updateNeedMd5Config(mapName, mapPkgDes.getExtraFiles(), defaultMapPkg.getExtraFiles());

        return mapPkgDes;
    }

    private static void updateNeedMd5Config(String mapName, List<MapFileDes> itemConfig, List<MapFileDes> fileDes) {
        for (MapFileDes des : fileDes) {
            try {
                File file = new File(MapFileHelper.getMapFilePath(mapName), des.getRelPath());
                String fileMD5 = Md5Util.getFileMD5(file);
                if (TextUtils.isEmpty(fileMD5)) {
                    continue;
                }
                Log.d(TAG, "updateNeedMd5Config: fileMD5=" +fileMD5 + " file=" + file.getAbsolutePath());
                des.setMd5(fileMD5);
                itemConfig.add(des);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private static File createMapInfoJsonFile(String mapName) {
        return createNewFile(MapFileHelper.getMapInfoJsonFile(mapName));//createMapInfoJsonFile
    }

    private static File createNewFile(File file) {
        try {
            if (!file.exists()) {
                File dir = new File(file.getParent());
                if (!dir.exists()) {
                    dir.mkdirs();
                }
                file.createNewFile();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return file;
    }

    public static String getMapInfo(String mapName) {
        RemoteTargetsBean targetsBean = MapUtils.loadMapTargetInfo(mapName);
        int size = (targetsBean == null) ? 0 :
                (targetsBean.getTargetsData() == null ? 0 : targetsBean.getTargetsData().size());
        int hasRoadJson = new File(MapFileHelper.getRoadJsonPath(mapName)).exists() ? 1 : 0;//getMapInfo
        try {
            JSONObject obj = new JSONObject();
            obj.put(MAP_NAME, mapName);
            obj.put(MAP_TARGET_NUMBER, size);
            obj.put(MAP_HAS_ROAD_GRAPH, hasRoadJson);
            return obj.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 把 RoverMap 转化成共享内存数据，更新 map.pgm 文件
     */
    public static boolean saveRoverMapToPgm(FileInputStream fis, FileOutputStream fos) {
        Log.d(TAG, "saveRoverMapToPgm:");
        DataInputStream dataInputStream = null;
        DataOutputStream dataOutputStream = null;
        try {
            dataInputStream = new DataInputStream(fis);
            dataOutputStream = new DataOutputStream(fos);

            String magic = nextNonCommentLine(dataInputStream);
            Log.d(TAG, "saveRoverMapToPgm: magic=" + magic);
            if (!magic.equals("P5")) {
                throw new Exception("Unknown magic number: " + magic);
            }

            String widthHeight = nextNonCommentLine(dataInputStream);
            String[] tokens = widthHeight.split(" ");
            int width = Integer.parseInt(tokens[0]);
            int height = Integer.parseInt(tokens[1]);
            int size = width * height;

            String fileHeader = String.format("P5\n%d %d\n255\n", width, height);
            dataOutputStream.writeBytes(fileHeader);

            nextNonCommentLine(dataInputStream);

            byte[] pixelsByte = new byte[size];
            dataInputStream.read(pixelsByte, 0, size);
            dataOutputStream.write(pixelsByte);
            byte[] extra = new byte[16];
            dataInputStream.read(extra, 0, 16);
            dataOutputStream.write(extra);
            dataOutputStream.flush();
            Log.d(TAG, "saveRoverMapToPgm: Done!");
            return true;
        } catch (Exception e) {
            Log.d(TAG, "saveRoverMapToPgm:Exception: " + e.getMessage());
            e.printStackTrace();
        } finally {
            IOUtils.close(dataInputStream);
            IOUtils.close(dataOutputStream);
            IOUtils.close(fis);
            IOUtils.close(fos);
        }
        return false;
    }

    private static String nextNonCommentLine(DataInputStream dataInputStream) throws IOException {
        String s = nextAnyLine(dataInputStream);
        while (s.startsWith("#") || s.equals("")) {
            s = nextAnyLine(dataInputStream);
        }
        return s;
    }

    private static String nextAnyLine(DataInputStream dataInputStream) throws IOException {
        StringBuffer sb = new StringBuffer();
        byte b = 0;
        while (b != 10) // newline
        {
            b = dataInputStream.readByte();
            char c = (char) b;
            sb.append(c);
        }
        return sb.toString().trim();
    }

}
