package com.ainirobot.navigationservice.commonModule.data;


import static com.ainirobot.navigationservice.commonModule.data.utils.Constant.SYNC_STATE_UPLOAD;

import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.hardware.RobotCore;
import com.ainirobot.navigationservice.Defs.Def;
import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.commonModule.data.utils.UuidUtils;
import com.ainirobot.navigationservice.db.NavigationDataManager;
import com.ainirobot.navigationservice.db.entity.PlaceInfo;
import com.ainirobot.navigationservice.roversdkhelper.MapFileHelper;
import com.ainirobot.navigationservice.utils.GsonUtil;
import com.ainirobot.navigationservice.utils.IOUtils;
import com.ainirobot.navigationservice.utils.SpecialPlaceUtil;
import com.ainirobot.navigationservice.db.entity.MapInfo;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.io.IOException;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

/**
 * 底盘数据管理
 * <p>
 * 旧版本使用properties方式存取数据
 * 新版本使用数据库存取相关数据
 */
public class DataManager {
    private static final String TAG = Def.MAP_DB_PRE + DataManager.class.getSimpleName();
    private static final String CONFIG_DIR = "/robot/config";
    private static final String CONFIG_FILE = "navigation.properties";

    private static final String MD5 = "MD5";
    private static final String NAVI_MOTION_VALUE = "motionModeValue";
    private static final String TYPE_POSE = "pose_";
    private static final String TYPE_FORBID_LINE = "forbid_line_";
    private static final String TYPE_LANGUAGE = "language_";
    private static final String TRUE = "true";
    private static DataManager mInstance;
    private HashMap<String, List<String>> mSpecialPlace;
    private volatile boolean mInit = false;
    public static String DEFAULT_LANGUAGE = Locale.SIMPLIFIED_CHINESE.toString();

    private DataManager() {
        init();
    }

    public static synchronized DataManager getInstance() {
        if (mInstance == null) {
            mInstance = new DataManager();
        }
        return mInstance;
    }

    public void init() {
        Log.i(TAG, "init: ");
        if (mInit) {
            return;
        }
        mInit = true;
        mSpecialPlace = SpecialPlaceUtil.getInstance().getLangSpecialPlace();
    }

    /**
     * 禁行线存储在properties中的key
     */
    public synchronized String getTypeForbidLine(String mapName) {
        return TYPE_FORBID_LINE + mapName;
    }

    /**
     * 地图主语言存储在properties中的key
     */
    public synchronized String getTypeMapLanguage(String mapName) {
        return TYPE_LANGUAGE + mapName;
    }

    /**
     * 位置点前缀
     */
    private synchronized String getPoseType() {
        return TYPE_POSE + NavigationDataManager.getInstance().getMapName() + "_";
    }

    /**
     * 位置点前缀
     */
    public String getPoseTypeByName(String mapName) {
        return TYPE_POSE + mapName + "_";
    }

    //保存properties
    private boolean saveProperties(File file, Properties properties) {
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(file);
            properties.store(fos, "config");
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        } finally {
            IOUtils.close(fos);
        }

        return true;
    }

    /**
     * 解析是否有进行线标识
     */
    public boolean parseTypeForbidLineFromPlaceProp(String mapName) {
        Log.d(TAG, "parseTypeForbidLineFromPlaceProp: " + mapName);
        Properties placeProp = getPlaceProperties(mapName);
        boolean hasForbidLine = false;
        if (placeProp != null) {
            String typeForbidLine = getTypeForbidLine(mapName);
            String forbidLine = placeProp.getProperty(typeForbidLine);
            if (TRUE.equals(forbidLine)) {//只有true，才保存true；null和false均为false;
                hasForbidLine = true;
            }
        }
        return hasForbidLine;
    }

    /**
     * 解析地图语言
     */
    public String parseMapLanguage(MapInfo mapInfo) {
        String mapLanguage = "";
        Log.i(TAG, "parseMapLanguage: mapInfo=" + mapInfo.toString());
        //先从 mapinfo.json 中解析
        if (MapFileHelper.isMapInfoJsonExists(mapInfo.getMapName())) {
            mapLanguage = parseMapLanguageFromMapInfoJsonFile(mapInfo.getMapName());
            Log.i(TAG, "parseMapLanguage: From json: mapLanguage=" + mapLanguage);
        }
        //从 place.properties 中解析，兼容旧版本地图
        if (TextUtils.isEmpty(mapLanguage)) {
            Properties placeProp = getPlaceProperties(mapInfo.getMapName());
            Log.d(TAG, "parseMapLanguage: getProperties: start --->>>");
            printProperty(placeProp);
            Log.d(TAG, "parseMapLanguage: getProperties: end <<<---");
            if (placeProp != null) {
                String typeMapLanguage = getTypeMapLanguage(mapInfo.getMapName());
                Log.i(TAG, "parseMapLanguage: typeMapLanguage=" + typeMapLanguage);
                mapLanguage = placeProp.getProperty(typeMapLanguage);
                Log.i(TAG, "parseMapLanguage: From Properties: mapLanguage=" + mapLanguage);
            }
        }
        //使用“点位语言最多” 或 默认语言
        if (TextUtils.isEmpty(mapLanguage)) {
            mapLanguage = NavigationDataManager.getInstance().getMaxLanguage(mapInfo.getMapName());
            Log.i(TAG, "parseMapLanguage: getMaxLanguage mapLanguage=" + mapLanguage);
        }
        return mapLanguage;
    }

    /**
     * 从 mapinfo.json 文件解析地图主语言
     */
    private String parseMapLanguageFromMapInfoJsonFile(String mapName) {
        Log.d(TAG, "parseMapLanguageFromMapInfoJsonFile: mapName=" + mapName);
        String mapLanguage = "";
        File file = getPlaceFile(mapName, MapFileHelper.MAP_INFO_JSON);
        try {
            InputStream is = new FileInputStream(file);
            int total = is.available();
            byte[] bytes = new byte[total];
            int len = is.read(bytes);
            if (total == len) {
                String mapInfoJson = new String(bytes);
                Log.d(TAG, "parseMapLanguageFromMapInfoJsonFile: mapInfoJson=" + mapInfoJson);
                JSONObject jsonObject = new JSONObject(mapInfoJson);
                String mapInfoString = jsonObject.getString("mapInfo");
                if (!TextUtils.isEmpty(mapInfoString)) {
                    MapInfo mapInfoBean = GsonUtil.fromJson(mapInfoString, MapInfo.class);
                    if (mapInfoBean != null) {
                        mapLanguage = mapInfoBean.getMapLanguage();
                    }
                }
            }
            Log.d(TAG, "parseMapLanguageFromMapInfoJsonFile: Success!");
        } catch (Exception e) {
            Log.d(TAG, "parseMapLanguageFromMapInfoJsonFile:Exception: " + e.getMessage());
            e.printStackTrace();
        }
        return mapLanguage;
    }

    //保存place文件，替换placeBean
    public void savePlacePropFile(MapInfo mapInfo, List<PlaceInfo> placeList) {
        String mapName = mapInfo.getMapName();
        Log.d(TAG, "savePlacePropFile: mapName=" + mapName + ", placeList=" + placeList);
        Properties placeProp = getPlaceProperties(mapName);
        if (null == placeProp) {
            Log.d(TAG, "savePlacePropFile: placeProp null");
            return;
        }
        Log.d(TAG, "savePlacePropFile: mapInfo=" + mapInfo.toString());
        boolean forbidLine = mapInfo.hasForbidLine(); //是否有禁行线
        String typeForbidLine = getTypeForbidLine(mapName);
        placeProp.setProperty(typeForbidLine, Boolean.toString(forbidLine));
        String typeMapLanguage = getTypeMapLanguage(mapName);
        String mapLanguage = mapInfo.getMapLanguage();//地图主语言
        if (TextUtils.isEmpty(mapLanguage)) {
            mapLanguage = Locale.SIMPLIFIED_CHINESE.toString();
        }
        placeProp.setProperty(typeMapLanguage, mapLanguage);

        for (PlaceInfo place : placeList) {
            Log.d(TAG, "savePlacePropFile: place=" + place);
            String placeName = place.getPlaceName(DEFAULT_LANGUAGE);
            Log.d(TAG, "savePlacePropFile: placeName=" + placeName);
            if (null != placeName) {
                placeProp.setProperty(getPoseType() + placeName,
                        GsonUtil.toJson(placeBeanToPose(place)));
            }
        }
        Log.d(TAG, "savePlacePropFile: placeProp=" + placeProp);
        boolean success = saveProperties(getPlaceFile(mapName, MapFileHelper.PLACE_PROP), placeProp);
        Log.d(TAG, "savePlacePropFile: saveProperties=" + success);
    }

    //保存地点到place.json中,多语言版本添加
    public void savePlaceJsonFile(String mapName, List<PlaceInfo> placeList) {
        Log.d(TAG, "savePlaceJsonFile: mapName=" + mapName + ", placeList=" + placeList);
        FileOutputStream fileOutputStream = null;
        try {
            JSONArray jsonArray = new JSONArray();
            for (PlaceInfo placeBean : placeList) {
                jsonArray.put(placeInfoToJson(placeBean));
            }
            fileOutputStream = new FileOutputStream(getPlaceFile(mapName, MapFileHelper.PLACE_JSON));
            fileOutputStream.write(jsonArray.toString().getBytes());
        } catch (Exception e) {
            e.printStackTrace();
        }
        IOUtils.close(fileOutputStream);
    }

    /**
     * 获取place的properties文件
     */
    public Properties getPlaceProperties(String mapName) {
        Properties properties = new Properties();
        File file = getPlaceFile(mapName, MapFileHelper.PLACE_PROP);
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            properties.load(fis);
        } catch (IOException e) {
            e.printStackTrace();
            return properties;
        } finally {
            IOUtils.close(fis);
        }
        return properties;
    }

    /**
     * 获取指定地图文件
     */
    public File getPlaceFile(String mapName, String fileName) {
        Log.d(TAG, "getPlaceFile: mapName: " + mapName + ", fileName: " + fileName);
        File dir = MapFileHelper.getMapDir(mapName);
        if (!dir.exists()) {
            boolean result = dir.mkdirs();
            Log.d(TAG, "getPlaceFile: mkdirs: " + result);
        }
        Log.d(TAG, "getPlaceFile: mapName: " + mapName + ", dir: " + dir);
        File file = new File(dir, fileName);
        if (!file.exists()) {
            try {
                boolean result = file.createNewFile();
                Log.d(TAG, "getPlaceFile: createNewFile: " + result);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        Log.d(TAG, "getPlaceFile: mapName: " + mapName + ", file: " + file.getAbsolutePath());
        return file;
    }

    public void sendPlaceDataUpdateReport() {
        RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE,
                Definition.REPORT_NAVI_CONFIG, "navi_update_place");
    }

    /**
     * ------------------------数据库数据初始化，兼容旧版本properties-------------------------------
     */

    //获取config
    private String getConfig(String key) {
        Properties properties = getProperties();
        Log.d(TAG, "properties size :" + (properties == null ? "null" : properties.size()));
        if (properties != null && properties.size() > 0) {
            for (Map.Entry<Object, Object> objectObjectEntry : properties.entrySet()) {
                Object key1 = objectObjectEntry.getKey();
                Object value1 = objectObjectEntry.getValue();
                Log.d(TAG, "key=" + key1 + " value=" + value1);
            }
            return properties.getProperty(key);
        }
        return null;
    }

    //拿properties
    private Properties getProperties() {
        Properties properties = new Properties();
        File file = getConfigFile();
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            properties.load(fis);
        } catch (IOException e) {
            e.printStackTrace();
            return properties;
        } finally {
            IOUtils.close(fis);
        }
        Log.d(TAG, "DataManager: getProperties: start --->>>");
        printProperty(properties);
        Log.d(TAG, "DataManager: getProperties: end <<<---");
        return properties;
    }

    //获取config文件
    public File getConfigFile() {
        File root = Environment.getExternalStorageDirectory();
        File dir = new File(root, CONFIG_DIR);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        File file = new File(dir, CONFIG_FILE);
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return file;
    }

    //log
    private void printProperty(Properties properties) {
        if (properties == null) {
            Log.d(TAG, "printProperty: properties is null");
            return;
        }

        for (Map.Entry<Object, Object> objectObjectEntry : properties.entrySet()) {
            Object key1 = objectObjectEntry.getKey();
            Object value1 = objectObjectEntry.getValue();
            Log.d(TAG, "printProperty: key1=" + key1 + " value1=" + value1);
        }
    }

    public static JSONObject placeInfoToJson(PlaceInfo placeInfo) {
        JSONObject place = new JSONObject();

        try {
            place.put("id", placeInfo.getPlaceId());
            place.put("icon_url", placeInfo.getIconUrl());
            place.put("type", placeInfo.getPlaceType());
            place.put("status", placeInfo.getPlaceStatus());
            place.put("theta", placeInfo.getPointTheta());
            place.put("x", placeInfo.getPointX());
            place.put("y", placeInfo.getPointY());
            place.put("time", placeInfo.getUpdateTime());
            JSONObject name = new JSONObject();
            if (null != placeInfo.getPlaceNameList()) {
                Iterator<Map.Entry<String, String>> var3 = placeInfo.getPlaceNameList().entrySet().iterator();
                while (var3.hasNext()) {
                    Map.Entry<String, String> stringStringEntry = var3.next();
                    name.put(stringStringEntry.getKey(), stringStringEntry.getValue());
                }
            }

            place.put("name", name);
            place.put("alaias", new JSONObject(placeInfo.getAlias()));
            place.put("ignoreDistance", placeInfo.getIgnoreDistance());
            place.put("noDirectionalParking", placeInfo.getNoDirectionalParking());
            place.put("safeDistance", placeInfo.getSafeDistance());
            place.put("typeId", placeInfo.getTypeId());
            place.put("priority", placeInfo.getPriority());
        } catch (JSONException var5) {
            var5.printStackTrace();
        }

        return place;
    }

    /**
     * check place is or not Special Place
     *
     * @param poseName place name
     * @return result
     */
    public boolean isSpecialPlace(String poseName) {
//        List<String> languageArray = new ArrayList<>();
//        for (Map.Entry<String, List<String>> specialName : mSpecialPlace.entrySet()) {
//            languageArray.addAll(specialName.getValue());
//        }
//        boolean isContainsPlace = false;
//        for (String name : languageArray) {
//            if (isContainsPlace = name.equalsIgnoreCase(poseName)) {
//                break;
//            }
//        }
//        return isContainsPlace;
        return isSpecialOrElevatorPlace(poseName);//isSpecialPlace
    }

    public PlaceInfo poseToPlaceBean(Pose pose, String language, String mapName) {
        if (null == pose) {
            return null;
        }
        PlaceInfo placeBean = new PlaceInfo();
        placeBean.setPlaceType(pose.getPostype());
        placeBean.setPlaceStatus(pose.getStatus());
        placeBean.setPointTheta(pose.getTheta());
        placeBean.setPointX(pose.getX());
        placeBean.setPointY(pose.getY());
        placeBean.setUpdateTime(pose.getTime());
        placeBean.setMapName(mapName);
        placeBean.addPlaceName(language, pose.getName());
        placeBean.setIgnoreDistance(pose.getIgnoreDistance());
        placeBean.setNoDirectionalParking(pose.getNoDirectionalParking());
        placeBean.setSafeDistance(pose.getSafeDistance());
        placeBean.setAlias("{\"zh_CN\": []}");
        placeBean.setPlaceId(UuidUtils.createPlaceId(placeBean));
        placeBean.setTypeId(pose.getTypeId());
        placeBean.setPriority(pose.getPriority());
        return placeBean;
    }

    public static Pose placeBeanToPose(PlaceInfo placeInfo) {
        if (null == placeInfo) {
            return null;
        }
        Pose pose = new Pose(
                placeInfo.getPointX(),
                placeInfo.getPointY(),
                placeInfo.getPointTheta(),
                placeInfo.getPlaceStatus(),
                placeInfo.getIgnoreDistance(),
                placeInfo.getNoDirectionalParking(),
                placeInfo.getSafeDistance());
        pose.setPostype(placeInfo.getPlaceType());
        pose.setTypeId(placeInfo.getTypeId());
        pose.setPriority(placeInfo.getPriority());
        return pose;
    }

    /**
     * save place info by place json format
     *
     * @param json    place json format
     * @param mapInfo map info that place belong to map
     * @return result true-suc false -fail
     */
    public PlaceInfo parsePlace(JSONObject json, MapInfo mapInfo) {
        String mapName = mapInfo.getMapName();
        Log.i(TAG, "addPlace: from down load: mapInfo=" + mapInfo + " place json=" + json);
        PlaceInfo placeInfo = new PlaceInfo();

        Log.i(TAG, "addPlace: mapInfo=" + mapInfo);
        String placeId = json.optString("id");
        if (TextUtils.isEmpty(placeId)) {
            Log.e(TAG, "addPlace: place json error,not found place id");
            return null;
        }
        String iconUrl = json.optString("icon_url");
        int placeType = json.optInt("type");
        int placeStatus = json.optInt("status");
        float pointTheta = (float) json.optDouble("theta");
        float pointX = (float) json.optDouble("x");
        float pointY = (float) json.optDouble("y");
        String alias = json.optString("alaias");
        boolean ignoreDistance = json.optBoolean("ignoreDistance");
        boolean noDirectionalParking = json.optBoolean("noDirectionalParking");
        int safeDistance = json.optInt("safeDistance");
        int typeId = json.optInt("typeId");
        int priority = json.optInt("priority");

        placeInfo.setPlaceId(placeId)
                .setMapName(mapName)
                .setIconUrl(iconUrl)
                .setPlaceType(placeType)
                .setPlaceStatus(placeStatus)
                .setPointX(pointX)
                .setPointY(pointY)
                .setPointTheta(pointTheta)
                .setAlias(alias)
                .setSyncState(SYNC_STATE_UPLOAD)
                .setIgnoreDistance(ignoreDistance)
                .setNoDirectionalParking(noDirectionalParking)
                .setSafeDistance(safeDistance)
                .setTypeId(typeId)
                .setPriority(priority);

        JSONObject name = json.optJSONObject("name");
        if (name != null) {
            Iterator it = name.keys();
            while (it.hasNext()) {
                String key = (String) it.next();
                placeInfo.addPlaceName(key, name.optString(key));
            }
        }
//        insertOrUpdatePlaceInfo(placeInfo);
        return placeInfo;
    }

    /**
     * check special place ，charge pile place and back charge place
     *
     * 只在本地新增该特殊点位时生效，传入的是固定中文，这里通过多语言文案替换掉固定的中文
     *
     * *
     * * @param placeBean place info
     */
    public void checkSpecialPlace(PlaceInfo placeBean) {
        Map<String, String> placeNameList = placeBean.getPlaceNameList();
        Log.d(TAG, "checkSpecialPlace: placeNameList: "+placeNameList);
        if (placeNameList.size() == 1) {
            Iterator<Map.Entry<String, String>> it = placeNameList.entrySet().iterator();
            Map.Entry<String, String> name = it.next();
            if (!TextUtils.isEmpty(name.getValue())
                    && name.getValue().contains(Definition.START_CHARGE_PILE_POSE + "-")) {//增加充电桩的逻辑
                int index = -1;
                List<String> cnList = mSpecialPlace.get("zh_CN");
                index = cnList.indexOf(Definition.START_CHARGE_PILE_POSE);
                if (index != -1) {
                    for (Map.Entry<String, List<String>> specialName : mSpecialPlace.entrySet()) {
                        placeBean.addPlaceName(specialName.getKey(),
                                name.getValue().replace(Definition.START_CHARGE_PILE_POSE,
                                        specialName.getValue().get(index)));
                    }
                }
            } else if (!TextUtils.isEmpty(name.getValue())
                    && name.getValue().contains(Definition.START_BACK_CHARGE_POSE + "-")) {
                int index = -1;
                List<String> cnList = mSpecialPlace.get("zh_CN");
                index = cnList.indexOf(Definition.START_BACK_CHARGE_POSE);
                if (index != -1) {
                    for (Map.Entry<String, List<String>> specialName : mSpecialPlace.entrySet()) {
                        placeBean.addPlaceName(specialName.getKey(),
                                name.getValue().replace(Definition.START_BACK_CHARGE_POSE,
                                        specialName.getValue().get(index)));
                    }
                }
            } else if (!TextUtils.isEmpty(name.getValue())
                    && name.getValue().contains("-" + Definition.ELEVATOR_CENTER_POSE)) { //电梯中心点位
                int index = -1;
                String poseSetName = name.getValue();
                Log.d(TAG, "checkSpecialPlace: poseSetName: "+poseSetName);
                List<String> cnList = mSpecialPlace.get("zh_CN");
                index = cnList.indexOf(Definition.ELEVATOR_CENTER_POSE);//5
                if (index != -1) {
                    for (Map.Entry<String, List<String>> specialName : mSpecialPlace.entrySet()) {
                        String language = specialName.getKey();
                        String languageName = specialName.getValue().get(index);
                        String finalName = poseSetName.replace(Definition.ELEVATOR_CENTER_POSE, languageName);
                        Log.d(TAG, "checkSpecialPlace:电梯中心: language: "+language+", languageName: "+languageName+", finalName: "+finalName);
                        placeBean.addPlaceName(language, finalName);
                    }
                }
            } else if (!TextUtils.isEmpty(name.getValue())
                    && name.getValue().contains("-" + Definition.ELEVATOR_ENTER_POSE)) { //电梯口点位
                int index = -1;
                String poseSetName = name.getValue();
                Log.d(TAG, "checkSpecialPlace: poseSetName: "+poseSetName);
                List<String> cnList = mSpecialPlace.get("zh_CN");
                index = cnList.indexOf(Definition.ELEVATOR_ENTER_POSE);//7
                if (index != -1) {
                    for (Map.Entry<String, List<String>> specialName : mSpecialPlace.entrySet()) {
                        String language = specialName.getKey();
                        String languageName = specialName.getValue().get(index);
                        String finalName = poseSetName.replace(Definition.ELEVATOR_ENTER_POSE, languageName);
                        Log.d(TAG, "checkSpecialPlace:电梯口: language: "+language+", languageName: "+languageName+", finalName: "+finalName);
                        placeBean.addPlaceName(language, finalName);
                    }
                }
            }  else {//特殊点名称赋值逻辑
                for (List<String> nameList : mSpecialPlace.values()) {
                    Log.d(TAG, "checkSpecialPlace: nameList: "+nameList);
                    int index = -1;
                    for (String item : nameList) {
                        if (item.equalsIgnoreCase(name.getValue())) {
                            index = nameList.indexOf(item);
                            break;
                        }
                    }
                    if (index != -1) {
                        Log.d(TAG, "checkSpecialPlace: entrySet : "+mSpecialPlace.entrySet());
                        for (Map.Entry<String, List<String>> specialName : mSpecialPlace.entrySet()) {
                            Log.d(TAG, "checkSpecialPlace: specialName : "+ specialName.getValue());
                            placeBean.addPlaceName(specialName.getKey(), specialName.getValue().get(index));
                        }
                    }
                }
            }
        }
    }

    /**
     * check place is or not Special Place
     *
     * 是否原始特殊点位（包括电梯中心、电梯口），根据固定字符串判断，TODO 后续改为类型&&字符串同时匹配
     *
     * @param poseName place name
     * @return result
     */
    public boolean isSpecialOrElevatorPlace(String poseName) {
        List<String> languageArray = new ArrayList<>();
        for (Map.Entry<String, List<String>> specialName : mSpecialPlace.entrySet()) {
            languageArray.addAll(specialName.getValue());
        }
        boolean isContainsPlace = false;
        for (String name : languageArray) {
            //非电梯相关特殊点位判断
            if (name.equalsIgnoreCase(poseName)) {
                return true;
            }

            //电梯相关特殊点位判断，电梯点位会是名字+"-"+特殊点位定义的名字 如：A梯-电梯中心
            if (!TextUtils.isEmpty(poseName)) {
                String elevatorPoseName = "-" + name;
                if (poseName.contains(elevatorPoseName) && poseName.endsWith(elevatorPoseName)) {
                    return true;
                }
            }
        }
        return false;
    }
}
