/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.tk1;

import androidx.annotation.NonNull;

import java.io.DataOutput;
import java.io.FilterOutputStream;
import java.io.IOException;
import java.io.OutputStream;

/**
 * A high and low level for convert data.
 */
public class ReverseOutputStream extends FilterOutputStream implements DataOutput{

    protected int written;

    public ReverseOutputStream(@NonNull OutputStream out) {
        super(out);
    }

    private void incCount(int value) {
        int temp = written + value;
        if (temp < 0) {
            temp = Integer.MAX_VALUE;
        }
        written = temp;
    }

    @Override
    public void flush() throws IOException {
        out.flush();
    }

    @Override
    public void write(@NonNull byte[] b, int off, int len) throws IOException {
        out.write(b, off, len);
        incCount(len);
    }

    @Override
    public void writeBoolean(boolean v) throws IOException {

    }

    @Override
    public void write(int b) throws IOException {
        out.write(b);
        incCount(1);
    }

    @Override
    public void writeByte(int v) throws IOException {
        out.write(v);
        incCount(1);
    }

    @Override
    public void writeShort(int v) throws IOException {

    }

    @Override
    public void writeChar(int v) throws IOException {

    }

    @Override
    public void writeInt(int v) throws IOException {
        out.write((v) & 0xFF);
        out.write((v >>> 8) & 0xFF);
        out.write((v >>> 16) & 0xFF);
        out.write((v >>> 24) & 0xFF);
        incCount(4);
    }

    private byte writeBuffer[] = new byte[8];

    @Override
    public void writeLong(long v) throws IOException {
        writeBuffer[0] = (byte)(v);
        writeBuffer[1] = (byte)(v >>> 8);
        writeBuffer[2] = (byte)(v >>> 16);
        writeBuffer[3] = (byte)(v >>> 24);
        writeBuffer[4] = (byte)(v >>> 32);
        writeBuffer[5] = (byte)(v >>> 40);
        writeBuffer[6] = (byte)(v >>> 48);
        writeBuffer[7] = (byte)(v >>> 56);
        out.write(writeBuffer, 0, 8);
        incCount(8);
    }

    @Override
    public void writeFloat(float v) throws IOException {
        writeInt(Float.floatToIntBits(v));
    }

    @Override
    public void writeDouble(double v) throws IOException {
        writeLong(Double.doubleToLongBits(v));
    }

    @Override
    public void writeBytes(@NonNull String s) throws IOException {
        int len = s.length();
        for (int i = 0 ; i < len ; i++) {
            out.write((byte)s.charAt(i));
        }
        incCount(len);
    }

    @Override
    public void writeChars(@NonNull String s) throws IOException {

    }

    @Override
    public void writeUTF(@NonNull String s) throws IOException {

    }

    public final int size() {
        return written;
    }

}
