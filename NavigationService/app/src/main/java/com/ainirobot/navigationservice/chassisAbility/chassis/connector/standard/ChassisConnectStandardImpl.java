package com.ainirobot.navigationservice.chassisAbility.chassis.connector.standard;

import android.util.Log;

import com.ainirobot.navigationservice.protocol.event.EventPacketCreator.EventPacketProto;
import com.ainirobot.navigationservice.protocol.response.ResPacketCreator.ResPacketProto;
import com.ainirobot.navigationservice.utils.NavigationConfig;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;

import org.java_websocket.WebSocket;

import java.net.InetSocketAddress;

import static com.ainirobot.navigationservice.Defs.Def.StandardDef.CMD_CHANNEL;
import static com.ainirobot.navigationservice.Defs.Def.StandardDef.EVENT_CHANNEL;
import static com.ainirobot.navigationservice.Defs.Def.StandardDef.PortCmd;
import static com.ainirobot.navigationservice.Defs.Def.StandardDef.PortEvent;
import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

public class ChassisConnectStandardImpl implements ConnectApi {
    private static final String TAG = TAGPRE + ChassisConnectStandardImpl.class.getSimpleName();
    private WebSocketServerImpl cmdChannel;
    private WebSocketServerImpl eventChannel;
    private ResListener resListener;
    private EventListener eventListener;
    private OnConnectListener onConnectListener;

    public ChassisConnectStandardImpl() {
        String hostEth0Ip = NavigationConfig.getEth0Ip();
        String hostWlan0Ip = NavigationConfig.getWlan0Ip();
        Log.d(TAG, "hostIp = " + hostEth0Ip);
        Log.d(TAG, "hostWlan0Ip = " + hostWlan0Ip);

        cmdChannel = new WebSocketServerImpl(new InetSocketAddress(hostEth0Ip, PortCmd), CMD_CHANNEL);
        cmdChannel.listenMsg(cmdResListener);
        cmdChannel.listenConnect(connectCallback);
        eventChannel = new WebSocketServerImpl(new InetSocketAddress(hostEth0Ip, PortEvent), EVENT_CHANNEL);
        eventChannel.listenMsg(eventMsgListener);
        cmdChannel.listenConnect(connectCallback);
    }

    @Override
    public void init() {
        if (cmdChannel != null && eventChannel != null) {
            cmdChannel.start();
            eventChannel.start();
        } else {
            throw new NullPointerException("WebSocketChannel is null");
        }
    }

    private WebSocketServerImpl.Callback cmdResListener = new WebSocketServerImpl.Callback() {
        @Override
        public void onMsgIn(byte[] data) {
            try {
                Log.d(TAG, "response Msg in");
                ResPacketProto msg = ResPacketProto.parseFrom(data);
                if (resListener != null) {
                    resListener.onResponse(msg);
                } else {
                    Log.d(TAG, "responseListener not register");
                }

            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
            }
        }
    };

    private WebSocketServerImpl.Callback eventMsgListener = new WebSocketServerImpl.Callback() {
        @Override
        public void onMsgIn(byte[] data) {
            try {
                Log.d(TAG, "event Msg in");
                EventPacketProto msg = EventPacketProto.parseFrom(data);
                if (eventListener != null) {
                    eventListener.onEvent(msg);
                } else {
                    Log.d(TAG, "eventListener not register");
                }
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
            }
        }
    };

    private WebSocketServerImpl.ConnectCallback connectCallback = new WebSocketServerImpl.ConnectCallback() {
        @Override
        public void onConnectIn(String name, WebSocket mConn) {
            Log.d(TAG, "onConnectIn");
            if (onConnectListener != null) {
                onConnectListener.onConnected();
            }
        }

        @Override
        public void onCloseIn(String name, WebSocket mConn, int code) {
            Log.d(TAG, "onCloseIn");
            if (onConnectListener != null) {
                onConnectListener.onDisconnected(name);
            }
        }

        @Override
        public void onError(String name, WebSocket mConn) {
            Log.d(TAG, "onError");
            if (onConnectListener != null) {
                onConnectListener.onDisconnected(name);
            }
        }
    };

    @Override
    public boolean request(Message message) {
        if (cmdChannel != null) {
            cmdChannel.sendMsgBytes(message.toByteArray());
            return true;
        }
        return false;
    }

    @Override
    public void registerEventListener(EventListener listener) {
        eventListener = listener;
    }

    @Override
    public void registerConnectListener(OnConnectListener listener) {
        onConnectListener = listener;
    }

    @Override
    public void registerResponseListener(ResListener listener) {
        resListener = listener;
    }
}
