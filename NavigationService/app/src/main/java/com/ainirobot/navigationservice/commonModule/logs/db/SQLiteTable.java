package com.ainirobot.navigationservice.commonModule.logs.db;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.provider.BaseColumns;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

public enum SQLiteTable {

    LOG("log_cache", LogDataHelper.LogField.VALUE) {

        @Override
        void update(Context context, SQLiteDatabase db, int oldVersion, int newVersion) {
            Log.d("SQLiteTable", "oldVersion " + oldVersion + " newVersion " + newVersion);
        }
    };

    abstract void update(Context context, SQLiteDatabase db, int oldVersion, int newVersion);

    private final String mTableName;

    private final TableField mTableField;

    SQLiteTable(String tableName, TableField field) {
        mTableName = tableName;
        this.mTableField = field;
    }

    public String getAuthority() {
        return LogDataProvider.AUTHORITY;
    }

    public Uri getContentUri() {
        return Uri.parse("content://" + getAuthority() + "/" + mTableName);
    }

    public String getContentType() {
        return "vnd.android.cursor.dir/" + mTableName;
    }

    public static SQLiteTable get(int ordinal) {
        return SQLiteTable.values()[ordinal];
    }

    public String getTableName() {
        return mTableName;
    }

    public void create(SQLiteDatabase db) {
        String formatter = " %s";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("CREATE TABLE IF NOT EXISTS ");
        stringBuilder.append(mTableName);
        stringBuilder.append("(");
        int columnCount = mTableField.getColumns().size();
        int index = 0;
        for (Column columnsDefinition : mTableField.getColumns()) {
            stringBuilder.append(columnsDefinition.getColumnName()).append(
                    String.format(formatter, columnsDefinition.getDataType().name()));

            Column.Constraint constraint = columnsDefinition.getConstraint();

            if (constraint != null) {
                stringBuilder.append(String.format(formatter, constraint.toString()));
            }
            if (index < columnCount - 1) {
                stringBuilder.append(",");
            }
            index++;
        }

        stringBuilder.append(");");

        db.execSQL(stringBuilder.toString());
    }

    public void delete(final SQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS " + mTableName);
    }

    public static class TableField {
        ArrayList<Column> mColumnsDefinitions = new ArrayList<Column>();

        public TableField() {
            mColumnsDefinitions.add(new Column(BaseColumns._ID, Column.Constraint.AUTO_INCREMENT,
                    Column.DataType.INTEGER));
        }

        List<Column> getColumns() {
            return mColumnsDefinitions;
        }

        TableField addColumn(String columnName, Column.DataType dataType) {
            mColumnsDefinitions.add(new Column(columnName, null, dataType));
            return this;
        }
    }
}
