package com.ainirobot.navigationservice.chassisAbility.chassis.client.x86;

import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.navigationservice.beans.tk1.Velocity;
import com.ainirobot.navigationservice.chassisAbility.ChassisManager;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.commonModule.bi.report.MotionCallChainReporter;

import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 加速度控制器，处理给定速度的加速过程
 *
 * <p>
 * Created by Orion on 2020/6/3.
 */
public class WheelControlX86 {
    private static final String TAG = WheelControlX86.class.getSimpleName();

    private static final Object SEND_PEED_LOCK = new Object();

    private static final double VELOCITY_DIFF_LINEAR = 0.007;
    private static final double VELOCITY_DIFF_ANGLE = 0.015;
    private static final double MIN_VELOCITY_LINEAR = 0.007; //min velocity, below this velocity is still
    private static final double MIN_VELOCITY_ANGLE = 0.02; //min velocity, below this velocity is still
    private static final double MIN_LINEAR_SPEED = -1.2;
    private static final double MAX_LINEAR_SPEED = 1.2;
    private static final double MIN_ANGULAR_SPEED = -2.2;
    private static final double MAX_ANGULAR_SPEED = 2.2; //角度值约为 103

    private static final float STOP_SPEED = 0f;
    private static final double MAX_LIMIT_ANGULAR_SPEED = 1.0;
    private static final double BASE_MIN_DEC = 0.025; //默认基准加速幅度，可以通过参数修改
    private static final double ANGLE_AEC_FACTOR = 2; //角-加速度增大倍数，相对基准加速度
    private static final double ANGLE_DEC_FACTOR = 4; //角-减速度增大倍数，相对基准加速度
    private static final double LINEAR_DEC_FACTOR = 2; //线-减速度增大倍数，相对基准加速度
    private static final float MOTION_SPEED = 0.0f; //预启动线速度
    private static final float ANGULAR_SPEED = 0.0f; //预启动角速度

    //加速度
    public static double A_LINEAR_ACC;
    public static double A_LINEAR_DEC;
    public static double A_ANGULAR_ACC;
    public static double A_ANGULAR_DEC;
    public static double A_LINEAR_DEC_FACTOR;

    private double lastAngularSpeed = 0d;
    private double lastLinearSpeed = 0d;
    private volatile double mAngularSpeed = 0; //Current angular speed
    private volatile double mLinearSpeed = 0; //Current linear speed
    private volatile double mTargetAngularSpeed = 0;
    private volatile double mTargetLinearSpeed = 0;
    private volatile long DEC_TIME = 50;
    private IChassisClient mChassisClient;
    private double mCurrentAcceleration = -1;

    private static final WheelControlX86 mInstance = new WheelControlX86();

    public static WheelControlX86 getInstance() {
        return mInstance;
    }

    private WheelControlX86() {
        this.mChassisClient = ChassisManager.getInstance().getChassisClient();
        initSpeedParams(BASE_MIN_DEC);
    }

    private void initSpeedParams(double acceleration) {
        if (mCurrentAcceleration == acceleration) {
            Log.d(TAG, "initSpeedParams: acceleration not change");
            return;
        }
        mCurrentAcceleration = acceleration;
        String model = RobotSettings.getProductModel();
        Log.d(TAG, "initSpeedParams: product model=" + model + ", acceleration=" + acceleration);

        //加速度
        A_LINEAR_ACC = 1000 / 50 * acceleration; //线-加速度：0.5m/s^2
        A_LINEAR_DEC = A_LINEAR_ACC * LINEAR_DEC_FACTOR; //线-减速度 -1.0m/s^2
        A_ANGULAR_ACC = A_LINEAR_ACC * ANGLE_AEC_FACTOR; //角-加速度 1.0radians/s^2
        A_ANGULAR_DEC = A_LINEAR_ACC * ANGLE_DEC_FACTOR; //角-减速度 -2.0radians/s^2(114.6度/s^2)
        A_LINEAR_DEC_FACTOR = LINEAR_DEC_FACTOR;

        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
            A_LINEAR_DEC_FACTOR = A_LINEAR_DEC_FACTOR * 2;
            A_LINEAR_DEC = A_LINEAR_ACC * A_LINEAR_DEC_FACTOR;
        }
        Log.d(TAG, "initSpeedParams: A_LINEAR_DEC_FACTOR=" + A_LINEAR_DEC_FACTOR +
                " A_LINEAR_DEC=" + A_LINEAR_DEC);
    }

    public synchronized void startDecTimer(final double tAngularSpeed, final double tLinearSpeed,
                                           double acceleration, boolean hasAcceleration) {
        Log.i(TAG, "startDecTimer start : hasAcceleration=" + hasAcceleration
                + " tAngularSpeed=" + tAngularSpeed + " tLinearSpeed=" + tLinearSpeed
                + " mAngularSpeed=" + mAngularSpeed + " mLinearSpeed=" + mLinearSpeed
                + " acceleration=" + acceleration);
        mTargetAngularSpeed = tAngularSpeed;
        mTargetLinearSpeed = tLinearSpeed;
        cancelDecTimer(false);
        lastAngularSpeed = tAngularSpeed;
        lastLinearSpeed = tLinearSpeed;

        if (!hasAcceleration) {
//            Log.d(TAG, "Motion mLinearSpeed=" + tLinearSpeed + "  mAngularSpeed=" + tAngularSpeed);
            sendPrimitiveMovingCommand(tAngularSpeed, tLinearSpeed);
//            Log.i(TAG, "startDecTimer: navigationService 给地盘发命令完成");
            return;
        }
        double baseMinDec = acceleration;
        if (acceleration <= 0 || acceleration >= 0.05) {
            baseMinDec = BASE_MIN_DEC;
        }
        initSpeedParams(baseMinDec);

        int decNumber = calculateDecNumber(tAngularSpeed, tLinearSpeed, baseMinDec);
        //加速度 <0加速过程，>0减速过程
        double linearDecT = Math.signum(mLinearSpeed - tLinearSpeed) * baseMinDec;
        double angularDecT = Math.signum(mAngularSpeed - tAngularSpeed) * baseMinDec;
        Log.i(TAG, "startDecTimer " + "Temp dec: linearDecT=" + linearDecT
                + " angularDecT=" + angularDecT + ", decNumber=" + decNumber);

        //加速度控制：加速放缓；减速放大；线加速等于基准加速度；角加速度倍数放大；
        if (tLinearSpeed == 0
                || (tLinearSpeed > 0 && linearDecT > 0)
                || (tLinearSpeed < 0 && linearDecT < 0)) { //线速度减速
            linearDecT *= A_LINEAR_DEC_FACTOR;
        }
        double angularAecT = angularDecT * ANGLE_AEC_FACTOR;
        if (tAngularSpeed == 0
                || (tAngularSpeed > 0 && angularDecT > 0)
                || (tAngularSpeed < 0 && angularDecT < 0)) { //角速度减速
            angularAecT = angularDecT * ANGLE_DEC_FACTOR;
        }

        final double linearDec = linearDecT;
        final double angularDec = angularAecT;

        MotionCallChainReporter.startDecTimerEntranceReport(TAG + "_startDecTimer",
                decNumber, tAngularSpeed, tLinearSpeed);
        Log.i(TAG, "startDecTimer: decNumber=" + decNumber
                + " Final dec: linearDec=" + linearDec + " angularDec=" + angularDec);
        ControlTask.mInstance.submit(new Runnable() {
            @Override
            public void run() {
                //达到目标速度||模式切换
                Log.d(TAG, "mLinearSpeed = " + mLinearSpeed
                        + ", mAngularSpeed = " + mAngularSpeed);
                if ((mLinearSpeed == tLinearSpeed && mAngularSpeed == tAngularSpeed)) {
                    cancelDecTimer(false);
                    return;
                }
                double linearSpeed = mLinearSpeed - linearDec;
                double angularSpeed = mAngularSpeed - angularDec;

                    /* if it is decelerating and the speed is less than the target speed,
                       or it is accelerating and the speed is greater than the target speed,
                       set speed as the target speed */
                if ((angularDec >= 0 && angularSpeed < tAngularSpeed)
                        || (angularDec <= 0 && angularSpeed > tAngularSpeed)) {
                    angularSpeed = tAngularSpeed;
                }
                if ((linearDec >= 0 && linearSpeed < tLinearSpeed)
                        || (linearDec <= 0 && linearSpeed > tLinearSpeed)) {
                    linearSpeed = tLinearSpeed;
                }

                //最后一次加减速处理
                if (Math.abs(tLinearSpeed - mLinearSpeed) < Math.abs(linearDec)) {
                    linearSpeed = tLinearSpeed;
                }
                if (Math.abs(tAngularSpeed - mAngularSpeed) < Math.abs(angularDec)) {
                    angularSpeed = tAngularSpeed;
                }

                angularSpeed = correctAngularSpeed(angularSpeed);
                linearSpeed = correctLinearSpeed(linearSpeed);
                Log.i(TAG, "run: 目标速度：角：" + tAngularSpeed + " 线:" + tLinearSpeed
                        + "\n当前速度：角：" + mAngularSpeed + " 线：" + mLinearSpeed
                        + " \n角加速度：" + angularDec + " 线加速度" + linearDec);
                Log.d(TAG, "Motion send linearSpeed=" + linearSpeed
                        + "  angularSpeed=" + angularSpeed);
                sendPrimitiveMovingCommand(angularSpeed, linearSpeed);
            }
        }, 0, DEC_TIME, TimeUnit.MILLISECONDS);
        Log.d(TAG, "DecTimer start");
    }

    public synchronized void cancelDecTimer(boolean forceStop) {
        Log.d(TAG, "cancelDecTimer : forceStop=" + forceStop);
        if (forceStop) {
            startDecTimer(0, 0, 0, true);
        } else {
            ControlTask.mInstance.cancelTask();
        }
    }

    private void sendPrimitiveMovingCommand(double angularSpeed, double linearSpeed) {
        synchronized (SEND_PEED_LOCK) {
            this.mAngularSpeed = angularSpeed;
            this.mLinearSpeed = linearSpeed;
            mChassisClient.sendPrimitiveMovingSpeed(angularSpeed, linearSpeed);
        }
    }

    private int calculateDecNumber(double tAngularSpeed, double tLinearSpeed, double baseMinDec) {
        double diffLinearSpeed = Math.abs(mLinearSpeed - tLinearSpeed);
        double diffAngularSpeed = Math.abs(mAngularSpeed - tAngularSpeed);

        if (diffLinearSpeed < baseMinDec && diffAngularSpeed < baseMinDec) {
            return 1;
        }

        return (int) Math.max(diffLinearSpeed / baseMinDec, diffAngularSpeed / baseMinDec);
    }

    public double correctLinearSpeed(double speed) {
        if (speed < MIN_LINEAR_SPEED) {
            return MIN_LINEAR_SPEED;
        }
        if (speed > MAX_LINEAR_SPEED) {
            return MAX_LINEAR_SPEED;
        }
        return speed;
    }

    public double correctAngularSpeed(double speed) {
        if (speed < MIN_ANGULAR_SPEED) {
            return MIN_ANGULAR_SPEED;
        }
        if (speed > MAX_ANGULAR_SPEED) {
            return MAX_ANGULAR_SPEED;
        }
        return speed;
    }

    public boolean isStill(Velocity velocity) {
        return isLinearStill(velocity) && isAngularStill(velocity);
    }

    public boolean isLinearStill(Velocity velocity) {
        return velocity.getX() <= MIN_VELOCITY_LINEAR;
    }

    public boolean isAngularStill(Velocity velocity) {
        return velocity.getZ() <= MIN_VELOCITY_ANGLE;
    }

    public boolean isStill() {
        IChassisClient client = ChassisManager.getInstance().getChassisClient();
        Velocity velocity = client.getRealtimeVelocity();
        if (velocity == null) {
            return true;
        }
        return isStill(velocity);
    }

    public boolean isVelocityDiff(Velocity preVelocity, Velocity velocity) {
        return preVelocity == null
                || Math.abs(velocity.getZ() - preVelocity.getZ()) > VELOCITY_DIFF_ANGLE
                || Math.abs(velocity.getX() - preVelocity.getX()) > VELOCITY_DIFF_LINEAR;
    }

    public void resetSpeed() {
//        resetLinearSpeed();
//        resetAngularSpeed();
//        resetTargetAngularSpeed();
//        resetTargetLinearSpeed();
    }

    public void resetLinearSpeed() {
        synchronized (SEND_PEED_LOCK) {
            mLinearSpeed = 0;
        }
    }

    public void resetAngularSpeed() {
        synchronized (SEND_PEED_LOCK) {
            mAngularSpeed = 0;
        }
    }

    public double getLinearSpeed() {
        return mLinearSpeed;
    }

    public double getAngularSpeed() {
        return mAngularSpeed;
    }

    public void resetTargetLinearSpeed() {
        mTargetLinearSpeed = 0;
    }

    public void resetTargetAngularSpeed() {
        mTargetAngularSpeed = 0;
    }

    public double getTargetLinearSpeed() {
        return mTargetLinearSpeed;
    }

    public double getTargetAngularSpeed() {
        return mTargetAngularSpeed;
    }

    private static class ControlTask {
        public static ControlTask mInstance = new ControlTask();
        private ScheduledExecutorService mExecutor = Executors.newScheduledThreadPool(1);
        private volatile Future mFuture;

        private ControlTask() {
        }

        public synchronized void submit(Runnable runnable, long delay, long period, TimeUnit unit) {
            this.mFuture = this.mExecutor.scheduleAtFixedRate(runnable, delay, period, unit);
        }

        public synchronized void cancelTask() {
            if (mFuture != null && !mFuture.isCancelled() && !mFuture.isDone()) {
                mFuture.cancel(true);
            }
        }

    }
}
