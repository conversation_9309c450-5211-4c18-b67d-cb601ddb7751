/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;
import static com.ainirobot.navigationservice.R.id.startNavigation;
import static com.ainirobot.navigationservice.beans.tk1.TargetPose.STATUS_AVOID_END;
import static com.ainirobot.navigationservice.beans.tk1.TargetPose.STATUS_OUT_MAP_END;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.speech.tts.TextToSpeech;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.hardware.RobotCore;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.navigationservice.Defs.Def;
import com.ainirobot.navigationservice.beans.tk1.BaseEvent;
import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.tk1.TargetPose;
import com.ainirobot.navigationservice.chassisAbility.ChassisManager;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.commonModule.data.DataManager;
import com.ainirobot.navigationservice.db.NavigationDataManager;
import com.ainirobot.navigationservice.db.entity.MapInfo;
import com.ainirobot.navigationservice.db.entity.PlaceInfo;
import com.ainirobot.navigationservice.roversdkhelper.MapFileHelper;
import com.ainirobot.navigationservice.utils.MapUtils;
import com.ainirobot.navigationservice.utils.NavigationConfig;
import com.ainirobot.navigationservice.utils.TempCode;
import com.ainirobot.navigationservice.utils.ZipUtil;
import com.google.gson.Gson;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

import ninjia.android.roversdk.RoverClientFactory;


public class MainActivity extends Activity {
    private final static String TAG = TAGPRE + MainActivity.class.getSimpleName();
    private final long MINUTE = 60 * 1000;

    private final long DEFAULT_INTERVAL_TIME = 1;

    String routeName = "test";
    private ChassisManager chassisManager;
    private IChassisClient mNav;

    private TextToSpeech mSpeech;
    private long mPreviousTime;
    private long mIntervalTime = DEFAULT_INTERVAL_TIME * MINUTE;

    private EditText mIntervalTimeEdit;
    private EditText mMapName;
    private EditText mNewMapName;
    private CheckBox mRepeat;
    private EditText mNavIpEdit;
    private EditText mRosIpEdit;
    private EditText mLDistanceEdit;
    private EditText mObsDistanceEdit;
    private EditText mLSpeedEdit;
    private EditText mADistanceEdit;
    private EditText mASpeedEdit;
    private RadioGroup mDeviceGroup;
    private RadioGroup mGroundGroup;
    private RadioGroup mScenesGroup;
    private CheckBox mCamera;
    private CheckBox mFishEye;
    private CheckBox mRgbd;
    private CheckBox mIR;
    private CheckBox mSonar;
    private TextView mEstimate;
    private BroadcastReceiver mBatteryReceiver;
    private boolean isCharging = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        Log.d(TAG, "onCreate: ++++");

        registerBattery();

        Intent intent = new Intent(this, NavigationService.class);
        startService(intent);

        DelayTask.submit(new Runnable() {
            @Override
            public void run() {
                chassisManager = ChassisManager.getInstance();
                mNav = chassisManager.getChassisClient();
            }
        }, 1000);

        mSpeech = new TextToSpeech(this, new TextToSpeech.OnInitListener() {
            @Override
            public void onInit(int status) {
                if (status == TextToSpeech.SUCCESS) {
                    int result = mSpeech.setLanguage(Locale.CHINA);
                    if (result == TextToSpeech.LANG_MISSING_DATA
                            || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                        Toast.makeText(getBaseContext(),
                                getString(R.string.not_support_chinese), Toast.LENGTH_SHORT).show();
                    }
                }
            }
        });


        // ((TextView) findViewById(R.id.text)).setText("IP : " + getIp());

        mIntervalTimeEdit = (EditText) findViewById(R.id.intervalTime);
        mMapName = (EditText) findViewById(R.id.mapName);
//        mMapName.setText(dataManager.getMapName());

        mNewMapName = (EditText) findViewById(R.id.newMapName);
        mNavIpEdit = (EditText) findViewById(R.id.navIp);
        mRosIpEdit = (EditText) findViewById(R.id.rosIp);
        mLDistanceEdit = (EditText) findViewById(R.id.linear_distance);
        mObsDistanceEdit = (EditText) findViewById(R.id.obs_distance);
        mLSpeedEdit = (EditText) findViewById(R.id.linear_speed);
        mADistanceEdit = (EditText) findViewById(R.id.angle_distance);
        mASpeedEdit = (EditText) findViewById(R.id.angle_speed);
        mEstimate = (TextView) findViewById(R.id.showEstimate);
        mNavIpEdit.setText(NavigationConfig.getNavIp());
        mRosIpEdit.setText(NavigationConfig.getSdkRosIp());

        mRepeat = (CheckBox) findViewById(R.id.isRepeat);

//        mIntervalTime = dataManager.getIntervalTime();
        mIntervalTimeEdit.setText(String.valueOf(mIntervalTime / MINUTE));

        findViewById(R.id.openIrLed).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "IrAndMono: Open IR LED");
                RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, "ir_led_status_change", 1+"");
            }
        });

        findViewById(R.id.closeIrLed).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "IrAndMono: Close IR LED");
                RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE, "ir_led_status_change", 0+"");
            }
        });

        //Start plan route
        findViewById(R.id.startPlan).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                mNav.startPlanRoute();
                mNav.hasObstacle(-45, 45, 1.0);
            }
        });

        //Stop plan route
        findViewById(R.id.stopPlan).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                mNav.savePlanRoute(routeName);
            }
        });

        findViewById(R.id.openRadar).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "Open radar");
                mNav.setRadarState(true, new IChassisClient.ChassisResListener() {
                    @Override
                    public void onResponse(boolean status, int resultCode, Object result) {
                        Log.d(TAG, "setRadarState onResponse:  " + status
                                + "  " + resultCode + "  " + result);
                    }
                });
            }
        });

        findViewById(R.id.closeRadar).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "Close radar");
                mNav.setRadarState(false, new IChassisClient.ChassisResListener() {
                    @Override
                    public void onResponse(boolean status, int resultCode, Object result) {
                        Log.d(TAG, "setRadarState onResponse:  " + status
                                + "  " + resultCode + "  " + result);
                    }
                });
            }
        });

        findViewById(R.id.getRadarStatus).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "Get radar status");
                mNav.getRadarState(new IChassisClient.ChassisResListener() {
                    @Override
                    public void onResponse(boolean status, int resultCode, Object result) {
                        Log.d(TAG, "getRadarState: onResponse:  " + status
                                + "  " + resultCode + "  " + result);
                    }
                });
            }
        });

//        //Stop plan route
//        findViewById(R.id.danceTool).setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                startActivity(new Intent(MainActivity.this, DanceToolActivity.class));
//            }
//        });

        //Stop navigation
        findViewById(R.id.stopNavigation).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                mNav.stopPatrol();
            }
        });

        //Start navigation
        findViewById(startNavigation).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPreviousTime = System.currentTimeMillis();
                String intervalTime = mIntervalTimeEdit.getText().toString();
                if (!TextUtils.isEmpty(intervalTime)) {
                    mIntervalTime = Long.valueOf(intervalTime) * MINUTE;
                }
                final boolean isRepeat = mRepeat.isChecked();
//                mNav.startPatrol(routeName, isRepeat, new ChassisRelyApiImpl.PatrolListener() {
//                    @Override
//                    public void onNext(Pose pose) {
//
//                    }
//
//                    @Override
//                    public void onFinish() {
//
//                    }
//
//                    @Override
//                    public void onCanceled() {
//                    }
//
//                    @Override
//                    public void onAborted() {
//
//                    }
//                });
            }
        });

        findViewById(R.id.changeConfig).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String navIp = mNavIpEdit.getText().toString();
                if (TextUtils.isEmpty(navIp)) {
                    mNavIpEdit.setError(getString(R.string.nav_ip_empty));
                    return;
                }
                NavigationDataManager.getInstance().updateIpNavigation(navIp.trim());

                String rosIp = mRosIpEdit.getText().toString();
                if (!TextUtils.isEmpty(rosIp)) {
                    NavigationDataManager.getInstance().updateIpSdkRos(rosIp.trim());
                }
//                mNav.restart();
            }
        });

        findViewById(R.id.turnLeft).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (checkCharging()) {
                    return;
                }
                double angle = Double.MAX_VALUE;
                double speed = 0.4;
                if (!TextUtils.isEmpty(mADistanceEdit.getText().toString().trim())) {
                    angle = Double.valueOf(mADistanceEdit.getText().toString().trim());
                }
                if (!TextUtils.isEmpty(mASpeedEdit.getText().toString().trim())) {
                    speed = Double.valueOf(mASpeedEdit.getText().toString().trim());
                }

                mNav.turnLeft(Math.toRadians(angle), speed, 0, false, null);
//                mNav.turnLeft(Double.MAX_VALUE, Math.toRadians(40), false, null);
            }
        });

        findViewById(R.id.turnRight).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (checkCharging()) {
                    return;
                }
                double angle = Double.MAX_VALUE;
                double speed = 0.4;
                if (!TextUtils.isEmpty(mADistanceEdit.getText().toString().trim())) {
                    angle = Double.valueOf(mADistanceEdit.getText().toString().trim());
                }
                if (!TextUtils.isEmpty(mASpeedEdit.getText().toString().trim())) {
                    speed = Double.valueOf(mASpeedEdit.getText().toString().trim());
                }

                mNav.turnRight(Math.toRadians(angle), speed, 0, false, null);
//                mNav.turnRight(Double.MAX_VALUE, Math.toRadians(40), false, null);
            }
        });

        findViewById(R.id.forward).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (checkCharging()) {
                    return;
                }
                double distance = Double.MAX_VALUE;
                double speed = 0.2;
                if (!TextUtils.isEmpty(mLDistanceEdit.getText().toString().trim())) {
                    distance = Double.valueOf(mLDistanceEdit.getText().toString().trim());
                }
                if (!TextUtils.isEmpty(mLSpeedEdit.getText().toString().trim())) {
                    speed = Double.valueOf(mLSpeedEdit.getText().toString().trim());
                }

                mNav.forward(distance, speed, 0, null);
            }
        });

        findViewById(R.id.forward_avoid).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (checkCharging()) {
                    return;
                }
                double distance = Double.MAX_VALUE;
                double speed = 0.2;
                if (!TextUtils.isEmpty(mLDistanceEdit.getText().toString().trim())) {
                    distance = Double.valueOf(mLDistanceEdit.getText().toString().trim());
                }
                if (!TextUtils.isEmpty(mLSpeedEdit.getText().toString().trim())) {
                    speed = Double.valueOf(mLSpeedEdit.getText().toString().trim());
                }

                mNav.forward(distance, speed, 0, true, null);
            }
        });

        findViewById(R.id.backward).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (checkCharging()) {
                    return;
                }
                double distance = Double.MAX_VALUE;
                double speed = 0.2;
                if (!TextUtils.isEmpty(mLDistanceEdit.getText().toString().trim())) {
                    distance = Double.valueOf(mLDistanceEdit.getText().toString().trim());
                }
                if (!TextUtils.isEmpty(mLSpeedEdit.getText().toString().trim())) {
                    speed = Double.valueOf(mLSpeedEdit.getText().toString().trim());
                }

                mNav.backward(distance, speed, 0, null);
            }
        });

        findViewById(R.id.stop).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mNav.stopMove();
            }
        });

        findViewById(R.id.stop_direct).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mNav.motion(0, 0, 0, false);
            }
        });

        findViewById(R.id.setEstimateLost).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
//                mNav.setEstimateLost();
            }
        });

        findViewById(R.id.setEstimateRecovery).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
//                mNav.setEstimateRecovery();
            }
        });

        findViewById(R.id.switchMap).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final String mapName = mMapName.getText().toString();
                Log.d(TAG, "Rover switch map name : " + mapName);
//                mNav.switchMap(mapName, new ResponseListener() {
//                    @Override
//                    public void onResponse(boolean status, int resultCode, Object result) {
//                        Log.d(TAG, "Rover switch map  : " + status + "  result=" + result);
//                    }
//                });
            }
        });

        findViewById(R.id.locateVision).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                mNav.resetPoseEstimate(new ResponseListener() {
//                    @Override
//                    public void onResponse(boolean status, int resultCode, Object result) {
//                        Log.d(TAG, "rover vision locate status=" + status + " result=" + result);
//                    }
//                });
            }
        });

        findViewById(R.id.createMap).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                mNav.startCreatingMap(new ResponseListener() {
//                    @Override
//                    public void onResponse(boolean status, int resultCode, Object result) {
//                        Log.d(TAG, "Rover start create map  : " + status + "  result=" + result);
//                    }
//                });
            }
        });

        findViewById(R.id.stopCreateMap).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                final String mapName = mNewMapName.getText().toString();
//                Log.d(TAG, "Rover stop create map name : " + mapName);
//                mNav.stopCreatingMap(mapName, new CreateMapStop() {
//                    @Override
//                    public void onResult(boolean result, Object value) {
//                        super.onResult(result, value);
//                        Log.d(TAG, "Rover stop create map onResult: " + result + "  value:" + value);
//                    }
//
//                    @Override
//                    public void onStatusUpdate(int status, String value) {
//                        super.onStatusUpdate(status, value);
//                        Log.d(TAG, "Rover stop create map onStatusUpdate: " + status + "  value:" + value);
//                    }
//                });
            }
        });

        findViewById(R.id.switchNavigation).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
//                        mNav.switchMode(WorkMode.NAVIGATION);
                    }
                }).start();
            }
        });

        findViewById(R.id.switchManual).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
//                        mNav.switchMode(WorkMode.FREE);
                    }
                }).start();
            }
        });

        findViewById(R.id.getWorkingMode).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
//                        mNav.getWorkingMode(null);
                    }
                }).start();
            }
        });

        findViewById(R.id.cancelNavigation).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        mNav.cancelNavigation(null);
                    }
                }).start();
            }
        });

        findViewById(R.id.getMotionMode).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
//                        mNav.getMotionMode(new ResponseListener() {
//                            @Override
//                            public void onResponse(boolean status, int resultCode, final Object result) {
//                                runOnUiThread(new Runnable() {
//                                    @Override
//                                    public void run() {
//                                        Toast.makeText(MainActivity.this, (int)result + "" , Toast.LENGTH_SHORT).show();
//                                    }
//                                });
//                            }
//                        });
                    }
                }).start();
            }
        });

        mDeviceGroup = (RadioGroup) findViewById(R.id.deviceGroup);
        mGroundGroup = (RadioGroup) findViewById(R.id.groundGroup);
        mScenesGroup = (RadioGroup) findViewById(R.id.scenesGroup);
        mCamera = (CheckBox) findViewById(R.id.checkboxEnableCamera);
        mFishEye = (CheckBox) findViewById(R.id.enableFishEye);
        mIR = (CheckBox) findViewById(R.id.enableIR);
        mRgbd = (CheckBox) findViewById(R.id.enableRgbd);
        mSonar = (CheckBox) findViewById(R.id.enableSonar);

        findViewById(R.id.setRoverConfig).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                String rosIp = NavigationConfig.getLocalIp();
                boolean isCamera = mCamera.isChecked();
                boolean isFishEye = mFishEye.isChecked();
                boolean isIR = mIR.isChecked();
                boolean isSonar = mSonar.isChecked();
                boolean isRgbd = mRgbd.isChecked();

                int device;
                int deviceId = mDeviceGroup.getCheckedRadioButtonId();
                if (deviceId == R.id.radioDeviceTrex) {
                    device = 1;
                } else {
                    device = 4;
                }

                int ground;
                int groundId = mGroundGroup.getCheckedRadioButtonId();
                if (groundId == R.id.radioGroundHard) {
                    ground = 2;
                } else {
                    ground = 1;
                }

                int scenes;
                int scenesId = mScenesGroup.getCheckedRadioButtonId();
                if (scenesId == R.id.radioScenesNarrow) {
                    scenes = 1;
                } else {
                    scenes = 2;
                }

                RoverConfig roverConfig = new RoverConfig(rosIp);
                roverConfig.setEnableCamera(isCamera);
                roverConfig.setEnableSonar(isSonar);
                roverConfig.setDeviceType(device);
                roverConfig.setEnableFishEye(isFishEye);
                roverConfig.setEnableIR(isIR);
                roverConfig.setScenesType(scenes);
                mNav.setRoverConfig(roverConfig, null);
            }
        });

        findViewById(R.id.getRoverConfig).setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
//                mNav.getRoverConfig(new ResponseListener() {
//                    @Override
//                    public void onResponse(boolean status, int resultCode, Object result) {
//                        Log.d(TAG, "getRoverConfig : status= " + status + " || result= " + result);
//                    }
//                });
            }
        });

        findViewById(R.id.navigation_recharge_point).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "goRechargePointAction:navigation to ChargePos");
                if (checkCharging()) {
                    return;
                }
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        PlaceInfo placeBean = NavigationDataManager.getInstance().getPlaceByName(Definition.START_BACK_CHARGE_POSE);
                        Pose pose = DataManager.placeBeanToPose(placeBean);
                        Log.d(TAG, "goRechargePointAction:navigation to ChargePos: pose= " + (pose == null ? "null" : pose.toString()));
                        TargetPose target = new TargetPose(pose);

                        double obsDistance = Def.ROBOT_NAVIGATION_DEFAULT_OBS_DISTANCE;
                        if (!TextUtils.isEmpty(mObsDistanceEdit.getText().toString().trim())) {
                            obsDistance = Double.valueOf(mObsDistanceEdit.getText().toString().trim());
                        }
                        Log.d(TAG, "goRechargePointAction: obsDistance:" + obsDistance);
                        target.setBlockObsDistance(obsDistance);

                        target.setResponseListener(new TargetPose.ResponseListener() {
                            @Override
                            public void onResult(final int result, BaseEvent event) {
                                Log.d(TAG, "goRechargePointAction:navigation onResult:" + result);

                                runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        if (result != TargetPose.RESULT_REPLACE) {
                                            String content = getString(R.string.navigation_content,
                                                    result == TargetPose.RESULT_ARRIVED ? "到达" : "未到达");
                                            Toast.makeText(getBaseContext(),
                                                    content, Toast.LENGTH_SHORT).show();
                                        }
                                    }
                                });
                            }

                            @Override
                            public void onStatusUpdate(final int status, BaseEvent event) {
                                Log.d(TAG, "goRechargePointAction:navigation onStatusUpdate:" + status);
                                runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        String content = null;
                                        switch (status) {
                                            case STATUS_AVOID_END:
                                                content = "目标点在临时障碍物内，尝试靠近";
                                                break;

                                            case STATUS_OUT_MAP_END:
                                                content = "目标点在地图外，停止导航";
                                                break;

                                            default:
                                                break;
                                        }

                                        if (content != null) {
                                            Toast.makeText(getBaseContext(),
                                                    content, Toast.LENGTH_SHORT).show();
                                        }
                                    }
                                });
                            }
                        });
                        mNav.go(target, new IChassisClient.ChassisResListener() {
                            @Override
                            public void onResponse(final boolean status, int resultCode, Object result) {
                                Log.d(TAG, "goRechargePointAction:navigation status = " + status + "  resultCode = " + resultCode + "  result = " + result);

                                runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        String content = getString(R.string.goaction_content, status ? "开始" : "失败");
                                        Toast.makeText(getBaseContext(),
                                                content, Toast.LENGTH_SHORT).show();
                                    }
                                });
                            }
                        });
                    }
                }).start();
            }
        });

        findViewById(R.id.automatic_recharge).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "goCharge");
                if (checkCharging()) {
                    return;
                }
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        mNav.goCharge(false, new IChassisClient.ChassisResListener() {

                            @Override
                            public void onResponse(final boolean status, final int resultCode, final Object result) {
                                Log.d(TAG, "goCharge status:" + status
                                        + ", resultCode:" + resultCode + ", result:" + result);

                                runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        final String content;
                                        if (status) {
                                            if (resultCode == 0) {
                                                content = getString(R.string.charge_content, "开始");
                                            } else {
                                                content = getString(R.string.charge_content, "成功");
                                            }
                                        } else {
                                            content = getString(R.string.charge_content, "失败");
                                        }

                                        Toast.makeText(getBaseContext(),
                                                content, Toast.LENGTH_SHORT).show();
                                    }
                                });
                            }
                        });
                    }
                }).start();
            }
        });

        findViewById(R.id.get_system_info_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String chassisVersion = RoverClientFactory.version();
                Log.d(TAG, "RoverClientFactory getSystemInformation:" + chassisVersion);
                mNav.getSystemInformation(new IChassisClient.ChassisResListener() {
                    @Override
                    public void onResponse(boolean status, int resultCode, Object result) {
                        Log.d(TAG, "ICommandHandle getSystemInformation status:" + status + " resultCode:" + resultCode
                                + " result:" + result);
                    }
                });
            }
        });
        findViewById(R.id.transfer_targets_data_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                transferTargetDataToJson();
            }
        });
        findViewById(R.id.transfer_road_data_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                transferRoadJsonToData();
            }
        });
        findViewById(R.id.transfer_map_area_data_btn).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                transferMapAreaJson2Data();
            }
        });
        findViewById(R.id.setMapSyncState).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setMapSyncState();
            }
        });
        TextView currentDbMode = findViewById(R.id.current_db_mode);
        updateCurrentDbMode(currentDbMode);
        findViewById(R.id.change_db_mode).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                NavigationDataManager.getInstance().changeDbMode(ApplicationWrapper.getContext());
                updateCurrentDbMode(currentDbMode);
            }
        });
        findViewById(R.id.query_db_data).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                List<MapInfo> allMap = NavigationDataManager.getInstance().getAllMap();
                Log.d(TAG, "onClick: allMap.size()=" + allMap.size());
            }
        });
    }

    private void updateCurrentDbMode(TextView textView) {
        textView.setText(NavigationDataManager.getInstance().dbModeIsSqlite() ? "Sqlite" : "ObjectBox");
    }

    private void setMapSyncState() {
        String mapName = "1222One-1222114800";
        MapInfo mapInfo = NavigationDataManager.getInstance().getMapByName(mapName);
        int syncState = mapInfo.getSyncState();
        syncState = syncState == MapInfo.SyncState.UPLOADED ?
                MapInfo.SyncState.NOT_UPLOADED : MapInfo.SyncState.UPLOADED;
        mapInfo.setSyncState(syncState);
        NavigationDataManager.getInstance().updateMapInfo(mapInfo);
    }

    private void transferTargetDataToJson() {
        final String mapName = mMapName.getText().toString();
        Log.d(TAG, "transferTargetDataToJson mapName:" + mapName);
        if (TextUtils.isEmpty(mapName)) {
            return;
        }
        //        String mapName = "0917big-0918103311";
        MapUtils.transferTargetJson2Data(mapName);
    }

    private void transferRoadJsonToData() {
        final String mapName = mMapName.getText().toString();
        Log.d(TAG, "transferRoadJsonToData mapName:" + mapName);
        if (TextUtils.isEmpty(mapName)) {
            return;
        }
        //        String mapName = "0917big-0918103311";
        //        String mapName = "工位送餐-1012145302";
        MapUtils.transferRoadJson2Data(mapName);

    }

    private void transferMapAreaJson2Data() {
        final String mapName = mMapName.getText().toString();
        //final String mapName = "CT工位地图-0517112026";
        Log.d(TAG, " transferMapAreaJson2Data:: mapName:" + mapName);
        if (TextUtils.isEmpty(mapName)) {
            return;
        }
        MapUtils.transferMapAreaJson2Data(mapName);

    }

    private void transferRoadJsonToData_mapAreaConfig() {
        final String mapName = mMapName.getText().toString();
        Log.d(TAG, " transferRoadJsonToData_mapAreaConfig:: mapName:" + mapName);
        if (TextUtils.isEmpty(mapName)) {
            return;
        }
        //        String mapName = "0917big-0918103311";
        MapUtils.transferRoadJson2Data(mapName);

    }

    private void speak() {
        String speakId = UUID.randomUUID().toString();
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int min = calendar.get(Calendar.MINUTE);
        String content = getString(R.string.speak_content
                , String.valueOf(hour), String.valueOf(min));
        if (mSpeech != null) {
            mSpeech.speak(content, TextToSpeech.QUEUE_FLUSH, null, speakId);
        }
    }

    private boolean isTimeout() {
        return (System.currentTimeMillis() - mPreviousTime) > mIntervalTime;
    }


    public static String getIp() {
        try {
            Enumeration<NetworkInterface> nets = NetworkInterface.getNetworkInterfaces();
            while (nets.hasMoreElements()) {
                NetworkInterface net = nets.nextElement();
                Enumeration<InetAddress> addresses = net.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress inetA = addresses.nextElement();
                    if (inetA instanceof Inet4Address && !inetA.isLoopbackAddress()) {
                        return inetA.getHostAddress();
                    }
                }
            }
            return null;
        } catch (SocketException e) {
            return null;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        this.unregisterReceiver(mBatteryReceiver);
    }

    public void queryEstimate(View view) {
        if (mNav.isPoseEstimate()) {
            mEstimate.setText("定位成功");
        } else {
            mEstimate.setText("定位失败");
        }
    }

    public void ZipMapPKG(View view) {
        String mapName = "vv";
        Log.d(TAG, "ZipMapPKG: mapName=" + mapName);
        List<String> targetFile = new ArrayList<>();
        boolean mapZipResult = ZipUtil.zipFiles(targetFile, MapFileHelper.getMapZipPath(mapName), mapName);
        Log.d(TAG, "ZipMapPKG: mapZipResult=" + mapZipResult);
        if (!mapZipResult) {
            Log.d(TAG, "ZipMapPKG: Zip mapName.zip fail!");
            return;
        }
    }

    public void CopyFile(View view) {
        String mapName = "vv nn-0118161333";
        Log.d(TAG, "CopyFile: mapName=" + mapName);
        String srcPath = MapFileHelper.getMapFilePath(mapName);
        Log.d(TAG, "CopyFile: srcPath=" + srcPath);
        boolean result = TempCode.CopyFile(srcPath, MapFileHelper.SDCARD_PATH);
        Log.d(TAG, "CopyFile: result=" + result);
    }

    public boolean checkCharging() {
        if (isCharging) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    showChargingTips();
                }
            });
        }
        return isCharging;
    }

    public void registerBattery() {
        IntentFilter batteryFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        mBatteryReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                int plugged = intent.getIntExtra(android.os.BatteryManager.EXTRA_PLUGGED, -1);
                isCharging = plugged != 0;
            }
        };
        registerReceiver(mBatteryReceiver, batteryFilter);
    }

    public void showChargingTips() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this, R.style.TransparentAlertDialogStyle);
        builder.setMessage(R.string.charging_tips)
                .setPositiveButton(R.string.confirm, new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int id) {
                        // 点击 OK 按钮时的操作
                        dialog.dismiss();
                    }
                });

        builder.setCancelable(true);
        // 显示对话框
        AlertDialog alertDialog = builder.create();
        alertDialog.show();
    }
}
