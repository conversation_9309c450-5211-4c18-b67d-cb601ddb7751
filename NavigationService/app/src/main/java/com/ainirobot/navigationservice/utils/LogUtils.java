package com.ainirobot.navigationservice.utils;

import android.util.Log;

public class LogUtils {
    private static long preVelLogTime = System.currentTimeMillis();
    private static long preOnPoseLogTime = System.currentTimeMillis();
    private static long preLaserLogTime = System.currentTimeMillis();
    private static long preOdomLinearLogTime = System.currentTimeMillis();
    private static long preOdomAngularLogTime = System.currentTimeMillis();
    public final static int TYPE_VEL = 0;
    public final static int TYPE_POSE = 1;
    public final static int TYPE_LASER = 2;
    public final static int TYPE_ODOM_LINEAR = 3;
    public final static int TYPE_ODOM_ANGULAR = 4;

    public static void printLog(int type, String tag, String msg, long gapTime) {
        long timeNow = System.currentTimeMillis();
        switch (type) {
            case TYPE_VEL:
                if (timeNow - preVelLogTime > gapTime) {
                    preVelLogTime = timeNow;
                    Log.d(tag, msg);
                }
                break;
            case TYPE_POSE:
                if (timeNow - preOnPoseLogTime > gapTime) {
                    preOnPoseLogTime = timeNow;
                    Log.d(tag, msg);
                }
                break;

            case TYPE_LASER:
                if (timeNow - preLaserLogTime > gapTime) {
                    preLaserLogTime = timeNow;
                    Log.d(tag, msg);
                }
                break;

            case TYPE_ODOM_LINEAR:
                if (timeNow - preOdomLinearLogTime > gapTime) {
                    preOdomLinearLogTime = timeNow;
                    Log.d(tag, msg);
                }
                break;

            case TYPE_ODOM_ANGULAR:
                if (timeNow - preOdomAngularLogTime > gapTime) {
                    preOdomAngularLogTime = timeNow;
                    Log.d(tag, msg);
                }
                break;

            default:
                break;
        }
    }
}
