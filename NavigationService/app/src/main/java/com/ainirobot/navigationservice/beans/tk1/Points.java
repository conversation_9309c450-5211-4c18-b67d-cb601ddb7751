/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.beans.tk1;
import java.io.ByteArrayOutputStream;
import java.io.DataOutput;
import java.io.IOException;

/**
 * A set of point data, used to parse message from the navigation for ROS, The later will be deleted
 */
public class Points extends Message {

    private final int ROWS = 3;
    private byte[] data;
    private int columns = 0;

    public Points(int type) {
        super(type);
    }

    @Override
    public void writeTo(DataOutput out) throws IOException {
        out.writeInt(columns);
        out.writeLong(getLength());
        out.write(data);
    }

    @Override
    public void readData(ReverseInputStream in) throws IOException {
        columns = in.readInt();
        long destLength = in.readLong();

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int len;
        byte[] buffer = new byte[4096];
        while (destLength > 0) {
            len = buffer.length;
            if (destLength < len) {
                len = (int) destLength;
            }
            len = in.read(buffer,0,len);
            if (len != -1) {
                out.write(buffer,0,len);
                destLength -= len;
            }
        }

        data = out.toByteArray();
    }

    @Override
    public long getLength() {
        return data.length;
    }
}
