package com.ainirobot.navigationservice.db.helper.sqlite;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import androidx.annotation.NonNull;

import com.ainirobot.navigationservice.db.entity.MappingInfo;
import com.ainirobot.navigationservice.db.helper.iml.MappingInfoHelperIml;
import com.ainirobot.navigationservice.db.sqlite.TableInfoDef;
import com.ainirobot.navigationservice.db.sqlite.SqliteDbMigrate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MappingInfoSqliteHelper extends BaseSqliteHelper<MappingInfo> implements MappingInfoHelperIml {

    public MappingInfoSqliteHelper(SqliteDbMigrate sqliteDbMigrate) {
        super(sqliteDbMigrate, TableInfoDef.TABLE_NAME_MAPPING_INFO);
    }

    @Override
    protected Map<String, Integer> updateCursorIndexMap(Cursor cursor) {
        return sqliteDbMigrate.getMappingInfoIndex(cursor);
    }

    @NonNull
    @Override
    public List<MappingInfo> getMappingInfoByMapName(String[] mapNames) {
        SQLiteDatabase readDb = sqliteDbMigrate.getReadDb();
        List<MappingInfo> mappingInfoList = new ArrayList<>();
        Cursor cursor = null;
        try {
            if (mapNames == null || mapNames.length == 0) {
                cursor = readDb.query(mTableName, null, null, null, null, null, null);
            } else {
                String placeholders = new String(new char[mapNames.length - 1]).replace("\0", "?,") + "?";
                cursor = readDb.query(mTableName, null, TableInfoDef.COLUMN_MAP_NAME + " IN (" + placeholders + ")", mapNames, null, null, null);
            }
            if (cursor != null) {
                Map<String, Integer> map = getCursorIndexMap(cursor);
                while (cursor.moveToNext()) {
                    MappingInfo mappingInfo = sqliteDbMigrate.cursorToMappingInfo(cursor, map);
                    mappingInfoList.add(mappingInfo);
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return mappingInfoList;
    }

    @Override
    public void deleteMappingInfo(String mapName) {
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        int rowsDeleted = writeDb.delete(mTableName, TableInfoDef.COLUMN_MAP_NAME + " = ?", new String[]{mapName});
        Log.d(TAG, "deleteMappingInfo: " + rowsDeleted);
    }

    @Override
    public void initMappingData(List<MappingInfo> mappingList) {
        if (mappingList == null || mappingList.isEmpty()) {
            Log.d(TAG, "initMappingData: list is null");
            return;
        }
        Log.d(TAG, "initMappingData: start");
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        writeDb.beginTransaction();
        try {
            writeDb.delete(mTableName, null, null);
            for (MappingInfo mappingInfo : mappingList) {
                ContentValues values = sqliteDbMigrate.mappingInfoToContentValues(mappingInfo);
                writeDb.insert(mTableName, null, values);
                Log.d(TAG, "initMappingData: insert mappingInfo = " + mappingInfo);
            }
            writeDb.setTransactionSuccessful();
        } catch (Exception e) {
            Log.e(TAG, "initMappingData: ", e);
        } finally {
            writeDb.endTransaction();
        }
    }

    @Override
    public boolean updateMappingInfo(MappingInfo mappingInfo) {
        SQLiteDatabase writeDb = sqliteDbMigrate.getWriteDb();
        ContentValues values = sqliteDbMigrate.mappingInfoToContentValues(mappingInfo);
        String selection = TableInfoDef.COLUMN_MAP_NAME + " = ?";
        String[] selectionArgs = {String.valueOf(mappingInfo.getMapName())};
        // 尝试更新数据
        int rowsUpdated = writeDb.update(mTableName, values, selection, selectionArgs);
        // 如果更新失败（没有记录被更新），尝试插入新记录
        if (rowsUpdated == 0) {
            long rowId = writeDb.insert(mTableName, null, values);
            Log.d(TAG, "updateMappingInfo: insert rowId = " + rowId);
            return rowId != -1;
        } else {
            Log.d(TAG, "updateMappingInfo: update success");
            return true;
        }
    }

    @Override
    public List<MappingInfo> getMappingInfoByMapName(String mapName) {
        return getMappingInfoByMapName(new String[]{mapName});
    }
}
