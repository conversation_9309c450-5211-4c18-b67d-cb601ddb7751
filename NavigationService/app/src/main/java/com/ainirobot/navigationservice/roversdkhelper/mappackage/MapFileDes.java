package com.ainirobot.navigationservice.roversdkhelper.mappackage;

/**
 * 地图文件描述
 */
public class MapFileDes {

    /**
     * 名称，必传
     */
    private String name;

    /**
     * 相对路径，必传
     * 根目录：默认值 “”
     * 底盘文件生成目录："navi_data/"
     */
    private String relPath = "";

    /**
     * 名称，非必传
     */
    private String md5;

    /**
     * 大小，非必传
     */
    private long size;

    /**
     * 是否建图完成此文件在任何情况下都不会发生变化，非必传
     * 默认值 0，可能会发生变化
     * 1，不会发生变化
     */
    private int persistent = 0;

    public String getName() {
        return name;
    }

    public MapFileDes setName(String name) {
        this.name = name;
        return this;
    }

    public String getRelPath() {
        return relPath;
    }

    public MapFileDes setRelPath(String relPath) {
        this.relPath = relPath;
        return this;
    }

    public String getMd5() {
        return md5;
    }

    public MapFileDes setMd5(String md5) {
        this.md5 = md5;
        return this;
    }

    public long getSize() {
        return size;
    }

    public MapFileDes setSize(long size) {
        this.size = size;
        return this;
    }

    public int getPersistent() {
        return persistent;
    }

    public MapFileDes setPersistent(int persistent) {
        this.persistent = persistent;
        return this;
    }

    @Override
    public String toString() {
        return "MapPkgFile{" +
                "name='" + name + '\'' +
                ", relPath='" + relPath + '\'' +
                ", md5='" + md5 + '\'' +
                ", size=" + size +
                ", persistent=" + persistent +
                '}';
    }
}
