package com.ainirobot.navigationservice.chassisAbility.controller;

import android.util.Log;

import com.ainirobot.navigationservice.beans.tk1.Velocity;

import java.util.LinkedList;

public class MotionPIDPolicy {
    private static PID hpid = new PID(0.0068f,50.0f,50.0f,200);
    /**
     * PID跟踪算法
     */
    private static class PID {
        float Kp, Ti, Td;
        private long last_time = -1;
        private float last_err, accum_err;
        private LinkedList<DtErr> err_list;
        private long keep_span;

        class DtErr {
            long time;
            float error;

            DtErr(long time, float error) {
                this.time = time;
                this.error = error;
            }
        }

        public PID(float Kp, float Ti, float Td, long keep_span) {
            this.Kp = Kp;
            this.Ti = Ti;
            this.Td = Td;
            this.keep_span = keep_span;
            last_time = -1;
            err_list = new LinkedList<>();
        }

        public void updateParams(int pp, int pi, int pd) {
            if (pp > 0)
                Kp *= pp / 100.0f;
            if (pi > 0)
                Ti *= pi / 100.0f;
            if (pd > 0)
                Td *= pd / 100.0f;
        }

        public float next(float err) {
            long curr_time = System.currentTimeMillis();
            if (curr_time - last_time > 180) {
                last_err = err;
                last_time = curr_time;
                accum_err = 0;
                err_list.clear();
                return err * Kp;
            } else {
                float dt = (curr_time - last_time);
                float de = err - last_err;
                last_time = curr_time;
                last_err = err;
                // Remove old errors
                while (err_list.size() > 0 && curr_time - err_list.getFirst().time > keep_span) {
                    DtErr removed = err_list.removeFirst();
                    accum_err -= removed.error;
                }
                DtErr dterr = new DtErr(curr_time, dt * err);
                err_list.addLast(dterr);
                accum_err += dterr.error;
                return Kp * (err + accum_err / Ti + Td * de / dt);
            }
        }
    }
    public static Velocity follow(double angle,double latency){
        if (latency > 600 || Math.abs(angle) > 230 ) {
            return new Velocity(0, 0);
        }
        double angularSpeed;
        angularSpeed = hpid.next((float) angle);
       /* if (angle < 5.0d && angle > -5.0d) {
            angularSpeed = 0;
        }*/
        double  sign=-Math.signum(angle);
        angularSpeed=Math.abs(angularSpeed)*sign;
        if(Double.isNaN(angularSpeed)||Double.isInfinite(angularSpeed)){
            angularSpeed=0;
        }
        return new Velocity(0,angularSpeed);
    }
}
