package com.ainirobot.navigationservice.commonModule.bi;

import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.upload.bi.BiReport;
import com.ainirobot.navigationservice.beans.tk1.Event;
import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.beans.tk1.Statistic;
import com.ainirobot.navigationservice.beans.waiter.MultiRobotConfigBean;
import com.ainirobot.navigationservice.beans.waiter.SubDeviceBean;
import com.ainirobot.navigationservice.chassisAbility.controller.BasicMotionProcess;
import com.ainirobot.navigationservice.commonModule.bi.bean.BiCorrectLocationBean;
import com.ainirobot.navigationservice.commonModule.bi.bean.BiDeviceType;
import com.ainirobot.navigationservice.commonModule.bi.bean.BiPoseBean;
import com.ainirobot.navigationservice.commonModule.bi.report.BiCorrectLocationReport;
import com.ainirobot.navigationservice.commonModule.bi.report.BiMultiStatusReport;
import com.ainirobot.navigationservice.commonModule.bi.report.BiNaviSettingReport;
import com.ainirobot.navigationservice.commonModule.bi.report.BiNavigationLogReport;
import com.ainirobot.navigationservice.commonModule.bi.report.BiPoseReport;
import com.ainirobot.navigationservice.commonModule.bi.report.BiStopMoveReport;
import com.ainirobot.navigationservice.commonModule.bi.report.CreateMapResultReport;
import com.ainirobot.navigationservice.commonModule.bi.report.MileageReport;
import com.ainirobot.navigationservice.commonModule.bi.report.NaviDetectShakeReport;
import com.ainirobot.navigationservice.commonModule.bi.report.NavigationApiReport;
import com.ainirobot.navigationservice.commonModule.configuration.ConfigManager;
import com.ainirobot.navigationservice.commonModule.logs.beans.LogCacheBean;
import com.ainirobot.navigationservice.roversdkhelper.MapFileHelper;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static com.ainirobot.navigationservice.Defs.Def.TAGPRE;

public class BiManager {

    private final static String TAG = TAGPRE + BiManager.class.getSimpleName();

    private static final String DISCONNECT_EVENT = "event";
    public static final String CHARGE_PILE = "charge_pile";
    public static final String VISION = "vision";
    private static final String MATCH_ERR = "match_err";
    private static final String CHECK_ERR = "check_err";

    private String cmd = "";
    private static List<String> list = new ArrayList<>();
    private static List<Integer> pushEvent = new ArrayList<>();

    private static final int CORE_POOL_SIZE = 2;
    private static final int PERIOD_MINUTES = 10;

    private volatile double lastUploadMileageValue;

    private ScheduledExecutorService executorService;
    private ScheduledFuture uploadFuture;
    private AtomicInteger atomicInteger;
    private MileageReport mileageReport;
    private ArrayList<String> poseList = new ArrayList<>();
    //点位信息上报，每50条数据上报一次
    private static final int MAX_CACHE_POSE_NUM = 50;
    private Gson mGson = new Gson();
    //最近一次上报的点位信息
    private AtomicReference<Pose> mLastPose = new AtomicReference<>();

    static {
        list.add(Definition.CMD_NAVI_MOVE_DISTANCE_ANGLE);
        list.add(Definition.CMD_NAVI_MOVE_DIRECTION_ANGLE);
        list.add(Definition.CMD_NAVI_STOP_MOVE);
        list.add(Definition.CMD_NAVI_GET_LOCATION);
        list.add(Definition.CMD_NAVI_GET_LOCATION_HIGH_FREQUENCY);
        list.add(Definition.CMD_NAVI_IS_IN_LOCATION);
        list.add(Definition.CMD_NAVI_GET_PLACE_NAME);
        list.add(Definition.CMD_NAVI_GET_PLACE_LIST);
        list.add(Definition.CMD_NAVI_GET_PLACE_LIST_WITH_NAME);
        list.add(Definition.CMD_NAVI_GET_PLACELIST_WITH_NAMELIST);
        list.add(Definition.CMD_NAVI_IS_ESTIMATE);

        pushEvent.add(Event.LOCAL_GOAL_INVAILD);
//        pushEvent.add(Event.PATH_FAILED);
        pushEvent.add(Event.LOCAL_PATH_FAILED);
//        pushEvent.add(Event.GOAL_INVALID);
//        pushEvent.add(Event.GOAL_IS_DANGEROUS);
//        pushEvent.add(Event.ROBOT_IS_OUT);
        pushEvent.add(Event.DETECT_PEOPLE);
    }

    private static BiManager mInstance;
    private BiManager() {

    }

    public static synchronized BiManager getInstance() {
        if (mInstance == null) {
            mInstance = new BiManager();
        }
        return mInstance;
    }


    public void init() {
        BiReport.init();
        atomicInteger = new AtomicInteger(0);
        executorService = Executors.newScheduledThreadPool(CORE_POOL_SIZE, new ThreadFactory() {
            @Override
            public Thread newThread(@NonNull Runnable r) {
                return new Thread(r, "MileageUpload Thread-" + atomicInteger.incrementAndGet());
            }
        });
        mileageReport = new MileageReport();
    }

    public void doFixedRateTask() {
        if (uploadFuture != null) {
            uploadFuture.cancel(true);
        }
        uploadFuture = executorService.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                double totalDistance = BasicMotionProcess.getInstance().getTotalDistance();
                Log.i(TAG, "doFixedRateTask: currentMileage=" + totalDistance + ",lastUploadMileage=" + lastUploadMileageValue);
                double changeValue = totalDistance - lastUploadMileageValue;
                if (changeValue != 0) {
                    int changeInt = (int) changeValue;
                    if (changeInt > 0) {
                        mileageReport.clearReportDatas();
                        mileageReport.getMileage(changeInt)
                                .createTime()
                                .report();
                    }

                    lastUploadMileageValue = totalDistance;
                }
            }
        }, 0, PERIOD_MINUTES, TimeUnit.MINUTES);
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    private boolean isReportCmd(String cmd) {
        return cmd != null && !list.contains(cmd);
    }

    private boolean isReportEvent(int cmd) {
        return !pushEvent.contains(cmd);
    }

    public void pushNavigationReport(int statusCode, String msg) {
        if (!isReportEvent(statusCode)) {
            return;
        }
        reportError(NavigationApiReport.TYPE_EVENT
                , null
                , NavigationApiReport.ACTION_TYPE_EVENT
                , String.valueOf(statusCode)
                , msg);
    }

    public void pushNavigationReport(int statusCode, String actionType, String msg) {
        if (!isReportEvent(statusCode)) {
            return;
        }
        reportError(NavigationApiReport.TYPE_EVENT
                , null
                , actionType
                , String.valueOf(statusCode)
                , msg);
    }

    public void eventErrorNavigationReport(LogCacheBean bean, String errorId) {
        if (bean == null) {
            Log.w(TAG, "eventErrorNavigationReport: bean=null");
            return;
        }
        reportError(NavigationApiReport.TYPE_EVENT
                , null
                , NavigationApiReport.ACTION_TYPE_EVENT
                , null
                , bean.getType()
                , errorId);
    }

    public void disconnectNavigationReport(String msg) {
        if (TextUtils.isEmpty(msg)) {
            Log.e(TAG, "disconnectNavigationReport: msg is empty");
            return;
        }
        String reportMsg = DISCONNECT_EVENT.equals(msg)
                ? NavigationApiReport.MSG_EVENT_DISCONNECT
                : NavigationApiReport.MSG_CMD_DISCONNECT;

        reportError(NavigationApiReport.TYPE_CONNECT_STATE
                , NavigationApiReport.REQUEST_SOCKET_CHASSIS
                , NavigationApiReport.ACTION_TYPE_EVENT
                , null
                , reportMsg);
    }

    public void lostEventNavigationReport(boolean isOutOfMap, String msg) {
        String statusCode = isOutOfMap ? NavigationApiReport.STATUS_CODE_LOST_OUT_MAP
                : NavigationApiReport.STATUS_CODE_LOST;
        reportError(NavigationApiReport.TYPE_EVENT
                , null
                , NavigationApiReport.ACTION_TYPE_EVENT
                , statusCode
                , msg);
    }

    public void relocationFunctionReport(boolean isSuccess, String type, String reason) {
        String actionType = "";
        int statusCode = isSuccess ? NavigationApiReport.STATUS_CODE_RELOCATION_SUCCESS
                : NavigationApiReport.STATUS_CODE_RELOCATION_FAIL;
        String msg = getFailMsg(isSuccess,reason);

        switch (type) {
            case CHARGE_PILE:
                actionType = NavigationApiReport.ACTION_TYPE_RELOCATION_CHARGE_PILE;
                break;
            case VISION:
                actionType = NavigationApiReport.ACTION_TYPE_RELOCATION_VISION;
                break;
            default:
        }
        reportError(NavigationApiReport.TYPE_FUNCTION
                , NavigationApiReport.REQUEST_RELOCATION
                , actionType
                , statusCode
                , msg);
    }

    public void reportDetectShakeFromTk1() {
        NaviDetectShakeReport report = new NaviDetectShakeReport();
        report.report();
    }
    private String getFailMsg(boolean isSuccess, String reason) {
        if (TextUtils.isEmpty(reason)) {
            return isSuccess ? NavigationApiReport.MSG_RELOCATION_SUCCESS
                    : NavigationApiReport.MSG_RELOCATION_FAIL;
        } else {
            if (reason.equals(MATCH_ERR)) {
                return NavigationApiReport.REASON_MAP_ERROR;
            } else if (reason.equals(CHECK_ERR)) {
                return NavigationApiReport.REASON_CHECK_ERROR;
            }
        }
        return NavigationApiReport.REASON_OTHERS;
    }


    private void reportError(String type
            , String request
            , String actionType
            , Object statusCode
            , String msg
            , String errorId) {
        NavigationApiReport navigationApiReport = new NavigationApiReport();
        navigationApiReport.addType(type)
                .addRequest(request)
                .addActionType(actionType)
                .addStatusCode(statusCode)
                .addMsg(msg)
                .addErrorId(errorId)
                .report();
    }

    private void reportError(String type
            , String request
            , String actionType
            , Object statusCode
            , String msg) {
        reportError(type, request, actionType, statusCode, msg, null);
    }


    public void reportNavigationLog(Statistic statistic) {
        BiNavigationLogReport biNavigationLogReport = new BiNavigationLogReport();
        biNavigationLogReport.addInt1(statistic.getInt1())
                .addInt2(statistic.getInt2())
                .addInt3(statistic.getInt3())
                .addInt4(statistic.getInt4())
                .addDouble1(statistic.getDouble1())
                .addDouble2(statistic.getDouble2())
                .addDouble3(statistic.getDouble3())
                .addDouble4(statistic.getDouble4())
                .addType(statistic.getType())
                .addStrValue(statistic.getStrValue())
                .report();
    }

    public void reportRGBDStatus(int mode, int serverMode) {
        try {
            BiNaviSettingReport naviReport = new BiNaviSettingReport();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(BiNaviSettingReport.MODE, mode);
            jsonObject.put(BiNaviSettingReport.SERVER_SWITCH, serverMode);
            naviReport.reportRgbdStatus(jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public void reportRgbdAvoidObs(int mode, int serverMode){
        try {
            BiNaviSettingReport naviReport = new BiNaviSettingReport();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(BiNaviSettingReport.MODE, mode);
            jsonObject.put(BiNaviSettingReport.SERVER_SWITCH, serverMode);
            naviReport.reportRgbdAvoidObsStatus(jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public void reportMultiStatus(boolean isMulti){
        BiMultiStatusReport biMultiStatusReport = new BiMultiStatusReport();
        biMultiStatusReport.addMultiStatus(isMulti)
            .report();
    }

    //清空上次上报的点位信息
    public void resetLastPose() {
        mLastPose.set(null);
    }

    public void reportPoseData(Pose pose) {
        if (pose == null) {
            return;
        }

        //判断前后两个点位的移动距离是否大于0.1米，如果大于0.1米则上报
        Pose lastPose = mLastPose.get();
        if (lastPose != null) {
            double distance = Math.sqrt(Math.pow(pose.getX() - lastPose.getX(), 2)
                    + Math.pow(pose.getY() - lastPose.getY(), 2));
            if (distance < 0.1) {
                return;
            }
        }
        mLastPose.set(pose);
//        Log.d(TAG, "reportPoseData: Add pose: " + pose);

        if (poseList == null) {
            poseList = new ArrayList<>();
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("x", pose.getX());
            jsonObject.put("y", pose.getY());
            jsonObject.put("theta", pose.getTheta());
            jsonObject.put("status", pose.getStatus());
            jsonObject.put("time", pose.getTime());
            poseList.add(jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        if (!poseList.isEmpty() && poseList.size() >= MAX_CACHE_POSE_NUM){
            BiPoseReport poseReport = new BiPoseReport();
            String poseData = mGson.toJson(poseList);
            poseReport.addLocationData(poseData)
                    .report();
            Log.d(TAG, "reportPoseData: Do report!");
            poseList.clear();
        }
    }

    public void reportChassisCorrectLocation(BiCorrectLocationBean switchBean){
        BiCorrectLocationReport locationReport = new BiCorrectLocationReport();
        locationReport.addGuessPose(mGson.toJson(switchBean.getGuess_pose()))
                .addVisionPose(mGson.toJson(switchBean.getVision_pose()))
                .addVisionRefinePose(mGson.toJson(switchBean.getVision_refine_pose()))
                .addLocateType(switchBean.getLocate_type())
                .addLostDistance(switchBean.getLost_distance())
                .addArg1(switchBean.getArg1())
                .addArg2(switchBean.getArg2())
                .addArg3(switchBean.getArg3())
                .addArg4(switchBean.getArg4())
                .addMessage(switchBean.getMessage())
                .report();
    }

    public void reportCreateMapResult(String mapName, int errorCode, int targetTotalNum, int targetSuccNum, int targetErrorNum, String targetErrorInfo, String targetData){
        CreateMapResultReport createMapResultReport = new CreateMapResultReport();
        createMapResultReport.mapName(mapName)
                .mapType(0)
                .errorCode(errorCode)
                .targetTotalNum(targetTotalNum)
                .targetSuccNum(targetSuccNum)
                .targetErrorNum(targetErrorNum)
                .targetErrorInfo(targetErrorInfo)
                .targetsData(targetData)
                .createTime()
                .report();
    }


    public void reportRobotStopMoving(Pose startPose, Pose stopPose){
        BiStopMoveReport stopMoveReport = new BiStopMoveReport();
        BiPoseBean startPostBean = new BiPoseBean(startPose.getX(), startPose.getY(), startPose.getTheta());
        BiPoseBean stopPostBean = new BiPoseBean(stopPose.getX(), stopPose.getY(), stopPose.getTheta());
        stopMoveReport.addStartPose(mGson.toJson(startPostBean))
                .addStopPose(mGson.toJson(stopPostBean))
                .addErrorStartTime(startPose.getTime())
                .addCTime(stopPose.getTime())
                .report();
    }

    /**
     * 上报多机相关配置信息，设置成功时上报
     * @param loraConfig
     */
    public void reportMultiRobotSetting(MultiRobotConfigBean loraConfig){
        try {
            String configInfo = mGson.toJson(loraConfig);
            BiNaviSettingReport naviReport = new BiNaviSettingReport();
            naviReport.reportMultiRobotSetting(configInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 上报设备类型
     */
    public void reportDeviceType(){
        try {
            int deviceType = ConfigManager.getInstance().getSubDeviceTypeNumber();
            SubDeviceBean subDeviceInfo = MapFileHelper.getSubDeviceInfo();
            BiDeviceType deviceTypeBean = new BiDeviceType(deviceType, subDeviceInfo);
            String deviceTypeStr = mGson.toJson(deviceTypeBean);
            Log.d(TAG, "reportDeviceType: " + deviceTypeStr);
            BiNaviSettingReport naviReport = new BiNaviSettingReport();
            naviReport.reportNaviDevType(deviceTypeStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
