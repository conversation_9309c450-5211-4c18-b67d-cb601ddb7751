package com.ainirobot.navigationservice.beans.waiter;

import com.ainirobot.navigationservice.beans.tk1.Pose;

import java.util.ArrayList;
import java.util.List;

/**
 * 导航路线信息
 */
public class NaviPathDetail {
    private NaviPathInfo pathInfo;
    /**
     * 导航路径
     */
    private List<Vector2d> pathList;

    public NaviPathInfo getPathInfo() {
        return pathInfo;
    }

    public void setPathInfo(NaviPathInfo pathInfo) {
        this.pathInfo = pathInfo;
    }

    public List<Vector2d> getPathList() {
        return pathList;
    }

    public void setPathList(List<Vector2d> pathList) {
        this.pathList = pathList;
    }

}
