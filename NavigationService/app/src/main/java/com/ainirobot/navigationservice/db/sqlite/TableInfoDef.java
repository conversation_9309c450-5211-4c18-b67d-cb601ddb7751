package com.ainirobot.navigationservice.db.sqlite;

/**
 * @version V1.0.0
 * @date 2020/7/20 17:39
 */
public class TableInfoDef {

    /**
     * 点位信息表
     */
    public static final String TABLE_NAME_PLACE_INFO = "place_info";
    public static final String COLUMN_PLACE_ID = "place_id";
    public static final String COLUMN_ICON_URL = "icon_url";
    public static final String COLUMN_PLACE_TYPE = "place_type";
    public static final String COLUMN_PLACE_STATUS = "place_status";
    public static final String COLUMN_POINT_THETA = "point_theta";
    public static final String COLUMN_POINT_X = "point_x";
    public static final String COLUMN_POINT_Y = "point_y";
    public static final String COLUMN_UPDATE_TIME = "update_time";
    public static final String COLUMN_MAP_NAME = "map_name";
    public static final String COLUMN_ALIAS = "alias";
    public static final String COLUMN_IGNORE_DISTANCE = "ignore_distance";
    public static final String COLUMN_SAFE_DISTANCE = "safe_distance";
    public static final String COLUMN_NO_DIRECTIONAL_PARKING = "no_directional_parking";
    public static final String COLUMN_TYPE_ID = "type_id";
    public static final String COLUMN_PRIORITY = "priority";
    public static final String TABLE_NAME_PLACE_INFO_BACKUP = TABLE_NAME_PLACE_INFO + "_backup";
    public static final String TABLE_NAME_PLACE_TYPE = "place_type";

    /**
     * 地点名称表
     */
    public static final String TABLE_NAME_PLACE_NAME = "place_name";
    public static final String COLUMN_PLACE_NAME = "place_name";
    public static final String COLUMN_LANGUAGE_TYPE = "language_type";

    /**
     * 地图信息表
     */
    public static final String TABLE_NAME_MAP_INFO = "map_info";
    public static final String COLUMN_MAP_ID = "map_id";//只本地使用，不上传云端
    public static final String COLUMN_MAP_TYPE = "map_type";
    public static final String COLUMN_USE_STATE = "use_state";
    public static final String COLUMN_MAP_PATH = "map_path";
    public static final String COLUMN_SYNC_STATE = "sync_state";
    public static final String COLUMN_MAP_MD5 = "map_md5";
    public static final String COLUMN_MAP_UUID = "map_uuid";//云端生成，全网唯一
    public static final String COLUMN_FORBID_LINE = "forbid_line";
    public static final String COLUMN_MAP_LANGUAGE = "map_language";
    public static final String COLUMN_CREATE_TIME = "create_time";
    public static final String COLUMN_FINISH_STATE = "finish_state";
    public static final String COLUMN_PATROL_ROUTE = "patrol_route";
    public static final String COLUMN_POSE_ESTIMATE = "pose_estimate";
    public static final String COLUMN_NAVI_MAP_NAME = "navi_map_name";
    public static final String COLUMN_HAS_TARGET_DATA = "target_data";
    public static final String COLUMN_MAP_VERSION = "map_version";
    public static final String COLUMN_MAP_TRANSIT_MAX_WIDTH = "map_transit_max_width";

    /**
     * 底盘信息表
     */
    public static final String TABLE_NAME_CHASSIS_INFO = "chassis_info";
    public static final String COLUMN_ROVER_CONFIG = "rover_config";
    public static final String COLUMN_IP_NAVIGATION = "ip_navigation";
    public static final String COLUMN_IP_ROS = "ip_ros";
    public static final String COLUMN_IP_SDK_ROS = "ip_sdk_ros";
    public static final String COLUMN_SERVER_IP = "server_ip";
    public static final String COLUMN_LORA_CONFIG = "lora_config"; //存储多机配置信息（Lora,Esp32配置信息）

    /**
     * 特殊点位映射配置表
     */
    public static final String TABLE_NAME_MAPPING_INFO = "mapping_info";
    public static final String COLUMN_PLACE_CN_NAME = "place_cn_name"; //特殊点中文名
    public static final String COLUMN_MAPPING_POSE_ID = "mapping_pose_id"; //映射点id
    public static final String COLUMN_POSE_PRIORITY = "pose_priority"; //映射点优先级（预留给补位使用）

    /**
     * 多楼层地图配置信息表
     */
    public static final String TABLE_NAME_MULTI_FLOOR_INFO = "multi_floor_info";
    public static final String COLUMN_MULTI_FLOOR_ID = "floor_id"; //id 主键
    public static final String COLUMN_MULTI_FLOOR_INDEX = "floor_index"; //映射id
    public static final String COLUMN_MULTI_FLOOR_ALIAS = "floor_alias"; //楼层名称
    public static final String COLUMN_MULTI_FLOOR_STATE = "floor_state"; //楼层类型（1：主楼层）
    public static final String COLUMN_MULTI_MAP_ID = "map_id"; //地图id   外键
    public static final String COLUMN_MULTI_MAP_NAME = "map_name"; //地图名称   外键
    public static final String COLUMN_MULTI_MAP_UPDATE_TIME = "map_update_time"; //该地图最后更新时间
    public static final String COLUMN_MULTI_CREATE_TIME = "create_time"; //创建时间
    public static final String COLUMN_MULTI_UPDATE_TIME = "update_time"; //更新时间
    public static final String COLUMN_MULTI_AVAILABLE_ELEVATORS = "available_elevators"; //该地图可用的电梯名

    /**
     *视觉信息表
     */
    public static final String TABLE_NAME_EXTRA_INFO = "extra_info "; //视觉信息表名
    public static final String COLUMN_VISION_MAP_NAME = "map_name"; //外键，使用mapName是为了便于查看数据
    public static final String COLUMN_VISION_ID = "extra_id"; //视觉文件id，上传云端后，云端返回此id
    public static final String COLUMN_VISION_MD5 = "extra_md5"; //文件md5值
}
