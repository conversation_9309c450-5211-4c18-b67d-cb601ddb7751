package com.ainirobot.navigationservice.business.rpc;

import androidx.annotation.NonNull;
import android.util.Log;

import com.ainirobot.navigationservice.business.rpc.algorithm.MotionAlgorithmFactory;

import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * motion control
 *
 * @version V1.0.0
 * @date 2019/11/15 15:00
 */
public class MotionControl {
    private static final String TAG = "MotionControl";

    private static final AtomicReference<MotionControl> INSTANCE = new AtomicReference<>();
    private static final int MAX_CAPACITY = 2;
    private ThreadPoolExecutor consumer;
    private LinkedBlockingDeque<SpeedBean> deque = new LinkedBlockingDeque<>(MAX_CAPACITY);
    private Future future;
    private SpeedBean lastTarget;
    private ThreadPoolExecutor motionExecutor;

    private MotionControl() {
        initThreadPool();
        this.consumer.execute(new Runnable() {
            @Override
            public void run() {
                MotionControl.this.consumeEvent();
            }
        });
    }

    public static MotionControl getInstance() {
        MotionControl motionControl;
        for (; ; ) {
            motionControl = INSTANCE.get();
            if (motionControl != null) {
                return motionControl;
            }
            motionControl = new MotionControl();
            if (INSTANCE.compareAndSet(null, motionControl)) {
                return motionControl;
            }
        }
    }

    public synchronized void motion(SpeedBean target) {
        Log.i(TAG, "motion: product:" + target);
        if (target == null) {
            Log.w(TAG, "motion: illegal speed");
            return;
        }
        if (deque.isEmpty()) {
            deque.offer(target);
            Log.i(TAG, "motion: queue is empty offer");
            return;
        }
        SpeedBean last = deque.peekLast();
        if (target.equals(last)) {
            Log.i(TAG, "motion: target equals last,ignore");
            return;
        }

        if (deque.size() == MAX_CAPACITY) {
            Log.i(TAG, "motion: deque is full");
            SpeedBean drop = deque.pollFirst();
            deque.offer(target);
            Log.i(TAG, "motion: update last, drop speed=" + drop);
            return;
        }

        deque.offer(target);
        Log.i(TAG, "motion: target speed offer");
    }

    private void consumeEvent() {
        try {
            for (; ; ) {
                SpeedBean speedBean = this.deque.pollFirst(Integer.MAX_VALUE, TimeUnit.DAYS);
                if (speedBean.equals(this.lastTarget)) {
                    Log.i(TAG, "consumeEvent: target equal last target");
                    continue;
                }
                lastTarget = speedBean;
                executeMotion(speedBean);
            }
        } catch (InterruptedException interruptedException) {
            Log.e(TAG, "consumer thread intercept!");
        }
    }

    private ThreadFactory createFactory(final String threadName) {
        return new ThreadFactory() {
            @Override
            public Thread newThread(@NonNull Runnable param1Runnable) {
                return new Thread(param1Runnable, threadName);
            }
        };
    }

    private void executeMotion(SpeedBean paramSpeedBean) {
        Log.i(TAG, "consumer:" + paramSpeedBean);
        if (future != null && !future.isDone() && !this.future.isCancelled()) {
            boolean cancelResult = this.future.cancel(true);
            motionExecutor.purge();
            Log.i(TAG, "executeMotion: last motion task canceled : cancelResult=" + cancelResult);
        }
        future = motionExecutor.submit(new MotionRunnable(paramSpeedBean));
    }


    private void initThreadPool() {
        consumer = new ThreadPoolExecutor(1
                , 1
                , 0L
                , TimeUnit.SECONDS
                , new LinkedBlockingQueue<Runnable>(1)
                , createFactory("motion_consumer_thread"));
        motionExecutor = new ThreadPoolExecutor(1
                , 1
                , 0L
                , TimeUnit.SECONDS
                , new LinkedBlockingQueue<Runnable>(1)
                , createFactory("motion_executor_thread"));
    }


    static class MotionRunnable implements Runnable {

        private SpeedBean target;

        public MotionRunnable(SpeedBean param1SpeedBean) {
            this.target = param1SpeedBean;
        }

        @Override
        public void run() {
            MotionAlgorithmFactory.check(target).motion();
        }
    }

}