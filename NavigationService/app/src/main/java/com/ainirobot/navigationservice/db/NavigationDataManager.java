package com.ainirobot.navigationservice.db;

import static com.ainirobot.navigationservice.commonModule.data.DataManager.DEFAULT_LANGUAGE;

import android.content.Context;
import android.os.Build;
import android.os.FileObserver;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.ainirobot.coreservice.bean.GateLineNode;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.GateLineUnit;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.actionbean.RoadGraphEdgePixelBean;
import com.ainirobot.coreservice.client.hardware.RobotCore;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.navigationservice.Defs.Def;
import com.ainirobot.navigationservice.beans.tk1.RoverConfig;
import com.ainirobot.navigationservice.beans.waiter.MultiRobotConfigBean;
import com.ainirobot.navigationservice.commonModule.data.DataManager;
import com.ainirobot.navigationservice.commonModule.data.MapVersionManager;
import com.ainirobot.navigationservice.commonModule.data.utils.UpdateTimeUtils;
import com.ainirobot.navigationservice.commonModule.data.utils.UuidUtils;
import com.ainirobot.navigationservice.db.entity.ChargeArea;
import com.ainirobot.navigationservice.db.entity.ExtraInfo;
import com.ainirobot.navigationservice.db.entity.GateRelationInfo;
import com.ainirobot.navigationservice.db.entity.LocalPlaceInfo;
import com.ainirobot.navigationservice.db.entity.MapInfo;
import com.ainirobot.navigationservice.db.entity.MappingInfo;
import com.ainirobot.navigationservice.db.entity.MultiFloorInfo;
import com.ainirobot.navigationservice.db.entity.MyObjectBox;
import com.ainirobot.navigationservice.db.entity.PlaceInfo;
import com.ainirobot.navigationservice.db.entity.PlaceName;
import com.ainirobot.navigationservice.db.helper.iml.ChassisInfoHelperIml;
import com.ainirobot.navigationservice.db.helper.iml.ExtraInfoHelperIml;
import com.ainirobot.navigationservice.db.helper.iml.GateRelationInfoHelperIml;
import com.ainirobot.navigationservice.db.helper.iml.LocalPlaceInfoIml;
import com.ainirobot.navigationservice.db.helper.iml.MapInfoHelperIml;
import com.ainirobot.navigationservice.db.helper.iml.MappingInfoHelperIml;
import com.ainirobot.navigationservice.db.helper.iml.MultiFloorInfoHelperIml;
import com.ainirobot.navigationservice.db.helper.iml.PlaceInfoHelperIml;
import com.ainirobot.navigationservice.db.helper.iml.PlaceNameHelperIml;
import com.ainirobot.navigationservice.db.helper.iml.PlaceTypeHelperIml;
import com.ainirobot.navigationservice.db.helper.objectbox.ChargeAreaObjectHelper;
import com.ainirobot.navigationservice.db.helper.objectbox.ChassisInfoObjectHelper;
import com.ainirobot.navigationservice.db.helper.objectbox.ExtraInfoObjectHelper;
import com.ainirobot.navigationservice.db.helper.objectbox.GateRelationInfoObjectHelper;
import com.ainirobot.navigationservice.db.helper.objectbox.LocalPlaceInfoObjectHelper;
import com.ainirobot.navigationservice.db.helper.objectbox.MapInfoObjectHelper;
import com.ainirobot.navigationservice.db.helper.objectbox.MappingInfoObjectHelper;
import com.ainirobot.navigationservice.db.helper.objectbox.MultiFloorInfoObjectHelper;
import com.ainirobot.navigationservice.db.helper.objectbox.PlaceInfoObjectHelper;
import com.ainirobot.navigationservice.db.helper.objectbox.PlaceNameObjectHelper;
import com.ainirobot.navigationservice.db.helper.objectbox.PlaceTypeObjectHelper;
import com.ainirobot.navigationservice.db.helper.sqlite.ChassisInfoSqliteHelper;
import com.ainirobot.navigationservice.db.helper.sqlite.ExtraInfoSqliteHelper;
import com.ainirobot.navigationservice.db.helper.sqlite.MapInfoSqliteHelper;
import com.ainirobot.navigationservice.db.helper.sqlite.MappingInfoSqliteHelper;
import com.ainirobot.navigationservice.db.helper.sqlite.MultiFloorInfoSqliteHelper;
import com.ainirobot.navigationservice.db.helper.sqlite.PlaceInfoSqliteHelper;
import com.ainirobot.navigationservice.db.helper.sqlite.PlaceNameSqliteHelper;
import com.ainirobot.navigationservice.db.sqlite.MapDbOpenHelper;
import com.ainirobot.navigationservice.db.sqlite.SqliteDbMigrate;
import com.ainirobot.navigationservice.roversdkhelper.MapFileHelper;
import com.ainirobot.navigationservice.roversdkhelper.maptype.MapTypeHelper;
import com.ainirobot.navigationservice.utils.CovertUtils;
import com.ainirobot.navigationservice.utils.GsonUtil;
import com.ainirobot.navigationservice.utils.MapUtils;
import com.ainirobot.navigationservice.utils.PosSeparateUtils;
import com.ainirobot.navigationservice.utils.SharedPrefUtil;
import com.ainirobot.navigationservice.utils.SpecialPlaceUtil;
import com.google.gson.Gson;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.function.Function;
import java.util.LinkedHashMap;

import io.objectbox.BoxStore;
import io.objectbox.reactive.DataObserver;

public class NavigationDataManager {

    private static final String TAG = Def.MAP_DB_PRE + NavigationDataManager.class.getSimpleName();
    private static final int DB_MODE_SQLITE = 1;
    private static final int DB_MODE_OBJECT_BOX = 0;
    private static final String KEY_DB_MODE = "db_mode";
    private static final String KEY_IS_FINISH_MIGRATION = "is_finish_migration";

    private static volatile NavigationDataManager sInstance;
    private BoxStore mBoxStore;
    private static volatile String sMapName;
    private ChassisInfoHelperIml mChassisInfoHelper;
    private ExtraInfoHelperIml mExtraInfoHelper;
    private MapInfoHelperIml mMapInfoHelper;
    private MappingInfoHelperIml mMappingInfoHelper;
    private MultiFloorInfoHelperIml mMultiFloorInfoHelper;
    private ChargeAreaObjectHelper mChargeAreaObjectHelper;
    private PlaceInfoHelperIml mPlaceInfoHelper;
    private PlaceNameHelperIml mPlaceNameHelper;
    private PlaceTypeHelperIml mPlaceTypeHelper;
    private LocalPlaceInfoIml mLocalPlaceInfoHelper;

    private GateRelationInfoHelperIml mGateRelationInfoHelper;

    private int mDbMode;
    private SqliteDbMigrate mDbMigrate;
    private Context mContext;

    private NavigationDataManager() {
    }

    public static NavigationDataManager getInstance() {
        if (null == sInstance) {
            synchronized (NavigationDataManager.class) {
                if (null == sInstance) {
                    sInstance = new NavigationDataManager();
                }
            }
        }
        return sInstance;
    }

    public void init(Context context) {
        mContext = context;
        mDbMode = SharedPrefUtil.getInt(context, KEY_DB_MODE, DB_MODE_OBJECT_BOX);

        //objectbox备份数据恢复
        ObjectBoxVersionManager.getInstance().init(context);
        ObjectBoxVersionManager.getInstance().restoreData();

        Log.d(TAG, "init:Debug: dbMode=" + mDbMode);
        if (dbModeIsSqlite()) {
            mDbMigrate = new SqliteDbMigrate(context);
            initSqliteHelper(mDbMigrate);
        } else {
            mBoxStore = getBoxStore();
            initObjectHelper(mBoxStore);
            initObjectBoxData(context);
        }

        PosSeparateUtils.listenNapPosSeparate(context);
    }

    public BoxStore getBoxStore() {
        Log.d(TAG, "getBoxStore:Debug: dbModeIsSqlite=" + dbModeIsSqlite());
        // 未初始化时，则初始化
        if (null == mBoxStore) {
            Log.d(TAG, "getBoxStore:Debug: init BoxStore");
            mBoxStore = MyObjectBox.builder()
                    .androidContext(mContext.getApplicationContext())
                    .build();
            mBoxStore.startObjectBrowser(8091);
        }
        return mBoxStore;
    }

    public void changeDbMode(Context context) {
        if (dbModeIsSqlite()) {
            SharedPrefUtil.putInt(context, KEY_DB_MODE, DB_MODE_OBJECT_BOX);
        } else {
            SharedPrefUtil.putInt(context, KEY_DB_MODE, DB_MODE_SQLITE);
        }
        closeLastDb();
        init(context);
    }

    public void closeLastDb() {
        if (null != mDbMigrate) {
            Log.d(TAG, "closeLastDb: close mDbMigrate");
            mDbMigrate.close();
            mDbMigrate = null;
        }
        if (null != mBoxStore) {
            Log.d(TAG, "closeLastDb: close mBoxStore");
            mBoxStore.close();
            mBoxStore = null;
        }
    }

    public boolean dbModeIsSqlite() {
        return dbModeIsSqlite(mDbMode);
    }

    private boolean dbModeIsSqlite(int dbMode) {
        return dbMode == DB_MODE_SQLITE;
    }

    private void initSqliteHelper(SqliteDbMigrate dbMigrate) {
        Log.d(TAG, "initSqliteHelper");
        mChassisInfoHelper = new ChassisInfoSqliteHelper(dbMigrate);
        mExtraInfoHelper = new ExtraInfoSqliteHelper(dbMigrate);
        mMapInfoHelper = new MapInfoSqliteHelper(dbMigrate);
        mMappingInfoHelper = new MappingInfoSqliteHelper(dbMigrate);
        mMultiFloorInfoHelper = new MultiFloorInfoSqliteHelper(dbMigrate);
        mPlaceInfoHelper = new PlaceInfoSqliteHelper(dbMigrate);
        mPlaceNameHelper = new PlaceNameSqliteHelper(dbMigrate);
    }

    private void initObjectHelper(BoxStore boxStore) {
        Log.d(TAG, "initObjectHelper");
        mChassisInfoHelper = new ChassisInfoObjectHelper(boxStore);
        mExtraInfoHelper = new ExtraInfoObjectHelper(boxStore);
        mMapInfoHelper = new MapInfoObjectHelper(boxStore);
        mMappingInfoHelper = new MappingInfoObjectHelper(boxStore);
        mMultiFloorInfoHelper = new MultiFloorInfoObjectHelper(boxStore);
        mChargeAreaObjectHelper = new ChargeAreaObjectHelper(boxStore);
        mPlaceInfoHelper = new PlaceInfoObjectHelper(boxStore);
        mPlaceNameHelper = new PlaceNameObjectHelper(boxStore);
        mPlaceTypeHelper = new PlaceTypeObjectHelper(boxStore);
        mLocalPlaceInfoHelper = new LocalPlaceInfoObjectHelper(boxStore);
        mGateRelationInfoHelper = new GateRelationInfoObjectHelper(boxStore);
    }

    public void initObjectBoxData(Context context) {
        boolean isFinishMigration = SharedPrefUtil.getBoolean(context, KEY_IS_FINISH_MIGRATION, false);
        File targetDbFile = context.getDatabasePath(MapDbOpenHelper.DB_NAME);
        boolean existDb = targetDbFile.exists();
        Log.d(TAG, "initObjectBoxData: isFinishMigration=" + isFinishMigration + " existDb=" + existDb);
        if (isFinishMigration || !existDb) {
            Log.d(TAG, "initObjectBoxData no db data");
            SpecialPlaceUtil.getInstance().init(context);
            // 初始化PlaceInfo中的typeId和priority
            initTypeIdAndPriority();
            initLocalPlaceInfoData();
            return;
        }
        SpecialPlaceUtil.getInstance().init(context);
        mDbMigrate = new SqliteDbMigrate(context);
        long startTime = System.currentTimeMillis();
        mChassisInfoHelper.initChassisInfoData(context, mDbMigrate.getDbChassisInfoList());
        mMapInfoHelper.initMapInfoData(mDbMigrate.getDbMapInfoList());

        List<PlaceInfo> placeInfoList = mDbMigrate.getDbPlaceList();
        List<PlaceName> placeNameList = mDbMigrate.getDbPlaceNameList();
        //逐条打印全部
        if(placeInfoList != null && placeInfoList.size() > 0) {
            for (PlaceInfo placeInfo : placeInfoList) {
                Log.d(TAG, "initObjectBoxData:Debug: placeInfo=" + placeInfo);
            }
        }
        if (placeNameList != null && placeNameList.size() > 0) {
            for (PlaceName placeName : placeNameList) {
                Log.d(TAG, "initObjectBoxData:Debug: placeName=" + placeName);
            }
        }
        mPlaceInfoHelper.initPlaceInfoData(placeInfoList);
        mPlaceNameHelper.initPlaceNameData(placeNameList);

        mExtraInfoHelper.initExtraInfoData(mDbMigrate.getDbExtraInfoList());
        mMappingInfoHelper.initMappingData(mDbMigrate.getDbMappingList());
        mMultiFloorInfoHelper.initMultiFloorInfoData(mDbMigrate.getDbMultiFloorInfoList());
        // 10.3版本新增根据类型获取特殊点位，需要兼容旧数据——PlaceInfo表填充typeId值
        initTypeIdAndPriority();
        // 点图同步模式，且LocalPlaceInfo表没有数据，初始化LocalPlaceInfo表
        initLocalPlaceInfoData();
        Log.d(TAG, "initObjectBoxData end cost " + (System.currentTimeMillis() - startTime));
        mDbMigrate.close();
        mDbMigrate = null;
        updateMigrationState(context, true);
    }

    /**
     * 迁移数据时，初始化LocalPlaceInfo表
     */
    public void initLocalPlaceInfoData() {
        Log.d(TAG, "initLocalPlaceInfoData");
        if (isNapPosNotSeparate() && !mLocalPlaceInfoHelper.hasLocalPlaceInfoData()) {
            Log.d(TAG, "initLocalPlaceInfoData: 点图同步-没有本地点位数据");
            mLocalPlaceInfoHelper.initLocalPlaceInfoData();
        }
    }

    public void updateMigrationState(Context context, boolean isFinishMigration) {
        SharedPrefUtil.putBoolean(context, KEY_IS_FINISH_MIGRATION, isFinishMigration);
    }

    public void registerMapInfoListener(DataChangeListener listener) {
        if (mDbMode == DB_MODE_SQLITE) {
            mMapInfoHelper.setOnDataChangeListener(listener);
        } else {
            if (null == mBoxStore) {
                Log.e(TAG, "registerMapInfoListener: mBoxStore is null");
                return;
            }
            DataObserver<Class<MapInfo>> observer = data -> {
                if (null != listener)
                    listener.onDataChanged();
            };
            mBoxStore.subscribe(MapInfo.class).observer(observer);
        }
    }

    /**
     * @return 当前地图的主语言
     */
    public String getCurrentMapLanguage() {
        String mapLanguage = getMapLanguage(getMapName());
        if (TextUtils.isEmpty(mapLanguage)) {
            mapLanguage = Locale.SIMPLIFIED_CHINESE.toString();
        }
        Log.d(TAG, "getCurrentMapLanguage: mapName=" + getMapName() +
                " mapLanguage=" + mapLanguage);
        return mapLanguage;
    }

    private String getMapLanguage(String mapName) {
        MapInfo mapInfo = getMapByName(mapName);
        if (null == mapInfo) {
            return "";
        }
        return mapInfo.getMapLanguage();
    }

    /**
     * 设置当前地图名称（老架构当前地图名称给上层做显示，导航地图用于和底盘交互）
     * 1.加载地图成功 2.save方式结束建图成功 3.地图重命名
     */
    public void setMapName(String mapName) {
        sMapName = mapName;
        setNavMapByName(mapName);
        updateChargingPileStatus();
    }

    /**
     * 清除当前地图名称和信息
     * 没有本地地图或当前地图状态未未完成状态
     */
    public void clearMapName() {
        sMapName = "";
        mMapInfoHelper.clearUseState();
        updateChargingPileStatus();
    }

    public void updateChargingPileStatus() {
         if (null == getPlaceByType(Definition.CHARGING_POLE_TYPE, Definition.SPECIAL_PLACE_HIGH_PRIORITY) &&
            null == getPlaceByName(Definition.START_CHARGE_PILE_POSE)) {
            Log.d(TAG, "updateChargingPileStatus CHARGING_PILE_CONFIGURED: " + 0);
            RobotSettingApi.getInstance().setRobotInt(Definition.CHARGING_PILE_CONFIGURED, 0);
        } else {
            Log.d(TAG, "updateChargingPileStatus CHARGING_PILE_CONFIGURED: " + 1);
            RobotSettingApi.getInstance().setRobotInt(Definition.CHARGING_PILE_CONFIGURED, 1);
        }
    }

    public List<com.ainirobot.navigationservice.beans.tk1.Pose> getPlaceListOldVersion(String language, String mapName) {
        MapInfo mapInfo = getMapByName(mapName);
        if (null == mapInfo) {
            return getPlaceListOldVersion(language);
        }
        return getPlaceListOldVersion(language, mapInfo.getMapLanguage(), mapInfo.getMapName());
    }

    private List<com.ainirobot.navigationservice.beans.tk1.Pose> getPlaceListOldVersion(
            String language, String mapLanguage, String mapName) {
        List<PlaceInfo> placeInfos = getPlaceList(mapName);
        List<com.ainirobot.navigationservice.beans.tk1.Pose> poseList = new ArrayList<>();
        for (PlaceInfo placeBean : placeInfos) {
            HashMap<String, String> names = placeBean.getPlaceNameList();
            if (names == null) {
                continue;
            }
            String name = names.get(language);
            Log.d(TAG, "getPlaceListOldVersion: pose lanage name= " + name);
            if (TextUtils.isEmpty(name)) {
                //当前语言地点名不存在，取地图默认语言名字
                name = names.get(mapLanguage);
                Log.d(TAG, "getPlaceListOldVersion: pose Maplanage name= " + name);
            }
            com.ainirobot.navigationservice.beans.tk1.Pose pose = DataManager.placeBeanToPose(placeBean);
            pose.setId(placeBean.getPlaceId());
            pose.setName(name);
            Log.d(TAG, "getPlaceListOldVersion: Add pose=" + pose);
            poseList.add(pose);
        }
        return poseList;
    }

    public boolean hasPlaceInMap(String mapName, String placeName) {
        if (TextUtils.isEmpty(mapName)) {
            mapName = getMapName();
            Log.d(TAG, "hasPlaceInMap: Using current map: " + mapName);
        }
        
        if (TextUtils.isEmpty(mapName) || TextUtils.isEmpty(placeName)) {
            Log.e(TAG, "hasPlaceInMap: mapName or placeName is empty");
            return false;
        }
        
        // 通过PlaceName表查询是否存在该名称的点位
        String[] placeIds = mPlaceNameHelper.getPlaceIdsByName(placeName);
        if (placeIds == null || placeIds.length == 0) {
            Log.d(TAG, "hasPlaceInMap: No place found with name: " + placeName);
            return false;
        }
        
        // 检查这些placeId是否属于指定的地图
        List<PlaceInfo> placeInfos = mPlaceInfoHelper.getPlaceInfos(mapName, placeIds);
        boolean hasPlace = placeInfos != null && !placeInfos.isEmpty();
        
        Log.d(TAG, "hasPlaceInMap: mapName=" + mapName + ", placeName=" + placeName + 
                ", found=" + hasPlace + ", placeCount=" + (placeInfos != null ? placeInfos.size() : 0));
        
        return hasPlace;
    }

    /**
     * 以类型查找点位接口
     *
     * @param typedId 特殊点类型
     */
    public List<PlaceInfo> getPlacesByType(int typedId) {
        String mapName = getMapName();
        List<PlaceInfo> placeInfoList = mPlaceInfoHelper.getPlacesByType(typedId, mapName);
        if (placeInfoList == null || placeInfoList.isEmpty()) {
            Log.e(TAG, "getPlacesByType: placeInfo not found,typedId is " + typedId);
            return null;
        }
        List<MappingInfo> mappingInfo = getMappingInfoByMapName(mapName);
        processPlaceInfos(placeInfoList, mappingInfo);
        return placeInfoList;
    }

    /**
     * 根据typeId批量查找特殊点
     * @param typeIdArr 特殊点类型int数组
     */
    public List<PlaceInfo> getPlacesByTypeArr(int[] typeIdArr) {
        String mapName = getMapName();
        List<PlaceInfo> placeInfoList = mPlaceInfoHelper.getPlacesByTypeArr(typeIdArr, mapName);
        if (placeInfoList == null || placeInfoList.isEmpty()) {
            Log.e(TAG, "getPlacesByType: placeInfo not found, typedIdArr is " + Arrays.toString(typeIdArr));
            return null;
        }
        List<MappingInfo> mappingInfo = getMappingInfoByMapName(mapName);
        processPlaceInfos(placeInfoList, mappingInfo);
        return placeInfoList;
    }

    /**
     * 查询是否存在当前特殊点位
     *
     * @param typeId 特殊点位类型
     * @param priority 特殊点位优先级
     */
    public PlaceInfo getPlaceByType(int typeId, int priority) {
        Log.d(TAG, "getPlaceByType typeId: " + typeId + " priority: " + priority);
        String mapName = getMapName();
        if (mapName == null) {
            Log.e(TAG, "getPlaceByType: mapName is null");
            return null;
        }
        PlaceInfo placeInfo;
        if (isNapPosNotSeparate() && mLocalPlaceInfoHelper.containsLocalPlaceInfoByMapName(mapName)) { // 先优先判断是非点图分离，是则从LocalPlaceInfo表中获取
            Log.d(TAG, "getPlaceByType: 点图同步，且localInfo表有数据.");
            LocalPlaceInfo localPlaceInfo = mLocalPlaceInfoHelper.getLocalPlaceInfoByTypeId(mapName, typeId);
            if (localPlaceInfo != null) {
                Log.d(TAG, "getPlaceByType: localPlaceInfo found is: " + localPlaceInfo);
                placeInfo = CovertUtils.convertToPlaceInfo(mLocalPlaceInfoHelper.getLocalPlaceInfoByTypeId(mapName, typeId));
                return placeInfo;
            }
        }
        placeInfo = mPlaceInfoHelper.getPlaceByTypeIdAndPriority(typeId, priority, mapName);
        Log.d(TAG, "getPlaceByType placeInfo: " + placeInfo);
        if (placeInfo == null) {
            Log.e(TAG, "getPlaceByType: placeInfo not found,typedId is " + typeId + " priority is " + priority);
            return null;
        }
        return placeInfo;
    }

    private void processPlaceInfos(List<PlaceInfo> placeInfoList, List<MappingInfo> mappingInfos) {
        processPlaceInfos(placeInfoList, mappingInfos, false);
    }

    private void processPlaceInfos(List<PlaceInfo> placeInfoList, List<MappingInfo> mappingInfos, boolean printLog) {
        printList(printLog, "processPlaceInfos_placeInfoList", placeInfoList);
        printList(printLog, "processPlaceInfos_mappingInfos", mappingInfos);

        int infoSize = placeInfoList.size();
        String[] placeIds = new String[infoSize];
        Map<String, PlaceInfo> placeInfoMap = new HashMap<>();
        for (int i = 0; i < infoSize; i++) {
            PlaceInfo info = placeInfoList.get(i);
            String placeId = info.getPlaceId();
            placeIds[i] = placeId;
            placeInfoMap.put(placeId, info);
        }

        int mappingSize = mappingInfos.size();
        Map<String, String> nameMappingMap = new HashMap<>(mappingSize);
        Map<String, String> idMappingMap = new HashMap<>(mappingSize);
        for (MappingInfo info : mappingInfos) {
            String mappingPoseId = info.getMappingPoseId();
            nameMappingMap.put(info.getPlaceCnName(), mappingPoseId);
            idMappingMap.put(info.getPlaceId(), mappingPoseId);
        }

        List<PlaceName> placeNames = mPlaceNameHelper.getPlaceNameByPlaceId(placeIds);
        for (PlaceName name : placeNames) {
            String placeId = name.getPlaceId();
            String placeName = name.getPlaceName();
            String languageType = name.getLanguageType();
            PlaceInfo placeInfo = placeInfoMap.get(placeId);
            if (placeInfo != null) {
                placeInfo.addPlaceName(languageType, placeName);
            }
        }

        for (PlaceInfo info : placeInfoList) {
            checkMapping(info, placeInfoMap, nameMappingMap, idMappingMap);
        }
        printList(printLog, "processPlaceInfos_placeInfoList_update", placeInfoList);
    }

    private void checkMapping(PlaceInfo info, Map<String, PlaceInfo> placeInfoList, Map<String, String> nameMapping, Map<String, String> idMapping) {
        //根据id获取映射点信息，能匹配到特殊点id就说明有映射数据
        String placeCnName = info.getPlaceName("zh_CN");
        boolean isChargePose = Definition.START_BACK_CHARGE_POSE.equals(placeCnName);
        String mappingPoseId;
        if (isChargePose) {
            mappingPoseId = nameMapping.get(Definition.START_CHARGE_PILE_POSE);
        } else {
            mappingPoseId = idMapping.get(info.getPlaceId());
        }
        if (TextUtils.isEmpty(mappingPoseId)) {
            return;
        }
        //映射点的原始坐标信息
        PlaceInfo mappingPlace = placeInfoList.get(mappingPoseId);
        if (isChargePose) {
            //获取"充电桩"的映射点,再计算
            if (mappingPlace != null) {
                Pose backChargeP = calculateBackChargePose(mappingPlace);
                info.setPointX(backChargeP.getX());
                info.setPointY(backChargeP.getY());
                info.setPointTheta(backChargeP.getTheta());
                Log.d(TAG, "checkMapping: mappingPlace not null");
            }
        }
    }

    private Pose calculateBackChargePose(PlaceInfo placeInfo) {
        Log.d(TAG, "calculateBackChargePose: Def.sRobotRadius=" + Def.sRobotRadius);
        float x = placeInfo.getPointX();
        float y = placeInfo.getPointY();
        float theta = placeInfo.getPointTheta();

        Pose backChargePose = new Pose();
        backChargePose.setX((float) (x + (0.7 - Def.sRobotRadius) * Math.cos(theta)));
        backChargePose.setY((float) (y + (0.7 - Def.sRobotRadius) * Math.sin(theta)));
        backChargePose.setTheta(placeInfo.getPointTheta());
        Log.d(TAG, "calculateBackChargePose: backChargePose=" + backChargePose);
        return backChargePose;
    }

    public boolean updatePlaceInfoList(List<com.ainirobot.navigationservice.beans.tk1.Pose> poses, String language, MapInfo mapInfo) {
        if (null == mapInfo) {
            Log.e(TAG, "updatePlaceInfoList: Not found nav map");
            return false;
        }
        List<PlaceInfo> placeInfos = new ArrayList<>();
        String mapName = mapInfo.getMapName();
        for (com.ainirobot.navigationservice.beans.tk1.Pose pose : poses) {
            PlaceInfo placeInfo = DataManager.getInstance().poseToPlaceBean(pose, language, mapName);
            placeInfos.add(placeInfo);
        }
        updatePlaceInfoList(placeInfos, mapName);
        return true;
    }

    public boolean updatePlaceInfoListByEdit(List<com.ainirobot.navigationservice.beans.tk1.Pose> poses, String language, MapInfo mapInfo) {
        if (null == mapInfo) {
            Log.e(TAG, "updatePlaceInfoList: Not found nav map");
            return false;
        }
        List<PlaceInfo> placeInfos = new ArrayList<>();
        String mapName = mapInfo.getMapName();
        for (com.ainirobot.navigationservice.beans.tk1.Pose pose : poses) {
            PlaceInfo placeInfo = DataManager.getInstance().poseToPlaceBean(pose, language, mapName);
            placeInfos.add(placeInfo);
        }
        // 区分 非点图分离地图
        if (isNapPosNotSeparate()) {
            updateLocalPlaceList(CovertUtils.covertToLocalPlaceInfoList(placeInfos, mapInfo.getMapLanguage()));
        }
        updatePlaceInfoList(placeInfos, mapName);
        return true;
    }

    public boolean deletePlaceByNameList(List<String> nameList) {
        String mapName = getMapName();
        if (TextUtils.isEmpty(mapName)) {
            Log.e(TAG, "bulkDeletePlaceByNameList: mapName empty");
            return false;
        }
        return deletePlaceByNameList(nameList, mapName);
    }

    public void parseCloudInfoToLocalMapInfo(String mapName, JSONObject params) {
        MapInfo jsonFileMapInfo = MapUtils.getMapInfoFromJsonFile(mapName);
        int mapType, mapVersion;
        double transitMaxWidth;
        if (jsonFileMapInfo != null) {
            mapType = compatibleOldVersionMapType(jsonFileMapInfo.getMapType());
            mapVersion = jsonFileMapInfo.getMapVersion();
            transitMaxWidth = jsonFileMapInfo.getTransit_max_width();
        } else {
            mapType = compatibleOldVersionMapType(-1);
            mapVersion = 0;
            transitMaxWidth = 0.65;
        }
        mapVersion = MapVersionManager.updateDownloadMapVersion(mapVersion);
        String md5 = params.optString(Definition.JSON_MAP_MD5);
        String uuid = params.optString(Definition.JSON_MAP_UUID);
        String version = String.valueOf(params.optInt(Definition.JSON_MAP_UPDATE_TIME));
        MapInfo dbMapInfo = getMapByName(mapName);
        if (dbMapInfo == null) {
            Log.d(TAG, "parseCloudInfoToLocalMapInfo: Insert new map info");
            //云端地图列表信息中version精度是秒
            long updateTime = TextUtils.isEmpty(version) ? 0 : Long.parseLong(version);
            dbMapInfo = new MapInfo();
            dbMapInfo.setMapId(UuidUtils.createMapId(mapName))
                    .setMapName(mapName)
                    .setMapVersion(mapVersion)
                    .setMd5(md5)
                    .setMapType(mapType)
                    .setMapPath(MapFileHelper.getMapFilePath(mapName))
                    .setSyncState(MapInfo.SyncState.UPLOADED_MAP)
                    .setMapUuid(uuid)
                    .setTransit_max_width(transitMaxWidth)
                    .setUpdateTime(UpdateTimeUtils.timestampToDateSeconds(updateTime));
        } else {
            Log.d(TAG, "parseCloudInfoToLocalMapInfo: Update map info");
            long updateTime = TextUtils.isEmpty(version) ? 0 : Long.parseLong(version);
            dbMapInfo.setMd5(md5)
                    .setMapUuid(uuid)
                    .setMapType(mapType)
                    .setMapVersion(mapVersion)
                    .setTransit_max_width(transitMaxWidth)
                    .setUpdateTime(UpdateTimeUtils.timestampToDateSeconds(updateTime));
        }
        updateMapInfo(dbMapInfo);
    }

    /**
     * 兼容没有地图类型字段的版本，赋予默认值
     */
    private int compatibleOldVersionMapType(int mapType) {
        Log.d(TAG, "compatibleOldVersionMapType: mapType=" + mapType);
        if (mapType <= 0) {
            if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus()) {
                mapType = MapInfo.MapType.TARGET_NORMAL;
            } else {
                mapType = MapInfo.MapType.NORMAL;
            }
        }
        return mapType;
    }

    /**
     * read place.json, if no exist read place.properties
     * <p>
     * 初始版本只会上传place.properties给云端，多语言版本之后会同时上传place.json
     * place.json数据结构中只可以保存点位信息，
     * place.properties文件中保存了主语言和禁行线，但是云端编辑后会把这两个值删掉只保留点位信息
     **/
    public void parsePlacePropToNaviProp(String mapName) {
        MapInfo mapInfo = getMapByName(mapName);
        if (mapInfo == null) {
            Log.e(TAG, "parsePlacePropToNaviProp:error: Not found map. mapName=" + mapName);
            return;
        }

        boolean hasForbidLine = DataManager.getInstance().parseTypeForbidLineFromPlaceProp(mapName);
        mapInfo.setForbidLine(hasForbidLine);
        if (TextUtils.isEmpty(mapInfo.getMapLanguage())) {
            String language = DataManager.getInstance().parseMapLanguage(mapInfo);
            mapInfo.setMapLanguage(language);
        }
        updateMapInfo(mapInfo);

        if (MapFileHelper.isPlaceJsonExists(mapName)) {
            //json to place
            addPlaceListFromJson(mapInfo);
        } else {
            //properties to place
            addPlaceListFromProfile(mapInfo);
        }
    }

    /**
     * 解析turnstile.json，存储到数据库
     */
    public void parseCloudInfoTurnstileJson(String mapName) {
        Log.d(TAG, "parseCloudInfoTurnstileJson: mapName=" + mapName);
        File file = DataManager.getInstance().getPlaceFile(mapName, MapFileHelper.GATE_JSON);
        try (InputStream is = Files.newInputStream(file.toPath())) {
            byte[] bytes = new byte[is.available()];
            int len = is.read(bytes);
            if (bytes.length == len) {
                RoadGraphEdgePixelBean gateEdge = GsonUtil.fromJson(new String(bytes), RoadGraphEdgePixelBean.class);
                if(gateEdge!=null){
                    List<GateRelationInfo> deletedItems = mGateRelationInfoHelper.deleteExceptLineIds(Collections.singletonList(gateEdge.getId()));
                    if(!deletedItems.isEmpty()){
                        Map<String, Boolean> deletedGateIdsMap = deletedItems.stream()
                                .map(GateRelationInfo::getGateId)
                                .filter(gateId -> gateId != null && !gateId.isEmpty())
                                .collect(Collectors.toMap(gateId -> gateId, gateId -> true, (existing, replacement) -> existing));
                        Log.d(TAG, "deleteExceptLineIds: 成功删除" + deletedItems.size() + "条记录 !");
                        SystemApi.getInstance().updateGateDevicesInfo(0,GsonUtil.toJson(deletedGateIdsMap), new CommandListener() {
                            @Override
                            public void onResult(int result, String message, String extraData) {
                                super.onResult(result, message, extraData);
                                Log.d(TAG, "updateGateDevicesInfo onResult msg : " + message + ", extraData : " + extraData);
                            }
                        });
                    }
                }
                List<RoadGraphEdgePixelBean> graphEdgePixelBeans = new ArrayList<>();
                graphEdgePixelBeans.add(gateEdge);
                boolean result = MapUtils.saveLocalGateData(mapName, graphEdgePixelBeans.toString());
                Log.d(TAG, "saveGateData: result=" + result);
                Log.d(TAG, "parseCloudInfoTurnstileJson: Success!");
            } else {
                Log.d(TAG, "parseCloudInfoTurnstileJson: Failed to read the complete file");
            }
        } catch (Exception e) {
            Log.e(TAG, "parseCloudInfoTurnstileJson: Exception: " + e.getMessage(), e);
        }
    }

    /**
     * 解析下载地图的place.prop存储到数据库
     */
    private void addPlaceListFromProfile(MapInfo mapInfo) {
        String mapName = mapInfo.getMapName();
        Properties placeProp = DataManager.getInstance().getPlaceProperties(mapName);
        String keyPose = DataManager.getInstance().getPoseTypeByName(mapName);
        if (placeProp != null) {
            List<PlaceInfo> placeInfos = new ArrayList<>();
            for (Map.Entry<Object, Object> entry : placeProp.entrySet()) {
                String key = entry.getKey().toString();
                if (key.startsWith(keyPose)) {
                    com.ainirobot.navigationservice.beans.tk1.Pose pose =
                            GsonUtil.fromJson(entry.getValue().toString(), com.ainirobot.navigationservice.beans.tk1.Pose.class);
                    if (null != pose) {
                        pose.setName(key.replace(keyPose, ""));
                        PlaceInfo placeInfo = DataManager.getInstance().poseToPlaceBean(pose,
                                DEFAULT_LANGUAGE, mapName);
                        placeInfos.add(placeInfo);
                    }
                }
            }
            if (!placeInfos.isEmpty()) {
                Log.i(TAG, "addPlaceListFromProfile: addPoseList");
                updatePlaceInfoList(placeInfos, mapName);
                sendPlaceDataUpdateReport();
            }
        }
    }

    /**
     * 解析place.json，存储到数据库
     */
    private void addPlaceListFromJson(MapInfo mapInfo) {
        Log.d(TAG, "addPlaceListFromJson: mapInfo=" + mapInfo);
        String mapName = mapInfo.getMapName();
        File file = DataManager.getInstance().getPlaceFile(mapName, MapFileHelper.PLACE_JSON);
        try (InputStream is = Files.newInputStream(file.toPath())) {
            byte[] bytes = new byte[is.available()];
            int len = is.read(bytes);
            if (bytes.length == len) {
                List<PlaceInfo> updatePlaceInfos = parsePlaceInfosFromJson(new String(bytes), mapInfo);
                updatePlaceInfoList(updatePlaceInfos, mapName);
                sendPlaceDataUpdateReport();
                Log.d(TAG, "addPlaceListFromJson: Success!");
            } else {
                Log.d(TAG, "addPlaceListFromJson: Failed to read the complete file");
            }
        } catch (Exception e) {
            Log.e(TAG, "addPlaceListFromJson: Exception: " + e.getMessage(), e);
        }
    }

    private List<PlaceInfo> parsePlaceInfosFromJson(String json, MapInfo mapInfo) throws JSONException {
        Log.d(TAG, "parsePlaceInfosFromJson: json="+json);
        Log.d(TAG, "parsePlaceInfosFromJson: mapInfo="+mapInfo);
        List<PlaceInfo> updatePlaceInfos = new ArrayList<>();
        JSONArray array = new JSONArray(json);
        int size = array.length();
        String[] placeIds = new String[size];
        for (int i = 0; i < size; i++) {
            JSONObject jsonPlace = array.optJSONObject(i);
            PlaceInfo tempInfo = DataManager.getInstance().parsePlace(jsonPlace, mapInfo);
            placeIds[i] = tempInfo.getPlaceId();
            updatePlaceInfos.add(tempInfo);
        }
        List<PlaceInfo> placeInfos = mPlaceInfoHelper.getPlaceInfos(mapInfo.getMapName(), placeIds);
        Log.d(TAG, "parsePlaceInfosFromJson: placeInfos="+ placeInfos);
        Map<String, PlaceInfo> placeInfoMap = new HashMap<>();
        for (PlaceInfo info : placeInfos) {
            placeInfoMap.put(info.getPlaceId(), info);
        }
        Log.d(TAG, "parsePlaceInfosFromJson: placeInfoMap="+placeInfoMap);
        for (PlaceInfo updateInfo : updatePlaceInfos) {
            PlaceInfo placeInfo = placeInfoMap.get(updateInfo.getPlaceId());
            if (placeInfo != null) {
                updateInfo.setIgnoreDistance(placeInfo.getIgnoreDistance());
                updateInfo.setNoDirectionalParking(placeInfo.getNoDirectionalParking());
                updateInfo.setSafeDistance(placeInfo.getSafeDistance());
                updateInfo.setTypeId(placeInfo.getTypeId());
                updateInfo.setPriority(placeInfo.getPriority());
            }
        }
        return updatePlaceInfos;
    }

    private void sendPlaceDataUpdateReport() {
        RobotCore.sendStatusReport(RobotOS.NAVIGATION_SERVICE,
                Definition.REPORT_NAVI_CONFIG, String.valueOf(FileObserver.MODIFY));
    }

    public void savePlaceToPlaceFile(String mapName, MapInfo mapInfo) {
        Log.d(TAG, "savePlaceToPlaceFile: " + mapName);
        MapFileHelper.deletePlacePropFile(mapName);
        List<PlaceInfo> placeList = getPlaceList(mapName);
        //兼容旧版本使用:新版本只使用place.json文件，不再使用place.properties
        DataManager.getInstance().savePlacePropFile(mapInfo, placeList);
        //保存多语言地点
        DataManager.getInstance().savePlaceJsonFile(mapName, placeList);
    }


    public boolean updatePlaceByIdList(List<String> idList, List<String> newNameList, String language) {
        return updatePlaceByIdList(idList.toArray(new String[0]), newNameList, language);
    }

    public boolean renameMap(String oldMapName, String newMapName) {
        String curMapName = getMapName();
        boolean isSuccess = false;
        Log.d(TAG, "renameMap: oldMapName=" + oldMapName + "  newMapName=" + newMapName +
                "  curMapName=" + curMapName);
        MapInfo oldMap = getMapByName(oldMapName);
        Log.d(TAG, "renameMap: oldMap=" + (oldMap == null ? "null" : oldMap.toString()));
        if (oldMap != null) {
            String newMapId = UuidUtils.createMapId(newMapName);
            //插入新地图
            boolean saveMapInfo = saveMapInfoAsNewMap(oldMap.clone(), newMapName, MapFileHelper.getMapFilePath(newMapName), newMapId);
            //删除旧点+插入新点
            boolean renameMapPlace = renameMapPlace(oldMap, newMapName);
            //视觉图，删除额外数据
            boolean deleteExtraData = !MapTypeHelper.isTypeSupportVision(oldMap.getMapType()) || mExtraInfoHelper.deleteExtraData(oldMapName);
            Log.d(TAG, "renameMap: saveMapInfo=" + saveMapInfo + " renameMapPlace=" + renameMapPlace + " deleteExtraData=" + deleteExtraData);
            //是否删除额外数据不影响是否成功（bugfix:上传额外文件失败本地数据库没有额外导致后续重命名操作失败，重命名操作执行一部分导致新旧地图数据都不完整）
            isSuccess = saveMapInfo && renameMapPlace;
        }
        if (TextUtils.equals(oldMapName, curMapName)) {
            Log.d(TAG, "renameMap: Modify curMapName to newMapName when oldMap is curMap");
            setMapName(newMapName);//renameMap
        }
        Log.d(TAG, "renameMap: isSuccess=" + isSuccess);
        return isSuccess;
    }

    private boolean saveMapInfoAsNewMap(MapInfo oldMapInfo, String newMapName, String newMapPath, String newMapId) {
        Log.i(TAG, "saveMapInfoAsNewMap: oldMapInfo:" + oldMapInfo.toString());
        Log.i(TAG, "saveMapInfoAsNewMap: newMapName:" + newMapName + " newMapId=" + newMapId);
        oldMapInfo.setMapName(newMapName);
        oldMapInfo.setMapId(newMapId);
        oldMapInfo.setMapPath(newMapPath);
        oldMapInfo.setMapUuid("");
        updateMapInfo(oldMapInfo);
        return true;
    }

    /**
     * ------------------------上传云端地图信息的解析与封装-----------------------------------------
     * 同步云端地图信息到本地：解析地图包信息之前先添加云端 push 消息中携带的地图信息，增加一张地图表。
     * 目前只有 Home 中接收道云端推送地图时用，保证下载地图后可以有 uuid 信息。
     */
    public void addMapInfo(String mapName, String md5, String mapUuid, String updateTime) {
        MapInfo jsonFileMapInfo = MapUtils.getMapInfoFromJsonFile(mapName);
        int mapType, mapVersion;
        if (jsonFileMapInfo != null) {
            mapType = compatibleOldVersionMapType(jsonFileMapInfo.getMapType());
            mapVersion = jsonFileMapInfo.getMapVersion();
        } else {
            mapType = compatibleOldVersionMapType(-1);
            mapVersion = 0;
        }
        mapVersion = MapVersionManager.updateDownloadMapVersion(mapVersion);
        MapInfo dbMapInfo = getMapByName(mapName);
        if (dbMapInfo == null) {
            Log.d(TAG, "addMapInfo: Insert new map info");
            saveNewMapInfoFromCloud(UuidUtils.createMapId(mapName), mapName,
                    md5, mapUuid, updateTime, mapType, mapVersion);
        } else {
            Log.d(TAG, "addMapInfo: Update map info");
            if (TextUtils.isEmpty(md5)) {
                md5 = dbMapInfo.getMd5();
            }
            boolean updatedMapCloudInfo = updateMapCloudInfo(mapName, md5, mapUuid, updateTime, mapType, mapVersion);
            Log.d(TAG, "addMapInfo: updatedMapCloudInfo=" + updatedMapCloudInfo);
            deletePlaceByMapName(mapName);
        }
    }

    /**
     * 下载云端地图，创建本地地图信息
     *
     * @param version    地图修改时间
     * @param mapVersion 地图版本号
     */
    private void saveNewMapInfoFromCloud(String mapId, String mapName, String md5, String uuid,
                                         String version, int mapType, int mapVersion) {
        Log.i(TAG, "saveNewMapInfoFromCloud: mapId=" + mapId + " mapName=" + mapName +
                " md5=" + md5 + " uuid=" + uuid + " version=" + version +
                " mapType=" + mapType + " mapVersion=" + mapVersion);
        //云端地图列表信息中version精度是秒
        long updateTime = TextUtils.isEmpty(version) ? 0 : Long.parseLong(version);
        MapInfo mapInfo = new MapInfo();
        mapInfo.setMapId(mapId)
                .setMapName(mapName)
                .setMapVersion(mapVersion)
                .setMd5(md5)
                .setMapType(mapType)
                .setMapPath(MapFileHelper.getMapFilePath(mapName))
                .setSyncState(MapInfo.SyncState.UPLOADED_MAP)
                .setMapUuid(uuid)
                .setUpdateTime(UpdateTimeUtils.timestampToDateSeconds(updateTime));
        if (!updateMapInfo(mapInfo)) {
            Log.e(TAG, "saveNewMapInfoFromCloud: insert map info fail!");
        }
    }

    /**
     * 保存本地已存在的云端地图信息
     *
     * @param version 更新时间
     */
    public boolean updateMapCloudInfo(String mapName, String md5, String uuid, String version, int mapType, int mapVersion) {
        Log.d(TAG, "updateMapCloudInfo: mapName=" + mapName + " md5=" + md5 + " uuid=" + uuid +
                " version=" + version + " mapType=" + mapType + " mapVersion=" + mapVersion);
        if (TextUtils.isEmpty(mapName)) {
            return false;
        }
        MapInfo mapInfo = getMapByName(mapName);
        if (mapInfo != null) {
            Log.d(TAG, "updateMapCloudInfo: mapInfo=" + mapInfo);
            long updateTime = TextUtils.isEmpty(version) ? 0 : Long.parseLong(version);
            mapInfo.setMd5(md5)
                    .setMapUuid(uuid)
                    .setSyncState(mapInfo.getSyncState())
                    .setMapType(mapType)
                    .setMapVersion(mapVersion)
                    .setUpdateTime(UpdateTimeUtils.timestampToDateSeconds(updateTime));
            return updateMapInfo(mapInfo);
        }
        return false;
    }

    /**
     * 获得多层配置及每层地图里的点位信息
     */
    public List<MultiFloorInfo> getMultiFloorConfigAndPose(String language, boolean isHaveSpecialPose) {
        List<MultiFloorInfo> infoList = getMultiFloorConfig();
        Map<String, MultiFloorInfo> floorInfoMap = new HashMap<>();
        int size = infoList.size();
        String[] mapNames = new String[size];
        for (int i = 0; i < size; i++) {
            MultiFloorInfo floorInfo = infoList.get(i);
            String mapName = floorInfo.getMapName();
            floorInfoMap.put(mapName, floorInfo);
            mapNames[i] = mapName;
        }
        List<MapInfo> mapInfos = getMapByName(mapNames);
        Map<String, MapInfo> mapInfoMap = new HashMap<>();
        for (MapInfo mapInfo : mapInfos) {
            mapInfoMap.put(mapInfo.getMapName(), mapInfo);
        }

        updatePoseList(language, isHaveSpecialPose, floorInfoMap, mapInfoMap, getPlaceList(mapNames));
        return infoList;
    }

    //返回的是CoreService中pose对象的list
    private Pose placeBeanToPose(PlaceInfo placeInfo, String name) {
        com.ainirobot.coreservice.client.actionbean.Pose pose = new com.ainirobot.coreservice.client.actionbean.Pose(
                placeInfo.getPointX(),
                placeInfo.getPointY(),
                placeInfo.getPointTheta());
        pose.setStatus(placeInfo.getPlaceStatus());
        pose.setName(name);
        pose.setTypeId(placeInfo.getTypeId());
        pose.setPriority(placeInfo.getPriority());
        return pose;
    }

    public boolean updateExtraInfo(ExtraInfo extraInfo) {
        return mExtraInfoHelper.updateExtraInfo(extraInfo);
    }

    /**
     * 插入新建地图信息
     *
     * @param mapId       map id
     * @param mapName     map name
     * @param md5         map pkg md5
     * @param mapPath     map file path
     * @param mapLanguage map language
     * @param type        map type
     * @param finishState map complete degree
     */
    public boolean saveNewCreatedMapInfo(String mapId, String mapName, String md5,
                                         String mapPath, String mapLanguage, int type,
                                         int finishState, int hasTarget) {
        Log.d(TAG, "saveNewCreatedMapInfo: mapId：" + mapId + " mapName:" + mapName + " md5=" + md5
                + " mapPath=" + mapPath + " mapLanguage=" + mapLanguage + " type=" + type
                + " finishState=" + finishState + ", hasTarget : " + hasTarget);
        MapInfo mapInfo = new MapInfo();
        mapInfo.setMapId(mapId);
        mapInfo.setMapName(mapName);
        mapInfo.setMd5(md5);
        mapInfo.setMapPath(mapPath);
        mapInfo.setMapLanguage(mapLanguage);
        mapInfo.setMapType(type);
        mapInfo.setFinishState(finishState);
        mapInfo.setCreateTime(UpdateTimeUtils.timestampToDateMillis(System.currentTimeMillis()));
        mapInfo.setUpdateTime(mapInfo.getCreateTime());
        mapInfo.setTargetData(hasTarget);
        mapInfo.setMapVersion(MapVersionManager.MAP_VERSION);
        return updateMapInfo(mapInfo);
    }

    public void updateIpNavigation(String ipNavigation) {
        boolean updated = mChassisInfoHelper.updateIpNavigation(ipNavigation);
        Log.d(TAG, "updateIpNavigation: updated=" + updated);
    }

    public void updateIpSdkRos(String ip) {
        boolean updated = mChassisInfoHelper.updateIpSdkRos(ip);
        Log.d(TAG, "updateIpSdkRos: updated=" + updated);
    }

    public void updateMultiRobotConfig(RoverConfig roverConfig) {
        boolean updated = mChassisInfoHelper.updateMultiRobotConfig(roverConfig);
        Log.d(TAG, "updateMultiRobotConfig: updated roverConfig=" + updated);
    }

    public void updateMultiRobotConfig(MultiRobotConfigBean multiRobotConfigBean) {
        boolean updated = mChassisInfoHelper.updateMultiRobotConfig(multiRobotConfigBean);
        Log.d(TAG, "updateMultiRobotConfig: updated multiRobotConfig=" + updated);
    }

    public String getRoverConfig() {
        return mChassisInfoHelper.getRoverConfig();
    }

    public String getMultiRobotConfig() {
        String multiRobotConfig = mChassisInfoHelper.getMultiRobotConfig();
        Log.d(TAG, "getMultiRobotConfig: multiRobotConfig=" + multiRobotConfig);
        return multiRobotConfig;
    }

    public ExtraInfo getExtraInfo(String mapName) {
        return mExtraInfoHelper.getExtraInfo(mapName);
    }

    public String getIpNavigation() {
        return mChassisInfoHelper.getIpNavigation();
    }

    public String getIpSdkRos() {
        return mChassisInfoHelper.getIpSdkRos();
    }

    public void deleteAndUpdatePlace(com.ainirobot.navigationservice.beans.tk1.Pose pose) {
        String language = getCurrentMapLanguage();
        PlaceInfo placeBean = DataManager.getInstance().poseToPlaceBean(
                pose, language, getMapName());

        Log.d(TAG , "deleteAndUpdatePlace typeId: " + pose.getTypeId() + " priority: " + pose.getPriority());
        //delete
        if (pose.getTypeId() != Definition.NORMAL_POINT_TYPE) {
            deletePlaceByTypeId(pose.getTypeId(), pose.getPriority());
        } else {
            deletePlaceByPlaceName(pose.getName());
        }

        //update
        addPlace(placeBean);
    }

    /**
     * 获取当前地图名称（同上成对出现）
     */
    public String getMapName() {
        if (TextUtils.isEmpty(sMapName)) {
            MapInfo mapInfo = mMapInfoHelper.getNavMapInfo();
            if (mapInfo != null) {
                sMapName = mapInfo.getMapName();
            }
        }
        return sMapName;
    }

    public MapInfo getNavMapInfo() {
        return mMapInfoHelper.getNavMapInfo();
    }

    public boolean getHasVision() {
        MapInfo navMapInfo = mMapInfoHelper.getNavMapInfo();
        if (null == navMapInfo) {
            Log.d(TAG, "getHasVision navMapInfo is null");
            return false;
        }
        navMapInfo.updateNaviType();
        return navMapInfo.isMapHasVision();
    }

    public PlaceInfo getPlaceByName(String placeName) {
        MapInfo mapInfo = mMapInfoHelper.getNavMapInfo();
        if (mapInfo == null) {
            Log.e(TAG, "getPlaceByName: mapinfo not found,name is " + placeName);
            return null;
        }

        String[] placeIds = mPlaceNameHelper.getPlaceIdsByName(placeName);

        List<PlaceInfo> placeInfo = mPlaceInfoHelper.getPlaceInfos(mapInfo.getMapName(), placeIds);
        return placeInfo.isEmpty() ? null : placeInfo.get(0);
    }

    public List<PlaceInfo> getPlaceByName(List<String> placeNames) {
        MapInfo mapInfo = mMapInfoHelper.getNavMapInfo();
        if (mapInfo == null) {
            Log.e(TAG, "getPlaceByName: mapinfo not found,names is " + placeNames);
            return null;
        }
        String[] placeIds = getPlaceIdsByNameList(placeNames);
        return mPlaceInfoHelper.getPlaceInfos(mapInfo.getMapName(), placeIds);
    }

    public MapInfo getMapByName(String mapName) {
        return mMapInfoHelper.getMapByName(mapName);
    }

    public List<MapInfo> getMapByName(String[] mapName) {
        return mMapInfoHelper.getMapInfos(mapName);
    }

    public List<MapInfo> getAllMap() {
        return mMapInfoHelper.getMapInfos(null);
    }

    public List<com.ainirobot.navigationservice.beans.tk1.Pose> getPlaceListOldVersion(String language) {
        MapInfo navMapInfo = mMapInfoHelper.getNavMapInfo();
        if (null == navMapInfo) {
            return null;
        }
        return getPlaceListOldVersion(language, navMapInfo.getMapLanguage(), navMapInfo.getMapName());
    }

    public boolean updateMapInfo(MapInfo mapInfo) {
        return mMapInfoHelper.updateMapInfo(mapInfo);
    }

    public void updateMapInfo(List<MapInfo> mapInfos) {
        mMapInfoHelper.updateMapInfo(mapInfos);
    }

    public void updateMapInfoEstimate(String poseStr) {
        MapInfo mapInfo = mMapInfoHelper.getNavMapInfo();
        if (null != mapInfo) {
            mapInfo.setPoseEstimate(poseStr);
            mMapInfoHelper.updateMapInfo(mapInfo);
        }
    }

    /**
     * editPlaceProcess 编辑地图刷新点位时调用
     */
    public boolean updatePlaceInfoList(List<com.ainirobot.navigationservice.beans.tk1.Pose> poses) {
        return updatePlaceInfoListByEdit(poses, getCurrentMapLanguage(), mMapInfoHelper.getNavMapInfo());
    }

    /**
     * 获取保存的位置点
     */
    public com.ainirobot.navigationservice.beans.tk1.Pose getPoseEstimate() {
        MapInfo navMapInfo = mMapInfoHelper.getNavMapInfo();
        if (navMapInfo != null) {
            return GsonUtil.fromJson(navMapInfo.getPoseEstimate(), com.ainirobot.navigationservice.beans.tk1.Pose.class);
        }
        return null;
    }

    /**
     * 设置当前使用中地图
     */
    private void setNavMapByName(String mapName) {
        Log.i(TAG, "setNavMapByName: mapName=" + mapName);
        if (TextUtils.isEmpty(mapName)) {
            return;
        }
        MapInfo curMap = getMapByName(mapName);
        if (curMap == null) {
            Log.e(TAG, "setNavMapByName:fail: Has no map " + mapName);
            return;
        }
        if (curMap.getUseState() == MapInfo.UseState.IN_USE) {
            Log.d(TAG, String.format("setNavMapByName: Map %s already in use ", mapName));
            return;
        }
        mMapInfoHelper.updateUseState(curMap);
    }

    /**
     * 获取多楼层点位列表
     * @param isHaveSpecialPose 是否有特殊点位，false时不添加特殊点位
     */
    private void updatePoseList(String language, boolean isHaveSpecialPose, Map<String, MultiFloorInfo> floorInfoMap, Map<String, MapInfo> mapInfoMap, List<PlaceInfo> placeList) {
        MapInfo navMapInfo = mMapInfoHelper.getNavMapInfo();
        for (PlaceInfo placeInfo : placeList) {
            HashMap<String, String> names = placeInfo.getPlaceNameList();
            if (names == null) {
                continue;
            }
            String mapName = placeInfo.getMapName();
            String name = names.get(language);
            if (TextUtils.isEmpty(name)) {
                //当前语言地点名不存在，取地图默认语言名字
                MapInfo mapInfo = mapInfoMap.get(mapName);
                if (null == mapInfo && null == (mapInfo = navMapInfo)) {
                    Log.d(TAG, "getMultiFloorConfigAndPose mapInfo and navMapInfo is null");
                    continue;
                }
                name = names.get(mapInfo.getMapName());
                Log.d(TAG, "getPlaceListOldVersion: pose Maplanage name= " + name);
            }
            if (!isHaveSpecialPose && DataManager.getInstance().isSpecialOrElevatorPlace(name)) { //获取多楼层点位列表
                continue;
            }
            MultiFloorInfo multiFloorInfo = floorInfoMap.get(mapName);
            if (multiFloorInfo != null) {
                List<Pose> poseList = multiFloorInfo.getPoseList();
                if (null == poseList) {
                    poseList = new ArrayList<>();
                    multiFloorInfo.setPoseList(poseList);
                }
                poseList.add(placeBeanToPose(placeInfo, name));
            }
        }
    }

    public List<PlaceInfo> getPlaceList(String[] mapNames) {
        List<PlaceInfo> placeInfoList = new ArrayList<>();
        if (mapNames == null || mapNames.length == 0) {
            return placeInfoList;
        }
        placeInfoList = mPlaceInfoHelper.getPlaceInfoByMapName(mapNames);
        List<MappingInfo> mappingInfos = mMappingInfoHelper.getMappingInfoByMapName(mapNames);

        processPlaceInfos(placeInfoList, mappingInfos);
        return placeInfoList;
    }

    public List<PlaceInfo> getPlaceList(String mapName) {
        return getPlaceList(mapName, false);
    }

    public List<PlaceInfo> getPlaceList(String mapName, boolean printLog) {
        if (TextUtils.isEmpty(mapName)) {
            mapName = getMapName();
        }
        if (TextUtils.isEmpty(mapName)) {
            return new ArrayList<>();
        }
        List<PlaceInfo> placeInfoList;
        if (isNapPosNotSeparate() && mLocalPlaceInfoHelper.containsLocalPlaceInfoByMapName(mapName)) {//getPlaceList-点图同步
            Log.d(TAG, "getPlaceList: 点图同步，且localInfo表有数据.");
            placeInfoList = mPlaceInfoHelper.getPlaceInfoByMapName(mapName);
            printList(printLog, "getPlaceInfoByMapName", placeInfoList);

            List<LocalPlaceInfo> localPlaceInfoList = mLocalPlaceInfoHelper.getLocalPlaceInfo(mapName);
            printList(printLog, "getPlaceList_localPlaceInfo", localPlaceInfoList);

            // 🔥 新的合并逻辑
            // 1. 转换LocalPlaceInfo为PlaceInfo
            List<PlaceInfo> placeInfoListNew = CovertUtils.coverToPlaceInfoList(localPlaceInfoList);
            
            // 2. 创建新转换数据的placeId映射
            Map<String, PlaceInfo> newPlaceMap = placeInfoListNew.stream()
                .collect(Collectors.toMap(PlaceInfo::getPlaceId, Function.identity()));
            
            // 3. 遍历原列表，替换相同placeId的记录，添加新记录
            Map<String, PlaceInfo> resultMap = new LinkedHashMap<>();
            
            // 先处理原列表，如果有新数据则替换
            for (PlaceInfo originalPlace : placeInfoList) {
                String placeId = originalPlace.getPlaceId();
                if (newPlaceMap.containsKey(placeId)) {
                    // 🔥 用新数据替换，但保留原始的placeNameList
                    PlaceInfo newPlace = newPlaceMap.get(placeId);
                    PlaceInfo mergedPlace = createMergedPlaceInfo(originalPlace, newPlace);
                    resultMap.put(placeId, mergedPlace);
                    Log.d(TAG, "getPlaceList: 替换placeId=" + placeId + "的数据，保留原placeNameList");
                } else {
                    // 保留原数据
                    resultMap.put(placeId, originalPlace);
                }
            }
            
            // 添加原列表中不存在的新数据
            for (PlaceInfo newPlace : placeInfoListNew) {
                String placeId = newPlace.getPlaceId();
                if (!resultMap.containsKey(placeId)) {
                    resultMap.put(placeId, newPlace);
                    Log.d(TAG, "getPlaceList: 新增placeId=" + placeId + "的数据");
                }
            }
            
            // 4. 转换回列表
            placeInfoList = new ArrayList<>(resultMap.values());
            
            Log.d(TAG, "getPlaceList: 合并完成，原数据=" + mPlaceInfoHelper.getPlaceInfoByMapName(mapName).size() + 
                     "，新数据=" + placeInfoListNew.size() + 
                     "，最终数据=" + placeInfoList.size());
        } else {
            placeInfoList = mPlaceInfoHelper.getPlaceInfoByMapName(mapName);
        }
        List<MappingInfo> mappingInfos = mMappingInfoHelper.getMappingInfoByMapName(mapName);

        processPlaceInfos(placeInfoList, mappingInfos, printLog);
        return placeInfoList;
    }

    /**
     * 创建合并后的PlaceInfo对象，使用新数据但保留原始的placeNameList
     * @param originalPlace 原始PlaceInfo（包含placeNameList）
     * @param newPlace 新的PlaceInfo（来自LocalPlaceInfo转换，placeNameList可能为空）
     * @return 合并后的PlaceInfo对象
     */
    private PlaceInfo createMergedPlaceInfo(PlaceInfo originalPlace, PlaceInfo newPlace) {
        // 创建新的PlaceInfo对象，基于新数据
        PlaceInfo mergedPlace = new PlaceInfo();
        
        // 复制新数据的所有字段
        mergedPlace.setPlaceId(newPlace.getPlaceId());
        mergedPlace.setMapName(newPlace.getMapName());
        mergedPlace.setPointX(newPlace.getPointX());
        mergedPlace.setPointY(newPlace.getPointY());
        mergedPlace.setPointTheta(newPlace.getPointTheta());
        mergedPlace.setTypeId(newPlace.getTypeId());
        mergedPlace.setPriority(newPlace.getPriority());
        mergedPlace.setIgnoreDistance(newPlace.getIgnoreDistance());
        mergedPlace.setNoDirectionalParking(newPlace.getNoDirectionalParking());
        mergedPlace.setSafeDistance(newPlace.getSafeDistance());
        mergedPlace.setUpdateTime(newPlace.getUpdateTime());
        mergedPlace.setCreateTime(newPlace.getCreateTime());
        
        // 🔥 关键：保留原始的placeNameList
        mergedPlace.setPlaceNames(originalPlace.getPlaceNameList());
        
        Log.d(TAG, "createMergedPlaceInfo: 合并placeId=" + mergedPlace.getPlaceId() + 
               "，保留原placeNameList数量=" + 
               (originalPlace.getPlaceNameList() != null ? originalPlace.getPlaceNameList().size() : 0));
        
        return mergedPlace;
    }

    public boolean updateMappingInfo(MappingInfo mappingInfo) {
        return mMappingInfoHelper.updateMappingInfo(mappingInfo);
    }

    public List<MappingInfo> getMappingInfoByMapName(String mapName) {
        return mMappingInfoHelper.getMappingInfoByMapName(mapName);
    }

    public boolean deleteMapAllDataByName(String mapName) {
        mExtraInfoHelper.deleteExtraData(mapName);
        mMappingInfoHelper.deleteMappingInfo(mapName);
        mMapInfoHelper.deleteMapByName(mapName);
        deletePlaceByMapName(mapName);
        if (isNapPosNotSeparate()) {
            deleteLocalPlaceByMapName(mapName);
        }
        return true;
    }

    public List<MultiFloorInfo> getMultiFloorConfig() {
        return mMultiFloorInfoHelper.getMultiFloorConfig();
    }

    public boolean deleteMultiFloorInfo(MultiFloorInfo info) {
        boolean result = mMultiFloorInfoHelper.deleteMultiFloorInfo(info);
        if (result) {
            notifyDataChange();
        }
        return result;
    }

    public boolean updateMultiFloorInfoByType(MultiFloorInfo info, String type) {
        MultiFloorInfo floorInfo = mMultiFloorInfoHelper.getMultiFloorConfigByFloorId(info.getFloorId());
        switch (type) {
            case Definition.JSON_NAVI_MULTI_FLOOR_UPDATE_FLOOR_STATE:
                floorInfo.setFloorState(info.getFloorState());
                break;
            case Definition.JSON_NAVI_MULTI_FLOOR_UPDATE_FLOOR_INDEX:
                floorInfo.setFloorIndex(info.getFloorIndex());
                break;
            case Definition.JSON_NAVI_MULTI_FLOOR_UPDATE_FLOOR_ALIAS:
                floorInfo.setFloorAlias(info.getFloorAlias());
                break;
            case Definition.JSON_NAVI_MULTI_FLOOR_UPDATE_ELEVATORS:
                floorInfo.setAvailableElevators(info.getAvailableElevators());
                break;
        }
        boolean result = mMultiFloorInfoHelper.updateMultiFloorInfo(floorInfo);
        if (result) {
            notifyDataChange();
        }
        return result;
    }

    public boolean updateMultiFloorInfo(List<MultiFloorInfo> infoList) {
        boolean result = mMultiFloorInfoHelper.updateMultiFloorInfo(infoList);
        if (result) {
            notifyDataChange();
        }
        return result;
    }

    public List<ChargeArea> getChargeAreaConfig() {
        return mChargeAreaObjectHelper.getChargeAreaConfig();
    }

    public boolean deleteChargeAreaInfo(ChargeArea info) {
        ChargeArea chargeAreaInfo = mChargeAreaObjectHelper.getChargeAreaConfigByAreaId(info.getAreaId());
        boolean result = mChargeAreaObjectHelper.deleteChargeArea(chargeAreaInfo);
        if (result) {
            notifyChargeAreaChange();
        }
        return result;
    }

    public boolean updateChargeAreaInfo(List<ChargeArea> infoList) {
        boolean result = mChargeAreaObjectHelper.updateChargeArea(infoList);
        if (result) {
            notifyChargeAreaChange();
        }
        return result;
    }

    public boolean updateChargeAreaInfoByType(ChargeArea info) {
        ChargeArea chargeAreaInfo = mChargeAreaObjectHelper.getChargeAreaConfigByAreaId(info.getAreaId());
        chargeAreaInfo.setAreaId(info.getAreaId());
        chargeAreaInfo.setAreaAlias(info.getAreaAlias());
        chargeAreaInfo.setMapName(info.getMapName());
        chargeAreaInfo.setUseState(info.getUseState());
        chargeAreaInfo.setAvailableChargePiles(info.getAvailableChargePiles());
        chargeAreaInfo.setAvailableWaitPoints(info.getAvailableWaitPoints());
        boolean result = mChargeAreaObjectHelper.updateChargeArea(chargeAreaInfo);
        if (result) {
            notifyChargeAreaChange();
        }
        return result;
    }

    public boolean deletePlaceByPlaceIds(List<String> placeIds) {
        String[] placeIdArr = placeIds.toArray(new String[0]);
        mPlaceInfoHelper.deletePlaceByPlaceIds(placeIdArr);
        mPlaceNameHelper.deletePlaceNameByPlaceId(placeIdArr);
        return true;
    }

    private void deletePlaceByPlaceName(String placeName) {
        if (placeName.equals(Definition.START_CHARGE_PILE_POSE)) {
            RobotSettingApi.getInstance().setRobotInt(Definition.CHARGING_PILE_CONFIGURED, 0);
        }
        String[] placeIds = mPlaceNameHelper.getPlaceIdsByName(placeName);

        deletePlace(getMapName(), placeIds);
    }

    private void deletePlace(String mapName, String[] placeIdArr) {
        String[] placeIds = mPlaceInfoHelper.deletePlaceInfo(mapName, placeIdArr);
        mPlaceNameHelper.deletePlaceNameByPlaceId(placeIds);
    }

    private void deletePlaceByTypeId(int typeId, int priority) {
        if (typeId == Definition.CHARGING_POLE_TYPE && priority == Definition.SPECIAL_PLACE_HIGH_PRIORITY) {
            RobotSettingApi.getInstance().setRobotInt(Definition.CHARGING_PILE_CONFIGURED, 0);
        }
        String[] placeIds;
        if (isNapPosNotSeparate() && priority == Definition.SPECIAL_PLACE_HIGH_PRIORITY) {
            placeIds = mLocalPlaceInfoHelper.deleteLocalPlaceByTypeId(getMapName(), typeId);
        } else {
            placeIds = mPlaceInfoHelper.deletePlaceByTypeId(getMapName(), typeId, priority);
        }
        if (placeIds != null && placeIds.length > 0) {
            mPlaceNameHelper.deletePlaceNameByPlaceId(placeIds);
        }
    }

    public boolean deleteLocalPlaceByPlaceIds(List<String> placeIds) {
        String[] placeIdArr = placeIds.toArray(new String[0]);
        String[] delIds = mLocalPlaceInfoHelper.deleteLocalPlaceInfoByIds(getMapName(), placeIdArr);
        if (delIds.length > 0) {
            mPlaceNameHelper.deletePlaceNameByPlaceId(placeIdArr);
        }
        return true;
    }

    public boolean deleteLocalPlaceByNameList(List<String> nameList) {
        Log.d(TAG, "deleteLocalPlaceByNameList: " + new Gson().toJson(nameList));
        String[] localIds = mPlaceNameHelper.getPlaceIdsByNameList(nameList.toArray(new String[0]));
        if (localIds != null && localIds.length > 0) {
            String[] localPlaceIds = mLocalPlaceInfoHelper.deleteLocalPlaceInfoByIds(getMapName(), localIds);
            mPlaceNameHelper.deletePlaceNameByPlaceId(localPlaceIds);
            return true;
        }
        return false;
    }

    public PlaceInfo getPlaceById(String placeId) {
        return mPlaceInfoHelper.getPlaceById(placeId);
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    public String getMaxLanguage(String mapName) {
        String[] placeIds = mPlaceInfoHelper.getPlaceIdByMapName(mapName);
        List<PlaceName> placeNames = mPlaceNameHelper.getPlaceNameByPlaceId(placeIds);

        return placeNames.stream()
                .collect(Collectors.groupingBy(PlaceName::getLanguageType, Collectors.counting()))
                .entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);
    }

    /**
     * 从云端拉取点位需要根据场景区分, 从Core中下载地图的Action处调用
     * 1. 点图同步：和之前保持一致
     * 2. 点图分离：需要优先筛选出地图包和点图分离列表中相同placeId和同名的点位，然后更新点位列表
     */
    public void updatePlaceInfoListByScene(List<PlaceInfo> placeList, String mapName) {
        Log.d(TAG, "updatePlaceInfoListByScene: Start");
        long startTime = System.currentTimeMillis();

        boolean result;

        // 根据当前模式选择不同的处理逻辑
        if (!isNapPosNotSeparate()) {
            // 点图分离模式
            result = updatePlaceInfoListNapPosSeparate(placeList, mapName);
            Log.d(TAG, "updatePlaceInfoListNapPosSeparate, result = " + result);
        } else {
            // 点图同步模式 - 保持原有逻辑
            updatePlaceInfoList(placeList, mapName);
        }

        Log.d(TAG, "updatePlaceInfoList: End, total cost time: " + (System.currentTimeMillis() - startTime));
    }

    public void updatePlaceInfoList(List<PlaceInfo> placeList, String mapName) {
        Log.d(TAG, "addPlaceList: resultInfo -Start");
        long startTime = System.currentTimeMillis();
        long startTimeOrigin = startTime;
        startTime = System.currentTimeMillis();
        List<PlaceName> placeNames = new ArrayList<>();
        List<String> nameList = new ArrayList<>();
        for (PlaceInfo placeInfo : placeList) {
            DataManager.getInstance().checkSpecialPlace(placeInfo);
            for (Map.Entry<String, String> entry : placeInfo.getPlaceNameList().entrySet()) {
                PlaceName placeName = new PlaceName();
                placeName.setPlaceId(placeInfo.getPlaceId());
                placeName.setLanguageType(entry.getKey());
                placeName.setPlaceName(entry.getValue());
                placeNames.add(placeName);
                nameList.add(placeName.getPlaceName());
            }
        }
        Log.d(TAG, "addPlaceList:--Pre data cost time:" + (System.currentTimeMillis() - startTime));
        Log.d(TAG, "addPlaceList:--Pre data name size:" + nameList.size());
        boolean resultDel = deletePlaceByNameList(nameList, mapName);
        boolean resultInfo = mPlaceInfoHelper.updatePlaceInfo(placeList);
        boolean resultName = mPlaceNameHelper.updatePlaceNames(placeNames);
        if (resultInfo && resultName) {
            mPlaceTypeHelper.updatePlaceTypeList();
            mPlaceInfoHelper.connectTablePlaceType();
        }
        Log.d(TAG, "addPlaceList: resultInfo=" + resultInfo + " resultName=" + resultName + " resultDel=" + resultDel);
        Log.d(TAG, "addPlaceList:--End Total cost time:" + (System.currentTimeMillis() - startTimeOrigin));
        if (resultInfo & resultName) {
            updateChargingPileStatus();
        }
        // 非点图分离状态 & 没有下载过该地图存储一份本机使用点位到LocalPlaceInfo
        // 下载过的话LocalPlaceInfo不能被覆盖
        if (isNapPosNotSeparate()) {
            boolean isContainsLocalPlace = mLocalPlaceInfoHelper.containsLocalPlaceInfoByMapName(mapName);
            Log.d(TAG, "isContainsLocalPlace: " + isContainsLocalPlace + " mapName: " + mapName);
            if (!isContainsLocalPlace) {
                updateLocalPlaceInfos(mapName);
            } else {
                boolean fixResult = checkPlaceInfoByLocalPlaceInfo(mapName);
                boolean exitInResult = checkLocalPlaceExitInPlaceInfo(mapName);
                boolean specialNotInLocalResult = checkSpecialPlaceInfoNotInLocal(mapName);
                Log.d(TAG, "checkPlaceInfoByLocalPlaceInfo fixResult: " + fixResult +
                        ", exitInResult: " + exitInResult +
                        ", specialNotInLocalResult: " + specialNotInLocalResult);
            }
        }
        Log.d(TAG, "addPlaceList:--End Total cost time(must update local place info):" + (System.currentTimeMillis() - startTimeOrigin));
    }

    /**
     * 点图分离模式下更新点位信息
     * 只替换相同placeId的点位，保留其他非重复点位
     */
    private boolean updatePlaceInfoListNapPosSeparate(List<PlaceInfo> placeList, String mapName) {
        Log.d(TAG, "updatePlaceInfoListNapPosSeparate: Start, placeList size: " + placeList.size());
        long startTime = System.currentTimeMillis();

        // 1. 获取当前地图中的所有点位
        List<PlaceInfo> existingPlaces = mPlaceInfoHelper.getPlaceInfoByMapName(mapName);

        // 检查当前地图是否存在点位
        if (existingPlaces.isEmpty()) {
            Log.d(TAG, "当前地图不存在任何点位，直接添加所有新点位");

            List<PlaceName> placeNames = new ArrayList<>();
            List<String> nameList = new ArrayList<>();
            for (PlaceInfo placeInfo : placeList) {
                DataManager.getInstance().checkSpecialPlace(placeInfo);
                for (Map.Entry<String, String> entry : placeInfo.getPlaceNameList().entrySet()) {
                    PlaceName placeName = new PlaceName();
                    placeName.setPlaceId(placeInfo.getPlaceId());
                    placeName.setLanguageType(entry.getKey());
                    placeName.setPlaceName(entry.getValue());
                    placeNames.add(placeName);
                    nameList.add(placeName.getPlaceName());
                }
            }

            // 原有逻辑：先根据名称删除点位
            boolean resultDel = deletePlaceByNameList(nameList, mapName);

            // 更新PlaceInfo和PlaceName
            boolean resultInfo = false;
            boolean resultName = false;

            if (!placeList.isEmpty()) {
                resultInfo = mPlaceInfoHelper.updatePlaceInfo(placeList);
            } else {
                resultInfo = true; // 如果没有需要更新的点位，视为成功
            }

            if (!placeNames.isEmpty()) {
                resultName = mPlaceNameHelper.updatePlaceNames(placeNames);
            } else {
                resultName = true; // 如果没有需要更新的名称，视为成功
            }

            // 更新PlaceType表
            if (resultInfo && resultName) {
                mPlaceTypeHelper.updatePlaceTypeList();
                mPlaceInfoHelper.connectTablePlaceType();
            }

            Log.d(TAG, "updatePlaceInfoListNapPosSeparate: resultInfo=" + resultInfo + " resultName=" + resultName);

            if (resultInfo & resultName) {
                updateChargingPileStatus();
            }

            return resultInfo & resultName;
        }

        // 2. 收集所有新点位的placeId和名称
        Set<String> newPlaceIds = new HashSet<>();
        Set<String> newPlaceNames = new HashSet<>();
        List<PlaceInfo> specialPlaceToModify = new ArrayList<>();

        for (PlaceInfo place : placeList) {
            DataManager.getInstance().checkSpecialPlace(place);
            newPlaceIds.add(place.getPlaceId());

            // 收集所有点位的名称
            Map<String, String> placeNameList = place.getPlaceNameList();
            if (placeNameList != null && !placeNameList.isEmpty()) {
                for (String name : placeNameList.values()) {
                    if (!TextUtils.isEmpty(name)) {
                        newPlaceNames.add(name);
                    }
                }
            }
        }

        // 创建一个集合，存储placeList中所有typeId不为0且priority为0的typeId
        Set<Integer> specialTypeIds = new HashSet<>();
        for (PlaceInfo newPlace : placeList) {
            if (newPlace.getTypeId() != 0 && newPlace.getPriority() == 0) {
                specialTypeIds.add(newPlace.getTypeId());
            }
        }

        // 遍历existingPlaces，找出typeId不为0且priority为0的特殊点位
        // 并且该typeId在placeList中存在相同的typeId和priority为0的点位，placeId不同
        for (PlaceInfo existingPlace : existingPlaces) {
            if (existingPlace.getTypeId() != 0 && existingPlace.getPriority() == 0
                    && specialTypeIds.contains(existingPlace.getTypeId()) && !newPlaceIds.contains(existingPlace.getPlaceId())) {
                specialPlaceToModify.add(existingPlace);
                Log.d(TAG, "发现需要调整优先级的特殊点位: placeId=" + existingPlace.getPlaceId()
                        + ", typeId=" + existingPlace.getTypeId() + ", priority=" + existingPlace.getPriority());
            }
        }

        // 3. 删除所有重复的placeId对应的PlaceInfo和PlaceName记录
        if (!newPlaceIds.isEmpty()) {
            String[] placeIdsToDelete = newPlaceIds.toArray(new String[0]);
            Log.d(TAG, "删除重复的placeId: " + Arrays.toString(placeIdsToDelete));

            // 先删除PlaceName记录
            mPlaceNameHelper.deletePlaceNameByPlaceId(placeIdsToDelete);

            // 然后删除PlaceInfo记录
            mPlaceInfoHelper.deletePlaceByPlaceIds(placeIdsToDelete);
        }

        // 4. 删除所有重名的点位
        if (!newPlaceNames.isEmpty()) {
            List<String> nameList = new ArrayList<>(newPlaceNames);
            boolean resultDel = deletePlaceByNameList(nameList, mapName);
            Log.d(TAG, "删除重名的点位结果: " + resultDel);
        }

        // 调整特殊点：重复优先级点位
        if (!specialPlaceToModify.isEmpty()) {
            Log.d(TAG, "开始调整特殊点位优先级，共 " + specialPlaceToModify.size() + " 个点位");

            // 1. 统计 placeList 中每个 typeId 的最高优先级
            Map<Integer, Integer> typeIdMaxPriority = new HashMap<>();

            for (PlaceInfo place : placeList) {
                int typeId = place.getTypeId();
                if (typeId != 0) {
                    // 获取当前记录的最高优先级
                    Integer currentMax = typeIdMaxPriority.get(typeId);
                    // 如果当前最高优先级为null或者小于当前点位的优先级，则更新
                    if (currentMax == null || currentMax < place.getPriority()) {
                        typeIdMaxPriority.put(typeId, place.getPriority());
                    }
                }
            }

            // 2. 调整 specialPlaceToModify 中点位的优先级
            for (PlaceInfo placeToModify : specialPlaceToModify) {
                int typeId = placeToModify.getTypeId();

                // 获取该 typeId 在 placeList 中的最高优先级
                Integer maxPriority = typeIdMaxPriority.get(typeId);
                if (maxPriority == null) {
                    maxPriority = 0; // 如果没有找到，默认为0
                }

                // 设置新优先级为最高优先级 + 1
                int newPriority = maxPriority + 1;

                // 更新数据库中的点位优先级
                PlaceInfo dbPlace = mPlaceInfoHelper.getPlaceById(placeToModify.getPlaceId());
                if (dbPlace != null) {
                    dbPlace.setPriority(newPriority);
                    mPlaceInfoHelper.updatePlaceInfo(dbPlace);
                    Log.d(TAG, "调整点位优先级: placeId=" + placeToModify.getPlaceId()
                            + ", typeId=" + typeId
                            + ", 旧优先级=" + placeToModify.getPriority()
                            + ", 新优先级=" + newPriority);
                }
            }

            Log.d(TAG, "完成调整特殊点位优先级");
        }

        // 5. 准备所有点位的PlaceName数据
        List<PlaceName> allPlaceNames = new ArrayList<>();
        for (PlaceInfo placeInfo : placeList) {
            Map<String, String> placeNameList = placeInfo.getPlaceNameList();
            if (placeNameList == null || placeNameList.isEmpty()) {
                Log.w(TAG, "警告: placeId=" + placeInfo.getPlaceId() + " 的点位没有名称");
                continue;
            }

            for (Map.Entry<String, String> entry : placeNameList.entrySet()) {
                String languageType = entry.getKey();
                String placeName = entry.getValue();

                if (TextUtils.isEmpty(placeName)) {
                    Log.w(TAG, "警告: placeId=" + placeInfo.getPlaceId() + ", languageType=" + languageType + " 的点位名称为空");
                    continue;
                }

                PlaceName pn = new PlaceName();
                pn.setPlaceId(placeInfo.getPlaceId());
                pn.setLanguageType(languageType);
                pn.setPlaceName(placeName);
                allPlaceNames.add(pn);
            }
        }

        // 6. 更新PlaceInfo和PlaceName
        boolean resultInfo = false;
        boolean resultName = false;

        if (!placeList.isEmpty()) {
            resultInfo = mPlaceInfoHelper.updatePlaceInfo(placeList);
            Log.d(TAG, "更新PlaceInfo: " + resultInfo + ", 点位数量: " + placeList.size());
        } else {
            resultInfo = true;
        }

        if (!allPlaceNames.isEmpty()) {
            resultName = mPlaceNameHelper.updatePlaceNames(allPlaceNames);
            Log.d(TAG, "更新PlaceName: " + resultName + ", 名称数量: " + allPlaceNames.size());
        } else {
            resultName = true;
        }

        // 7. 更新PlaceType表
        if (resultInfo && resultName) {
            mPlaceTypeHelper.updatePlaceTypeList();
            mPlaceInfoHelper.connectTablePlaceType();
        }

        if (resultInfo & resultName) {
            updateChargingPileStatus();
        }

        Log.d(TAG, "updatePlaceInfoListNapPosSeparate: End, total cost time: " + (System.currentTimeMillis() - startTime));
        return resultInfo & resultName;
    }

    private void updateLocalPlaceInfos(String mapName) {
        List<PlaceInfo> placeInfoList = mPlaceInfoHelper.getPlaceInfoHighPriority(mapName);
        handlePlaceInfoWithName(placeInfoList);
        Log.d(TAG, "updateLocalPlaceInfos placeInfoList: " + new Gson().toJson(placeInfoList));
        String mapLanguage = getMapLanguage(mapName);
        List<LocalPlaceInfo> localPlaceInfoList = CovertUtils.covertToLocalPlaceInfoList(placeInfoList, mapLanguage);
        List<PlaceName> placeNames = new ArrayList<>();
        for(LocalPlaceInfo placeInfo : localPlaceInfoList) {
            PlaceName placeName = new PlaceName();
            placeName.setPlaceId(placeInfo.getPlaceId());
            placeName.setLanguageType(mapLanguage);
            placeName.setPlaceName(placeInfo.getPlaceName());
            placeNames.add(placeName);
        }
        if (!localPlaceInfoList.isEmpty()) {
            mLocalPlaceInfoHelper.updateLocalPlaceInfo(localPlaceInfoList);
        } else {
            Log.i(TAG, "updateLocalPlaceInfos failed");
        }
    }

    /**
     * 编辑地图更新点位使用
     * 适用范围：更新本地表-本机使用点位-LocalPlaceInfo
     */
    private void updateLocalPlaceList(List<LocalPlaceInfo> placeList) {
        // 更新本地列表，当前要更新的Local表点位。从上层的编辑地图处调用
        Log.d(TAG, "updateLocalPlaceList: " + new Gson().toJson(placeList));
        // 1. 找到当前需要更新的点位
        List<LocalPlaceInfo> finalLocalPlaces = getLocalPlaceList(placeList);
        Log.d(TAG, "finalLocalPlaces: " + new Gson().toJson(finalLocalPlaces));
        // 2. 判断当前点位需要删除的和需要更新的
        List<LocalPlaceInfo> currentLocalPlaces = mLocalPlaceInfoHelper.getLocalPlaceInfo(getMapName());
        // 创建一个Map来存储finalLocalPlaces中的typeId到LocalPlaceInfo的映射
        Map<Integer, LocalPlaceInfo> finalTypeToPlace = new HashMap<>();
        for (LocalPlaceInfo place : finalLocalPlaces) {
            finalTypeToPlace.put(place.getTypeId(), place);
        }
        // 筛选出需要删除的点位：typeId相同但placeId不同的点位
        List<LocalPlaceInfo> deleteLocalPlaces = new ArrayList<>();
        for (LocalPlaceInfo currentPlace : currentLocalPlaces) {
            int typeId = currentPlace.getTypeId();
            // 如果当前点位的typeId在finalLocalPlaces中存在
            LocalPlaceInfo finalPlace = finalTypeToPlace.get(typeId);
            if (finalPlace != null) {
                // 但是placeId不同
                if (!finalPlace.getPlaceId().equals(currentPlace.getPlaceId())) {
                    deleteLocalPlaces.add(currentPlace);
                }
            }
        }
        // 3. 执行删除和更新
        String[] deleteIds = deleteLocalPlaces.stream()
                .map(LocalPlaceInfo::getPlaceId)
                .toArray(String[]::new);
        Log.d(TAG, "deleteIds local info: " + Arrays.toString(deleteIds));
        mLocalPlaceInfoHelper.deleteLocalPlaceInfoByIds(getMapName(), deleteIds);
        mLocalPlaceInfoHelper.updateLocalPlaceInfo(finalLocalPlaces);
    }

    private void handlePlaceInfoWithName(List<PlaceInfo> placeInfoList) {
        int size = placeInfoList.size();
        String[] placeIds = new String[size];
        Map<String, PlaceInfo> placeInfoMap = new HashMap<>();
        for (int i = 0; i < size; i++) {
            PlaceInfo info = placeInfoList.get(i);
            String placeId = info.getPlaceId();
            placeIds[i] = placeId;
            placeInfoMap.put(placeId, info);
        }

        List<PlaceName> placeNames = mPlaceNameHelper.getPlaceNameByPlaceId(placeIds);
        for (PlaceName name : placeNames) {
            String placeId = name.getPlaceId();
            String placeName = name.getPlaceName();
            String languageType = name.getLanguageType();
            PlaceInfo placeInfo = placeInfoMap.get(placeId);
            if (placeInfo != null) {
                placeInfo.addPlaceName(languageType, placeName);
            }
        }
    }

    private boolean deletePlaceByNameList(List<String> nameList, String mapName) {
        String[] placeIds = getPlaceIdsByNameList(nameList);

        deletePlace(mapName, placeIds);
        return true;
    }

    public void deletePlaceByMapName(String mapName) {
        String[] placeIds = mPlaceInfoHelper.deletePlaceByMapName(mapName);
        mPlaceNameHelper.deletePlaceNameByPlaceId(placeIds);
    }

    public void deleteLocalPlaceByMapName(String mapName) {
        String[] placeIds = mLocalPlaceInfoHelper.deleteLocalPlaceInfo(mapName);
        mPlaceNameHelper.deletePlaceNameByPlaceId(placeIds);
    }

    /**
     * 第一步 批量更新placeName表中placeId在idArr中且languageType是language的数据，更新这些数据name在newNameList
     * 第二步 批量更新placeInfo表中placeId在idArr中的数据，更新这些数据的updateTime为当前时间戳
     *
     * @param idArr       placeId数组
     * @param newNameList 新名字列表
     * @param language    语言
     * @return 更新是否成功
     */
    public boolean updatePlaceByIdList(String[] idArr, List<String> newNameList, String language) {
        //第一步
        mPlaceNameHelper.updatePlace(idArr, newNameList, language);

        //第二步
        mPlaceInfoHelper.updateTimeOneOfIdArr(idArr);
        return true;
    }

    /**
     * 更新旧名字到新名字
     *
     * @param oldNames 旧名字列表
     * @param newNames 新名字列表
     * @param language 语言
     * @return 更新是否成功
     */
    public boolean updatePlaceByNameList(List<String> oldNames, List<String> newNames, String language) {
        String[] placeIds = getPlaceIdsByNameList(oldNames);
        String[] placeIdArr = mPlaceInfoHelper.getStrings(placeIds, getMapName());

        return updatePlaceByIdList(placeIdArr, newNames, language);
    }

    private boolean renameMapPlace(MapInfo mapInfo, String newMapName) {
        List<PlaceInfo> placeBeanList = mPlaceInfoHelper.getPlaceInfoByMapName(mapInfo.getMapName());
        Log.d(TAG, "renameMapPlace: placeBeanList=" + placeBeanList.toString());
        int size = placeBeanList.size();
        String[] placeIds = new String[size];
        Map<String, String> map = new HashMap<>();
        for (int i = 0; i < size; i++) {
            PlaceInfo placeInfo = placeBeanList.get(i);
            placeInfo.setMapName(newMapName);
            String oldPlaceId = placeInfo.getPlaceId();
            String newPlaceId = UuidUtils.createPlaceId(placeInfo);
            placeInfo.setPlaceId(newPlaceId);
            placeIds[i] = oldPlaceId;
            map.put(oldPlaceId, newPlaceId);
        }
        mPlaceInfoHelper.updatePlaceInfo(placeBeanList);
        // 如果是点图同步地图，需要更新LocalPlaceInfo数据表
        if (isNapPosNotSeparate()) {
            List<LocalPlaceInfo> localPlaceInfoList = mLocalPlaceInfoHelper.getLocalPlaceInfo(mapInfo.getMapName());
            Log.d(TAG, "renameMapPlace: localPlaceInfoList=" + localPlaceInfoList.toString());
            for (LocalPlaceInfo localPlaceInfo : localPlaceInfoList) {
                localPlaceInfo.setMapName(newMapName);
                localPlaceInfo.setPlaceId(map.get(localPlaceInfo.getPlaceId()));
            }
            if (localPlaceInfoList.size() > 0) {
                mLocalPlaceInfoHelper.updateLocalPlaceInfo(localPlaceInfoList);
            }
        }

        List<PlaceName> placeNames = mPlaceNameHelper.getPlaceNameByPlaceId(placeIds);
        for (PlaceName placeName : placeNames) {
            placeName.setPlaceId(map.get(placeName.getPlaceId()));
        }
        mPlaceNameHelper.updatePlaceNames(placeNames);
        return true;
    }

    private void addPlace(PlaceInfo placeBean) {
        DataManager.getInstance().checkSpecialPlace(placeBean);
        HashMap<String, String> placeNameList = placeBean.getPlaceNameList();
        if ((placeBean.getTypeId() == Definition.CHARGING_POLE_TYPE &&
                placeBean.getPriority() == Definition.SPECIAL_PLACE_HIGH_PRIORITY) ||
                Definition.START_CHARGE_PILE_POSE.equals(placeNameList.get(DEFAULT_LANGUAGE))
                && placeBean.getMapName().equals(getMapName())) {
            RobotSettingApi.getInstance().setRobotInt(Definition.CHARGING_PILE_CONFIGURED, 1);
        }
        if (isNapPosNotSeparate()) {
            LocalPlaceInfo localPlaceInfo = CovertUtils.convertToLocalPlaceInfo(placeBean, getCurrentMapLanguage());
            placeBean.setPlaceId(localPlaceInfo.getPlaceId());
            mLocalPlaceInfoHelper.updateLocalPlaceInfo(localPlaceInfo);
            mPlaceInfoHelper.updatePlaceInfo(placeBean);
        } else {
            mPlaceInfoHelper.updatePlaceInfo(placeBean);
        }
        List<PlaceName> placeNames = new ArrayList<>();
        String placeId = placeBean.getPlaceId();
        for (Map.Entry<String, String> names : placeNameList.entrySet()) {
            placeNames.add(new PlaceName(placeId, names.getKey(), names.getValue()));
        }
        mPlaceNameHelper.updatePlaceNames(placeNames);
    }

    public String[] getPlaceIdsByNameList(List<String> nameList) {
        return mPlaceNameHelper.getPlaceIdsByNameList(nameList.toArray(new String[0]));
    }

    public List<PlaceInfo> getPlaceInfosByPlaceIds(String[] placeIds, String mapName) {
        return mPlaceInfoHelper.getPlaceInfos(mapName, placeIds);
    }

    private void initTypeIdAndPriority() {
        mPlaceTypeHelper.initPlaceTypeData();
        if (mPlaceNameHelper.hasPlaceNameData()) {
            mPlaceTypeHelper.updatePlaceTypeList();
            mPlaceInfoHelper.connectTablePlaceType();
        }

        //获取所有点位信息并遍历打印，
        String mapName = getMapName();
        Log.d(TAG, "initTypeIdAndPriority:Debug: mapName=" + mapName);
        if(!TextUtils.isEmpty(mapName)){
            List<PlaceInfo> placeInfoList = mPlaceInfoHelper.getPlaceInfoByMapName(mapName);
            if(placeInfoList != null && placeInfoList.size() > 0){
                for (PlaceInfo placeInfo : placeInfoList) {
                    Log.d(TAG, "initTypeIdAndPriority:Debug: placeInfo=" + placeInfo.toString());
                }
            }
        }
    }

    //是否点图同步状态
    public boolean isNapPosNotSeparate() {
        boolean isNapPosSeparate = PosSeparateUtils.isNapPosSeparate(mContext);
        Log.d(TAG, "isNapPosNotSeparate: 是否点图同步：" + !isNapPosSeparate);
        return !isNapPosSeparate;
    }

    //LocalPlaceInfo表是否有数据
    private boolean hasLocalPlaceInfoData() {
        boolean hasLocalPlaceInfoData = mLocalPlaceInfoHelper.hasLocalPlaceInfoData();
        Log.d(TAG, "hasLocalPlaceInfoData: " + hasLocalPlaceInfoData);
        return hasLocalPlaceInfoData;
    }

    private List<LocalPlaceInfo> getLocalPlaceList(List<LocalPlaceInfo> placeInfoList) {
        Log.d(TAG, "getLocalPlaceList: " + new Gson().toJson(placeInfoList));
        List<LocalPlaceInfo> placeInfos;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            placeInfos = placeInfoList.stream()
                    .filter(placeInfo -> placeInfo.getTypeId() != 0 && placeInfo.getPriority() == 0)
                    .collect(Collectors.toList());
        } else {
            placeInfos = new ArrayList<>();
            for (LocalPlaceInfo placeInfo : placeInfoList) {
                if (placeInfo.getTypeId() != 0 && placeInfo.getPriority() == 0) {
                    placeInfos.add(placeInfo);
                }
            }
        }
        return placeInfos;
    }

    private final List<MultiFloorChangeListener> multiFloorChangeListeners = new ArrayList<>();
    private final List<ChargeAreaChangeListener> chargeAreaChangeListeners = new ArrayList<>();

    /**
     * 多楼层数据变化监听
     */
    public interface MultiFloorChangeListener {
        void onDataChanged();
    }

    public interface ChargeAreaChangeListener {
        void onDataChanged();
    }

    public void registerDataChangeListener(MultiFloorChangeListener listener) {
        synchronized (multiFloorChangeListeners) {
            if (!multiFloorChangeListeners.contains(listener)) {
                multiFloorChangeListeners.add(listener);
            }
        }
    }

    public void unregisterDataChangeListener(MultiFloorChangeListener listener) {
        synchronized (multiFloorChangeListeners) {
            multiFloorChangeListeners.remove(listener);
        }
    }

    private void notifyDataChange() {
        synchronized (multiFloorChangeListeners) {
            for (MultiFloorChangeListener listener : multiFloorChangeListeners) {
                listener.onDataChanged();
            }
        }
    }
    public void registerChargeAreaChangeListener(ChargeAreaChangeListener listener) {
        synchronized (chargeAreaChangeListeners) {
            if (!chargeAreaChangeListeners.contains(listener)) {
                chargeAreaChangeListeners.add(listener);
            }
        }
    }

    public void unregisterChargeAreaChangeListener(ChargeAreaChangeListener listener) {
        synchronized (chargeAreaChangeListeners) {
            chargeAreaChangeListeners.remove(listener);
        }
    }

    private void notifyChargeAreaChange() {
        synchronized (chargeAreaChangeListeners) {
            for (ChargeAreaChangeListener listener : chargeAreaChangeListeners) {
                listener.onDataChanged();
            }
        }
    }


    public String getPlaceNameHighPriority(String name) {
        Log.d(TAG, "getPlaceNameHighPriority: " + name);
        String mapName = getMapName();
        int typeId = 0;
        if (!name.isEmpty() && DataManager.getInstance().isSpecialPlace(name)) {//特殊点位是否最高优先级
            String[] placeIds = mPlaceNameHelper.getPlaceIdsByName(name);
            List<PlaceInfo> placeInfos = mPlaceInfoHelper.getPlaceInfos(mapName, placeIds);
            if (!placeInfos.isEmpty()) {
                PlaceInfo placeInfo = placeInfos.get(0);
                // 说明当前传入点位名称本身就被本机使用，无需再做本机使用特殊点位映射
                if(placeInfo.getPriority() == Definition.SPECIAL_PLACE_HIGH_PRIORITY) {
                    return name;
                }
                typeId = placeInfo.getTypeId();
            }
        }
        if (typeId != Definition.NORMAL_POINT_TYPE) {
            PlaceInfo placeInfo = mPlaceInfoHelper.getPlaceByType(typeId, Definition.SPECIAL_PLACE_HIGH_PRIORITY, mapName);
            List<PlaceName> placeNameList = mPlaceNameHelper.getPlaceNameByPlaceId(new String[]{placeInfo.getPlaceId()});
            if (!placeNameList.isEmpty()) {
                Log.d(TAG, "getPlaceNameHighPriority findNameHighPriorityPoint: " + placeNameList.get(0).getPlaceName());
                return placeNameList.get(0).getPlaceName();
            }
        }
        return name;
    }

    public void deleteAllData() {
        mExtraInfoHelper.deleteAllData();
        mMapInfoHelper.deleteAllData();
        mMappingInfoHelper.deleteAllData();
        mMultiFloorInfoHelper.deleteAllData();
        mChargeAreaObjectHelper.deleteAllData();
        mPlaceInfoHelper.deleteAllData();
        mPlaceNameHelper.deleteAllData();
        mPlaceTypeHelper.deleteAllData();
        mLocalPlaceInfoHelper.deleteAllData();
        mGateRelationInfoHelper.deleteAllData();
        deleteNavigationConfig();
    }

    //删除导航配置
    public void deleteNavigationConfig() {
        mChassisInfoHelper.deleteAllData();
    }

    public void migrateMapDataToSqlite(Context context) {
        if (dbModeIsSqlite()){
            Log.d(TAG, "migrateMapDataToSqlite dbModeIsSqlite, no need to migrate");
            return;
        }
        Log.d(TAG, "migrateMapDataToSqlite: ");
        SqliteDbMigrate dbMigrate = new SqliteDbMigrate(context);
        ChassisInfoSqliteHelper chassisInfoSqliteHelper = new ChassisInfoSqliteHelper(dbMigrate);
        chassisInfoSqliteHelper.initChassisInfoData(context, mChassisInfoHelper.getAllDbInfo());

        ExtraInfoSqliteHelper extraInfoSqliteHelper = new ExtraInfoSqliteHelper(dbMigrate);
        extraInfoSqliteHelper.initExtraInfoData(mExtraInfoHelper.getAllDbInfo());

        MapInfoSqliteHelper mapInfoSqliteHelper = new MapInfoSqliteHelper(dbMigrate);
        mapInfoSqliteHelper.initMapInfoData(mMapInfoHelper.getAllDbInfo());

        MappingInfoSqliteHelper mappingInfoSqliteHelper = new MappingInfoSqliteHelper(dbMigrate);
        mappingInfoSqliteHelper.initMappingData(mMappingInfoHelper.getAllDbInfo());

        MultiFloorInfoSqliteHelper multiFloorInfoSqliteHelper = new MultiFloorInfoSqliteHelper(dbMigrate);
        multiFloorInfoSqliteHelper.initMultiFloorInfoData(mMultiFloorInfoHelper.getAllDbInfo());

        PlaceInfoSqliteHelper placeInfoSqliteHelper = new PlaceInfoSqliteHelper(dbMigrate);
        placeInfoSqliteHelper.initPlaceInfoData(mPlaceInfoHelper.getAllDbInfo());

        PlaceNameSqliteHelper placeNameSqliteHelper = new PlaceNameSqliteHelper(dbMigrate);
        placeNameSqliteHelper.initPlaceNameData(mPlaceNameHelper.getAllDbInfo());
    }

    /**
     * 解决非点位分离地图，多机操作时，修改点位优先级上传到云端,
     * 另一台机器下载地图特殊点位和本地存储LocalPlaceInfo表中点位重名问题。
     */
    private boolean checkPlaceInfoByLocalPlaceInfo(String mapName) {
        boolean result = false;
        Log.d(TAG, "start checkPlaceInfoByLocalPlaceInfo");
        List<LocalPlaceInfo> localPlaceInfoList = mLocalPlaceInfoHelper.getLocalPlaceInfo(mapName);
        MapInfo mapInfo = mMapInfoHelper.getMapByName(mapName);
        if (mapInfo == null) return false;
        String mapLanguage = mapInfo.getMapLanguage();
        if (localPlaceInfoList != null && !localPlaceInfoList.isEmpty()) {
            for (LocalPlaceInfo localPlaceInfo : localPlaceInfoList) {
                int typeId = localPlaceInfo.getTypeId();
                String localPlaceName = localPlaceInfo.getPlaceName();
                String placeInfoName = "";
                List<PlaceInfo> placeInfoList = mPlaceInfoHelper.getPlacesByType(typeId, mapName); // 此方法已按照优先级从小到大排序
                if (placeInfoList == null || placeInfoList.isEmpty()) {
                    Log.e(TAG, "placeInfoList is empty");
                    continue;
                }
                int nextPriority = placeInfoList.get(placeInfoList.size() - 1).getPriority() + 1;
                Map<String, PlaceInfo> tempMap = new HashMap<>();
                for (PlaceInfo placeInfo : placeInfoList) {
                    tempMap.put(placeInfo.getPlaceId(), placeInfo);
                }
                PlaceInfo placeInfo1 = null;
                for (String placeId : tempMap.keySet()) {
                    placeInfo1 = tempMap.get(placeId);
                    List<PlaceName> placeNameList = mPlaceNameHelper.getPlaceNameByPlaceId(new String[]{placeId});
                    if (placeNameList != null && !placeNameList.isEmpty()) {
                        if (placeNameList.size() == 1) {
                            placeInfoName = placeNameList.get(0).getPlaceName();
                        } else {
                            List<PlaceName> placeNames = placeNameList.stream()
                                    .filter(placeName -> mapLanguage.equals(placeName.getLanguageType()))
                                    .collect(Collectors.toList());
                            placeInfoName = placeNames.get(0).getPlaceName();
                        }
                    }
                    // 这里通过名字判断是否local表中和placeInfo中有相同名称但不同优先级点位。
                    if (localPlaceName.equals(placeInfoName) && placeInfo1 != null &&
                            placeInfo1.getPriority() != Definition.SPECIAL_PLACE_HIGH_PRIORITY) {
                        placeInfo1.setPriority(Definition.SPECIAL_PLACE_HIGH_PRIORITY);
                        mPlaceInfoHelper.updatePlaceInfo(placeInfo1);
                        Log.d(TAG, "check fix placeInfo1 priority: " + placeInfo1);
                        List<PlaceInfo> placeInfos = placeInfoList.stream()
                            .filter(info -> typeId == info.getTypeId() && info.getPriority() == Definition.SPECIAL_PLACE_HIGH_PRIORITY)
                            .collect(Collectors.toList());
                        if (placeInfos.size() > 0) {
                            PlaceInfo placeInfo2 = placeInfos.get(0);
                            if (placeInfo2 != null) {
                                placeInfo2.setPriority(nextPriority);
                                mPlaceInfoHelper.updatePlaceInfo(placeInfo2);
                                Log.d(TAG, "check fix placeInfo2 priority: " + placeInfo2);
                            }
                        }
                        result = true;
                    }
                }
             }
        }
        return result;
    }

    private boolean checkLocalPlaceExitInPlaceInfo(String mapName) {
        boolean result = false;
        Log.d(TAG, "start checkPlaceInfoByLocalPlaceInfo");
        List<LocalPlaceInfo> localPlaceInfoList = mLocalPlaceInfoHelper.getLocalPlaceInfo(mapName);
        MapInfo mapInfo = mMapInfoHelper.getMapByName(mapName);
        if (mapInfo == null) return false;
        String mapLanguage = mapInfo.getMapLanguage();

        // 处理Local表中记录在PlaceInfo表中被删除的情况
        if (localPlaceInfoList != null && !localPlaceInfoList.isEmpty()) {
            // 需要更新的局部变量
            boolean needUpdate = false;

            // 处理每个Local点位
            for (LocalPlaceInfo localInfo : localPlaceInfoList) {
                int typeId = localInfo.getTypeId();
                // 检查PlaceInfo表中是否有相同typeId且优先级为0的点位
                List<PlaceInfo> matchingPlaceInfos = mPlaceInfoHelper.getPlacesByType(typeId, mapName);

                // 从匹配的点位中筛选出优先级为0的点位
                List<PlaceInfo> priorityZeroInfos = matchingPlaceInfos.stream()
                        .filter(info -> info.getPriority() == 0)
                        .collect(Collectors.toList());

                // 检查我们的local点位是否在这些优先级为0的点位中
                boolean localPlaceExists = priorityZeroInfos.stream()
                        .anyMatch(info -> info.getPlaceId().equals(localInfo.getPlaceId()));

                // 如果local点位不在优先级为0的点位中，但存在其他优先级为0的点位
                if (!localPlaceExists && !priorityZeroInfos.isEmpty()) {
                    // 获取第一个优先级为0的点位作为替代
                    PlaceInfo replacementInfo = priorityZeroInfos.get(0);

                    // 删除旧的LocalPlaceInfo
                    mLocalPlaceInfoHelper.deleteLocalPlaceInfoByIds(mapName, new String[]{localInfo.getPlaceId()});
                    mPlaceNameHelper.deletePlaceNameByPlaceId(new String[]{localInfo.getPlaceId()});

                    LocalPlaceInfo newLocalInfo = CovertUtils.convertToLocalPlaceInfo(replacementInfo, getMapLanguage(mapName));

                    // 获取点位名称
                    List<PlaceName> placeNames = mPlaceNameHelper.getPlaceNameByPlaceId(new String[]{replacementInfo.getPlaceId()});
                    String placeName = null;
                    if (!placeNames.isEmpty()) {
                        placeName = placeNames.stream()
                                .filter(name -> mapLanguage.equals(name.getLanguageType()))
                                .map(PlaceName::getPlaceName)
                                .findFirst()
                                .orElseGet(() -> placeNames.get(0).getPlaceName());
                    }
                    newLocalInfo.setPlaceName(placeName);

                    // 更新LocalPlaceInfo表
                    mLocalPlaceInfoHelper.updateLocalPlaceInfo(newLocalInfo);

                    Log.d(TAG, "Replaced deleted local place with typeId: " + typeId +
                            ", old placeId: " + localInfo.getPlaceId() +
                            ", new placeId: " + replacementInfo.getPlaceId());

                    needUpdate = true;
                }

                // 如果存在，判断该点位是否可能在另一台机器被替换了位置
                if (localPlaceExists) {
                    PlaceInfo replacementInfo = priorityZeroInfos.get(0);
                    if (localInfo.getPointX() != replacementInfo.getPointX() ||
                        localInfo.getPointY() != replacementInfo.getPointY() ||
                        localInfo.getPointTheta() != replacementInfo.getPointTheta()) {
                        localInfo.setPointX(replacementInfo.getPointX());
                        localInfo.setPointY(replacementInfo.getPointY());
                        localInfo.setPointTheta(replacementInfo.getPointTheta());
                        Log.d(TAG, "update local info, because same place id update position: " + localInfo.toString());
                        mLocalPlaceInfoHelper.updateLocalPlaceInfo(localInfo);
                    }
                }
            }

            if (needUpdate) {
                result = true;
            }
        }
        return result;
    }

    /**
     * 处理PlaceInfo表中有特殊点（优先级为0）但在Local表中不存在对应类型点位的情况
     * 这种情况可能发生在其他机器添加了新的特殊点位，而本机尚未同步
     */
    private boolean checkSpecialPlaceInfoNotInLocal(String mapName) {
        boolean result = false;
        Log.d(TAG, "start checkSpecialPlaceInfoNotInLocal");

        // 获取地图信息
        MapInfo mapInfo = mMapInfoHelper.getMapByName(mapName);
        if (mapInfo == null) return false;
        String mapLanguage = mapInfo.getMapLanguage();

        // 获取Local表中所有点位的typeId集合
        List<LocalPlaceInfo> localPlaceInfoList = mLocalPlaceInfoHelper.getLocalPlaceInfo(mapName);
        Set<Integer> localTypeIds = new HashSet<>();
        if (localPlaceInfoList != null && !localPlaceInfoList.isEmpty()) {
            for (LocalPlaceInfo localInfo : localPlaceInfoList) {
                localTypeIds.add(localInfo.getTypeId());
            }
        }

        // 获取PlaceInfo表中所有特殊点位（typeId不为0且priority为0）
        List<PlaceInfo> allPlaceInfos = mPlaceInfoHelper.getPlaceInfoByMapName(mapName);
        if (allPlaceInfos == null || allPlaceInfos.isEmpty()) {
            return false;
        }

        // 找出PlaceInfo表中有但Local表中没有的特殊点位
        for (PlaceInfo placeInfo : allPlaceInfos) {
            int typeId = placeInfo.getTypeId();
            if (typeId != 0 && placeInfo.getPriority() == 0 && !localTypeIds.contains(typeId)) {
                // 创建新的LocalPlaceInfo
                LocalPlaceInfo newLocalInfo = CovertUtils.convertToLocalPlaceInfo(placeInfo, mapLanguage);

                // 获取点位名称
                List<PlaceName> placeNames = mPlaceNameHelper.getPlaceNameByPlaceId(new String[]{placeInfo.getPlaceId()});
                String placeName = null;
                if (placeNames != null && !placeNames.isEmpty()) {
                    placeName = placeNames.stream()
                            .filter(name -> mapLanguage.equals(name.getLanguageType()))
                            .map(PlaceName::getPlaceName)
                            .findFirst()
                            .orElseGet(() -> placeNames.get(0).getPlaceName());
                }
                newLocalInfo.setPlaceName(placeName);

                // 更新LocalPlaceInfo表
                mLocalPlaceInfoHelper.updateLocalPlaceInfo(newLocalInfo);

                Log.d(TAG, "Added missing special place to LocalPlaceInfo with typeId: " + typeId +
                        ", placeId: " + placeInfo.getPlaceId() +
                        ", name: " + placeName);

                result = true;

                // 将该typeId添加到localTypeIds，避免重复处理同一typeId
                localTypeIds.add(typeId);
            }
        }

        return result;
    }

    /**
     * 根据PlaceInfo表更新PlaceName表中的名字
     * 1. 点图同步和点图分离都需要更新PlaceInfo中对应的PlaceName
     * 2. 点图同步，需要根据PlaceInfo和LocalPlaceInfo有相同PlaceId的Local对象，更新名字
     */
    public boolean updatePlaceNameByRename(List<PlaceInfo> renamePlaceInfos, HashMap<String, String> renameMap) {
        // 根据renamePlaceInfos批量更新名字到PlaceName表中 区分三种情况
        boolean result;
        for(PlaceInfo placeInfo : renamePlaceInfos) {
            String placeId = placeInfo.getPlaceId();
            if (placeId != null) {
                List<PlaceName> placeNames = mPlaceNameHelper.getPlaceNameByPlaceId(new String[]{placeId});
                if (placeNames.isEmpty()) {
                    continue;
                }
                // 查找与renameMap中的键匹配的PlaceName
                PlaceName matchedPlaceName = placeNames.stream()
                        .filter(pn -> renameMap.containsKey(pn.getPlaceName()))
                        .findFirst()
                        .orElse(null);

                String oldName;
                String newName;

                if (matchedPlaceName != null) {
                    // 找到匹配的名称
                    oldName = matchedPlaceName.getPlaceName();
                    newName = renameMap.get(oldName);
                } else {
                    // 没有找到匹配的名称，使用第一个名称尝试
                    oldName = placeNames.get(0).getPlaceName();
                    newName = renameMap.get(oldName);
                    if (newName == null) {
                        // 如果renameMap中没有对应的新名称，跳过此点位
                        continue;
                    }
                }
                Log.d(TAG, "updatePlaceName typeId: " + placeInfo.getTypeId() + ", oldName: " + oldName + ", newName: " + newName);

                switch (placeInfo.getTypeId()) {
                    case Definition.CHARGING_POINT_TYPE:
                        String chargePileName = SpecialPlaceUtil.getInstance().getAfterDash(newName != null ? newName : "error");
                        if (chargePileName.equals("error")) break;
                        for (PlaceName placeName : placeNames) {
                            placeName.setPlaceName(SpecialPlaceUtil.getInstance().replaceAfterDash(placeName.getPlaceName(), chargePileName));
                        }
                        result = mPlaceNameHelper.updatePlaceNames(placeNames);
                        Log.d(TAG, "update rename place charging point result: " + result);
                        break;
                    case Definition.ELEVATOR_CENTER_TYPE:
                    case Definition.ELEVATOR_ENTRANCE_TYPE:
                        String elevatorName = SpecialPlaceUtil.getInstance().getBeforeDash(newName != null ? newName : "error");
                        if (elevatorName.equals("error")) break;
                        for (PlaceName placeName : placeNames) {
                            placeName.setPlaceName(SpecialPlaceUtil.getInstance().replaceBeforeDash(placeName.getPlaceName(), elevatorName));
                        }
                        result = mPlaceNameHelper.updatePlaceNames(placeNames);
                        Log.d(TAG, "update rename place elevator result: " + result + ", typeId: " + placeInfo.getTypeId());
                        break;
                    default:
                        // 更新特殊点位名字编辑只有一个非多语言情况
                        placeNames.get(0).setPlaceName(renameMap.get(oldName));
                        result = mPlaceNameHelper.updatePlaceNames(placeNames);
                        Log.d(TAG, "update rename place result: " + result + ", typeId: " + placeInfo.getTypeId());
                        break;
                }
            }
        }

        // 点图同步情况下，需要单独处理: 编辑的点位和Local表中的点位PlaceId是否有相同，有相同的话更新名字
        if (isNapPosNotSeparate()) {
            String mapName = getMapName();
            String currentMapLanguage = getCurrentMapLanguage();

            if (!renamePlaceInfos.isEmpty()) {
                List<LocalPlaceInfo> localPlaceInfoList = mLocalPlaceInfoHelper.getLocalPlaceInfo(mapName);
                if (!localPlaceInfoList.isEmpty()) {
                    // 创建placeId到LocalPlaceInfo的映射，提高查找效率
                    Map<String, LocalPlaceInfo> localPlaceMap = new HashMap<>();
                    for (LocalPlaceInfo localInfo : localPlaceInfoList) {
                        localPlaceMap.put(localInfo.getPlaceId(), localInfo);
                    }

                    // 创建需要更新的LocalPlaceInfo列表
                    List<LocalPlaceInfo> updatedLocalPlaceList = new ArrayList<>();
                    // 遍历renamePlaceInfos，查找并更新对应的本地点位
                    for (PlaceInfo placeInfo : renamePlaceInfos) {
                        String placeId = placeInfo.getPlaceId();
                        LocalPlaceInfo localInfo = localPlaceMap.get(placeId);

                        if (localInfo != null) {
                            // 找到匹配的本地点位，直接从PlaceName表获取最新名称
                            List<PlaceName> placeNames = mPlaceNameHelper.getPlaceNameByPlaceId(new String[]{placeId});
                            if (!placeNames.isEmpty()) {
                                // 优先选择当前地图语言的名称
                                String newName = placeNames.stream()
                                        .filter(placeName -> currentMapLanguage.equals(placeName.getLanguageType()))
                                        .map(PlaceName::getPlaceName)
                                        .findFirst()
                                        .orElseGet(() -> placeNames.isEmpty() ? null : placeNames.get(0).getPlaceName());

                                // 如果没找到当前语言的名称，使用第一个
                                if (newName == null && !placeNames.isEmpty()) {
                                    newName = placeNames.get(0).getPlaceName();
                                }

                                if (newName != null) {
                                    localInfo.setPlaceName(newName);
                                    updatedLocalPlaceList.add(localInfo);
                                    Log.d(TAG, "Updated local place info rename: " + localInfo);
                                }
                            }
                        }
                    }
                    // 批量更新修改后的本地点位
                    if (!updatedLocalPlaceList.isEmpty()) {
                        mLocalPlaceInfoHelper.updateLocalPlaceInfo(updatedLocalPlaceList);
                        Log.d(TAG, "Updated " + updatedLocalPlaceList.size() + " local place info names");
                    }
                }
            }
        }
        return true;
    }

    /**
     * 更新PlaceInfo表中点位的位置
     * 1. 点图分离和点位同步：都需要修改PlaceInfo表
     * 2. 点图同步：需要单独更新LocalPlaceInfo中，若PlaceInfo中有相同placeId在Local表中，需要更新Local点位
     */
    public boolean updatePlaceNameByReplace(List<PlaceInfo> placeInfos, HashMap<String, JSONObject> replaceMap) {
        if (placeInfos == null || placeInfos.isEmpty()) {
            return false;
        }
        String currentMapLanguage = getCurrentMapLanguage();
        // 更新每个PlaceInfo对象的坐标信息
        for (PlaceInfo placeInfo : placeInfos) {
            List<PlaceName> placeNames = mPlaceNameHelper.getPlaceNameByPlaceId(new String[]{placeInfo.getPlaceId()});
            if (placeNames.isEmpty()) {
                continue;
            }
            String placeName = placeNames.get(0).getPlaceName();
            if (placeNames.size() > 1) {
                // 筛选当前地图语言对应的点位名称
                List<PlaceName> filteredNames = placeNames.stream()
                        .filter(name -> currentMapLanguage.equals(name.getLanguageType()))
                        .collect(Collectors.toList());

                // 如果找到匹配当前语言的名称，则使用它
                if (!filteredNames.isEmpty()) {
                    placeName = filteredNames.get(0).getPlaceName();
                } else {
                    // 如果没有当前语言的名称，使用第一个可用的名称
                    placeName = placeNames.get(0).getPlaceName();
                }
            }
            JSONObject replaceBean = replaceMap.get(placeName);

            if (replaceBean != null) {
                try {
                    // 获取pose2d对象
                    JSONObject pose2d = replaceBean.optJSONObject("pose2d");
                    Log.d(TAG, "replace info: " + pose2d.toString() + ", replace place name: " + placeName + ", placeId: " + placeInfo.getPlaceId());
                    if (pose2d != null) {
                        // 更新PlaceInfo的坐标信息
                        placeInfo.setPointX((float) pose2d.optDouble("px"));
                        placeInfo.setPointY((float) pose2d.optDouble("py"));
                        placeInfo.setPointTheta((float) pose2d.optDouble("theta"));
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error updating place info: " + e.getMessage());
                }
            }
        }
        boolean result = mPlaceInfoHelper.updatePlaceInfo(placeInfos);

        // 点图同步情况下，需要单独处理: 替换的点位和Local表中的点位PlaceId相同
        if (NavigationDataManager.getInstance().isNapPosNotSeparate()) {
            if (!placeInfos.isEmpty()) {
                List<LocalPlaceInfo> localPlaceInfoList = mLocalPlaceInfoHelper.getLocalPlaceInfo(getMapName());
                if (!localPlaceInfoList.isEmpty()) {
                        // 创建placeId到LocalPlaceInfo的映射，提高查找效率
                        Map<String, LocalPlaceInfo> localPlaceMap = new HashMap<>();
                        for (LocalPlaceInfo localInfo : localPlaceInfoList) {
                            localPlaceMap.put(localInfo.getPlaceId(), localInfo);
                        }
                        // 创建需要更新的LocalPlaceInfo列表
                        List<LocalPlaceInfo> updatedLocalPlaceList = new ArrayList<>();

                        // 遍历placeInfos，查找并更新对应的本地点位
                        for (PlaceInfo placeInfo : placeInfos) {
                            String placeId = placeInfo.getPlaceId();
                            LocalPlaceInfo localInfo = localPlaceMap.get(placeId);

                            if (localInfo != null) {
                                // 找到匹配的本地点位，更新坐标
                                localInfo.setPointX(placeInfo.getPointX());
                                localInfo.setPointY(placeInfo.getPointY());
                                localInfo.setPointTheta(placeInfo.getPointTheta());
                                updatedLocalPlaceList.add(localInfo);
                                Log.d(TAG, "Updated local place info by replace: " + localInfo);
                            }
                        }

                        // 批量更新修改后的本地点位
                        if (!updatedLocalPlaceList.isEmpty()) {
                            mLocalPlaceInfoHelper.updateLocalPlaceInfo(updatedLocalPlaceList);
                            Log.d(TAG, "Updated " + updatedLocalPlaceList.size() + " local place info items");
                        }
                }
            }
        }
        return result;
    }

    public void restoreDbFile(Context context) {
        File databaseFile = context.getDatabasePath(MapDbOpenHelper.BACK_DB_NAME);
        if (!databaseFile.exists()) {
            Log.d(TAG, "restoreDbFile: backup db file not exist");
            return;
        }
        String dbSrc = databaseFile.getAbsolutePath();
        File targetDbFile = context.getDatabasePath(MapDbOpenHelper.DB_NAME);
        String targetDbPath = targetDbFile.getAbsolutePath();

        renameDbFile(dbSrc, targetDbPath);
        renameDbFile(dbSrc + "-shm", targetDbPath + "-shm");
        renameDbFile(dbSrc + "-wal", targetDbPath + "-wal");
    }

    private void renameDbFile(String srcPath, String destPath) {
        Log.d(TAG, "renameDbFile: srcPath=" + srcPath + " destPath=" + destPath);
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            Path source = Paths.get(srcPath);
            Path target = Paths.get(destPath);
            try {
                Files.move(source, target, StandardCopyOption.REPLACE_EXISTING);
                Log.d(TAG, "renameDbFile move: rename success");
            } catch (Exception e) {
                e.printStackTrace();
                Log.e(TAG, "renameDbFile move: rename failed error: " + e.getMessage());
            }
        } else {
            Log.d(TAG, "renameDbFile: not support move file, current version=" + android.os.Build.VERSION.SDK_INT);
        }
    }
    public JSONObject findByGateIds(String[] gateIds) {
        try {
            if (gateIds == null || gateIds.length == 0) {
                Log.d(TAG, "findByGateIds: 参数无效，gateIds为空");
                return createResult(false, null,"findByGateIds: 参数无效，gateIds为空!");
            }
            // 过滤掉空字符串
            List<String> validGateIds = new ArrayList<>();
            for (String gateId : gateIds) {
                if (!TextUtils.isEmpty(gateId)) {
                    validGateIds.add(gateId);
                }
            }
            if (validGateIds.isEmpty()) {
                Log.d(TAG, "findByGateIds: 过滤后没有有效的gateId");
                return createResult(false, null,"findByGateIds: 过滤后没有有效的gateId!");
            }
            List<GateRelationInfo> results= mGateRelationInfoHelper.findByGateIds(validGateIds);
            return createResult(true, results.toString(),"findByGateIds: 查询到" + results.size() + "条记录!");
        } catch (Exception e) {
            Log.e(TAG, "findByGateIds: 查询失败 - " + e.getMessage());
            return createResult(false, null,"findByGateIds: 查询失败 - " + e.getMessage());
        }
    }


    public JSONObject findByLineIds(int[] gateLineIds) {
        try {
            if (gateLineIds == null || gateLineIds.length == 0) {
                Log.d(TAG, "findByLineIds: 参数无效，gateLineIds为空");
                return createResult(false, null,"findByLineIds: 参数无效，gateLineIds为空!");
            }

            // 过滤掉无效值（小于等于0的值）
            List<Integer> validGateLineIds = new ArrayList<>();
            for (int gateLineId : gateLineIds) {
                if (gateLineId > 0) {
                    validGateLineIds.add(gateLineId);
                }
            }
            if (validGateLineIds.isEmpty()) {
                Log.d(TAG, "findByLineIds: 过滤后没有有效的gateLineId");
                return createResult(false, null,"findByLineIds: 过滤后没有有效的gateLineId!");
            }
            Log.d(TAG, "validGateLineIds: " + validGateLineIds.size());
            List<GateRelationInfo> results = mGateRelationInfoHelper.findByLineIds(validGateLineIds);
            return createResult(true, GsonUtil.toJson(results),"findByLineIds: 查询到" + results.size() + "条记录!");
        } catch (Exception e) {
            Log.e(TAG, "findByLineIds: 查询失败 - " + e.getMessage());
            return createResult(false, null,"findByLineIds: 查询失败 - " + e.getMessage());
        }
    }

    /**
     * 批量插入或更新闸机关系信息
     * 如果记录已存在（ID相同），则会更新该记录
     *
     * @param gateRelationInfos 闸机关系信息集合
     * @return 成功处理的记录数量
     */
    public JSONObject batchInsertOrUpdateGate(List<GateRelationInfo> gateRelationInfos) {
        try {
            if (gateRelationInfos == null || gateRelationInfos.isEmpty()) {
                Log.d(TAG, "batchInsertOrUpdateGate: 参数无效，gateRelationInfos为空");
                return createResult(false, null,"batchInsertOrUpdateGate: 参数无效，gateRelationInfos为空!");
            }

            // 过滤无效数据
            List<GateRelationInfo> validInfos = new ArrayList<>();
            for (GateRelationInfo info : gateRelationInfos) {
                if (info != null && info.getGateLineId() > 0 && !TextUtils.isEmpty(info.getGateId())) {
                    validInfos.add(info);
                }
            }

            if (validInfos.isEmpty()) {
                Log.d(TAG, "batchInsertOrUpdateGate: 过滤后没有有效的数据");
                return createResult(false, null,"batchInsertOrUpdateGate: 过滤后没有有效的数据!");
            }
            // 记录处理前的数量
            int count = validInfos.size();
            Log.e(TAG, "batchInsertOrUpdateGate: validInfos" + validInfos);
            mGateRelationInfoHelper.batchInsertOrUpdateGate(validInfos);
            return createResult(true, validInfos.toString(),"batchInsertOrUpdateGate: 成功处理" + count + "条记录!");
        } catch (Exception e) {
            Log.e(TAG, "batchInsertOrUpdateGate: 处理失败 - " + e.getMessage());
            return createResult(false, null,"batchInsertOrUpdateGate: 处理失败 - " + e.getMessage());
        }
    }

    /**
     * 删除传入闸机线主键以外的数据并返回删除的闸机关系集合
     * @param gateLineIds
     * @return
     */
    public JSONObject deleteExceptLineIds(int[] gateLineIds) {
        try {
            if (gateLineIds == null || gateLineIds.length == 0) {
                Log.d(TAG, "deleteExceptLineIds: 参数无效，gateLineIds为空");
                return createResult(false, null,"deleteExceptLineIds:参数无效，gateLineIds为空!");
            }
            List<Integer> validGateLineIds = new ArrayList<>();
            for (int gateLineId : gateLineIds) {
                if (gateLineId > 0) {
                    validGateLineIds.add(gateLineId);
                }
            }
            if (validGateLineIds.isEmpty()) {
                return createResult(false, null,"deleteExceptLineIds:过滤后没有有效的gateLineId!");
            }
            Log.d(TAG, "deleteExceptLineIds:validGateLineIds"+ validGateLineIds);
            List<GateRelationInfo> deletedItems = mGateRelationInfoHelper.deleteExceptLineIds(validGateLineIds);
            return createResult(true, GsonUtil.toJson(deletedItems),"deleteExceptLineIds: 成功删除" + deletedItems.size() + "条记录!");
        } catch (Exception e) {
            return createResult(false, null,"deleteExceptLineIds: 删除失败:"+e.getMessage());
        }
    }


    public JSONObject deleteByLineIds(int[] gateLineIds) {
        try {
            if (gateLineIds == null || gateLineIds.length == 0) {
                Log.d(TAG, "deleteByLineIds: 参数无效，gateLineIds为空");
                return createResult(false, null,"deleteByLineIds:参数无效，gateLineIds为空!");
            }
            List<Integer> validGateLineIds = new ArrayList<>();
            for (int gateLineId : gateLineIds) {
                if (gateLineId > 0) {
                    validGateLineIds.add(gateLineId);
                }
            }
            if (validGateLineIds.isEmpty()) {
                return createResult(false, null,"deleteByLineIds:过滤后没有有效的gateLineId!");
            }
            Log.d(TAG, "deleteByLineIds:validGateLineIds"+ validGateLineIds);
            long deletedCount = mGateRelationInfoHelper.deleteByLineIds(validGateLineIds);
            return createResult(true, validGateLineIds.toString(),"deleteByLineIds: 成功删除" + deletedCount + "条记录!");
        } catch (Exception e) {
            return createResult(false, null,"deleteByLineIds: 删除失败:"+e.getMessage());
        }
    }



    /**
     * 根据闸机ID数组批量删除闸机关系信息
     * @param gateIds 闸机ID字符串数组
     */
    public JSONObject deleteByGateIds(String[] gateIds) {
        try {
            if (gateIds == null || gateIds.length == 0) {
                Log.d(TAG, "deleteByGateIds: 参数无效，gateIds为空");
                return createResult(false, null,"deleteByGateIds:参数无效，gateIds为空!");
            }
            List<String> validGateIds = new ArrayList<>();
            for (String gateId : gateIds) {
                if (!TextUtils.isEmpty(gateId)) {
                    validGateIds.add(gateId);
                }
            }
            if (validGateIds.isEmpty()) {
                Log.d(TAG, "deleteByGateIds: 过滤后没有有效的gateId");
                return createResult(false, null,"deleteByGateIds:过滤后没有有效的gateId!");
            }
            long deletedCount = mGateRelationInfoHelper.deleteByGateIds(validGateIds);
            return createResult(true, validGateIds.toString(),"deleteByGateIds: 成功删除" + deletedCount + "条记录!");
        } catch (Exception e) {
            return createResult(false, null,"deleteByGateIds: 删除失败:"+e.getMessage());
        }
    }

    public JSONObject getAllGateRelationData() {
        try {
            List<GateRelationInfo> results =mGateRelationInfoHelper.getAllGateRelationData();
            return createResult(true, GsonUtil.toJson(results),"getAllGateRelationData: 查询到" + results.size() + "条记录!");
        } catch (Exception e) {
            Log.e(TAG, "getAllGateRelationData: 查询失败 - " + e.getMessage());
            return createResult(false, null,"getAllGateRelationData: 查询失败 - " + e.getMessage());
        }
    }

    /**
     * 创建操作结果的JSON对象
     * @param success 操作是否成功
     * @param message 操作结果消息
     * @return 包含操作结果的JSON对象
     */
    private JSONObject createResult(boolean success ,String data, String message) {
        JSONObject result = new JSONObject();
        try {
            result.put("success", success);
            result.put("data", data);
            result.put("message", message);
        } catch (JSONException e) {
            Log.e(TAG, "创建操作结果JSON对象失败: " + e.getMessage());
        }
        return result;
    }

    public GateLineNode.LineNode getGateLinesFromFile(String mapName) {
        File file = DataManager.getInstance().getPlaceFile(mapName, MapFileHelper.GATE_JSON);
        if (file == null || !file.exists()) {
            Log.e(TAG, "Gate JSON file not found for map: " + mapName);
            return null;
        }

        try {
            // 1. 读取文件内容为字符串
            String jsonContent = new String(Files.readAllBytes(file.toPath()), StandardCharsets.UTF_8);
            Log.d(TAG, "Read gate JSON file content: " + (jsonContent.length() > 100 ? jsonContent.substring(0, 100) + "..." : jsonContent));

            // 2. 直接使用JSONArray解析JSON数据
            JSONArray jsonArray = new JSONArray(jsonContent);
            Log.d(TAG, "Parsed JSON array with " + jsonArray.length() + " items");

            // 3. 创建 GateLineNode 和 LineNode 对象
            GateLineNode.LineNode lineNode = new GateLineNode.LineNode();
            List<GateLineUnit> gateLineList = new ArrayList<>();

            // 4. 遍历JSON数组并创建闸机线对象
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject edgeObj = jsonArray.getJSONObject(i);

                // 获取闸机线 ID
                int gateLineId = edgeObj.optInt("id", -1);

                // 获取起点和终点
                JSONObject startNodeObj = edgeObj.optJSONObject("startNode");
                JSONObject endNodeObj = edgeObj.optJSONObject("endNode");

                if (startNodeObj != null && endNodeObj != null) {
                    JSONObject startPosObj = startNodeObj.optJSONObject("position");
                    JSONObject endPosObj = endNodeObj.optJSONObject("position");

                    if (startPosObj != null && endPosObj != null) {
                        // 获取坐标值
                        double startX = startPosObj.optDouble("x", 0);
                        double startY = startPosObj.optDouble("y", 0);
                        double endX = endPosObj.optDouble("x", 0);
                        double endY = endPosObj.optDouble("y", 0);

                        // 创建闸机线单元
                        GateLineUnit lineUnit = new GateLineUnit(gateLineId,
                            new GateLineNode.Node(startX, startY), new GateLineNode.Node(endX, endY));

                        gateLineList.add(lineUnit);
                        Log.d(TAG, "Added gate line: " + lineUnit);
                    }
                }
            }
            // 5. 设置LineNode的闸机线列表
            lineNode.setGateLines(gateLineList);
            Log.d(TAG, "Successfully parsed gate JSON file for map: " + mapName + ", found " + gateLineList.size() + " gate lines");
            return lineNode;
        } catch (Exception e) {
            Log.e(TAG, "getGateLinesFromFile: Exception: " + e.getMessage(), e);
        }
        return null;
    }

    //分块输出 List 内容，防止内容太多，日志输出不全
    private <T> void printList(boolean printLog, String tag, List<T> list) {
        if (!printLog || list == null) {
            return;
        }
        int size = list.size();
        Log.d(TAG, tag + " all size:" + size);
        Log.d(TAG, tag + " ---printList start");

        int maxLogSize = 4000; // 每条日志的最大大小
        int partIndex = 0;

        for (T item : list) {
            String itemJson = GsonUtil.toJson(item);
            byte[] itemBytes = itemJson.getBytes(StandardCharsets.UTF_8);
            int itemLength = itemBytes.length;
            int start = 0;

            while (start < itemLength) {
                int overhead = tag.length() + TAG.length() + String.valueOf(partIndex).length() + 10;
                int end = Math.min(itemLength, start + maxLogSize - overhead);
                String part = new String(itemBytes, start, end - start, StandardCharsets.UTF_8);
                Log.d(TAG, String.format("%s %d: %s", tag, partIndex++, part));
                start = end;
            }
        }
        Log.d(TAG, tag + " ---printList end");
    }
}