package com.ainirobot.navigationservice.chassisAbility.wifinan;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.wifi.WifiManager;
import android.net.wifi.aware.AttachCallback;
import android.net.wifi.aware.DiscoverySessionCallback;
import android.net.wifi.aware.IdentityChangedListener;
import android.net.wifi.aware.PeerHandle;
import android.net.wifi.aware.PublishConfig;
import android.net.wifi.aware.PublishDiscoverySession;
import android.net.wifi.aware.SubscribeConfig;
import android.net.wifi.aware.SubscribeDiscoverySession;
import android.net.wifi.aware.WifiAwareManager;
import android.net.wifi.aware.WifiAwareSession;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import android.util.Log;

import com.ainirobot.navigationservice.ApplicationWrapper;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


/**
 * WifiNAN 功能需要Android 8.0及其以上的系统版本才支持.
 * WifiNAN 通信控制类，
 */
@RequiresApi(api = Build.VERSION_CODES.O)
public class RobotAwareManager {

    private static final String TAG = RobotAwareManager.class.getSimpleName();
    private Context mContext;
    private static RobotAwareManager sInstance;
    private WifiAwareManager mWifiAwareManager;
    private WifiAwareSession mWifiAwareSession;
    private PublishDiscoverySession mPublishDisSession;
    private SubscribeDiscoverySession mSubscribeDisSession;
    private volatile boolean mIsHasNan = false;
    private volatile boolean mWifiNanAvailable = false;
    private BroadcastReceiver mWifiAwareStateReceiver;
    private final String SERVICE_NAME = "com.ainirobot.WifiNanTest";
    private final byte[] mTestServiceInfo = "android_nan_test".getBytes();
    private MyHandler mHandler;
    public static final int MSG_PULISH_DATA = 0x01;
    public static final int MSG_SUBSCRIBE_DATA = 0x02;
    private SessionMessageListener mMessageListener;

    private RobotAwareManager() {
        mContext = ApplicationWrapper.getContext();
    }

    private static class SingletonHolder {
        private static final RobotAwareManager mInstance = new RobotAwareManager();
    }

    public static RobotAwareManager getInstance() {
        return SingletonHolder.mInstance;
    }

    public void init() {
        resetAllConfig();
        initDefaultConfig();
        registWifiAwareStateReceiver();
        HandlerThread handlerThread = new HandlerThread(TAG);
        handlerThread.start();
        mHandler = new MyHandler(handlerThread.getLooper());
    }

    public void registMessageListener(SessionMessageListener listener) {
        this.mMessageListener = listener;
    }

    public void unregistMessageListener() {
        this.mMessageListener = null;
    }

    public void destoryAll() {
        Log.d(TAG, "destoryAll");
        removeAllHandlerMsg();
        unregistWifiAwareStateReceiver();
        closeSession();
    }

    /**
     * 重置所有的配置信息
     */
    private void resetAllConfig() {
        mWifiAwareManager = null;
        mWifiAwareSession = null;
        mPublishDisSession = null;
        mSubscribeDisSession = null;
    }

    private void initDefaultConfig() {
        PackageManager packageManager = mContext.getPackageManager();
        if (packageManager != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mIsHasNan = packageManager.hasSystemFeature(PackageManager.FEATURE_WIFI_AWARE);
        }

        if (mIsHasNan) {
            mWifiAwareManager = (WifiAwareManager) mContext.getSystemService(Context.WIFI_AWARE_SERVICE);
            mWifiNanAvailable = mWifiAwareManager.isAvailable();
            Log.d(TAG,"initDefaultConfig mWifiNanAvailable :"+mWifiNanAvailable);
            if (mWifiNanAvailable) {
                attachToNanSession();
            }
        }
    }

    private void registWifiAwareStateReceiver() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Log.d(TAG, "register WifiAwareStateReceiver");
            IntentFilter filter = new IntentFilter(WifiAwareManager.ACTION_WIFI_AWARE_STATE_CHANGED);
            mWifiAwareStateReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    mWifiNanAvailable = mWifiAwareManager.isAvailable();
                    if (mWifiNanAvailable) {
                        Log.d(TAG, "AwareStateReceiver NAN Available");
                        attachToNanSession();
                    } else {
                        Log.d(TAG, "AwareStateReceiver NAN unavailable");
                        closeSession();
                    }
                }
            };

            mContext.registerReceiver(mWifiAwareStateReceiver, filter);
        }

    }

    private void unregistWifiAwareStateReceiver() {
        Log.d(TAG, "unregistWifiAwareStateReceiver");
        try {
            mContext.unregisterReceiver(mWifiAwareStateReceiver);
        } catch (Exception e) {
            Log.w(TAG, "Error unregistering receiver: " + e.getMessage());
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    private void attachToNanSession() {
        Log.d(TAG, "mWifiAwareManager :" + mWifiAwareManager);
        if (mWifiAwareManager == null || !mWifiAwareManager.isAvailable()) {
            Log.d(TAG, "attachToNanSession mWifiAwareManager is not Available!");
            return;
        }
        mWifiAwareManager.attach(new AttachCallback() {
            @Override
            public void onAttached(WifiAwareSession session) {
                super.onAttached(session);
                Log.d(TAG, "attachToNanSession onAttached:" + session);
                mWifiAwareSession = session;
                subscribeToService();
            }

            @Override
            public void onAttachFailed() {
                super.onAttachFailed();
                Log.d(TAG, "attachToNanSession onAttachFailed!");
            }

        }, new IdentityChangedListener() {
            @Override
            public void onIdentityChanged(byte[] mac) {
                super.onIdentityChanged(mac);
                Log.d(TAG, "attachToNanSession onIdentityChanged:" + mac);

            }
        }, null);
    }


    private void subscribeToService() {

        if (mWifiAwareSession == null) {
            Log.d(TAG,"subscribe mWifiAwareSession is null ");
            return;
        }
        SubscribeConfig config = new SubscribeConfig.Builder()
                .setServiceName(SERVICE_NAME)
                .setServiceSpecificInfo(mTestServiceInfo)
                .build();
        Log.d(TAG, "build finish");
        mWifiAwareSession.subscribe(config, new DiscoverySessionCallback() {

            @Override
            public void onServiceDiscovered(PeerHandle peerHandle, byte[] serviceSpecificInfo,
                                            List<byte[]> matchFilter) {
                super.onServiceDiscovered(peerHandle, serviceSpecificInfo, matchFilter);
                if (mMessageListener != null) {
                    mMessageListener.onDiscoverDevice(serviceSpecificInfo);
                }
//                String info = new String(serviceSpecificInfo);
//                Log.d(TAG, "subscribe onServiceDiscovered handle:" + peerHandle.hashCode()
//                        + " info = " + info);
            }

            @Override
            public void onSessionConfigUpdated() {
                Log.d(TAG, "subscribe session Config Update OK");
            }

            @Override
            public void onSessionConfigFailed() {
                Log.d(TAG, "subscribe session Config Update fail");
            }

            @Override
            public void onSubscribeStarted(@NonNull SubscribeDiscoverySession session) {
                super.onSubscribeStarted(session);
                Log.d(TAG, "subscribe onSubscribe Started send mac:" + session);
                mSubscribeDisSession = session;

            }

            @Override
            public void onMessageReceived(PeerHandle peerHandle, byte[] message) {
                super.onMessageReceived(peerHandle, message);
                Log.d(TAG, "subscribe onMessageReceived:" + new String(message));

            }

            @Override
            public void onPublishStarted(@NonNull PublishDiscoverySession session) {
                super.onPublishStarted(session);
                Log.d(TAG, "subscribe onPublishStarted: session:" + session);
            }

            @Override
            public void onSessionTerminated() {
                super.onSessionTerminated();
                Log.d(TAG, "subscribe onSessionTerminated");
            }

            @Override
            public void onServiceDiscoveredWithinRange(PeerHandle peerHandle,
                                                       byte[] serviceSpecificInfo,
                                                       List<byte[]> matchFilter, int distanceMm) {
                super.onServiceDiscoveredWithinRange(peerHandle, serviceSpecificInfo, matchFilter
                        , distanceMm);
                Log.d(TAG, "subscribe onServiceDiscoveredWithinRange: peerHandle:" + peerHandle
                        + " info:" + new String(serviceSpecificInfo) + " filter:" + matchFilter + " distance:" + distanceMm);
            }

            @Override
            public void onMessageSendSucceeded(int messageId) {
                super.onMessageSendSucceeded(messageId);
                Log.d(TAG, "subscribe onMessageSendSucceeded: messageId:" + messageId);
            }

            @Override
            public void onMessageSendFailed(int messageId) {
                super.onMessageSendFailed(messageId);
                Log.d(TAG, "subscribe onMessageSendFailed: msgId:" + messageId);
            }
        }, null);
    }

    private void closeSession() {
        if (mPublishDisSession != null) {
            mPublishDisSession.close();
            mPublishDisSession = null;
        }

        if (mSubscribeDisSession != null) {
            mSubscribeDisSession.close();
            mSubscribeDisSession = null;
        }
    }

    public void updatePublishConfig(byte[] serviceInfo) {
        Log.d(TAG, "updatePublish info length : " + serviceInfo.length);
        if (mPublishDisSession != null) {
            PublishConfig config = new PublishConfig.Builder()
                    .setServiceName(SERVICE_NAME)
                    .setServiceSpecificInfo(serviceInfo)
                    .build();
            mPublishDisSession.updatePublish(config);
        } else {
            publishService(serviceInfo);
        }
    }

    private void publishService(byte[] serviceInfo) {

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            return;
        }
        if (serviceInfo == null || serviceInfo.length <= 0) {
            Log.d(TAG, "publishService serviceInfo is null!");
            return;
        }

        if (mWifiAwareSession == null) {
            Log.d(TAG, "publishService mWifiAwareSession is null!");
            return;
        }

        PublishConfig config = new PublishConfig.Builder()
                .setServiceName(SERVICE_NAME)
                .setServiceSpecificInfo(serviceInfo)
                .build();

        Log.d(TAG, "publishService config:" + config.toString());
        mWifiAwareSession.publish(config, new DiscoverySessionCallback() {
            @Override
            public void onPublishStarted(@NonNull PublishDiscoverySession session) {
                super.onPublishStarted(session);
                Log.d(TAG, "publish onPublishStarted------- " + session);
                mPublishDisSession = session;
            }

            @Override
            public void onSessionConfigUpdated() {
                Log.d(TAG, "publish session Config Update OK");
            }

            @Override
            public void onSessionConfigFailed() {
                Log.d(TAG, "publish session Config Update fail");
            }

            @Override
            public void onMessageReceived(PeerHandle peerHandle, byte[] message) {
                super.onMessageReceived(peerHandle, message);
                Log.d(TAG, "publish onMessageReceived-----------" + message);

            }

            @Override
            public void onServiceDiscovered(PeerHandle peerHandle, byte[] serviceSpecificInfo,
                                            List<byte[]> matchFilter) {
                super.onServiceDiscovered(peerHandle, serviceSpecificInfo, matchFilter);
                Log.d(TAG, "publish onServiceDiscovered peer:" + peerHandle + " info:" + serviceSpecificInfo
                        + " match:" + matchFilter);
            }

            @Override
            public void onServiceDiscoveredWithinRange(PeerHandle peerHandle,
                                                       byte[] serviceSpecificInfo,
                                                       List<byte[]> matchFilter, int distanceMm) {
                super.onServiceDiscoveredWithinRange(peerHandle, serviceSpecificInfo, matchFilter
                        , distanceMm);
                Log.d(TAG, "publish onServiceDiscoveredWithinRange peer:" + peerHandle + " info:" + serviceSpecificInfo
                        + " match:" + matchFilter + " distance:" + distanceMm);
            }

            @Override
            public void onMessageSendSucceeded(int messageId) {
                super.onMessageSendSucceeded(messageId);
                Log.d(TAG, "publish onMessageSendSucceeded messageId:" + messageId);
            }

            @Override
            public void onMessageSendFailed(int messageId) {
                super.onMessageSendFailed(messageId);
                Log.d(TAG, "publish onMessageSendFailed messageId:" + messageId);
            }
        }, null);

    }

    public String getCurrentTime() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss-SSS");
        Date curDate = new Date(System.currentTimeMillis());
        //获取当前时间
        String str = formatter.format(curDate);
        return str;
    }

    public interface SessionMessageListener {
        void onDiscoverDevice(byte[] message);
    }

    class MyHandler extends Handler {
        public MyHandler() {

        }

        public MyHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_PULISH_DATA:

                    break;
                case MSG_SUBSCRIBE_DATA:

                    break;

                default:
                    break;
            }
        }
    }

    private void removeAllHandlerMsg() {
        if (mHandler != null) {
            mHandler.removeMessages(MSG_PULISH_DATA);
            mHandler.removeMessages(MSG_SUBSCRIBE_DATA);
            mHandler = null;
        }
    }

}
