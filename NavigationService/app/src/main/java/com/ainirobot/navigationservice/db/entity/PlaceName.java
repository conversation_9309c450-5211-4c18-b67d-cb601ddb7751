package com.ainirobot.navigationservice.db.entity;

import io.objectbox.annotation.Entity;
import io.objectbox.annotation.Id;

/**
 * @version V1.0.0
 * @date 2020/7/21 14:15
 */
@Entity
public class PlaceName {
    @Id
    public long id;
    private String placeId;
    private String placeName;
    private String languageType;

    public PlaceName() {
    }

    public PlaceName(String placeId, String languageType, String placeName) {
        this.placeId = placeId;
        this.languageType = languageType;
        this.placeName = placeName;
    }

    public String getPlaceId() {
        return placeId;
    }

    public void setPlaceId(String placeId) {
        this.placeId = placeId;
    }

    public String getPlaceName() {
        return placeName;
    }

    public void setPlaceName(String placeName) {
        this.placeName = placeName;
    }

    public String getLanguageType() {
        return languageType;
    }

    public void setLanguageType(String languageType) {
        this.languageType = languageType;
    }

    @Override
    public String toString() {
        return "PlaceName{" +
                "placeId='" + placeId + '\'' +
                ", placeName='" + placeName + '\'' +
                ", languageType='" + languageType + '\'' +
                '}';
    }
}
