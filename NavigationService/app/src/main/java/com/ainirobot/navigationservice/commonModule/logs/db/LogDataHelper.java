/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.navigationservice.commonModule.logs.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.provider.BaseColumns;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.navigationservice.commonModule.logs.LogManager;
import com.ainirobot.navigationservice.commonModule.logs.beans.LogCacheBean;

import java.util.ArrayList;
import java.util.List;

public class LogDataHelper extends BaseDataHelper {

    private static final String LOG_TAG = "LogDataHelper";

    public LogDataHelper(Context context) {
        super(context);
    }

    @Override
    public SQLiteTable getTable() {
        return SQLiteTable.LOG;
    }

    public LogCacheBean insertLog(LogCacheBean logBean) {
        //判断缓存是否已经超出最大限制
        int deleteNumber = getAllLog().size() - LogManager.MAX_LOG_TASK_LIMIT;
        if (deleteNumber > 0) {
            boolean deleteSuccess = deleteOlderLog(deleteNumber);
            Log.d(LOG_TAG, " delete result " + deleteSuccess + " deleteNumber " + deleteNumber);
        }

        ContentValues values = getContentValues(logBean);
        insert(values);
        return logBean;
    }

    private boolean deleteOlderLog(int deleteNumber) {
        if (deleteNumber <= 0) {
            return false;
        }
        int row = delete( BaseColumns._ID + " in (select " + BaseColumns._ID + " from " + SQLiteTable.LOG.getTableName() + " order by "
                + BaseColumns._ID + " limit ?)", new String[]{String.valueOf(deleteNumber)});
        return row > 0;
    }

    public boolean deleteLogById(String taskId) {
        if (TextUtils.isEmpty(taskId)) {
            return false;
        }
        int row = delete(LogField.CACHE_ID + "=?", new String[]{String.valueOf(taskId)});
        return row > 0;
    }

    public boolean deleteAllLog() {
        int row = delete(null, null);
        return row > 0;
    }

    public void updateLogStatus(LogCacheBean logBean) {
        if (logBean == null) {
            return;
        }

        ContentValues values = getContentValues(logBean);
        update(values, LogField.CACHE_ID + "=?", new String[]{String.valueOf(logBean.getCacheId())});
    }

    public boolean updateLogStatusById(String cacheId, int status) {
        if (TextUtils.isEmpty(cacheId)) {
            Log.d(LOG_TAG, " updateLogStatus failed, because taskId is empty");
            return false;
        }

        ContentValues values = new ContentValues();
        values.put(LogField.TASK_STATUS, status);
        int rows = update(values, LogField.CACHE_ID + "=?", new String[]{cacheId});
        return rows > 0;
    }

    public void updateLogByTime(String cacheId, String time) {
        if (TextUtils.isEmpty(cacheId)) {
            Log.d(LOG_TAG, " updateLogByTime failed, because taskId is empty");
            return;
        }

        ContentValues values = new ContentValues();
        values.put(LogField.TASK_TIME, time);
        update(values, LogField.CACHE_ID + "=?", new String[]{cacheId});
    }

    public LogCacheBean getLogById(String taskId) {
        if (TextUtils.isEmpty(taskId)) {
            return null;
        }

        Cursor cursor = query(LogField.CACHE_ID + "=?", new String[]{taskId});
        return parseOneCursor(cursor);
    }

    public LogCacheBean getOneLog() {
        Cursor cursor = query(LogField.TASK_STATUS + "=?",
                new String[]{String.valueOf(Definition.STATUS_LOG_DEFAULT_TASK)});
        return parseOneCursor(cursor);
    }

    public List<LogCacheBean> getAllLog() {
        Cursor cursor = query(null, null);
        return parseCursor(cursor);
    }

    private LogCacheBean parseOneCursor(Cursor cursor) {
        List<LogCacheBean> logBeans = parseCursor(cursor);
        if (logBeans.size() > 0) {
            return logBeans.get(0);
        }
        return null;
    }

    private LogCacheBean parseLastCursor(Cursor cursor) {
        List<LogCacheBean> logBeans = parseCursor(cursor);
        if (logBeans.size() > 0) {
            return logBeans.get(logBeans.size() - 1);
        }
        return null;
    }

    private List<LogCacheBean> parseCursor(Cursor cursor) {
        List<LogCacheBean> logBeans = new ArrayList<>();
        if (cursor == null) {
            return logBeans;
        }

        while (cursor.moveToNext()) {
            LogCacheBean logBean = new LogCacheBean();

            logBean.setCacheId(cursor.getString(cursor.getColumnIndex(LogField.CACHE_ID)));
            logBean.setType(cursor.getString(cursor.getColumnIndex(LogField.TASK_TYPE)));
            logBean.setTime(cursor.getString(cursor.getColumnIndex(LogField.TASK_TIME)));
            logBean.setStatus(cursor.getInt(cursor.getColumnIndex(LogField.TASK_STATUS)));
            logBean.setTk1Version(cursor.getString(cursor.getColumnIndex(LogField.Tk1VERSION)));
            logBean.setExpandField(cursor.getString(cursor.getColumnIndex(LogField.EXPAND_FIELD)));
            logBean.setExpandField2(cursor.getString(cursor.getColumnIndex(LogField.EXPAND_FIELD_2)));
            logBean.setExpandField3(cursor.getString(cursor.getColumnIndex(LogField.EXPAND_FIELD_3)));
            logBean.setExpandField4(cursor.getString(cursor.getColumnIndex(LogField.EXPAND_FIELD_4)));
            logBean.setExpandField5(cursor.getString(cursor.getColumnIndex(LogField.EXPAND_FIELD_5)));

            logBeans.add(logBean);
        }
        cursor.close();
        return logBeans;
    }

    private ContentValues getContentValues(LogCacheBean logBean) {
        ContentValues values = new ContentValues();
        values.put(LogField.CACHE_ID, logBean.getCacheId());
        values.put(LogField.TASK_TYPE, logBean.getType());
        values.put(LogField.TASK_TIME, logBean.getTime());
        values.put(LogField.TASK_STATUS, logBean.getStatus());
        values.put(LogField.Tk1VERSION, logBean.getTk1Version());
        values.put(LogField.EXPAND_FIELD, logBean.getExpandField());
        values.put(LogField.EXPAND_FIELD_2, logBean.getExpandField2());
        values.put(LogField.EXPAND_FIELD_3, logBean.getExpandField3());
        values.put(LogField.EXPAND_FIELD_4, logBean.getExpandField4());
        values.put(LogField.EXPAND_FIELD_5, logBean.getExpandField5());
        return values;
    }

    static final class LogField implements BaseColumns {
        static final String CACHE_ID = "cacheId";
        static final String TASK_TYPE = "type";
        static final String TASK_TIME = "time";
        static final String TASK_STATUS = "status";
        static final String Tk1VERSION = "tk1Version";
        static final String EXPAND_FIELD = "expand_field";
        static final String EXPAND_FIELD_2 = "expand_field_2";
        static final String EXPAND_FIELD_3 = "expand_field_3";
        static final String EXPAND_FIELD_4 = "expand_field_4";
        static final String EXPAND_FIELD_5 = "expand_field_5";

        static final SQLiteTable.TableField VALUE = new SQLiteTable.TableField()
                .addColumn(CACHE_ID, Column.DataType.TEXT)
                .addColumn(TASK_TYPE, Column.DataType.TEXT)
                .addColumn(TASK_TIME, Column.DataType.TEXT)
                .addColumn(TASK_STATUS, Column.DataType.INTEGER)
                .addColumn(Tk1VERSION, Column.DataType.TEXT)
                .addColumn(EXPAND_FIELD, Column.DataType.TEXT)
                .addColumn(EXPAND_FIELD_2, Column.DataType.TEXT)
                .addColumn(EXPAND_FIELD_3, Column.DataType.TEXT)
                .addColumn(EXPAND_FIELD_4, Column.DataType.TEXT)
                .addColumn(EXPAND_FIELD_5, Column.DataType.TEXT);
    }
}
