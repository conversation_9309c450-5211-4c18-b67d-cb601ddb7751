package com.ainirobot.navigationservice.roversdkhelper.maptype;

import com.ainirobot.coreservice.client.Definition;

import ninjia.android.proto.CommonProtoWrapper;

/**
 * 底盘地图类型和端上业务类型做一层映射，不做死绑定
 * <p>
 * 业务层直接使用的自定义值{@link Definition.VisionType,Definition.TargetType}
 * <p>
 * 此映射值只在调用底盘接口时使用，把底盘映射值设置给底盘
 */
public enum NaviMapTypeMapping {
    //视觉类型
    VISION_TYPE_NONE(Definition.VisionType.TYPE_NONE.getValue(),
            CommonProtoWrapper.MapConfigProto.VisionModeProto.kVisionNone.getNumber()),
    VISION_TYPE_VISION(Definition.VisionType.TYPE_VISION.getValue(),
            CommonProtoWrapper.MapConfigProto.VisionModeProto.kVisionALikeDelg.getNumber()),
    //标识码类型
    TARGET_TYPE_NONE(Definition.TargetType.TYPE_TARGET_NONE.getValue(),
            CommonProtoWrapper.MapConfigProto.TargetModeProto.kTargetNone.getNumber()),
    TARGET_TYPE_NORMAL(Definition.TargetType.TYPE_TARGET_NORMAL.getValue(),
            CommonProtoWrapper.MapConfigProto.TargetModeProto.kTargetCrp.getNumber()),
    TARGET_TYPE_QL(Definition.TargetType.TYPE_TARGET_QL.getValue(),
            CommonProtoWrapper.MapConfigProto.TargetModeProto.kTargetBin.getNumber()),
    TARGET_TYPE_PD(Definition.TargetType.TYPE_TARGET_PD.getValue(),
            CommonProtoWrapper.MapConfigProto.TargetModeProto.kTargetHex.getNumber()),
    ;

    /**
     * 机器端类型，默认定义为和底盘值一致，有特殊要求的值也可以不一样
     */
    int mRobotType;
    /**
     * 底盘算法定义类型，后续可能会变化
     */
    int mNaviType;

    NaviMapTypeMapping(int robotType, int naviType) {
        this.mRobotType = robotType;
        this.mNaviType = naviType;
    }

    public int getRobotType() {
        return mRobotType;
    }

    public int getNaviType() {
        return mNaviType;
    }

    public static NaviMapTypeMapping fromRobotVisionType(int robotVisionType) {
        switch (robotVisionType) {
            case 0:
                return VISION_TYPE_NONE;
            case 1:
                return VISION_TYPE_VISION;
            default:
                return null;
        }
    }

    public static NaviMapTypeMapping fromRobotTargetType(int robotTargetType) {
        switch (robotTargetType) {
            case 0:
                return TARGET_TYPE_NONE;
            case 1:
                return TARGET_TYPE_NORMAL;
            case 2:
                return TARGET_TYPE_QL;
            case 3:
                return TARGET_TYPE_PD;
            default:
                return null;
        }
    }

}
