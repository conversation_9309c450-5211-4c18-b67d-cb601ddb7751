package com.ainirobot.navigationservice.chassisAbility.controller;

import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.navigationservice.beans.tk1.Pose;
import com.ainirobot.navigationservice.chassisAbility.chassis.client.IChassisClient;
import com.ainirobot.navigationservice.utils.RLog;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import ninjia.android.proto.ChassisPacketProtoWrapper;

/**
 * 方案初衷：解决新版远程建图调用基础运动时，无法通过窄通道问题
 * 方案逻辑：
 */
public class NarrowPassagePolicy extends BaseAvoidPolicy {
    private static final String TAG = NarrowPassagePolicy.class.getSimpleName();
    private IChassisClient mChassis;
    private ScheduledExecutorService mExecutor = Executors.newScheduledThreadPool(1);
    private Future mFuture;
    private Pose mCurrentPose; // 障碍物坐标系下的机器人当前位置
    private ChassisPacketProtoWrapper.RealtimeObsMapProto mRealtimeObsProto; // 前方障碍物数据
    private static final int MAX_AVOID_COUNT = 10; // 前方最多障碍物数
    private static final double RECT_WIDTH = 1f; // 矩形长1m
    private double RECT_HEIGHT = 0.6f; // 矩形宽0.6m 暂时以0.6m还需要进行测试
    private int avoidCount = 0; // 避障计数器
    private long lastAvoidTime = 0; // 上次避障时间

    // 日志控制参数
    private static final long LOG_INTERVAL = 5000; // 5秒内只打印一次日志
    private static long lastLogTime = 0; // 上次打印日志的时间
    private static int maxCountHitTimes = 0; // 记录进入最大计数条件的次数

    public NarrowPassagePolicy(IChassisClient mChassis) {
        super();
        init(mChassis);
    }

    private void init(IChassisClient mChassis) {
        this.mChassis = mChassis;
        if (ProductInfo.isCarryProduct()) {
            RECT_HEIGHT = 0.8f;
        }
        startNarrowPassageAvoidTask();
    }

    private void startNarrowPassageAvoidTask() {
        if (mFuture == null) {
            mFuture = mExecutor.scheduleAtFixedRate(new Runnable() {
                @Override
                public void run() {
                    try {
                        handleNarrowPassageAvoidTask();
                    } catch (Exception e) {
                        Log.e(TAG, "startNarrowPassageAvoidTask Exception : " + e.getMessage());
                    }
                }
            }, 0, 100, TimeUnit.MILLISECONDS);
        }
    }

    private void handleNarrowPassageAvoidTask() {
        mCurrentPose = mChassis.getRealtimePose();
        if (mCurrentPose == null) {
            Log.i(TAG, "handleNarrowPassageAvoid : real time pose data is null");
            return;
        }
        mRealtimeObsProto = mChassis.getRealtimeObsMapProto();
        if (mRealtimeObsProto == null) {
            Log.i(TAG, "handleNarrowPassageAvoid : mRealtimeObsProto data is null");
            return;
        }
        // 检测障碍物
        boolean hasObstacle = isObstacleInRectangle(mRealtimeObsProto);

        // 应用避障计数逻辑
        long currentTime = System.currentTimeMillis();
        if (hasObstacle) {
            if (currentTime - lastAvoidTime < 500) {
                // 在时间窗口内，增加计数
                avoidCount++;
            } else {
                // 超出时间窗口，重新开始计数
                avoidCount = 1;
            }
            lastAvoidTime = currentTime;

            RLog.v(TAG, "handleNarrowPassageAvoidTask lastAvoidTime:" + lastAvoidTime + " avoidCount:" + avoidCount);

            if (avoidCount >= MAX_AVOID_COUNT) {
                // 记录进入最大计数条件的次数
                maxCountHitTimes++;
                
                // 检查是否超过5秒没有打印日志
                long currentLogTime = System.currentTimeMillis();
                if (currentTime - lastLogTime >= LOG_INTERVAL) {
                    Log.d(TAG, "handleNarrowPassageAvoidTask avoid count max: " + avoidCount + ", hit times in last 5s: " + maxCountHitTimes);
                    lastLogTime = currentLogTime;
                    maxCountHitTimes = 0; // 重置计数
                }
                
                // 触发高危险评分
                onScoreResult(OBSTACLES_SCORE_PERILOUS);
                // 重置计数器，避免重复触发
                avoidCount = 0;
            } else {
                // 普通危险评分
                onScoreResult(OBSTACLES_SCORE_DANGEROUS);
            }
        } else {
            // 没有障碍物，重置计数
            avoidCount = 0;
            // 安全评分
            onScoreResult(OBSTACLES_SCORE_SAFE);
        }
    }

    private boolean isObstacleInRectangle(ChassisPacketProtoWrapper.RealtimeObsMapProto realtimeObsMapProto) {
        double[] rectPoints = calculateForwardRectangleVertices(mCurrentPose.getX(), mCurrentPose.getY(), mCurrentPose.getTheta(),
                RECT_WIDTH, RECT_HEIGHT);
        // 获取障碍物地图的原点、分辨率、宽度和高度
        float originX = realtimeObsMapProto.getOriginX();
        float originY = realtimeObsMapProto.getOriginY();
        float resolution = realtimeObsMapProto.getResolution();
        int width = realtimeObsMapProto.getWidth();
        int height = realtimeObsMapProto.getHeight();
        List<ChassisPacketProtoWrapper.RealtimeObsMapProto.ObsType> dataList = realtimeObsMapProto.getDataList();
        RLog.v(TAG, "isObstacleInRectangle: origin=(" + originX + "," + originY + ") resolution=" +
                resolution + " width=" + width + " height=" + height + ", SIZE: " + dataList.size());

        // 遍历障碍物地图数据
        for (int i = 0; i < height; i++) {
            for (int j = 0; j < width; j++) {
                if (dataList.get(i * width + j) != ChassisPacketProtoWrapper.RealtimeObsMapProto.ObsType.kNone) {
                    // 获取障碍物点的位置
                    double obsX = originX + j * resolution;
                    double obsY = originY + i * resolution;

                    // 判断障碍物点是否在矩形区域内
                    if (isPointInPolygon(obsX, obsY, rectPoints)) {
                        RLog.v(TAG, "isObstacleInRectangle: obstacle detected");
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 计算从当前机器人位置，向前推进1m后画矩形
     * 矩形：长1m, 宽60cm
     * @param x 机器人当前位置横坐标
     * @param y 机器人当前位置纵坐标
     * @param theta 机器人当前位置朝向角度
     * @param width 矩形长
     * @param height 矩形宽
     */
    private double[] calculateForwardRectangleVertices(double x, double y, double theta, double width, double height) {
        // 计算矩形中心点向前推进的位置
        double forwardX = x + width * Math.cos(theta);
        double forwardY = y + width * Math.sin(theta);

        // 计算矩形的四个顶点
        double halfWidth = height / 2.0;
        double cosTheta = Math.cos(theta);
        double sinTheta = Math.sin(theta);

        // 矩形顶点相对于中心点的偏移
        double[] vertices = new double[8];
        vertices[0] = forwardX + halfWidth * sinTheta; // 右前顶点
        vertices[1] = forwardY - halfWidth * cosTheta;
        vertices[2] = forwardX - halfWidth * sinTheta; // 左前顶点
        vertices[3] = forwardY + halfWidth * cosTheta;
        vertices[4] = x - halfWidth * sinTheta;        // 左后顶点
        vertices[5] = y + halfWidth * cosTheta;
        vertices[6] = x + halfWidth * sinTheta;        // 右后顶点
        vertices[7] = y - halfWidth * cosTheta;

        return vertices;
    }

    /**
     * 判断点 (x, y) 是否在多边形内（射线法）
     * @param x       点的X坐标
     * @param y       点的Y坐标
     * @param polygon 多边形顶点数组 [x1,y1, x2,y2, ...]
     * @return 是否在多边形内
     */
    private boolean isPointInPolygon(double x, double y, double[] polygon) {
        int n = polygon.length / 2;
        boolean inside = false;
        for (int i = 0, j = n - 1; i < n; j = i++) {
            double xi = polygon[2 * i], yi = polygon[2 * i + 1];
            double xj = polygon[2 * j], yj = polygon[2 * j + 1];
            boolean intersect = ((yi > y) != (yj > y)) &&
                    (x < (xj - xi) * (y - yi) / (yj - yi) + xi);
            if (intersect) inside = !inside;
        }
        return inside;
    }

}
