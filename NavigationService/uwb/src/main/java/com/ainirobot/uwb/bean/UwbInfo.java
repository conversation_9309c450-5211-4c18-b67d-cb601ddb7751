package com.ainirobot.uwb.bean;


/**
 * 文件名称: SerialDataInfo
 * 串口数据：
 *  mStationMac 基站Mac
 *  mTagMac    标签Mac
 *  mDistance  基站与标签距离
 *  mTagBatteryLevel 标签电源电量
 *
 * 功能描述: 串口数据信息
 */
public class UwbInfo {

    private String mStationMac;
    private String mTagMac;
    private float mDistance;
    private long mTagBatteryLevel;

    public String getStationMac() {
        return mStationMac;
    }

    public void setStationMac(String mStationMac) {
        this.mStationMac = mStationMac;
    }

    public String getTagMac() {
        return mTagMac;
    }

    public void setTagMac(String mTagMac) {
        this.mTagMac = mTagMac;
    }

    public float getDistance() {
        return mDistance;
    }

    public void setDistance(float mDistance) {
        this.mDistance = mDistance;
    }

    public long getTagBatteryLevel() {
        return mTagBatteryLevel;
    }

    public void setTagBatteryLevel(long mTagBatteryLevel) {
        this.mTagBatteryLevel = mTagBatteryLevel;
    }

    @Override
    public String toString() {
        return "UwbInfo{" +
                "mStationMac='" + mStationMac + '\'' +
                ", mTagMac='" + mTagMac + '\'' +
                ", mDistance=" + mDistance +
                ", mTagBatteryLevel=" + mTagBatteryLevel +
                '}';
    }
}
