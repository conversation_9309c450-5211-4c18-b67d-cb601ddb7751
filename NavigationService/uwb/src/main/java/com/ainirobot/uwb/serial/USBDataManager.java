package com.ainirobot.uwb.serial;

import android.content.Context;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbManager;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import com.ainirobot.uwb.bean.FilterLinkedDeQue;
import com.ainirobot.uwb.bean.UwbInfo;
import com.ainirobot.uwb.util.ByteUtil;
import com.ainirobot.uwb.util.MessageConstants;
import com.hoho.android.usbserial.driver.UsbSerialDriver;
import com.hoho.android.usbserial.driver.UsbSerialPort;
import com.hoho.android.usbserial.driver.UsbSerialProber;
import com.hoho.android.usbserial.util.SerialInputOutputManager;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.LinkedBlockingDeque;

import static android.content.Context.USB_SERVICE;

public class USBDataManager {


    private static final String TAG = USBDataManager.class.getSimpleName();
    private static final int CACHE_MAX_LENGTH = 2048;
    private static final int MIN_LENGTH = 4;// 包括数据长度字节位的串口数据包的最小长度.
    private static final int UWB_PARAM_LENGTH = 4 ; // 数据包中的 各个参数数据的标准长度
    private static final long SECOND = 1000;
    private int index = 0;
    private Context appContext = null;
    private final LinkedBlockingDeque<UwbInfo> mUwbInfoDeque;
    private volatile boolean isRunning = true;
    private byte[] cacheData = new byte[CACHE_MAX_LENGTH];
    private byte[] stickHeadData;

    private UsbSerialDriver mCurrentDriver;
    private IOnNewUwbDataListener mOnNewUwbDataListener = null;
    private SerialInputOutputManager mUsbIoManager;
    private UsbSerialPort mPort;
    private UsbDeviceConnection connection;
    private final MyHandler workHandler;
    private static final int MSG_INIT_USB = 0;
    private static final int MSG_OPEN_USB_CONNECTION = 1;
    private static final int MSG_CLOSE_USB_CONNECTION = 2;
    private UsbManager mUsbManager;

    private static final class SingletonHolder {
        private static final USBDataManager mInstance = new USBDataManager();
    }

    public static USBDataManager getInstance() {
        return SingletonHolder.mInstance;
    }

    private USBDataManager() {
        mUwbInfoDeque = new FilterLinkedDeQue<>();
        new Thread(mRunnable).start();

        HandlerThread mWorkThread = new HandlerThread("UwbWorkThread");
        mWorkThread.start();
        workHandler = new MyHandler(mWorkThread.getLooper());
    }

    private final class MyHandler extends android.os.Handler {

        public MyHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case MSG_OPEN_USB_CONNECTION:
                    connectUsbData();
                    break;
                case MSG_CLOSE_USB_CONNECTION:
                    closeUsb();
                    break;
                default:
                    break;
            }
        }
    }


    public void initUSB(Context applicationContext) {
        appContext = applicationContext;
        mUsbManager = (UsbManager) applicationContext.getSystemService(USB_SERVICE);
        // 检索设备
        HashMap<String, UsbDevice> deviceList = mUsbManager.getDeviceList();
        Log.d(TAG, "initUSB deviceList : "+deviceList.toString());


        List<UsbSerialDriver> availableDrivers = UsbSerialProber.getDefaultProber().findAllDrivers(mUsbManager);
        if (availableDrivers.isEmpty()) {
            Log.e(TAG, "initUSB empty");
            return;
        }
        mCurrentDriver = availableDrivers.get(0);
        Log.d(TAG, "availableDrivers:" + availableDrivers.size());
        if (mCurrentDriver == null) {
            Log.e(TAG, "initUSB mCurrentDriver is null");
        }
    }

    public void startConnectUSBData() {
        workHandler.sendEmptyMessage(MSG_OPEN_USB_CONNECTION);
    }

    public void stopConnectUSB() {
        mOnNewUwbDataListener = null;
        workHandler.sendEmptyMessage(MSG_CLOSE_USB_CONNECTION);
    }

    private void connectUsbData() {
        // 连接获取数据
        if (mUsbManager == null || mCurrentDriver == null) {
            Log.d(TAG, "mUsbIoManager or mCurrentDriver is null , initUSB again. ");
            initUSB(appContext);
            return;
        }

        connection = mUsbManager.openDevice(mCurrentDriver.getDevice());
        if (connection == null) {
            // add UsbManager.requestPermission(driver.getDevice(), ..) handling here
            Log.e(TAG, "permission err");
            return;
        }

        // Most devices have just one port (port 0)
        mPort = mCurrentDriver.getPorts().get(0);
        Log.d(TAG, "initUSB mPort : " + mPort+", devices :"+mCurrentDriver.getDevice().toString());

        try {
            mPort.open(connection);// 先打开连接,然后设置连接参数,否则报mPort.setParameters 空指针
            mPort.setParameters(115200, 8, UsbSerialPort.STOPBITS_1, UsbSerialPort.PARITY_NONE);

            mUsbIoManager = new SerialInputOutputManager(mPort, new SerialInputOutputManager.Listener() {
                @Override
                public void onNewData(byte[] data) {
                    handleNewData(data);
                }

                @Override
                public void onRunError(Exception e) {
                    Log.e(TAG, "SerialInputOutputManager onRunError: " + e.getLocalizedMessage());
                    workHandler.sendEmptyMessageDelayed(MSG_OPEN_USB_CONNECTION, 5 * SECOND);
                }
            });
            mUsbIoManager.start();
            Log.d(TAG, "connectUsbData and start listen data");
        } catch (IOException e) {
            e.printStackTrace();
            workHandler.sendEmptyMessageDelayed(MSG_OPEN_USB_CONNECTION, 5 * SECOND);
        }
    }


    /**
     * UWB串口通信,数据协议解析
     * <p>
     * 1、55 AA :前导符；
     * 2、21 10 7F 34 00 90 43 83 00 00 EC B1 01 00 02 12 00 00为T L V数据；
     *      a、21 :type,即基站和标签测距的数据帧标识符；
     *      b、10：lengh，即TLV数据的长度（“7F 34 00 90 43 83 00 00 EC B1 01 00 02 12 00 00”长度为0x10）；
     *      c、7F 34 00 90 43 83 00 00 EC B1 01 00 02 12 00 00：value，即数据内容；
     *          7F 34 00 90：9000347f 基站ID (注意接收原始数据和真实含义的差异)
     *          43 83 00 00：00008343 标签ID
     *          EC B1 01 00：0x0001B1EC基站和标签之间的距离（十进制是 111,084毫米）
     *          02 12 00 00：0x00001202  标签的电源电量（十进制是 4612毫伏）
     * 3、0D 0A：结束符。
     *
     * @param data
     */
    public void handleNewData(byte[] data) {
        int size = data.length;
        String content = ByteUtil.bytes2HexStr(data, 0, size);
        Log.d(TAG, "handleNewData: size:" + size + " , content:" + content);

        //判断是否为 一个数据的开头
        if (size > MIN_LENGTH
                && (data[0] == MessageConstants.HEAD_FIRST)
                && (data[1] == MessageConstants.HEAD_SECOND)
                && (data[2] == MessageConstants.HEAD_TYPE)) {
            cleanCacheData();
            cleanStickData();
            System.arraycopy(data, 0, cacheData, index, size);
            index = index + size;
            Log.d(TAG, "data size > MIN_LENGTH index:" + index);
            checkData();

        } else if (index > MIN_LENGTH
                && cacheData[0] == MessageConstants.HEAD_FIRST
                && cacheData[1] == MessageConstants.HEAD_SECOND
                && cacheData[2] == MessageConstants.HEAD_TYPE) {    //处理分包数据
            if (index + size > CACHE_MAX_LENGTH) {
                Log.e(TAG, "handleData: receive data is too long, 超过最大缓存,不可做后续数据复制");
                cleanCacheData();
                return;
            }
            System.arraycopy(data, 0, cacheData, index, size);
            index = index + size;
            Log.d(TAG, "index > MIN_LENGTH index:" + index);
            checkData();

        } else {
            if (stickHeadData != null) {
                //如果有缓存的粘包头数据
                Log.d(TAG, "handleData: handle stick data");
                byte[] newData = new byte[stickHeadData.length + data.length];
                System.arraycopy(stickHeadData, 0, newData, 0, stickHeadData.length);
                System.arraycopy(data, 0, newData, stickHeadData.length, data.length);
                cleanStickData();
                handleNewData(newData);
            } else {
                if (index + size > CACHE_MAX_LENGTH) {
                    Log.e(TAG, "handleData: receive data is too long, 超过最大缓存,不可做后续数据复制");
                    cleanCacheData();
                    return;
                }
                Log.d(TAG, "index < MIN_LENGTH index:" + index);
                System.arraycopy(data, 0, cacheData, index, size);
                index = index + size;
            }
        }
    }

    private synchronized void checkData() {

        int messageLength = cacheData[3];
        int totalSize = MIN_LENGTH + messageLength + 2;
        Log.d(TAG, "checkData index:" + index + " ,messageLength : " + messageLength+ ", totalSize:" + totalSize);
        if (index < totalSize) {
            Log.d(TAG, "checkData index < totalSize return");
            return;
        }

        byte[] finalData = new byte[totalSize];
        System.arraycopy(cacheData, 0, finalData, 0, finalData.length);
        if (checkTail(finalData[totalSize-2], finalData[totalSize - 1])){

            parseStandardData(finalData);

            if (index > totalSize) { //判断是否出现了粘包数据
                //生成粘包头数据
                Log.d(TAG, "checkData: save stick data");
                stickHeadData = new byte[index - totalSize];
                System.arraycopy(cacheData, totalSize, stickHeadData, 0, stickHeadData.length);
                Log.d(TAG, "checkData: stick data:" + ByteUtil.bytes2HexStr(stickHeadData));
                cleanCacheData();
                //如果粘包的数据大于等于最小长度，直接处理；否则仅保存粘包数据头和下次数据做拼接
                if (stickHeadData.length >= MIN_LENGTH) {
                    byte[] newData = stickHeadData;
                    cleanStickData();
                    handleNewData(newData);
                }
            } else {
                cleanCacheData();
            }
        }else {
            Log.e(TAG, "handleData: checkcode fail"); //结尾校验失败
            cleanCacheData();
        }
        Log.d(TAG, "checkData end");
    }

    /**
     * 解析标准数据包的内容
     * @param finalData
     */
    private void parseStandardData(byte[] finalData) {

        UwbInfo uwbInfo = new UwbInfo();
        byte[] tmp = new byte[UWB_PARAM_LENGTH];

        System.arraycopy(finalData, 4, tmp, 0, UWB_PARAM_LENGTH);
        byte[] station = getRealBytes(tmp);
        String hexStation = ByteUtil.bytes2HexStr(tmp, 0, UWB_PARAM_LENGTH);
        String hexRealStation = ByteUtil.bytes2HexStr(station, 0, UWB_PARAM_LENGTH);
        Log.d(TAG, "parseStandardData hexStation : " + hexStation);
        uwbInfo.setStationMac(hexRealStation);
        Arrays.fill(tmp, (byte) 0x00);

        System.arraycopy(finalData, 8, tmp, 0, UWB_PARAM_LENGTH);
        byte[] tag = getRealBytes(tmp);
        String hexTag = ByteUtil.bytes2HexStr(tmp, 0, UWB_PARAM_LENGTH);
        String hexRealTag = ByteUtil.bytes2HexStr(tag, 0, UWB_PARAM_LENGTH);
        Log.d(TAG, "parseStandardData hexTag : " + hexTag);
        uwbInfo.setTagMac(hexRealTag);
        Arrays.fill(tmp, (byte) 0x00);

        System.arraycopy(finalData, 12, tmp, 0, UWB_PARAM_LENGTH);
        byte[] distance = getRealBytes(tmp);
        String hexDistance = ByteUtil.bytes2HexStr(tmp, 0, UWB_PARAM_LENGTH);
        String hexRealDistance = ByteUtil.bytes2HexStr(distance, 0, UWB_PARAM_LENGTH);
        Log.d(TAG, "parseStandardData hexDistance : " + hexDistance + " , hexRealDistance : " + hexRealDistance);
        uwbInfo.setDistance(ByteUtil.hexStr2decimal(hexRealDistance) / 1000f);
        Arrays.fill(tmp, (byte) 0x00);

        System.arraycopy(finalData, 16, tmp, 0, UWB_PARAM_LENGTH);
        byte[] batteryLevel = getRealBytes(tmp);
        String hexLevel = ByteUtil.bytes2HexStr(tmp, 0, UWB_PARAM_LENGTH);
        String hexRealLevel = ByteUtil.bytes2HexStr(batteryLevel, 0, UWB_PARAM_LENGTH);
        Log.d(TAG, "parseStandardData hexLevel : " + hexLevel + " , hexRealLevel : " + hexRealLevel);
        uwbInfo.setTagBatteryLevel(ByteUtil.hexStr2decimal(hexRealLevel));
        Arrays.fill(tmp, (byte) 0x00);

        Log.d(TAG, "parseStandardData: " + uwbInfo.toString());
        offer(uwbInfo);

    }

    /**
     * 根据协议格式 重新存储数据
     * @param tmp
     * @return
     */
    private byte[] getRealBytes(byte[] tmp) {
        if (tmp.length != UWB_PARAM_LENGTH){
            return null;
        }
        byte[] ret = new byte[UWB_PARAM_LENGTH];

        for (int i = 0; i < UWB_PARAM_LENGTH; i++) {
            ret[i] = tmp[UWB_PARAM_LENGTH - 1 - i];
        }
        return ret;
    }

    /**
     * 检查结尾字节
     * @param last 结尾字节
     * @param subLast 倒数第二
     * @return true 完整的数据包, false 异常数据包
     */
    private boolean checkTail(byte subLast, byte last) {
        return subLast == MessageConstants.TAIL_SUB_LAST
                && last == MessageConstants.TAIL_LAST;
    }


    private void cleanCacheData() {
        Log.d(TAG, "cleanCacheData");
        index = 0;
        Arrays.fill(cacheData, (byte) 0x00);
    }

    private void cleanStickData() {
        Log.d(TAG, "cleanStickData");
        stickHeadData = null;
    }

    private void closeUsb() {
        Log.d(TAG, "closeUsb and remove handle message");
        workHandler.removeMessages(MSG_OPEN_USB_CONNECTION);
        workHandler.removeMessages(MSG_CLOSE_USB_CONNECTION);
        try {
            if (mUsbIoManager != null) {
                mUsbIoManager.stop();
                mUsbIoManager = null;
            }

            if (mPort != null) {
                mPort.close();
                mPort = null;
            }

            if (connection != null) {
                connection.close();
                connection = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        exitMsgQueue();
    }


    private Runnable mRunnable = new Runnable() {
        @Override
        public void run() {
            while (isRunning) {
                pollStatusMessage();
            }
        }
    };

    private void pollStatusMessage() {
        UwbInfo uwbInfo = null;
        try {
            uwbInfo = mUwbInfoDeque.take();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        if (uwbInfo != null && mOnNewUwbDataListener != null) {
            mOnNewUwbDataListener.onNewUwbUpdate(uwbInfo);
        }
    }


    private void offer(UwbInfo msg) {
        if (mUwbInfoDeque != null && msg != null) {
            try {
                mUwbInfoDeque.put(msg);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    private void exitMsgQueue() {
//        isRunning = false;
        mUwbInfoDeque.clear();
    }

    public void setOnUwbInfoListener(IOnNewUwbDataListener listener) {
        this.mOnNewUwbDataListener = listener;
    }


}
