package com.ainirobot.uwb.bean;

/**
 * Uwb 方案中标签和基站的距离
 */
public class TagDistance {

    public static final float UNKNOWN = -1.0f;
    public String tag = "";
    public float d1 = UNKNOWN;
    public float d2 = UNKNOWN;
    public float d3 = UNKNOWN;


    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public float getD1() {
        return d1;
    }

    public void setD1(float d1) {
        this.d1 = d1;
    }

    public float getD2() {
        return d2;
    }

    public void setD2(float d2) {
        this.d2 = d2;
    }

    public float getD3() {
        return d3;
    }

    public void setD3(float d3) {
        this.d3 = d3;
    }

    @Override
    public String toString() {
        return "TagDistance{" +
                "tag='" + tag + '\'' +
                ", d1=" + d1 +
                ", d2=" + d2 +
                ", d3=" + d3 +
                '}';
    }
}
