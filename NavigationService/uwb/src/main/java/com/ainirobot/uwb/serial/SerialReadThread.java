package com.ainirobot.uwb.serial;

import android.os.SystemClock;
import android.util.Log;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 读串口线程
 */
public class SerialReadThread extends Thread {

    private static final String TAG = "SerialReadThread";

    private BufferedInputStream mInputStream;

    public SerialReadThread(InputStream is) {
        mInputStream = new BufferedInputStream(is);
    }

    @Override
    public void run() {
        byte[] received = new byte[1024];
        int size;

        Log.e(TAG,"开始读线程");

        while (true) {
//            LogPlus.e("true");
            if (Thread.currentThread().isInterrupted()) {
                break;
            }
            try {

                int available = mInputStream.available();

                if (available > 0) {
                    size = mInputStream.read(received);
                    if (size > 0) {
                        onDataReceive(received, size);
                    }
                } else {
                    // 暂停一点时间，免得一直循环造成CPU占用率过高
                    SystemClock.sleep(1);
                }
            } catch (IOException e) {
                Log.e(TAG,"读取数据失败", e);
            }
            //Thread.yield();
        }

        Log.e(TAG,"结束读进程");
    }

    public static StringBuffer STRING_BUFFER = new StringBuffer();

    /**
     * 处理获取到的数据
     *
     * @param data
     * @param size
     */
    private void onDataReceive(byte[] data, int size) {
        Log.e(TAG,"SerialReadThread onDataReceive " + data + " ============= " + size);

        byte[] realData = new byte[size];
        System.arraycopy(data, 0, realData, 0, size);
//        handleNewData(realData);
    }


    /**
     * 停止读线程
     */
    public void close() {

        try {
            mInputStream.close();
        } catch (IOException e) {
            Log.e(TAG,"异常", e);
        } finally {
            super.interrupt();
        }
    }
}
