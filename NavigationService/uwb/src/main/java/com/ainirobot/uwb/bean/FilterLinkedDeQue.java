package com.ainirobot.uwb.bean;

import com.ainirobot.uwb.util.FilterUtil;

import java.util.concurrent.LinkedBlockingDeque;

public class FilterLinkedDeQue<E> extends LinkedBlockingDeque<UwbInfo> {

    public FilterLinkedDeQue(){
        super();
    }

    @Override
    public void put(UwbInfo info) throws InterruptedException {
        UwbInfo filter = FilterUtil.getInstance().filter(info);
        super.put(filter);
    }

    @Override
    public void clear() {
        FilterUtil.getInstance().cleanCacheD();
        super.clear();
    }
}
