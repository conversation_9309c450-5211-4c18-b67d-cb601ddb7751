package com.ainirobot.uwb.util;


import com.ainirobot.uwb.bean.TagDistance;
import com.ainirobot.uwb.bean.UwbInfo;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 串口数据波动滤波
 */
public class FilterUtil {

    private final Object LOCK = new Object();
    private ConcurrentHashMap<String, TagDistance> mHashMap = new ConcurrentHashMap<>();


    public static FilterUtil getInstance() {
        return FilterUtil.SingletonHolder.mInstance;
    }

    private static class SingletonHolder {
        private static final FilterUtil mInstance = new FilterUtil();
    }

    private FilterUtil() {
    }

    /**
     * 1.一个步长范围内，前后两帧数据偏差均小于DEVIATION，则本次步长内所使用d值以步长内3帧数据算术平均值为主
     * 2.一个步长范围内，条件1以外，则判断为数据波动，若出现了数据波动，为保证安全，本次步长内所使用d值以最大数据为主
     *
     * @param info 实时距离
     * @return
     */
    public UwbInfo filter(UwbInfo info) {

        synchronized (LOCK){
            String tagMac = info.getTagMac();
            boolean contains = mHashMap.containsKey(tagMac);
            if (contains){
                TagDistance tagDistance = mHashMap.get(tagMac);
                float result = deviation(info, tagDistance);
                mHashMap.put(tagMac,tagDistance);
                info.setDistance(result);
                return info;
            }else {
                TagDistance tagDistance = new TagDistance();
                tagDistance.setTag(tagMac);
                tagDistance.setD1(info.getDistance());
                mHashMap.put(tagMac,tagDistance);
                return info;
            }
        }
    }

    private float deviation(UwbInfo info,TagDistance tagDistance) {
        float distance = info.getDistance();

        if (tagDistance.getD1() <= 0) {
            tagDistance.setD1(distance);
            return distance;
        }
        if (tagDistance.getD2() <= 0) {
            tagDistance.setD2(distance);
            return distance;
        }

        float result;
        tagDistance.setD3(distance);

        float d1 = tagDistance.getD1();
        float d2 = tagDistance.getD2();
        float d3 = tagDistance.getD3();

        // 步长中的数据偏差
        float DEVIATION = 0.5f;
        if (Math.abs(d1 - d2) < DEVIATION && Math.abs(d2 - d3) < DEVIATION) {
            result = (d1 + d2 + d3) / 3.0f;

        } else {
            result = Math.max(Math.max(d1, d2), d3);
        }

        tagDistance.setD1(d2);
        tagDistance.setD2(d3);
        tagDistance.setD3(TagDistance.UNKNOWN);
        return result;
    }

    public void cleanCacheD() {
        mHashMap.clear();
    }
}
